@tailwind base;
@tailwind components;
@tailwind utilities;

/* Animated Background Elements */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Chat Animation Styles */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animate-slideUp {
  animation: slideUp 0.5s ease-out;
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in;
}

.animate-fadeOut {
  animation: fadeOut 0.3s ease-out;
}

/* تأثيرات مخصصة للتصميم المحسّن */
@layer utilities {
  /* تأثير التوهج المتحرك */
  .glow-animation {
    animation: glow 2s ease-in-out infinite alternate;
  }

  @keyframes glow {
    from {
      box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
    }
    to {
      box-shadow: 0 0 30px rgba(168, 85, 247, 0.8), 0 0 40px rgba(236, 72, 153, 0.6);
    }
  }

  /* تأثير الموجة */
  .wave-animation {
    animation: wave 3s ease-in-out infinite;
  }

  @keyframes wave {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* تأثير الدوران البطيء */
  .slow-spin {
    animation: slow-spin 8s linear infinite;
  }

  @keyframes slow-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* تأثير النبض المحسّن */
  .enhanced-pulse {
    animation: enhanced-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes enhanced-pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  /* تأثير الضوء المتحرك */
  .shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* دوران طولي (حول المحور Y) */
  .rotate-y {
    animation: rotateY 40s linear infinite;
  }

  .rotate-y-reverse {
    animation: rotateY 50s linear infinite reverse;
  }

  .rotate-y-fast {
    animation: rotateY 30s linear infinite;
  }

  .rotate-y-slow {
    animation: rotateY 45s linear infinite reverse;
  }

  @keyframes rotateY {
    0% {
      transform: rotateY(0deg);
    }
    100% {
      transform: rotateY(360deg);
    }
  }

  /* تأثير الكرة ثلاثية الأبعاد مع ألوان متحركة */
  .sphere-3d {
    position: relative;
    overflow: hidden;
    box-shadow:
      inset 10px 10px 20px rgba(255, 255, 255, 0.2),
      inset -10px -10px 20px rgba(0, 0, 0, 0.3),
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 60px rgba(37, 99, 235, 0.3);
  }

  .sphere-3d::before {
    content: '';
    position: absolute;
    inset: 0;
    background:
      radial-gradient(circle at 30% 30%,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(255, 255, 255, 0.4) 20%,
        transparent 45%),
      radial-gradient(circle at 70% 70%,
        rgba(0, 0, 0, 0.3) 0%,
        transparent 50%);
    border-radius: inherit;
    z-index: 3;
    pointer-events: none;
  }

  .sphere-3d::after {
    content: '';
    position: absolute;
    inset: 0;
    background:
      linear-gradient(45deg,
        #fbbf24 0%,
        #f59e0b 20%,
        #d97706 40%,
        #2563eb 60%,
        #1d4ed8 80%,
        #fbbf24 100%);
    background-size: 400% 400%;
    animation: colorFlow 8s ease-in-out infinite;
    border-radius: inherit;
    z-index: 1;
  }

  @keyframes colorFlow {
    0% {
      background-position: 0% 50%;
    }
    25% {
      background-position: 100% 50%;
    }
    50% {
      background-position: 100% 100%;
    }
    75% {
      background-position: 0% 100%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .sphere-glow {
    background: radial-gradient(circle at 30% 30%,
      rgba(251, 191, 36, 0.6) 0%,
      rgba(37, 99, 235, 0.4) 50%,
      transparent 70%);
    animation: glowPulse 6s ease-in-out infinite;
  }

  @keyframes glowPulse {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  /* تأثيرات التجسيم الكريستالي */
  .crystal-card {
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(0, 0, 0, 0.1) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .crystal-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }

  .crystal-button {
    position: relative;
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.8) 0%,
      rgba(37, 99, 235, 0.9) 50%,
      rgba(29, 78, 216, 1) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(147, 197, 253, 0.3);
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .crystal-button:hover {
    background: linear-gradient(135deg,
      rgba(96, 165, 250, 0.9) 0%,
      rgba(59, 130, 246, 1) 50%,
      rgba(37, 99, 235, 1) 100%);
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  .crystal-icon {
    position: relative;
    background: linear-gradient(135deg,
      rgba(251, 191, 36, 0.9) 0%,
      rgba(245, 158, 11, 1) 50%,
      rgba(217, 119, 6, 1) 100%);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(254, 243, 199, 0.3);
    box-shadow:
      0 4px 16px rgba(251, 191, 36, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .crystal-icon:hover {
    background: linear-gradient(135deg,
      rgba(252, 211, 77, 1) 0%,
      rgba(251, 191, 36, 1) 50%,
      rgba(245, 158, 11, 1) 100%);
    box-shadow:
      0 6px 20px rgba(251, 191, 36, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  .crystal-sidebar {
    background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 50%,
      rgba(51, 65, 85, 0.85) 100%);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow:
      4px 0 24px rgba(0, 0, 0, 0.3),
      inset -1px 0 0 rgba(255, 255, 255, 0.1);
  }

  .crystal-nav-item {
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
  }

  .crystal-nav-item:hover {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.2) 0%,
      rgba(37, 99, 235, 0.1) 100%);
    border: 1px solid rgba(147, 197, 253, 0.3);
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
  }

  .crystal-nav-item.active {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.3) 0%,
      rgba(37, 99, 235, 0.2) 100%);
    border: 1px solid rgba(147, 197, 253, 0.4);
    box-shadow:
      0 4px 16px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  /* تحسينات إضافية للألوان */
  .crystal-gradient-bg {
    background: linear-gradient(135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 25%,
      rgba(51, 65, 85, 0.85) 50%,
      rgba(71, 85, 105, 0.8) 75%,
      rgba(100, 116, 139, 0.75) 100%);
  }

  .crystal-text-glow {
    text-shadow:
      0 0 10px rgba(59, 130, 246, 0.5),
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 30px rgba(59, 130, 246, 0.1);
  }

  .crystal-border-glow {
    border: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow:
      0 0 10px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .crystal-hover-lift:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(59, 130, 246, 0.2);
  }

  /* تأثيرات خاصة للشعار */
  .logo-crystal-effect {
    position: relative;
    overflow: hidden;
  }

  .logo-crystal-effect::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
      from 0deg,
      transparent,
      rgba(59, 130, 246, 0.1),
      transparent,
      rgba(251, 191, 36, 0.1),
      transparent
    );
    animation: logoRotate 8s linear infinite;
  }

  @keyframes logoRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* تأثيرات الجسيمات */
  .particle-effect {
    position: relative;
    overflow: hidden;
  }

  .particle-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      radial-gradient(circle at 80% 80%, rgba(251, 191, 36, 0.1) 1px, transparent 1px),
      radial-gradient(circle at 40% 60%, rgba(168, 85, 247, 0.1) 1px, transparent 1px);
    background-size: 50px 50px, 30px 30px, 70px 70px;
    animation: particleFloat 20s linear infinite;
    pointer-events: none;
  }

  @keyframes particleFloat {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(-5px) translateX(-3px); }
    75% { transform: translateY(-15px) translateX(8px); }
    100% { transform: translateY(0) translateX(0); }
  }

  .crystal-game-card {
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(0, 0, 0, 0.1) 100%);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    overflow: hidden;
  }

  .crystal-game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
    transition: left 0.6s ease;
  }

  .crystal-game-card:hover::before {
    left: 100%;
  }

  .crystal-game-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 16px 48px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 20px rgba(59, 130, 246, 0.2);
  }

  .crystal-header {
    background: linear-gradient(135deg,
      rgba(30, 58, 138, 0.95) 0%,
      rgba(30, 41, 59, 0.9) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(59, 130, 246, 0.3);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.2),
      inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  }

  /* طبقة ألوان إضافية متحركة */
  .color-layer {
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background:
      radial-gradient(circle at 20% 80%,
        rgba(251, 191, 36, 0.4) 0%,
        transparent 50%),
      radial-gradient(circle at 80% 20%,
        rgba(37, 99, 235, 0.4) 0%,
        transparent 50%),
      radial-gradient(circle at 50% 50%,
        rgba(245, 158, 11, 0.3) 0%,
        transparent 60%);
    background-size: 300% 300%;
    animation: colorWave 12s linear infinite;
    z-index: 2;
    pointer-events: none;
  }

  @keyframes colorWave {
    0% {
      background-position: 0% 0%, 100% 100%, 50% 50%;
    }
    33% {
      background-position: 100% 0%, 0% 100%, 0% 50%;
    }
    66% {
      background-position: 0% 100%, 100% 0%, 100% 50%;
    }
    100% {
      background-position: 0% 0%, 100% 100%, 50% 50%;
    }
  }

  /* تأثيرات الغرف الصوتية */
  @keyframes floatUp {
    0% {
      transform: translateY(0px);
      opacity: 1;
    }
    100% {
      transform: translateY(-100px);
      opacity: 0;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
