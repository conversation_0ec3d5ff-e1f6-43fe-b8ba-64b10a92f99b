<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة Infinity Box</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
        canvas { border: 2px solid #ccc; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>🎨 إنشاء أيقونة Infinity Box</h1>
    <canvas id="iconCanvas" width="1024" height="1024"></canvas>
    <br>
    <button onclick="downloadIcon()">📥 تحميل الأيقونة</button>
    
    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        
        // رسم الأيقونة
        function drawIcon() {
            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, 1024, 1024);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 1024, 1024);
            
            // رسم رمز اللانهاية
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 80;
            ctx.lineCap = 'round';
            
            // رسم رمز ∞
            ctx.beginPath();
            ctx.arc(300, 512, 150, 0, Math.PI * 2);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.arc(724, 512, 150, 0, Math.PI * 2);
            ctx.stroke();
            
            // نص "BOX"
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 120px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('BOX', 512, 750);
        }
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'infinity-box-icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // رسم الأيقونة عند تحميل الصفحة
        drawIcon();
    </script>
</body>
</html>
