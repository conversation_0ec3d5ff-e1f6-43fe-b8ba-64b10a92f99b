<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌲 لعبة الغابة - اكتشف الحيوانات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5016, #4a7c59, #2d5016);
            color: white;
            min-height: 100vh;
            padding: 10px;
        }

        .game-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
        }

        .back-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            border-radius: 12px;
            width: 45px;
            height: 40px;
            color: white;
            font-size: 1.3em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.5);
            background: linear-gradient(135deg, #ff5252, #e53935);
        }

        .back-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        .game-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .score-display {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1a202c;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }

        /* واجهة العملات */
        .coins-display {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .coin-counter {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.2em;
            font-weight: bold;
        }

        .coin-icon {
            font-size: 1.5em;
        }

        #coins-count {
            color: #ffd700;
            font-size: 1.3em;
        }

        .game-info {
            display: flex;
            flex-direction: column;
            gap: 5px;
            font-size: 0.9em;
            color: #cbd5e0;
        }

        /* بيانات اللاعب */
        .player-info {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(255, 215, 0, 0.2);
            border-radius: 15px;
            padding: 15px;
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .player-avatar {
            flex-shrink: 0;
        }

        .player-details {
            flex: 1;
        }

        .player-balance {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: right;
        }

        /* إحصائيات اللعبة */
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            color: #cbd5e0;
        }

        /* منطقة اللعب */
        .game-area {
            background: linear-gradient(135deg, #1a4d2e, #2d5016);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            min-height: 400px;
            position: relative;
            border: 3px solid rgba(255, 215, 0, 0.3);
        }

        /* هدف البحث */
        .target-animal {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid rgba(255, 215, 0, 0.5);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .target-animal h3 {
            color: #ffd700;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .target-info {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .target-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 10px;
            border: 2px solid #ffd700;
        }

        .target-details {
            color: #e2e8f0;
        }

        .target-reward {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 8px 15px;
            border-radius: 10px;
            font-weight: bold;
        }

        /* شبكة الأقفاص */
        .cages-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin: 15px 0;
            max-width: 100%;
            padding: 0 10px;
        }

        .cage {
            background: linear-gradient(135deg, #8B4513, #CD853F, #DEB887);
            border: 3px solid #654321;
            border-radius: 12px;
            padding: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .cage:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 215, 0, 0.4);
            border-color: #ffd700;
        }

        .cage.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .cage-cover {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 5px;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.5));
        }

        .cage-number {
            position: absolute;
            top: 3px;
            left: 3px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1a202c;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7em;
            font-weight: bold;
            border: 2px solid #654321;
        }

        .cage-content {
            display: none;
        }

        .cage.opened .cage-cover {
            display: none;
        }

        .cage.opened .cage-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        /* تأثير فتح القفص */
        .cage.opening .cage-cover {
            animation: cageOpen 0.5s ease-out forwards;
        }

        @keyframes cageOpen {
            0% {
                transform: scale(1) rotateY(0deg);
                opacity: 1;
            }
            50% {
                transform: scale(1.1) rotateY(90deg);
                opacity: 0.5;
            }
            100% {
                transform: scale(0) rotateY(180deg);
                opacity: 0;
                display: none;
            }
        }

        .revealed-image {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #ffd700;
        }

        .revealed-name {
            font-weight: bold;
            color: #ffd700;
            font-size: 0.7em;
            margin: 2px 0;
        }

        .revealed-result {
            padding: 3px 6px;
            border-radius: 6px;
            font-size: 0.6em;
            font-weight: bold;
            margin-top: 2px;
        }

        .result-win {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .result-lose {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .result-target {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #1a202c;
        }

        .result-multiplier {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            animation: glow 1s ease-in-out infinite alternate;
        }

        .result-double-loss {
            background: linear-gradient(135deg, #dc2626, #991b1b);
            color: white;
        }

        /* أزرار التحكم */
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* رسائل اللعبة */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        .message.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .message.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }

        .message.info {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسينات للجوال */
        @media (max-width: 768px) {
            .game-container {
                margin: 2px;
                padding: 5px;
                max-width: 100vw;
                box-sizing: border-box;
            }

            .header {
                margin-bottom: 5px;
                padding: 4px 6px;
                flex-wrap: wrap;
            }

            .game-title {
                font-size: 0.9em;
                order: 1;
                flex: 1;
                text-align: center;
            }

            .back-btn {
                width: 35px;
                height: 32px;
                font-size: 1.1em;
                order: 0;
                border-radius: 8px;
            }

            .coins-display {
                padding: 6px;
                margin: 5px 0;
                flex-direction: column;
                gap: 5px;
                text-align: center;
            }

            .coin-counter {
                font-size: 1em;
            }

            .game-info {
                font-size: 0.7em;
            }

            .player-info {
                padding: 8px;
                margin: 8px 0;
                gap: 8px;
            }

            .player-info img {
                width: 35px !important;
                height: 35px !important;
            }

            .player-details {
                font-size: 0.8em;
            }

            .player-balance {
                font-size: 0.8em;
                gap: 3px;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 5px;
                margin: 5px 0;
            }

            .stat-item {
                padding: 6px;
            }

            .stat-value {
                font-size: 1em;
            }

            .stat-label {
                font-size: 0.7em;
            }

            .game-area {
                padding: 10px;
                margin: 5px 0;
                min-height: 300px;
            }

            .target-animal {
                padding: 10px;
                margin-bottom: 10px;
            }

            .target-animal h3 {
                font-size: 1em;
                margin-bottom: 8px;
            }

            .target-image {
                width: 40px;
                height: 40px;
            }

            .target-details {
                font-size: 0.8em;
            }

            .target-reward {
                padding: 4px 8px;
                font-size: 0.7em;
            }

            .cages-grid {
                gap: 5px;
                margin: 10px 0;
                padding: 0 5px;
            }

            .cage {
                min-height: 60px;
                padding: 5px;
                border-radius: 8px;
            }

            .cage-cover {
                width: 35px;
                height: 35px;
                margin-bottom: 2px;
            }

            .cage-number {
                width: 16px;
                height: 16px;
                font-size: 0.6em;
                top: 2px;
                left: 2px;
            }

            .revealed-image {
                width: 30px;
                height: 30px;
            }

            .revealed-name {
                font-size: 0.6em;
            }

            .revealed-result {
                font-size: 0.5em;
                padding: 2px 4px;
            }

            .controls {
                gap: 5px;
                margin: 5px 0;
            }

            .control-btn {
                padding: 6px 10px;
                font-size: 0.7em;
                flex: 1;
                min-width: 80px;
            }

            #start-message {
                font-size: 0.9em;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- الهيدر -->
        <div class="header">
            <button class="back-btn" onclick="goBack()" title="العودة للصفحة الرئيسية">←</button>
            <h1 class="game-title">🌲 لعبة الغابة</h1>
            <div class="score-display">
                النقاط: <span id="score">0</span>
            </div>
        </div>

        <!-- واجهة العملات -->
        <div class="coins-display">
            <div class="coin-counter">
                <span class="coin-icon">💰</span>
                <span id="coins-count">1000</span>
                <span class="coin-label">عملة</span>
            </div>
            <div class="game-info">
                <span>🎯 تكلفة الجولة: <span id="bet-amount">50</span> عملة</span>
                <span>🏆 مكافآت: عادية، مضاعف x5/x8/x10، استرداد</span>
                <span>💀 مخاطر: وحوش، خسارة مضاعفة</span>
            </div>
        </div>

        <!-- بيانات اللاعب -->
        <div class="player-info" id="player-info" style="display: none;">
            <div class="player-avatar">
                <img id="player-avatar" src="/images/default-avatar.png" alt="صورة اللاعب"
                     style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #ffd700;">
            </div>
            <div class="player-details">
                <div id="player-name" style="font-weight: bold; color: #ffd700;">اللاعب</div>
                <div id="player-level" style="color: #cbd5e0; font-size: 0.9em;">المستوى 1</div>
            </div>
            <div class="player-balance">
                <div style="color: #10b981; font-weight: bold;">
                    💰 <span id="player-gold">0</span>
                </div>
                <div style="color: #3b82f6; font-weight: bold;">
                    💎 <span id="player-pearls">0</span>
                </div>
            </div>
        </div>

        <!-- إحصائيات اللعبة -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="discovered">0</div>
                <div class="stat-label">جولات</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="correct">0</div>
                <div class="stat-label">فوز</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="wrong">0</div>
                <div class="stat-label">خسارة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="level">1</div>
                <div class="stat-label">المستوى</div>
            </div>
        </div>

        <!-- منطقة اللعب -->
        <div class="game-area">
            <!-- هدف البحث -->
            <div class="target-animal" id="target-animal" style="display: none;">
                <h3>🎯 ابحث عن هذا الحيوان:</h3>
                <div class="target-info">
                    <img id="target-image" class="target-image" src="" alt="">
                    <div class="target-details">
                        <div id="target-name" style="font-size: 1.2em; font-weight: bold; color: #ffd700;"></div>
                        <div style="margin: 5px 0;">إذا وجدته:</div>
                        <div class="target-reward" id="target-reward">+0 عملة</div>
                    </div>
                </div>
            </div>

            <!-- شبكة الأقفاص -->
            <div class="cages-grid" id="cages-grid">
                <!-- الأقفاص ستظهر هنا -->
            </div>

            <!-- رسالة البداية -->
            <div id="start-message" style="text-align: center; color: #ffd700; font-size: 1.2em; display: none;">
                🌲 مرحباً بك في الغابة!<br>
                اختر صندوقاً للبحث عن الحيوانات المخفية
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="controls">
            <button class="control-btn" onclick="enableAudio(); playSound('click'); startGame()">🔄 جولة جديدة</button>
            <button class="control-btn" onclick="enableAudio(); playSound('click'); resetGame()">🔄 إعادة تعيين</button>
            <button class="control-btn" onclick="refreshPlayerData()">👤 تحديث البيانات</button>
            <button class="control-btn" onclick="toggleSound()" id="sound-btn">🔊 الصوت</button>
        </div>
    </div>

    <script src="/js/player-header.js"></script>
    <script src="/js/game-economy.js"></script>
    <script>
        // متغيرات اللعبة
        let gameState = {
            score: 0,
            coins: 1000,
            gamesPlayed: 0,
            wins: 0,
            losses: 0,
            level: 1,
            isPlaying: false,
            targetAnimal: null,
            cageContents: [],
            soundEnabled: true,
            betAmount: 50, // تكلفة الجولة
            economySession: null
        };

        // قائمة الحيوانات مع المضاعفات والتأثيرات
        const animals = [
            // حيوانات عادية (تعطي نقاط عادية)
            { name: 'كلب', image: 'dog.png', points: 80, type: 'normal', effect: 'normal' },
            { name: 'قطة', image: 'cat.png', points: 70, type: 'normal', effect: 'normal' },
            { name: 'بقرة', image: 'cow.png', points: 90, type: 'normal', effect: 'normal' },
            { name: 'خروف', image: 'sheep.png', points: 85, type: 'normal', effect: 'normal' },
            { name: 'ديك', image: 'rooster.png', points: 60, type: 'normal', effect: 'normal' },
            { name: 'بطة', image: 'duck.png', points: 65, type: 'normal', effect: 'normal' },
            { name: 'أرنب', image: 'rabbit.png', points: 75, type: 'normal', effect: 'normal' },
            { name: 'ضفدع', image: 'frog.png', points: 55, type: 'normal', effect: 'normal' },
            { name: 'سمكة', image: 'fish.png', points: 50, type: 'normal', effect: 'normal' },
            { name: 'دجاجة', image: 'chicken.png', points: 55, type: 'normal', effect: 'normal' },
            { name: 'حمار', image: 'donkey.png', points: 70, type: 'normal', effect: 'normal' },
            { name: 'بطريق', image: 'pinguin.png', points: 105, type: 'normal', effect: 'normal' },

            // حيوانات تعيد قيمة الجولة فقط
            { name: 'قرد', image: 'monkey.png', points: 0, type: 'refund', effect: 'refund' },
            { name: 'كوالا', image: 'koala.png', points: 0, type: 'refund', effect: 'refund' },
            { name: 'راكون', image: 'raccoon.png', points: 0, type: 'refund', effect: 'refund' },

            // حيوانات مضاعف x5
            { name: 'حصان', image: 'horse.png', points: 0, type: 'multiplier', effect: 'x5', multiplier: 5 },
            { name: 'جمل', image: 'camel.png', points: 0, type: 'multiplier', effect: 'x5', multiplier: 5 },
            { name: 'غزال', image: 'deer.png', points: 0, type: 'multiplier', effect: 'x5', multiplier: 5 },

            // حيوانات مضاعف x8
            { name: 'ذئب', image: 'wolf.png', points: 0, type: 'multiplier', effect: 'x8', multiplier: 8 },
            { name: 'نسر', image: 'eagle.png', points: 0, type: 'multiplier', effect: 'x8', multiplier: 8 },
            { name: 'بومة', image: 'owl.png', points: 0, type: 'multiplier', effect: 'x8', multiplier: 8 },

            // حيوانات مضاعف x10
            { name: 'أسد', image: 'lion.png', points: 0, type: 'multiplier', effect: 'x10', multiplier: 10 },
            { name: 'فيل', image: 'elephant.png', points: 0, type: 'multiplier', effect: 'x10', multiplier: 10 },
            { name: 'نمر', image: 'tiger.png', points: 0, type: 'multiplier', effect: 'x10', multiplier: 10 },
            { name: 'زرافة', image: 'giraffe.png', points: 0, type: 'multiplier', effect: 'x10', multiplier: 10 },
            { name: 'باندا', image: 'panda.png', points: 0, type: 'multiplier', effect: 'x10', multiplier: 10 },

            // حيوانات تأخذ قيمة جولتين
            { name: 'كنغر', image: 'kangaroo.png', points: 0, type: 'double_loss', effect: 'double_loss' },
            { name: 'ببغاء', image: 'parrot.png', points: 0, type: 'double_loss', effect: 'double_loss' }
        ];

        // الوحوش (تسبب الخسارة)
        const monsters = [
            { name: 'وحش', image: 'monster.png', type: 'monster' },
            { name: 'شبح', image: 'Ghost.png', type: 'monster' },
            { name: 'شبح آخر', image: 'Ghost 2.png', type: 'monster' },
            { name: 'عفريت', image: 'default-avatar.png', type: 'monster' }
        ];

        // نظام الأصوات
        let audioContext;
        let audioInitialized = false;

        function initAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                console.log('🔊 تم تهيئة نظام الصوت - لعبة الغابة');
            } catch (error) {
                console.log('🔇 Web Audio API غير مدعوم');
                gameState.soundEnabled = false;
            }
        }

        function enableAudio() {
            if (!audioInitialized && audioContext) {
                if (audioContext.state === 'suspended') {
                    audioContext.resume().then(() => {
                        audioInitialized = true;
                        console.log('🔊 تم تفعيل نظام الصوت - لعبة الغابة');
                        setTimeout(() => playSound('click'), 100);
                    });
                } else {
                    audioInitialized = true;
                    console.log('🔊 نظام الصوت جاهز - لعبة الغابة');
                }
            }
        }

        function playSound(type) {
            if (!gameState.soundEnabled || !audioContext) return;

            // التحقق من حالة AudioContext
            if (audioContext.state === 'suspended') {
                // لا نحاول تشغيل الصوت إذا كان معلقاً
                return;
            }

            if (!audioInitialized) {
                return; // لا نحاول تفعيل الصوت تلقائياً
            }

            try {
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                switch(type) {
                    case 'click':
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                        oscillator.type = 'sine';
                        break;

                    case 'discover':
                        oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                        oscillator.frequency.exponentialRampToValueAtTime(900, audioContext.currentTime + 0.2);
                        gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                        oscillator.type = 'triangle';
                        break;

                    case 'correct':
                        oscillator.frequency.setValueAtTime(523, audioContext.currentTime);
                        oscillator.frequency.exponentialRampToValueAtTime(784, audioContext.currentTime + 0.3);
                        gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
                        oscillator.type = 'square';
                        break;

                    case 'wrong':
                        oscillator.frequency.setValueAtTime(300, audioContext.currentTime);
                        oscillator.frequency.exponentialRampToValueAtTime(150, audioContext.currentTime + 0.2);
                        gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
                        oscillator.type = 'sawtooth';
                        break;

                    case 'levelUp':
                        oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
                        oscillator.frequency.exponentialRampToValueAtTime(880, audioContext.currentTime + 0.5);
                        gainNode.gain.setValueAtTime(0.4, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
                        oscillator.type = 'sine';
                        break;

                    default:
                        return;
                }

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);

            } catch (error) {
                console.log('🔇 خطأ في تشغيل الصوت');
            }
        }

        function toggleSound() {
            enableAudio();

            gameState.soundEnabled = !gameState.soundEnabled;
            const soundBtn = document.getElementById('sound-btn');

            if (gameState.soundEnabled) {
                soundBtn.textContent = '🔊 الصوت';
                soundBtn.style.opacity = '1';
                setTimeout(() => playSound('click'), 100);
            } else {
                soundBtn.textContent = '🔇 صامت';
                soundBtn.style.opacity = '0.6';
            }

            localStorage.setItem('forest-game-sound-enabled', gameState.soundEnabled);
        }

        // دالة عرض الرسائل
        function showMessage(message, type = 'info') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;

            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // تحديث العرض
        function updateDisplay() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('coins-count').textContent = gameState.coins;
            document.getElementById('discovered').textContent = gameState.gamesPlayed;
            document.getElementById('correct').textContent = gameState.wins;
            document.getElementById('wrong').textContent = gameState.losses;
            document.getElementById('level').textContent = gameState.level;
            document.getElementById('bet-amount').textContent = gameState.betAmount;

            // تحديث بيانات اللاعب المحلية
            document.getElementById('player-gold').textContent = gameState.coins;

            // تحديث الهيدر الحقيقي
            if (window.playerHeader && typeof window.playerHeader.updateBalance === 'function') {
                const currentPearls = parseInt(document.getElementById('player-pearls').textContent) || 0;
                window.playerHeader.updateBalance({
                    gold: gameState.coins,
                    pearls: currentPearls,
                    balance: gameState.coins
                });
                console.log('💰 تم تحديث رصيد الهيدر:', { gold: gameState.coins, pearls: currentPearls });
            }
        }

        // إنشاء الأقفاص
        function createCages() {
            const grid = document.getElementById('cages-grid');
            grid.innerHTML = '';

            for (let i = 0; i < 9; i++) {
                const cage = document.createElement('div');
                cage.className = 'cage';
                cage.dataset.index = i;
                cage.onclick = () => openCage(i);

                cage.innerHTML = `
                    <div class="cage-number">${i + 1}</div>
                    <img class="cage-cover" src="/images/OIP.jpg" alt="صندوق" onerror="this.style.display='none'; this.parentNode.innerHTML += '📦';">
                    <div class="cage-content">
                        <img class="revealed-image" src="" alt="">
                        <div class="revealed-name"></div>
                        <div class="revealed-result"></div>
                    </div>
                `;

                grid.appendChild(cage);
            }
        }

        // اختيار هدف عشوائي
        function selectRandomTarget() {
            const randomAnimal = animals[Math.floor(Math.random() * animals.length)];
            gameState.targetAnimal = randomAnimal;

            // عرض الهدف
            document.getElementById('target-image').src = `/images/${randomAnimal.image}`;
            document.getElementById('target-image').onerror = function() {
                this.src = '/images/default-avatar.png';
            };
            document.getElementById('target-name').textContent = randomAnimal.name;
            document.getElementById('target-reward').textContent = `+${randomAnimal.points} عملة`;

            document.getElementById('target-animal').style.display = 'block';
            document.getElementById('start-message').style.display = 'none';
        }

        // ملء الأقفاص بالمحتوى العشوائي
        function fillCages() {
            gameState.cageContents = [];

            // إضافة الهدف المطلوب في قفص عشوائي
            const targetPosition = Math.floor(Math.random() * 9);

            // إضافة 1-2 وحوش في مواقع عشوائية
            const numMonsters = Math.random() < 0.7 ? 1 : 2; // 70% احتمال وحش واحد، 30% وحشين
            const monsterPositions = [];

            for (let m = 0; m < numMonsters; m++) {
                let monsterPosition;
                do {
                    monsterPosition = Math.floor(Math.random() * 9);
                } while (monsterPosition === targetPosition || monsterPositions.includes(monsterPosition));
                monsterPositions.push(monsterPosition);
            }

            // ملء الأقفاص
            for (let i = 0; i < 9; i++) {
                if (i === targetPosition) {
                    // الهدف المطلوب
                    gameState.cageContents[i] = { ...gameState.targetAnimal, isTarget: true };
                } else if (monsterPositions.includes(i)) {
                    // وحش عشوائي
                    const randomMonster = monsters[Math.floor(Math.random() * monsters.length)];
                    gameState.cageContents[i] = { ...randomMonster, isTarget: false };
                } else {
                    // حيوان عشوائي آخر
                    const randomAnimal = animals[Math.floor(Math.random() * animals.length)];
                    gameState.cageContents[i] = { ...randomAnimal, isTarget: false };
                }
            }
        }

        // فتح قفص
        async function openCage(index) {
            // تفعيل الصوت عند أول تفاعل من المستخدم
            enableAudio();

            if (!gameState.isPlaying) {
                // بدء جولة جديدة تلقائياً
                const canStart = await startNewRound();
                if (!canStart) {
                    return;
                }
                gameState.isPlaying = true;
            }

            const cage = document.querySelector(`[data-index="${index}"]`);
            if (cage.classList.contains('opened')) {
                return; // القفص مفتوح بالفعل
            }

            // تعطيل جميع الأقفاص
            document.querySelectorAll('.cage').forEach(c => c.classList.add('disabled'));

            const content = gameState.cageContents[index];

            // تأثير فتح القفص
            cage.classList.add('opening');

            // فتح القفص بعد التأثير
            setTimeout(() => {
                cage.classList.remove('opening');
                cage.classList.add('opened');
            }, 250);

            // عرض المحتوى
            const image = cage.querySelector('.revealed-image');
            const name = cage.querySelector('.revealed-name');
            const result = cage.querySelector('.revealed-result');

            image.src = `/images/${content.image}`;
            image.onerror = function() {
                this.src = '/images/default-avatar.png';
            };
            name.textContent = content.name;

            let resultText = '';
            let resultClass = '';
            let coinChange = 0;

            if (content.type === 'monster') {
                // وحش - خسارة
                resultText = `خسارة: -${gameState.betAmount}`;
                resultClass = 'result-lose';
                coinChange = -gameState.betAmount;
                gameState.losses++;
                playSound('wrong');
                showMessage(`💀 وحش! خسرت ${gameState.betAmount} عملة`, 'error');
            } else if (content.isTarget) {
                // الهدف المطلوب - فوز كبير
                if (content.type === 'multiplier') {
                    coinChange = gameState.betAmount * content.multiplier;
                    resultText = `🎯 الهدف! x${content.multiplier}`;
                    resultClass = 'result-target';
                    playSound('levelUp');
                    showMessage(`🎯 ممتاز! وجدت الهدف! مضاعف x${content.multiplier}! +${coinChange} عملة`, 'success');
                } else if (content.type === 'refund') {
                    coinChange = gameState.betAmount;
                    resultText = `🎯 الهدف! استرداد`;
                    resultClass = 'result-target';
                    playSound('levelUp');
                    showMessage(`🎯 ممتاز! وجدت الهدف! تم استرداد ${coinChange} عملة`, 'success');
                } else if (content.type === 'double_loss') {
                    coinChange = -gameState.betAmount;
                    resultText = `🎯 الهدف! خسارة مضاعفة`;
                    resultClass = 'result-double-loss';
                    playSound('wrong');
                    showMessage(`🎯 وجدت الهدف لكنه خطير! خسرت ${Math.abs(coinChange)} عملة`, 'error');
                } else {
                    coinChange = content.points;
                    resultText = `🎯 الهدف! +${content.points}`;
                    resultClass = 'result-target';
                    playSound('levelUp');
                    showMessage(`🎯 ممتاز! وجدت الهدف! +${content.points} عملة`, 'success');
                }
                gameState.wins++;
            } else {
                // حيوان آخر - تأثيرات مختلفة
                if (content.type === 'multiplier') {
                    coinChange = gameState.betAmount * content.multiplier;
                    resultText = `مضاعف x${content.multiplier}!`;
                    resultClass = 'result-multiplier';
                    playSound('levelUp');
                    showMessage(`🎉 مضاعف رائع! x${content.multiplier}! +${coinChange} عملة`, 'success');
                } else if (content.type === 'refund') {
                    coinChange = gameState.betAmount;
                    resultText = `استرداد`;
                    resultClass = 'result-win';
                    playSound('correct');
                    showMessage(`💰 تم استرداد قيمة الجولة: +${coinChange} عملة`, 'info');
                } else if (content.type === 'double_loss') {
                    coinChange = -gameState.betAmount * 2;
                    resultText = `خسارة مضاعفة!`;
                    resultClass = 'result-double-loss';
                    playSound('wrong');
                    showMessage(`💸 خسارة مضاعفة! خسرت ${Math.abs(coinChange)} عملة`, 'error');
                } else {
                    coinChange = content.points;
                    resultText = `+${content.points}`;
                    resultClass = 'result-win';
                    playSound('correct');
                    showMessage(`✅ حيوان جميل! +${content.points} عملة`, 'success');
                }
                gameState.wins++;
            }

            result.textContent = resultText;
            result.className = `revealed-result ${resultClass}`;

            // تحديث العملات والنقاط
            gameState.coins += coinChange;
            gameState.score += Math.max(0, coinChange);
            gameState.gamesPlayed++;

            // تحديث النظام الاقتصادي
            await updateEconomyBalance(coinChange);

            updateDisplay();
            saveGameData();

            // إعادة تعيين الأقفاص بعد 2.5 ثانية
            setTimeout(() => {
                resetCagesForNextRound();
            }, 2500);
        }

        // إعادة تحميل بيانات اللاعب
        async function refreshPlayerData() {
            console.log('🔄 إعادة تحميل بيانات اللاعب...');
            await loadPlayerData();
            updateDisplay();
        }

        // دالة العودة للخلف
        function goBack() {
            // تفعيل الصوت عند النقر
            enableAudio();
            playSound('click');

            // التحقق من حالة اللعبة
            if (gameState.isPlaying && gameState.gamesPlayed > 0) {
                // إذا كان اللاعب في منتصف اللعبة، طلب التأكيد
                const confirmExit = confirm('هل أنت متأكد من الخروج؟ ستفقد التقدم الحالي في اللعبة.');
                if (!confirmExit) {
                    return; // إلغاء الخروج
                }
            }

            // حفظ البيانات قبل الخروج
            saveGameData();

            // محاولة العودة للصفحة السابقة
            if (document.referrer &&
                document.referrer !== window.location.href &&
                !document.referrer.includes('forest-game.html')) {
                // إذا كان هناك صفحة سابقة صحيحة، العودة إليها
                window.history.back();
            } else {
                // إذا لم تكن هناك صفحة سابقة صحيحة، الذهاب للصفحة الرئيسية
                window.location.href = '/';
            }
        }

        // إعادة تعيين الأقفاص للجولة التالية
        function resetCagesForNextRound() {
            // إعادة تعيين الأقفاص
            document.querySelectorAll('.cage').forEach(cage => {
                cage.classList.remove('opened', 'disabled');
            });

            // اختيار هدف جديد وملء الأقفاص
            selectRandomTarget();
            fillCages();

            // فحص الارتقاء للمستوى التالي
            if (gameState.wins >= gameState.level * 3) {
                gameState.level++;
                gameState.betAmount += 10; // زيادة تكلفة الجولة
                playSound('levelUp');
                showMessage(`🎉 مبروك! وصلت للمستوى ${gameState.level}!`, 'success');
                updateDisplay();
            }

            showMessage('🔄 جولة جديدة! اختر صندوقاً آخر', 'info');
        }

        // إنهاء اللعبة
        function endGame() {
            gameState.isPlaying = false;

            // إعادة تعيين الأقفاص
            document.querySelectorAll('.cage').forEach(cage => {
                cage.classList.remove('opened', 'disabled');
            });

            // إخفاء الهدف
            document.getElementById('target-animal').style.display = 'none';
            document.getElementById('start-message').style.display = 'block';
        }

        // تهيئة النظام الاقتصادي
        async function initializeEconomy() {
            try {
                if (window.gameEconomy && typeof window.gameEconomy.initializeGameSession === 'function') {
                    gameState.economySession = await window.gameEconomy.initializeGameSession('forest-game', gameState.betAmount);
                    console.log('✅ تم تهيئة النظام الاقتصادي للعبة الغابة');
                } else {
                    console.log('🎮 تشغيل اللعبة في وضع التدريب');
                }
            } catch (error) {
                console.log('⚠️ خطأ في تهيئة النظام الاقتصادي:', error);
            }
        }

        // تحميل بيانات اللاعب الحقيقي
        async function loadPlayerData() {
            try {
                console.log('🔄 بدء تحميل بيانات اللاعب...');

                // التأكد من تهيئة الهيدر
                if (window.playerHeader) {
                    // الحصول على بيانات اللاعب من خصائص الهيدر
                    const playerName = window.playerHeader.username || 'اللاعب';
                    const playerBalance = window.playerHeader.balance || gameState.coins;
                    const playerId = window.playerHeader.playerId || '';
                    const playerAvatar = window.playerHeader.avatar || '/images/default-avatar.png';

                    console.log('👤 بيانات اللاعب من الهيدر:', {
                        username: playerName,
                        balance: playerBalance,
                        playerId: playerId,
                        avatar: playerAvatar
                    });

                    // عرض بيانات اللاعب الحقيقي
                    document.getElementById('player-name').textContent = playerName;
                    document.getElementById('player-level').textContent = `المستوى 1`; // يمكن تحسينه لاحقاً

                    // تحديث صورة اللاعب
                    if (playerAvatar && playerAvatar !== '/images/default-avatar.png') {
                        document.getElementById('player-avatar').src = playerAvatar;
                        document.getElementById('player-avatar').onerror = function() {
                            this.src = '/images/default-avatar.png';
                        };
                    }

                    // تحديث الرصيد الحقيقي
                    if (typeof playerBalance === 'number' && playerBalance > 0) {
                        gameState.coins = playerBalance;
                        document.getElementById('player-gold').textContent = playerBalance;
                        console.log('💰 تم تحديث الرصيد من الهيدر:', playerBalance);
                    } else {
                        // محاولة الحصول على الرصيد من النظام الاقتصادي
                        if (window.gameEconomy && typeof window.gameEconomy.getPlayerBalance === 'function') {
                            try {
                                const economyBalance = await window.gameEconomy.getPlayerBalance();
                                if (economyBalance && economyBalance.gold) {
                                    gameState.coins = economyBalance.gold;
                                    document.getElementById('player-gold').textContent = economyBalance.gold;
                                    document.getElementById('player-pearls').textContent = economyBalance.pearls || 0;
                                    console.log('💰 تم تحديث الرصيد من النظام الاقتصادي:', economyBalance);
                                } else {
                                    document.getElementById('player-gold').textContent = gameState.coins;
                                    document.getElementById('player-pearls').textContent = 0;
                                }
                            } catch (economyError) {
                                console.log('⚠️ خطأ في الحصول على الرصيد من النظام الاقتصادي:', economyError);
                                document.getElementById('player-gold').textContent = gameState.coins;
                                document.getElementById('player-pearls').textContent = 0;
                            }
                        } else {
                            document.getElementById('player-gold').textContent = gameState.coins;
                            document.getElementById('player-pearls').textContent = 0;
                        }
                    }

                    // محاولة الحصول على بيانات إضافية من localStorage
                    const userData = localStorage.getItem('userData');
                    if (userData) {
                        try {
                            const user = JSON.parse(userData);
                            if (user.level) {
                                document.getElementById('player-level').textContent = `المستوى ${user.level}`;
                            }
                            console.log('✅ تم تحميل بيانات إضافية من localStorage:', user);
                        } catch (parseError) {
                            console.log('⚠️ خطأ في تحليل بيانات localStorage:', parseError);
                        }
                    }

                    // إظهار معلومات اللاعب
                    document.getElementById('player-info').style.display = 'flex';
                    console.log('✅ تم تحميل بيانات اللاعب بنجاح');

                } else {
                    console.log('❌ window.playerHeader غير متاح');
                    // محاولة الحصول على البيانات من localStorage
                    const userData = localStorage.getItem('userData');
                    if (userData) {
                        try {
                            const user = JSON.parse(userData);
                            document.getElementById('player-name').textContent = user.username || 'اللاعب';
                            document.getElementById('player-level').textContent = `المستوى ${user.level || 1}`;
                            if (user.profilePicture || user.avatar) {
                                document.getElementById('player-avatar').src = user.profilePicture || user.avatar;
                            }
                            if (user.balance || user.coins) {
                                gameState.coins = user.balance || user.coins;
                                document.getElementById('player-gold').textContent = gameState.coins;
                            } else {
                                document.getElementById('player-gold').textContent = gameState.coins;
                            }
                            document.getElementById('player-pearls').textContent = user.pearls || 0;
                            console.log('✅ تم تحميل البيانات من localStorage:', user);
                        } catch (parseError) {
                            console.log('⚠️ خطأ في تحليل بيانات localStorage:', parseError);
                            // بيانات افتراضية
                            document.getElementById('player-name').textContent = 'اللاعب';
                            document.getElementById('player-level').textContent = 'المستوى 1';
                            document.getElementById('player-gold').textContent = gameState.coins;
                            document.getElementById('player-pearls').textContent = 0;
                        }
                    } else {
                        // بيانات افتراضية
                        document.getElementById('player-name').textContent = 'اللاعب';
                        document.getElementById('player-level').textContent = 'المستوى 1';
                        document.getElementById('player-gold').textContent = gameState.coins;
                        document.getElementById('player-pearls').textContent = 0;
                    }
                    document.getElementById('player-info').style.display = 'flex';
                }

            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات اللاعب:', error);
                // عرض بيانات افتراضية في حالة الخطأ
                document.getElementById('player-name').textContent = 'اللاعب';
                document.getElementById('player-level').textContent = 'المستوى 1';
                document.getElementById('player-gold').textContent = gameState.coins;
                document.getElementById('player-pearls').textContent = 0;
                document.getElementById('player-info').style.display = 'flex';
            }
        }

        // تحميل بيانات اللعبة
        function loadGameData() {
            try {
                // تحميل البيانات المحفوظة
                const savedData = localStorage.getItem('forest-game-data');
                if (savedData) {
                    const data = JSON.parse(savedData);
                    gameState.score = data.score || 0;
                    gameState.gamesPlayed = data.gamesPlayed || 0;
                    gameState.wins = data.wins || 0;
                    gameState.losses = data.losses || 0;
                    gameState.level = data.level || 1;
                    gameState.betAmount = data.betAmount || 50;
                }

                // تحميل إعدادات الصوت
                const soundSetting = localStorage.getItem('forest-game-sound-enabled');
                if (soundSetting !== null) {
                    gameState.soundEnabled = soundSetting === 'true';
                }
            } catch (error) {
                console.log('⚠️ خطأ في تحميل البيانات:', error);
            }
        }

        // حفظ بيانات اللعبة
        function saveGameData() {
            try {
                const dataToSave = {
                    score: gameState.score,
                    gamesPlayed: gameState.gamesPlayed,
                    wins: gameState.wins,
                    losses: gameState.losses,
                    level: gameState.level,
                    betAmount: gameState.betAmount
                };
                localStorage.setItem('forest-game-data', JSON.stringify(dataToSave));
            } catch (error) {
                console.log('⚠️ خطأ في حفظ البيانات:', error);
            }
        }

        // إنشاء شبكة الحيوانات
        function createAnimalsGrid() {
            const grid = document.getElementById('animals-grid');
            grid.innerHTML = '';

            // اختيار حيوانات عشوائية حسب المستوى
            const animalsToShow = getAnimalsForLevel(gameState.level);

            animalsToShow.forEach((animal, index) => {
                const card = document.createElement('div');
                card.className = 'animal-card';
                card.onclick = () => discoverAnimal(animal);

                card.innerHTML = `
                    <img src="/images/${animal.image}" alt="${animal.name}" class="animal-image"
                         onerror="this.src='/images/default-avatar.png'">
                    <div class="animal-name">❓</div>
                    <div class="animal-points">+${animal.points}</div>
                `;

                grid.appendChild(card);
            });
        }

        // اختيار الحيوانات حسب المستوى
        function getAnimalsForLevel(level) {
            const animalsPerLevel = Math.min(6 + level, 12);
            const shuffled = [...animals].sort(() => Math.random() - 0.5);
            return shuffled.slice(0, animalsPerLevel);
        }

        // اكتشاف حيوان
        async function discoverAnimal(animal) {
            if (!gameState.isPlaying) {
                showMessage('ابدأ اللعبة أولاً!', 'error');
                return;
            }

            // التحقق من العملات
            if (gameState.coins < gameState.discoveryCost) {
                showMessage('💰 ليس لديك عملات كافية لاكتشاف الحيوان!', 'error');
                return;
            }

            // خصم تكلفة الاكتشاف
            gameState.coins -= gameState.discoveryCost;
            gameState.discovered++;

            playSound('discover');

            // عرض سؤال عن الحيوان
            const isCorrect = await askAnimalQuestion(animal);

            if (isCorrect) {
                gameState.correct++;
                gameState.score += animal.points;
                gameState.coins += animal.points;

                playSound('correct');
                showMessage(`✅ صحيح! +${animal.points} نقطة و +${animal.points} عملة`, 'success');

                // تحديث النظام الاقتصادي
                updateEconomyBalance(animal.points);
            } else {
                gameState.wrong++;
                playSound('wrong');
                showMessage(`❌ خطأ! الإجابة الصحيحة: ${animal.name}`, 'error');
            }

            // فحص الارتقاء للمستوى التالي
            checkLevelUp();

            updateDisplay();
            saveGameData();
        }

        // سؤال عن الحيوان
        function askAnimalQuestion(animal) {
            return new Promise((resolve) => {
                const options = generateAnimalOptions(animal);
                const question = `🐾 ما اسم هذا الحيوان؟`;

                let optionsText = '';
                options.forEach((option, index) => {
                    optionsText += `${index + 1}. ${option}\n`;
                });

                const userAnswer = prompt(`${question}\n\n${optionsText}\nأدخل رقم الإجابة (1-4):`);

                if (userAnswer && parseInt(userAnswer) === options.indexOf(animal.name) + 1) {
                    resolve(true);
                } else {
                    resolve(false);
                }
            });
        }

        // توليد خيارات الإجابة
        function generateAnimalOptions(correctAnimal) {
            const options = [correctAnimal.name];
            const otherAnimals = animals.filter(a => a.name !== correctAnimal.name);

            // إضافة 3 خيارات خاطئة عشوائية
            while (options.length < 4) {
                const randomAnimal = otherAnimals[Math.floor(Math.random() * otherAnimals.length)];
                if (!options.includes(randomAnimal.name)) {
                    options.push(randomAnimal.name);
                }
            }

            // خلط الخيارات
            return options.sort(() => Math.random() - 0.5);
        }

        // فحص الارتقاء للمستوى التالي
        function checkLevelUp() {
            const requiredCorrect = gameState.level * 5;
            if (gameState.correct >= requiredCorrect) {
                gameState.level++;
                playSound('levelUp');
                showMessage(`🎉 مبروك! وصلت للمستوى ${gameState.level}!`, 'success');

                // إعادة إنشاء الشبكة للمستوى الجديد
                createAnimalsGrid();
            }
        }

        // تحديث رصيد النظام الاقتصادي
        async function updateEconomyBalance(coinChange) {
            try {
                // تخطي التحديث إذا لم يكن هناك تغيير في العملات
                if (coinChange === 0) return;

                console.log('💰 تحديث الرصيد:', coinChange);

                if (window.gameEconomy && typeof window.gameEconomy.updatePlayerBalance === 'function') {
                    const gameResult = {
                        isWin: coinChange > 0,
                        winAmount: Math.max(0, coinChange),
                        lossAmount: Math.abs(Math.min(0, coinChange)),
                        totalWin: Math.max(0, coinChange),
                        totalBet: gameState.betAmount,
                        gameType: 'forest-game'
                    };

                    const balanceUpdate = await window.gameEconomy.updatePlayerBalance(gameResult);
                    if (balanceUpdate && balanceUpdate.success) {
                        console.log('✅ تم تحديث النظام الاقتصادي بنجاح:', balanceUpdate);

                        // تحديث الرصيد المحلي
                        if (balanceUpdate.newBalance && typeof balanceUpdate.newBalance.gold === 'number') {
                            gameState.coins = balanceUpdate.newBalance.gold;
                        }

                        // تحديث الهيدر
                        if (window.playerHeader && typeof window.playerHeader.updateBalance === 'function') {
                            window.playerHeader.updateBalance(balanceUpdate.newBalance);
                        }

                        // تحديث العرض
                        updateDisplay();
                    } else {
                        console.log('⚠️ فشل في تحديث النظام الاقتصادي:', balanceUpdate);
                    }
                } else {
                    console.log('⚠️ النظام الاقتصادي غير متاح');
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث النظام الاقتصادي:', error);
                // لا نوقف اللعبة بسبب خطأ في النظام الاقتصادي
            }
        }

        // بدء اللعبة
        function startGame() {
            // لا نشغل الصوت تلقائياً لتجنب مشاكل المتصفح
            gameState.isPlaying = true;
            selectRandomTarget();
            fillCages();

            showMessage(`🌲 ابحث عن ${gameState.targetAnimal.name} في الصناديق!`, 'info');
            updateDisplay();
        }

        // بدء جولة جديدة
        async function startNewRound() {
            try {
                // التحقق من النظام الاقتصادي
                if (window.gameEconomy && typeof window.gameEconomy.canPlay === 'function') {
                    const canPlayCheck = window.gameEconomy.canPlay(gameState.betAmount);
                    if (!canPlayCheck.canPlay) {
                        showMessage(canPlayCheck.reason || 'لا يمكن بدء الجولة', 'error');
                        endGame();
                        return false;
                    }
                }

                // التحقق من العملات
                if (gameState.coins < gameState.betAmount) {
                    showMessage(`💰 تحتاج ${gameState.betAmount} عملة لبدء الجولة!`, 'error');
                    endGame();
                    return false;
                }

                // خصم تكلفة الجولة
                gameState.coins -= gameState.betAmount;
                updateDisplay();

                return true;
            } catch (error) {
                console.log('⚠️ خطأ في بدء الجولة:', error);
                return false;
            }
        }

        // إعادة تعيين اللعبة
        function resetGame() {
            if (confirm('هل أنت متأكد من إعادة تعيين اللعبة؟ ستفقد جميع التقدم!')) {
                gameState.score = 0;
                gameState.gamesPlayed = 0;
                gameState.wins = 0;
                gameState.losses = 0;
                gameState.level = 1;
                gameState.betAmount = 50;
                gameState.isPlaying = false;

                // إعادة تعيين الأقفاص
                document.querySelectorAll('.cage').forEach(cage => {
                    cage.classList.remove('opened', 'disabled');
                });

                // بدء لعبة جديدة
                startGame();

                updateDisplay();
                saveGameData();

                showMessage('🔄 تم إعادة تعيين اللعبة!', 'info');
                playSound('click');
            }
        }

        // معالجة زر الخلف في المتصفح
        window.addEventListener('beforeunload', function(e) {
            if (gameState.isPlaying && gameState.gamesPlayed > 0) {
                // حفظ البيانات قبل الخروج
                saveGameData();

                // رسالة تحذيرية (قد لا تظهر في جميع المتصفحات)
                const message = 'هل أنت متأكد من الخروج؟ ستفقد التقدم الحالي في اللعبة.';
                e.returnValue = message;
                return message;
            }
        });

        // معالجة تغيير التاريخ (زر الخلف)
        window.addEventListener('popstate', function(e) {
            if (gameState.isPlaying && gameState.gamesPlayed > 0) {
                const confirmExit = confirm('هل أنت متأكد من الخروج؟ ستفقد التقدم الحالي في اللعبة.');
                if (!confirmExit) {
                    // منع الخروج بإضافة حالة جديدة للتاريخ
                    history.pushState(null, null, window.location.href);
                    return;
                }
            }
            // حفظ البيانات قبل الخروج
            saveGameData();
        });

        // تهيئة اللعبة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // إضافة حالة للتاريخ لمعالجة زر الخلف
                history.pushState(null, null, window.location.href);

                // تهيئة هيدر اللاعب أولاً
                if (window.playerHeader && typeof window.playerHeader.init === 'function') {
                    await window.playerHeader.init();
                    console.log('✅ تم تهيئة هيدر اللاعب');
                } else {
                    console.log('⚠️ هيدر اللاعب غير متاح');
                }

                initAudio();
                loadPlayerData();
                loadGameData();
                await initializeEconomy();
                createCages();
                startGame(); // بدء اللعبة تلقائياً
                updateDisplay();
            } catch (error) {
                console.error('❌ خطأ في تهيئة اللعبة:', error);
                // تشغيل اللعبة حتى لو فشلت التهيئة
                initAudio();
                loadGameData();
                createCages();
                startGame();
                updateDisplay();
            }
        });
    </script>
</body>
</html>
