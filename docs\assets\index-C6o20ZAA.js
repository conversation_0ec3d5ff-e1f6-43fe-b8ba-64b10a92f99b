function em(e,t){for(var n=0;n<t.length;n++){const s=t[n];if(typeof s!="string"&&!Array.isArray(s)){for(const l in s)if(l!=="default"&&!(l in e)){const o=Object.getOwnPropertyDescriptor(s,l);o&&Object.defineProperty(e,l,o.get?o:{enumerable:!0,get:()=>s[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))s(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function tm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Kc={exports:{}},wl={},Yc={exports:{}},K={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var as=Symbol.for("react.element"),nm=Symbol.for("react.portal"),rm=Symbol.for("react.fragment"),sm=Symbol.for("react.strict_mode"),lm=Symbol.for("react.profiler"),om=Symbol.for("react.provider"),im=Symbol.for("react.context"),am=Symbol.for("react.forward_ref"),cm=Symbol.for("react.suspense"),dm=Symbol.for("react.memo"),um=Symbol.for("react.lazy"),_a=Symbol.iterator;function mm(e){return e===null||typeof e!="object"?null:(e=_a&&e[_a]||e["@@iterator"],typeof e=="function"?e:null)}var Xc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ed=Object.assign,td={};function lr(e,t,n){this.props=e,this.context=t,this.refs=td,this.updater=n||Xc}lr.prototype.isReactComponent={};lr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};lr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function nd(){}nd.prototype=lr.prototype;function ji(e,t,n){this.props=e,this.context=t,this.refs=td,this.updater=n||Xc}var Ni=ji.prototype=new nd;Ni.constructor=ji;ed(Ni,lr.prototype);Ni.isPureReactComponent=!0;var Ta=Array.isArray,rd=Object.prototype.hasOwnProperty,ki={current:null},sd={key:!0,ref:!0,__self:!0,__source:!0};function ld(e,t,n){var s,l={},o=null,i=null;if(t!=null)for(s in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)rd.call(t,s)&&!sd.hasOwnProperty(s)&&(l[s]=t[s]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var c=Array(a),u=0;u<a;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(s in a=e.defaultProps,a)l[s]===void 0&&(l[s]=a[s]);return{$$typeof:as,type:e,key:o,ref:i,props:l,_owner:ki.current}}function fm(e,t){return{$$typeof:as,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Si(e){return typeof e=="object"&&e!==null&&e.$$typeof===as}function hm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var La=/\/+/g;function Hl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?hm(""+e.key):t.toString(36)}function zs(e,t,n,s,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case as:case nm:i=!0}}if(i)return i=e,l=l(i),e=s===""?"."+Hl(i,0):s,Ta(l)?(n="",e!=null&&(n=e.replace(La,"$&/")+"/"),zs(l,t,n,"",function(u){return u})):l!=null&&(Si(l)&&(l=fm(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(La,"$&/")+"/")+e)),t.push(l)),1;if(i=0,s=s===""?".":s+":",Ta(e))for(var a=0;a<e.length;a++){o=e[a];var c=s+Hl(o,a);i+=zs(o,t,n,c,l)}else if(c=mm(e),typeof c=="function")for(e=c.call(e),a=0;!(o=e.next()).done;)o=o.value,c=s+Hl(o,a++),i+=zs(o,t,n,c,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function ys(e,t,n){if(e==null)return e;var s=[],l=0;return zs(e,s,"","",function(o){return t.call(n,o,l++)}),s}function pm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Oe={current:null},Os={transition:null},gm={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:Os,ReactCurrentOwner:ki};function od(){throw Error("act(...) is not supported in production builds of React.")}K.Children={map:ys,forEach:function(e,t,n){ys(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ys(e,function(){t++}),t},toArray:function(e){return ys(e,function(t){return t})||[]},only:function(e){if(!Si(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};K.Component=lr;K.Fragment=rm;K.Profiler=lm;K.PureComponent=ji;K.StrictMode=sm;K.Suspense=cm;K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gm;K.act=od;K.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var s=ed({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=ki.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)rd.call(t,c)&&!sd.hasOwnProperty(c)&&(s[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)s.children=n;else if(1<c){a=Array(c);for(var u=0;u<c;u++)a[u]=arguments[u+2];s.children=a}return{$$typeof:as,type:e.type,key:l,ref:o,props:s,_owner:i}};K.createContext=function(e){return e={$$typeof:im,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:om,_context:e},e.Consumer=e};K.createElement=ld;K.createFactory=function(e){var t=ld.bind(null,e);return t.type=e,t};K.createRef=function(){return{current:null}};K.forwardRef=function(e){return{$$typeof:am,render:e}};K.isValidElement=Si;K.lazy=function(e){return{$$typeof:um,_payload:{_status:-1,_result:e},_init:pm}};K.memo=function(e,t){return{$$typeof:dm,type:e,compare:t===void 0?null:t}};K.startTransition=function(e){var t=Os.transition;Os.transition={};try{e()}finally{Os.transition=t}};K.unstable_act=od;K.useCallback=function(e,t){return Oe.current.useCallback(e,t)};K.useContext=function(e){return Oe.current.useContext(e)};K.useDebugValue=function(){};K.useDeferredValue=function(e){return Oe.current.useDeferredValue(e)};K.useEffect=function(e,t){return Oe.current.useEffect(e,t)};K.useId=function(){return Oe.current.useId()};K.useImperativeHandle=function(e,t,n){return Oe.current.useImperativeHandle(e,t,n)};K.useInsertionEffect=function(e,t){return Oe.current.useInsertionEffect(e,t)};K.useLayoutEffect=function(e,t){return Oe.current.useLayoutEffect(e,t)};K.useMemo=function(e,t){return Oe.current.useMemo(e,t)};K.useReducer=function(e,t,n){return Oe.current.useReducer(e,t,n)};K.useRef=function(e){return Oe.current.useRef(e)};K.useState=function(e){return Oe.current.useState(e)};K.useSyncExternalStore=function(e,t,n){return Oe.current.useSyncExternalStore(e,t,n)};K.useTransition=function(){return Oe.current.useTransition()};K.version="18.3.1";Yc.exports=K;var x=Yc.exports;const xm=tm(x),ym=em({__proto__:null,default:xm},[x]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vm=x,wm=Symbol.for("react.element"),bm=Symbol.for("react.fragment"),jm=Object.prototype.hasOwnProperty,Nm=vm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,km={key:!0,ref:!0,__self:!0,__source:!0};function id(e,t,n){var s,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(s in t)jm.call(t,s)&&!km.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)l[s]===void 0&&(l[s]=t[s]);return{$$typeof:wm,type:e,key:o,ref:i,props:l,_owner:Nm.current}}wl.Fragment=bm;wl.jsx=id;wl.jsxs=id;Kc.exports=wl;var r=Kc.exports,ad={exports:{}},Ze={},cd={exports:{}},dd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(h,C){var T=h.length;h.push(C);e:for(;0<T;){var $=T-1>>>1,L=h[$];if(0<l(L,C))h[$]=C,h[T]=L,T=$;else break e}}function n(h){return h.length===0?null:h[0]}function s(h){if(h.length===0)return null;var C=h[0],T=h.pop();if(T!==C){h[0]=T;e:for(var $=0,L=h.length,G=L>>>1;$<G;){var Y=2*($+1)-1,fe=h[Y],ge=Y+1,ie=h[ge];if(0>l(fe,T))ge<L&&0>l(ie,fe)?(h[$]=ie,h[ge]=T,$=ge):(h[$]=fe,h[Y]=T,$=Y);else if(ge<L&&0>l(ie,T))h[$]=ie,h[ge]=T,$=ge;else break e}}return C}function l(h,C){var T=h.sortIndex-C.sortIndex;return T!==0?T:h.id-C.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var c=[],u=[],g=1,w=null,b=3,N=!1,j=!1,k=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(h){for(var C=n(u);C!==null;){if(C.callback===null)s(u);else if(C.startTime<=h)s(u),C.sortIndex=C.expirationTime,t(c,C);else break;C=n(u)}}function M(h){if(k=!1,p(h),!j)if(n(c)!==null)j=!0,$e(P);else{var C=n(u);C!==null&&be(M,C.startTime-h)}}function P(h,C){j=!1,k&&(k=!1,m(U),U=-1),N=!0;var T=b;try{for(p(C),w=n(c);w!==null&&(!(w.expirationTime>C)||h&&!O());){var $=w.callback;if(typeof $=="function"){w.callback=null,b=w.priorityLevel;var L=$(w.expirationTime<=C);C=e.unstable_now(),typeof L=="function"?w.callback=L:w===n(c)&&s(c),p(C)}else s(c);w=n(c)}if(w!==null)var G=!0;else{var Y=n(u);Y!==null&&be(M,Y.startTime-C),G=!1}return G}finally{w=null,b=T,N=!1}}var R=!1,z=null,U=-1,D=5,y=-1;function O(){return!(e.unstable_now()-y<D)}function Z(){if(z!==null){var h=e.unstable_now();y=h;var C=!0;try{C=z(!0,h)}finally{C?ne():(R=!1,z=null)}}else R=!1}var ne;if(typeof d=="function")ne=function(){d(Z)};else if(typeof MessageChannel<"u"){var We=new MessageChannel,Pe=We.port2;We.port1.onmessage=Z,ne=function(){Pe.postMessage(null)}}else ne=function(){S(Z,0)};function $e(h){z=h,R||(R=!0,ne())}function be(h,C){U=S(function(){h(e.unstable_now())},C)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(h){h.callback=null},e.unstable_continueExecution=function(){j||N||(j=!0,$e(P))},e.unstable_forceFrameRate=function(h){0>h||125<h?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<h?Math.floor(1e3/h):5},e.unstable_getCurrentPriorityLevel=function(){return b},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(h){switch(b){case 1:case 2:case 3:var C=3;break;default:C=b}var T=b;b=C;try{return h()}finally{b=T}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(h,C){switch(h){case 1:case 2:case 3:case 4:case 5:break;default:h=3}var T=b;b=h;try{return C()}finally{b=T}},e.unstable_scheduleCallback=function(h,C,T){var $=e.unstable_now();switch(typeof T=="object"&&T!==null?(T=T.delay,T=typeof T=="number"&&0<T?$+T:$):T=$,h){case 1:var L=-1;break;case 2:L=250;break;case 5:L=**********;break;case 4:L=1e4;break;default:L=5e3}return L=T+L,h={id:g++,callback:C,priorityLevel:h,startTime:T,expirationTime:L,sortIndex:-1},T>$?(h.sortIndex=T,t(u,h),n(c)===null&&h===n(u)&&(k?(m(U),U=-1):k=!0,be(M,T-$))):(h.sortIndex=L,t(c,h),j||N||(j=!0,$e(P))),h},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(h){var C=b;return function(){var T=b;b=C;try{return h.apply(this,arguments)}finally{b=T}}}})(dd);cd.exports=dd;var Sm=cd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cm=x,Je=Sm;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ud=new Set,Br={};function Mn(e,t){Kn(e,t),Kn(e+"Capture",t)}function Kn(e,t){for(Br[e]=t,e=0;e<t.length;e++)ud.add(t[e])}var It=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),wo=Object.prototype.hasOwnProperty,Mm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Pa={},Ra={};function Em(e){return wo.call(Ra,e)?!0:wo.call(Pa,e)?!1:Mm.test(e)?Ra[e]=!0:(Pa[e]=!0,!1)}function Im(e,t,n,s){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Am(e,t,n,s){if(t===null||typeof t>"u"||Im(e,t,n,s))return!0;if(s)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ue(e,t,n,s,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Ie={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ie[e]=new Ue(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ie[t]=new Ue(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ie[e]=new Ue(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ie[e]=new Ue(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ie[e]=new Ue(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ie[e]=new Ue(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ie[e]=new Ue(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ie[e]=new Ue(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ie[e]=new Ue(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ci=/[\-:]([a-z])/g;function Mi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ci,Mi);Ie[t]=new Ue(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ci,Mi);Ie[t]=new Ue(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ci,Mi);Ie[t]=new Ue(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ie[e]=new Ue(e,1,!1,e.toLowerCase(),null,!1,!1)});Ie.xlinkHref=new Ue("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ie[e]=new Ue(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ei(e,t,n,s){var l=Ie.hasOwnProperty(t)?Ie[t]:null;(l!==null?l.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Am(t,n,l,s)&&(n=null),s||l===null?Em(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,s=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,s?e.setAttributeNS(s,t,n):e.setAttribute(t,n))))}var Lt=Cm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,vs=Symbol.for("react.element"),Ln=Symbol.for("react.portal"),Pn=Symbol.for("react.fragment"),Ii=Symbol.for("react.strict_mode"),bo=Symbol.for("react.profiler"),md=Symbol.for("react.provider"),fd=Symbol.for("react.context"),Ai=Symbol.for("react.forward_ref"),jo=Symbol.for("react.suspense"),No=Symbol.for("react.suspense_list"),_i=Symbol.for("react.memo"),Ut=Symbol.for("react.lazy"),hd=Symbol.for("react.offscreen"),za=Symbol.iterator;function xr(e){return e===null||typeof e!="object"?null:(e=za&&e[za]||e["@@iterator"],typeof e=="function"?e:null)}var me=Object.assign,Wl;function Sr(e){if(Wl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Wl=t&&t[1]||""}return`
`+Wl+e}var Ql=!1;function Gl(e,t){if(!e||Ql)return"";Ql=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var s=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){s=u}e.call(t.prototype)}else{try{throw Error()}catch(u){s=u}e()}}catch(u){if(u&&s&&typeof u.stack=="string"){for(var l=u.stack.split(`
`),o=s.stack.split(`
`),i=l.length-1,a=o.length-1;1<=i&&0<=a&&l[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(l[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||l[i]!==o[a]){var c=`
`+l[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{Ql=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Sr(e):""}function _m(e){switch(e.tag){case 5:return Sr(e.type);case 16:return Sr("Lazy");case 13:return Sr("Suspense");case 19:return Sr("SuspenseList");case 0:case 2:case 15:return e=Gl(e.type,!1),e;case 11:return e=Gl(e.type.render,!1),e;case 1:return e=Gl(e.type,!0),e;default:return""}}function ko(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Pn:return"Fragment";case Ln:return"Portal";case bo:return"Profiler";case Ii:return"StrictMode";case jo:return"Suspense";case No:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case fd:return(e.displayName||"Context")+".Consumer";case md:return(e._context.displayName||"Context")+".Provider";case Ai:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case _i:return t=e.displayName||null,t!==null?t:ko(e.type)||"Memo";case Ut:t=e._payload,e=e._init;try{return ko(e(t))}catch{}}return null}function Tm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ko(t);case 8:return t===Ii?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Yt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function pd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Lm(e){var t=pd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){s=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(i){s=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ws(e){e._valueTracker||(e._valueTracker=Lm(e))}function gd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),s="";return e&&(s=pd(e)?e.checked?"true":"false":e.value),e=s,e!==n?(t.setValue(e),!0):!1}function Zs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function So(e,t){var n=t.checked;return me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Oa(e,t){var n=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;n=Yt(t.value!=null?t.value:n),e._wrapperState={initialChecked:s,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function xd(e,t){t=t.checked,t!=null&&Ei(e,"checked",t,!1)}function Co(e,t){xd(e,t);var n=Yt(t.value),s=t.type;if(n!=null)s==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Mo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Mo(e,t.type,Yt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ua(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Mo(e,t,n){(t!=="number"||Zs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Cr=Array.isArray;function Wn(e,t,n,s){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&s&&(e[n].defaultSelected=!0)}else{for(n=""+Yt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,s&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Eo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function $a(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(Cr(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Yt(n)}}function yd(e,t){var n=Yt(t.value),s=Yt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),s!=null&&(e.defaultValue=""+s)}function Fa(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function vd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Io(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?vd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var bs,wd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,s,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,s,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(bs=bs||document.createElement("div"),bs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=bs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ir={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Pm=["Webkit","ms","Moz","O"];Object.keys(Ir).forEach(function(e){Pm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ir[t]=Ir[e]})});function bd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ir.hasOwnProperty(e)&&Ir[e]?(""+t).trim():t+"px"}function jd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var s=n.indexOf("--")===0,l=bd(n,t[n],s);n==="float"&&(n="cssFloat"),s?e.setProperty(n,l):e[n]=l}}var Rm=me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ao(e,t){if(t){if(Rm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function _o(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var To=null;function Ti(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Lo=null,Qn=null,Gn=null;function Ba(e){if(e=us(e)){if(typeof Lo!="function")throw Error(_(280));var t=e.stateNode;t&&(t=Sl(t),Lo(e.stateNode,e.type,t))}}function Nd(e){Qn?Gn?Gn.push(e):Gn=[e]:Qn=e}function kd(){if(Qn){var e=Qn,t=Gn;if(Gn=Qn=null,Ba(e),t)for(e=0;e<t.length;e++)Ba(t[e])}}function Sd(e,t){return e(t)}function Cd(){}var Dl=!1;function Md(e,t,n){if(Dl)return e(t,n);Dl=!0;try{return Sd(e,t,n)}finally{Dl=!1,(Qn!==null||Gn!==null)&&(Cd(),kd())}}function qr(e,t){var n=e.stateNode;if(n===null)return null;var s=Sl(n);if(s===null)return null;n=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var Po=!1;if(It)try{var yr={};Object.defineProperty(yr,"passive",{get:function(){Po=!0}}),window.addEventListener("test",yr,yr),window.removeEventListener("test",yr,yr)}catch{Po=!1}function zm(e,t,n,s,l,o,i,a,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(g){this.onError(g)}}var Ar=!1,Ks=null,Ys=!1,Ro=null,Om={onError:function(e){Ar=!0,Ks=e}};function Um(e,t,n,s,l,o,i,a,c){Ar=!1,Ks=null,zm.apply(Om,arguments)}function $m(e,t,n,s,l,o,i,a,c){if(Um.apply(this,arguments),Ar){if(Ar){var u=Ks;Ar=!1,Ks=null}else throw Error(_(198));Ys||(Ys=!0,Ro=u)}}function En(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ed(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Va(e){if(En(e)!==e)throw Error(_(188))}function Fm(e){var t=e.alternate;if(!t){if(t=En(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,s=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(s=l.return,s!==null){n=s;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return Va(l),e;if(o===s)return Va(l),t;o=o.sibling}throw Error(_(188))}if(n.return!==s.return)n=l,s=o;else{for(var i=!1,a=l.child;a;){if(a===n){i=!0,n=l,s=o;break}if(a===s){i=!0,s=l,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,s=l;break}if(a===s){i=!0,s=o,n=l;break}a=a.sibling}if(!i)throw Error(_(189))}}if(n.alternate!==s)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Id(e){return e=Fm(e),e!==null?Ad(e):null}function Ad(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ad(e);if(t!==null)return t;e=e.sibling}return null}var _d=Je.unstable_scheduleCallback,qa=Je.unstable_cancelCallback,Bm=Je.unstable_shouldYield,Vm=Je.unstable_requestPaint,xe=Je.unstable_now,qm=Je.unstable_getCurrentPriorityLevel,Li=Je.unstable_ImmediatePriority,Td=Je.unstable_UserBlockingPriority,Xs=Je.unstable_NormalPriority,Hm=Je.unstable_LowPriority,Ld=Je.unstable_IdlePriority,bl=null,vt=null;function Wm(e){if(vt&&typeof vt.onCommitFiberRoot=="function")try{vt.onCommitFiberRoot(bl,e,void 0,(e.current.flags&128)===128)}catch{}}var dt=Math.clz32?Math.clz32:Dm,Qm=Math.log,Gm=Math.LN2;function Dm(e){return e>>>=0,e===0?32:31-(Qm(e)/Gm|0)|0}var js=64,Ns=4194304;function Mr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function el(e,t){var n=e.pendingLanes;if(n===0)return 0;var s=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~l;a!==0?s=Mr(a):(o&=i,o!==0&&(s=Mr(o)))}else i=n&~l,i!==0?s=Mr(i):o!==0&&(s=Mr(o));if(s===0)return 0;if(t!==0&&t!==s&&!(t&l)&&(l=s&-s,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(s&4&&(s|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)n=31-dt(t),l=1<<n,s|=e[n],t&=~l;return s}function Jm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zm(e,t){for(var n=e.suspendedLanes,s=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-dt(o),a=1<<i,c=l[i];c===-1?(!(a&n)||a&s)&&(l[i]=Jm(a,t)):c<=t&&(e.expiredLanes|=a),o&=~a}}function zo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Pd(){var e=js;return js<<=1,!(js&4194240)&&(js=64),e}function Jl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function cs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-dt(t),e[t]=n}function Km(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-dt(n),o=1<<l;t[l]=0,s[l]=-1,e[l]=-1,n&=~o}}function Pi(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var s=31-dt(n),l=1<<s;l&t|e[s]&t&&(e[s]|=t),n&=~l}}var te=0;function Rd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var zd,Ri,Od,Ud,$d,Oo=!1,ks=[],Ht=null,Wt=null,Qt=null,Hr=new Map,Wr=new Map,Ft=[],Ym="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ha(e,t){switch(e){case"focusin":case"focusout":Ht=null;break;case"dragenter":case"dragleave":Wt=null;break;case"mouseover":case"mouseout":Qt=null;break;case"pointerover":case"pointerout":Hr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Wr.delete(t.pointerId)}}function vr(e,t,n,s,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:s,nativeEvent:o,targetContainers:[l]},t!==null&&(t=us(t),t!==null&&Ri(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Xm(e,t,n,s,l){switch(t){case"focusin":return Ht=vr(Ht,e,t,n,s,l),!0;case"dragenter":return Wt=vr(Wt,e,t,n,s,l),!0;case"mouseover":return Qt=vr(Qt,e,t,n,s,l),!0;case"pointerover":var o=l.pointerId;return Hr.set(o,vr(Hr.get(o)||null,e,t,n,s,l)),!0;case"gotpointercapture":return o=l.pointerId,Wr.set(o,vr(Wr.get(o)||null,e,t,n,s,l)),!0}return!1}function Fd(e){var t=un(e.target);if(t!==null){var n=En(t);if(n!==null){if(t=n.tag,t===13){if(t=Ed(n),t!==null){e.blockedOn=t,$d(e.priority,function(){Od(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Us(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Uo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var s=new n.constructor(n.type,n);To=s,n.target.dispatchEvent(s),To=null}else return t=us(n),t!==null&&Ri(t),e.blockedOn=n,!1;t.shift()}return!0}function Wa(e,t,n){Us(e)&&n.delete(t)}function ef(){Oo=!1,Ht!==null&&Us(Ht)&&(Ht=null),Wt!==null&&Us(Wt)&&(Wt=null),Qt!==null&&Us(Qt)&&(Qt=null),Hr.forEach(Wa),Wr.forEach(Wa)}function wr(e,t){e.blockedOn===t&&(e.blockedOn=null,Oo||(Oo=!0,Je.unstable_scheduleCallback(Je.unstable_NormalPriority,ef)))}function Qr(e){function t(l){return wr(l,e)}if(0<ks.length){wr(ks[0],e);for(var n=1;n<ks.length;n++){var s=ks[n];s.blockedOn===e&&(s.blockedOn=null)}}for(Ht!==null&&wr(Ht,e),Wt!==null&&wr(Wt,e),Qt!==null&&wr(Qt,e),Hr.forEach(t),Wr.forEach(t),n=0;n<Ft.length;n++)s=Ft[n],s.blockedOn===e&&(s.blockedOn=null);for(;0<Ft.length&&(n=Ft[0],n.blockedOn===null);)Fd(n),n.blockedOn===null&&Ft.shift()}var Dn=Lt.ReactCurrentBatchConfig,tl=!0;function tf(e,t,n,s){var l=te,o=Dn.transition;Dn.transition=null;try{te=1,zi(e,t,n,s)}finally{te=l,Dn.transition=o}}function nf(e,t,n,s){var l=te,o=Dn.transition;Dn.transition=null;try{te=4,zi(e,t,n,s)}finally{te=l,Dn.transition=o}}function zi(e,t,n,s){if(tl){var l=Uo(e,t,n,s);if(l===null)lo(e,t,s,nl,n),Ha(e,s);else if(Xm(l,e,t,n,s))s.stopPropagation();else if(Ha(e,s),t&4&&-1<Ym.indexOf(e)){for(;l!==null;){var o=us(l);if(o!==null&&zd(o),o=Uo(e,t,n,s),o===null&&lo(e,t,s,nl,n),o===l)break;l=o}l!==null&&s.stopPropagation()}else lo(e,t,s,null,n)}}var nl=null;function Uo(e,t,n,s){if(nl=null,e=Ti(s),e=un(e),e!==null)if(t=En(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ed(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return nl=e,null}function Bd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(qm()){case Li:return 1;case Td:return 4;case Xs:case Hm:return 16;case Ld:return 536870912;default:return 16}default:return 16}}var Vt=null,Oi=null,$s=null;function Vd(){if($s)return $s;var e,t=Oi,n=t.length,s,l="value"in Vt?Vt.value:Vt.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(s=1;s<=i&&t[n-s]===l[o-s];s++);return $s=l.slice(e,1<s?1-s:void 0)}function Fs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ss(){return!0}function Qa(){return!1}function Ke(e){function t(n,s,l,o,i){this._reactName=n,this._targetInst=l,this.type=s,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Ss:Qa,this.isPropagationStopped=Qa,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ss)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ss)},persist:function(){},isPersistent:Ss}),t}var or={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ui=Ke(or),ds=me({},or,{view:0,detail:0}),rf=Ke(ds),Zl,Kl,br,jl=me({},ds,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$i,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==br&&(br&&e.type==="mousemove"?(Zl=e.screenX-br.screenX,Kl=e.screenY-br.screenY):Kl=Zl=0,br=e),Zl)},movementY:function(e){return"movementY"in e?e.movementY:Kl}}),Ga=Ke(jl),sf=me({},jl,{dataTransfer:0}),lf=Ke(sf),of=me({},ds,{relatedTarget:0}),Yl=Ke(of),af=me({},or,{animationName:0,elapsedTime:0,pseudoElement:0}),cf=Ke(af),df=me({},or,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),uf=Ke(df),mf=me({},or,{data:0}),Da=Ke(mf),ff={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},pf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=pf[e])?!!t[e]:!1}function $i(){return gf}var xf=me({},ds,{key:function(e){if(e.key){var t=ff[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?hf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$i,charCode:function(e){return e.type==="keypress"?Fs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),yf=Ke(xf),vf=me({},jl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ja=Ke(vf),wf=me({},ds,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$i}),bf=Ke(wf),jf=me({},or,{propertyName:0,elapsedTime:0,pseudoElement:0}),Nf=Ke(jf),kf=me({},jl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Sf=Ke(kf),Cf=[9,13,27,32],Fi=It&&"CompositionEvent"in window,_r=null;It&&"documentMode"in document&&(_r=document.documentMode);var Mf=It&&"TextEvent"in window&&!_r,qd=It&&(!Fi||_r&&8<_r&&11>=_r),Za=" ",Ka=!1;function Hd(e,t){switch(e){case"keyup":return Cf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Rn=!1;function Ef(e,t){switch(e){case"compositionend":return Wd(t);case"keypress":return t.which!==32?null:(Ka=!0,Za);case"textInput":return e=t.data,e===Za&&Ka?null:e;default:return null}}function If(e,t){if(Rn)return e==="compositionend"||!Fi&&Hd(e,t)?(e=Vd(),$s=Oi=Vt=null,Rn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qd&&t.locale!=="ko"?null:t.data;default:return null}}var Af={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ya(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Af[e.type]:t==="textarea"}function Qd(e,t,n,s){Nd(s),t=rl(t,"onChange"),0<t.length&&(n=new Ui("onChange","change",null,n,s),e.push({event:n,listeners:t}))}var Tr=null,Gr=null;function _f(e){ru(e,0)}function Nl(e){var t=Un(e);if(gd(t))return e}function Tf(e,t){if(e==="change")return t}var Gd=!1;if(It){var Xl;if(It){var eo="oninput"in document;if(!eo){var Xa=document.createElement("div");Xa.setAttribute("oninput","return;"),eo=typeof Xa.oninput=="function"}Xl=eo}else Xl=!1;Gd=Xl&&(!document.documentMode||9<document.documentMode)}function ec(){Tr&&(Tr.detachEvent("onpropertychange",Dd),Gr=Tr=null)}function Dd(e){if(e.propertyName==="value"&&Nl(Gr)){var t=[];Qd(t,Gr,e,Ti(e)),Md(_f,t)}}function Lf(e,t,n){e==="focusin"?(ec(),Tr=t,Gr=n,Tr.attachEvent("onpropertychange",Dd)):e==="focusout"&&ec()}function Pf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Nl(Gr)}function Rf(e,t){if(e==="click")return Nl(t)}function zf(e,t){if(e==="input"||e==="change")return Nl(t)}function Of(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var mt=typeof Object.is=="function"?Object.is:Of;function Dr(e,t){if(mt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var l=n[s];if(!wo.call(t,l)||!mt(e[l],t[l]))return!1}return!0}function tc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function nc(e,t){var n=tc(e);e=0;for(var s;n;){if(n.nodeType===3){if(s=e+n.textContent.length,e<=t&&s>=t)return{node:n,offset:t-e};e=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=tc(n)}}function Jd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Jd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Zd(){for(var e=window,t=Zs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zs(e.document)}return t}function Bi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Uf(e){var t=Zd(),n=e.focusedElem,s=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Jd(n.ownerDocument.documentElement,n)){if(s!==null&&Bi(n)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(s.start,l);s=s.end===void 0?o:Math.min(s.end,l),!e.extend&&o>s&&(l=s,s=o,o=l),l=nc(n,o);var i=nc(n,s);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>s?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $f=It&&"documentMode"in document&&11>=document.documentMode,zn=null,$o=null,Lr=null,Fo=!1;function rc(e,t,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Fo||zn==null||zn!==Zs(s)||(s=zn,"selectionStart"in s&&Bi(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Lr&&Dr(Lr,s)||(Lr=s,s=rl($o,"onSelect"),0<s.length&&(t=new Ui("onSelect","select",null,t,n),e.push({event:t,listeners:s}),t.target=zn)))}function Cs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var On={animationend:Cs("Animation","AnimationEnd"),animationiteration:Cs("Animation","AnimationIteration"),animationstart:Cs("Animation","AnimationStart"),transitionend:Cs("Transition","TransitionEnd")},to={},Kd={};It&&(Kd=document.createElement("div").style,"AnimationEvent"in window||(delete On.animationend.animation,delete On.animationiteration.animation,delete On.animationstart.animation),"TransitionEvent"in window||delete On.transitionend.transition);function kl(e){if(to[e])return to[e];if(!On[e])return e;var t=On[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Kd)return to[e]=t[n];return e}var Yd=kl("animationend"),Xd=kl("animationiteration"),eu=kl("animationstart"),tu=kl("transitionend"),nu=new Map,sc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function en(e,t){nu.set(e,t),Mn(t,[e])}for(var no=0;no<sc.length;no++){var ro=sc[no],Ff=ro.toLowerCase(),Bf=ro[0].toUpperCase()+ro.slice(1);en(Ff,"on"+Bf)}en(Yd,"onAnimationEnd");en(Xd,"onAnimationIteration");en(eu,"onAnimationStart");en("dblclick","onDoubleClick");en("focusin","onFocus");en("focusout","onBlur");en(tu,"onTransitionEnd");Kn("onMouseEnter",["mouseout","mouseover"]);Kn("onMouseLeave",["mouseout","mouseover"]);Kn("onPointerEnter",["pointerout","pointerover"]);Kn("onPointerLeave",["pointerout","pointerover"]);Mn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Mn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Mn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Mn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Mn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Mn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Er="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Er));function lc(e,t,n){var s=e.type||"unknown-event";e.currentTarget=n,$m(s,t,void 0,e),e.currentTarget=null}function ru(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var s=e[n],l=s.event;s=s.listeners;e:{var o=void 0;if(t)for(var i=s.length-1;0<=i;i--){var a=s[i],c=a.instance,u=a.currentTarget;if(a=a.listener,c!==o&&l.isPropagationStopped())break e;lc(l,a,u),o=c}else for(i=0;i<s.length;i++){if(a=s[i],c=a.instance,u=a.currentTarget,a=a.listener,c!==o&&l.isPropagationStopped())break e;lc(l,a,u),o=c}}}if(Ys)throw e=Ro,Ys=!1,Ro=null,e}function le(e,t){var n=t[Wo];n===void 0&&(n=t[Wo]=new Set);var s=e+"__bubble";n.has(s)||(su(t,e,2,!1),n.add(s))}function so(e,t,n){var s=0;t&&(s|=4),su(n,e,s,t)}var Ms="_reactListening"+Math.random().toString(36).slice(2);function Jr(e){if(!e[Ms]){e[Ms]=!0,ud.forEach(function(n){n!=="selectionchange"&&(Vf.has(n)||so(n,!1,e),so(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ms]||(t[Ms]=!0,so("selectionchange",!1,t))}}function su(e,t,n,s){switch(Bd(t)){case 1:var l=tf;break;case 4:l=nf;break;default:l=zi}n=l.bind(null,t,n,e),l=void 0,!Po||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),s?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function lo(e,t,n,s,l){var o=s;if(!(t&1)&&!(t&2)&&s!==null)e:for(;;){if(s===null)return;var i=s.tag;if(i===3||i===4){var a=s.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(i===4)for(i=s.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;i=i.return}for(;a!==null;){if(i=un(a),i===null)return;if(c=i.tag,c===5||c===6){s=o=i;continue e}a=a.parentNode}}s=s.return}Md(function(){var u=o,g=Ti(n),w=[];e:{var b=nu.get(e);if(b!==void 0){var N=Ui,j=e;switch(e){case"keypress":if(Fs(n)===0)break e;case"keydown":case"keyup":N=yf;break;case"focusin":j="focus",N=Yl;break;case"focusout":j="blur",N=Yl;break;case"beforeblur":case"afterblur":N=Yl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=Ga;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=lf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=bf;break;case Yd:case Xd:case eu:N=cf;break;case tu:N=Nf;break;case"scroll":N=rf;break;case"wheel":N=Sf;break;case"copy":case"cut":case"paste":N=uf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=Ja}var k=(t&4)!==0,S=!k&&e==="scroll",m=k?b!==null?b+"Capture":null:b;k=[];for(var d=u,p;d!==null;){p=d;var M=p.stateNode;if(p.tag===5&&M!==null&&(p=M,m!==null&&(M=qr(d,m),M!=null&&k.push(Zr(d,M,p)))),S)break;d=d.return}0<k.length&&(b=new N(b,j,null,n,g),w.push({event:b,listeners:k}))}}if(!(t&7)){e:{if(b=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",b&&n!==To&&(j=n.relatedTarget||n.fromElement)&&(un(j)||j[At]))break e;if((N||b)&&(b=g.window===g?g:(b=g.ownerDocument)?b.defaultView||b.parentWindow:window,N?(j=n.relatedTarget||n.toElement,N=u,j=j?un(j):null,j!==null&&(S=En(j),j!==S||j.tag!==5&&j.tag!==6)&&(j=null)):(N=null,j=u),N!==j)){if(k=Ga,M="onMouseLeave",m="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(k=Ja,M="onPointerLeave",m="onPointerEnter",d="pointer"),S=N==null?b:Un(N),p=j==null?b:Un(j),b=new k(M,d+"leave",N,n,g),b.target=S,b.relatedTarget=p,M=null,un(g)===u&&(k=new k(m,d+"enter",j,n,g),k.target=p,k.relatedTarget=S,M=k),S=M,N&&j)t:{for(k=N,m=j,d=0,p=k;p;p=Tn(p))d++;for(p=0,M=m;M;M=Tn(M))p++;for(;0<d-p;)k=Tn(k),d--;for(;0<p-d;)m=Tn(m),p--;for(;d--;){if(k===m||m!==null&&k===m.alternate)break t;k=Tn(k),m=Tn(m)}k=null}else k=null;N!==null&&oc(w,b,N,k,!1),j!==null&&S!==null&&oc(w,S,j,k,!0)}}e:{if(b=u?Un(u):window,N=b.nodeName&&b.nodeName.toLowerCase(),N==="select"||N==="input"&&b.type==="file")var P=Tf;else if(Ya(b))if(Gd)P=zf;else{P=Pf;var R=Lf}else(N=b.nodeName)&&N.toLowerCase()==="input"&&(b.type==="checkbox"||b.type==="radio")&&(P=Rf);if(P&&(P=P(e,u))){Qd(w,P,n,g);break e}R&&R(e,b,u),e==="focusout"&&(R=b._wrapperState)&&R.controlled&&b.type==="number"&&Mo(b,"number",b.value)}switch(R=u?Un(u):window,e){case"focusin":(Ya(R)||R.contentEditable==="true")&&(zn=R,$o=u,Lr=null);break;case"focusout":Lr=$o=zn=null;break;case"mousedown":Fo=!0;break;case"contextmenu":case"mouseup":case"dragend":Fo=!1,rc(w,n,g);break;case"selectionchange":if($f)break;case"keydown":case"keyup":rc(w,n,g)}var z;if(Fi)e:{switch(e){case"compositionstart":var U="onCompositionStart";break e;case"compositionend":U="onCompositionEnd";break e;case"compositionupdate":U="onCompositionUpdate";break e}U=void 0}else Rn?Hd(e,n)&&(U="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(U="onCompositionStart");U&&(qd&&n.locale!=="ko"&&(Rn||U!=="onCompositionStart"?U==="onCompositionEnd"&&Rn&&(z=Vd()):(Vt=g,Oi="value"in Vt?Vt.value:Vt.textContent,Rn=!0)),R=rl(u,U),0<R.length&&(U=new Da(U,e,null,n,g),w.push({event:U,listeners:R}),z?U.data=z:(z=Wd(n),z!==null&&(U.data=z)))),(z=Mf?Ef(e,n):If(e,n))&&(u=rl(u,"onBeforeInput"),0<u.length&&(g=new Da("onBeforeInput","beforeinput",null,n,g),w.push({event:g,listeners:u}),g.data=z))}ru(w,t)})}function Zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function rl(e,t){for(var n=t+"Capture",s=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=qr(e,n),o!=null&&s.unshift(Zr(e,o,l)),o=qr(e,t),o!=null&&s.push(Zr(e,o,l))),e=e.return}return s}function Tn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function oc(e,t,n,s,l){for(var o=t._reactName,i=[];n!==null&&n!==s;){var a=n,c=a.alternate,u=a.stateNode;if(c!==null&&c===s)break;a.tag===5&&u!==null&&(a=u,l?(c=qr(n,o),c!=null&&i.unshift(Zr(n,c,a))):l||(c=qr(n,o),c!=null&&i.push(Zr(n,c,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var qf=/\r\n?/g,Hf=/\u0000|\uFFFD/g;function ic(e){return(typeof e=="string"?e:""+e).replace(qf,`
`).replace(Hf,"")}function Es(e,t,n){if(t=ic(t),ic(e)!==t&&n)throw Error(_(425))}function sl(){}var Bo=null,Vo=null;function qo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ho=typeof setTimeout=="function"?setTimeout:void 0,Wf=typeof clearTimeout=="function"?clearTimeout:void 0,ac=typeof Promise=="function"?Promise:void 0,Qf=typeof queueMicrotask=="function"?queueMicrotask:typeof ac<"u"?function(e){return ac.resolve(null).then(e).catch(Gf)}:Ho;function Gf(e){setTimeout(function(){throw e})}function oo(e,t){var n=t,s=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(s===0){e.removeChild(l),Qr(t);return}s--}else n!=="$"&&n!=="$?"&&n!=="$!"||s++;n=l}while(n);Qr(t)}function Gt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function cc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ir=Math.random().toString(36).slice(2),xt="__reactFiber$"+ir,Kr="__reactProps$"+ir,At="__reactContainer$"+ir,Wo="__reactEvents$"+ir,Df="__reactListeners$"+ir,Jf="__reactHandles$"+ir;function un(e){var t=e[xt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[At]||n[xt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=cc(e);e!==null;){if(n=e[xt])return n;e=cc(e)}return t}e=n,n=e.parentNode}return null}function us(e){return e=e[xt]||e[At],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Un(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function Sl(e){return e[Kr]||null}var Qo=[],$n=-1;function tn(e){return{current:e}}function oe(e){0>$n||(e.current=Qo[$n],Qo[$n]=null,$n--)}function se(e,t){$n++,Qo[$n]=e.current,e.current=t}var Xt={},Le=tn(Xt),Ve=tn(!1),bn=Xt;function Yn(e,t){var n=e.type.contextTypes;if(!n)return Xt;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function qe(e){return e=e.childContextTypes,e!=null}function ll(){oe(Ve),oe(Le)}function dc(e,t,n){if(Le.current!==Xt)throw Error(_(168));se(Le,t),se(Ve,n)}function lu(e,t,n){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return n;s=s.getChildContext();for(var l in s)if(!(l in t))throw Error(_(108,Tm(e)||"Unknown",l));return me({},n,s)}function ol(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Xt,bn=Le.current,se(Le,e),se(Ve,Ve.current),!0}function uc(e,t,n){var s=e.stateNode;if(!s)throw Error(_(169));n?(e=lu(e,t,bn),s.__reactInternalMemoizedMergedChildContext=e,oe(Ve),oe(Le),se(Le,e)):oe(Ve),se(Ve,n)}var St=null,Cl=!1,io=!1;function ou(e){St===null?St=[e]:St.push(e)}function Zf(e){Cl=!0,ou(e)}function nn(){if(!io&&St!==null){io=!0;var e=0,t=te;try{var n=St;for(te=1;e<n.length;e++){var s=n[e];do s=s(!0);while(s!==null)}St=null,Cl=!1}catch(l){throw St!==null&&(St=St.slice(e+1)),_d(Li,nn),l}finally{te=t,io=!1}}return null}var Fn=[],Bn=0,il=null,al=0,Ye=[],Xe=0,jn=null,Ct=1,Mt="";function cn(e,t){Fn[Bn++]=al,Fn[Bn++]=il,il=e,al=t}function iu(e,t,n){Ye[Xe++]=Ct,Ye[Xe++]=Mt,Ye[Xe++]=jn,jn=e;var s=Ct;e=Mt;var l=32-dt(s)-1;s&=~(1<<l),n+=1;var o=32-dt(t)+l;if(30<o){var i=l-l%5;o=(s&(1<<i)-1).toString(32),s>>=i,l-=i,Ct=1<<32-dt(t)+l|n<<l|s,Mt=o+e}else Ct=1<<o|n<<l|s,Mt=e}function Vi(e){e.return!==null&&(cn(e,1),iu(e,1,0))}function qi(e){for(;e===il;)il=Fn[--Bn],Fn[Bn]=null,al=Fn[--Bn],Fn[Bn]=null;for(;e===jn;)jn=Ye[--Xe],Ye[Xe]=null,Mt=Ye[--Xe],Ye[Xe]=null,Ct=Ye[--Xe],Ye[Xe]=null}var De=null,Ge=null,ae=!1,ct=null;function au(e,t){var n=et(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function mc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Ge=Gt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Ge=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=jn!==null?{id:Ct,overflow:Mt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=et(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Ge=null,!0):!1;default:return!1}}function Go(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Do(e){if(ae){var t=Ge;if(t){var n=t;if(!mc(e,t)){if(Go(e))throw Error(_(418));t=Gt(n.nextSibling);var s=De;t&&mc(e,t)?au(s,n):(e.flags=e.flags&-4097|2,ae=!1,De=e)}}else{if(Go(e))throw Error(_(418));e.flags=e.flags&-4097|2,ae=!1,De=e}}}function fc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function Is(e){if(e!==De)return!1;if(!ae)return fc(e),ae=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!qo(e.type,e.memoizedProps)),t&&(t=Ge)){if(Go(e))throw cu(),Error(_(418));for(;t;)au(e,t),t=Gt(t.nextSibling)}if(fc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ge=Gt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ge=null}}else Ge=De?Gt(e.stateNode.nextSibling):null;return!0}function cu(){for(var e=Ge;e;)e=Gt(e.nextSibling)}function Xn(){Ge=De=null,ae=!1}function Hi(e){ct===null?ct=[e]:ct.push(e)}var Kf=Lt.ReactCurrentBatchConfig;function jr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var s=n.stateNode}if(!s)throw Error(_(147,e));var l=s,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=l.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function As(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function hc(e){var t=e._init;return t(e._payload)}function du(e){function t(m,d){if(e){var p=m.deletions;p===null?(m.deletions=[d],m.flags|=16):p.push(d)}}function n(m,d){if(!e)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function s(m,d){for(m=new Map;d!==null;)d.key!==null?m.set(d.key,d):m.set(d.index,d),d=d.sibling;return m}function l(m,d){return m=Kt(m,d),m.index=0,m.sibling=null,m}function o(m,d,p){return m.index=p,e?(p=m.alternate,p!==null?(p=p.index,p<d?(m.flags|=2,d):p):(m.flags|=2,d)):(m.flags|=1048576,d)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,d,p,M){return d===null||d.tag!==6?(d=po(p,m.mode,M),d.return=m,d):(d=l(d,p),d.return=m,d)}function c(m,d,p,M){var P=p.type;return P===Pn?g(m,d,p.props.children,M,p.key):d!==null&&(d.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Ut&&hc(P)===d.type)?(M=l(d,p.props),M.ref=jr(m,d,p),M.return=m,M):(M=Gs(p.type,p.key,p.props,null,m.mode,M),M.ref=jr(m,d,p),M.return=m,M)}function u(m,d,p,M){return d===null||d.tag!==4||d.stateNode.containerInfo!==p.containerInfo||d.stateNode.implementation!==p.implementation?(d=go(p,m.mode,M),d.return=m,d):(d=l(d,p.children||[]),d.return=m,d)}function g(m,d,p,M,P){return d===null||d.tag!==7?(d=gn(p,m.mode,M,P),d.return=m,d):(d=l(d,p),d.return=m,d)}function w(m,d,p){if(typeof d=="string"&&d!==""||typeof d=="number")return d=po(""+d,m.mode,p),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case vs:return p=Gs(d.type,d.key,d.props,null,m.mode,p),p.ref=jr(m,null,d),p.return=m,p;case Ln:return d=go(d,m.mode,p),d.return=m,d;case Ut:var M=d._init;return w(m,M(d._payload),p)}if(Cr(d)||xr(d))return d=gn(d,m.mode,p,null),d.return=m,d;As(m,d)}return null}function b(m,d,p,M){var P=d!==null?d.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return P!==null?null:a(m,d,""+p,M);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case vs:return p.key===P?c(m,d,p,M):null;case Ln:return p.key===P?u(m,d,p,M):null;case Ut:return P=p._init,b(m,d,P(p._payload),M)}if(Cr(p)||xr(p))return P!==null?null:g(m,d,p,M,null);As(m,p)}return null}function N(m,d,p,M,P){if(typeof M=="string"&&M!==""||typeof M=="number")return m=m.get(p)||null,a(d,m,""+M,P);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case vs:return m=m.get(M.key===null?p:M.key)||null,c(d,m,M,P);case Ln:return m=m.get(M.key===null?p:M.key)||null,u(d,m,M,P);case Ut:var R=M._init;return N(m,d,p,R(M._payload),P)}if(Cr(M)||xr(M))return m=m.get(p)||null,g(d,m,M,P,null);As(d,M)}return null}function j(m,d,p,M){for(var P=null,R=null,z=d,U=d=0,D=null;z!==null&&U<p.length;U++){z.index>U?(D=z,z=null):D=z.sibling;var y=b(m,z,p[U],M);if(y===null){z===null&&(z=D);break}e&&z&&y.alternate===null&&t(m,z),d=o(y,d,U),R===null?P=y:R.sibling=y,R=y,z=D}if(U===p.length)return n(m,z),ae&&cn(m,U),P;if(z===null){for(;U<p.length;U++)z=w(m,p[U],M),z!==null&&(d=o(z,d,U),R===null?P=z:R.sibling=z,R=z);return ae&&cn(m,U),P}for(z=s(m,z);U<p.length;U++)D=N(z,m,U,p[U],M),D!==null&&(e&&D.alternate!==null&&z.delete(D.key===null?U:D.key),d=o(D,d,U),R===null?P=D:R.sibling=D,R=D);return e&&z.forEach(function(O){return t(m,O)}),ae&&cn(m,U),P}function k(m,d,p,M){var P=xr(p);if(typeof P!="function")throw Error(_(150));if(p=P.call(p),p==null)throw Error(_(151));for(var R=P=null,z=d,U=d=0,D=null,y=p.next();z!==null&&!y.done;U++,y=p.next()){z.index>U?(D=z,z=null):D=z.sibling;var O=b(m,z,y.value,M);if(O===null){z===null&&(z=D);break}e&&z&&O.alternate===null&&t(m,z),d=o(O,d,U),R===null?P=O:R.sibling=O,R=O,z=D}if(y.done)return n(m,z),ae&&cn(m,U),P;if(z===null){for(;!y.done;U++,y=p.next())y=w(m,y.value,M),y!==null&&(d=o(y,d,U),R===null?P=y:R.sibling=y,R=y);return ae&&cn(m,U),P}for(z=s(m,z);!y.done;U++,y=p.next())y=N(z,m,U,y.value,M),y!==null&&(e&&y.alternate!==null&&z.delete(y.key===null?U:y.key),d=o(y,d,U),R===null?P=y:R.sibling=y,R=y);return e&&z.forEach(function(Z){return t(m,Z)}),ae&&cn(m,U),P}function S(m,d,p,M){if(typeof p=="object"&&p!==null&&p.type===Pn&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case vs:e:{for(var P=p.key,R=d;R!==null;){if(R.key===P){if(P=p.type,P===Pn){if(R.tag===7){n(m,R.sibling),d=l(R,p.props.children),d.return=m,m=d;break e}}else if(R.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===Ut&&hc(P)===R.type){n(m,R.sibling),d=l(R,p.props),d.ref=jr(m,R,p),d.return=m,m=d;break e}n(m,R);break}else t(m,R);R=R.sibling}p.type===Pn?(d=gn(p.props.children,m.mode,M,p.key),d.return=m,m=d):(M=Gs(p.type,p.key,p.props,null,m.mode,M),M.ref=jr(m,d,p),M.return=m,m=M)}return i(m);case Ln:e:{for(R=p.key;d!==null;){if(d.key===R)if(d.tag===4&&d.stateNode.containerInfo===p.containerInfo&&d.stateNode.implementation===p.implementation){n(m,d.sibling),d=l(d,p.children||[]),d.return=m,m=d;break e}else{n(m,d);break}else t(m,d);d=d.sibling}d=go(p,m.mode,M),d.return=m,m=d}return i(m);case Ut:return R=p._init,S(m,d,R(p._payload),M)}if(Cr(p))return j(m,d,p,M);if(xr(p))return k(m,d,p,M);As(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,d!==null&&d.tag===6?(n(m,d.sibling),d=l(d,p),d.return=m,m=d):(n(m,d),d=po(p,m.mode,M),d.return=m,m=d),i(m)):n(m,d)}return S}var er=du(!0),uu=du(!1),cl=tn(null),dl=null,Vn=null,Wi=null;function Qi(){Wi=Vn=dl=null}function Gi(e){var t=cl.current;oe(cl),e._currentValue=t}function Jo(e,t,n){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===n)break;e=e.return}}function Jn(e,t){dl=e,Wi=Vn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Be=!0),e.firstContext=null)}function nt(e){var t=e._currentValue;if(Wi!==e)if(e={context:e,memoizedValue:t,next:null},Vn===null){if(dl===null)throw Error(_(308));Vn=e,dl.dependencies={lanes:0,firstContext:e}}else Vn=Vn.next=e;return t}var mn=null;function Di(e){mn===null?mn=[e]:mn.push(e)}function mu(e,t,n,s){var l=t.interleaved;return l===null?(n.next=n,Di(t)):(n.next=l.next,l.next=n),t.interleaved=n,_t(e,s)}function _t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var $t=!1;function Ji(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function fu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Et(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Dt(e,t,n){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,ee&2){var l=s.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),s.pending=t,_t(e,n)}return l=s.interleaved,l===null?(t.next=t,Di(s)):(t.next=l.next,l.next=t),s.interleaved=t,_t(e,n)}function Bs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,Pi(e,n)}}function pc(e,t){var n=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:s.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:s.shared,effects:s.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ul(e,t,n,s){var l=e.updateQueue;$t=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,u=c.next;c.next=null,i===null?o=u:i.next=u,i=c;var g=e.alternate;g!==null&&(g=g.updateQueue,a=g.lastBaseUpdate,a!==i&&(a===null?g.firstBaseUpdate=u:a.next=u,g.lastBaseUpdate=c))}if(o!==null){var w=l.baseState;i=0,g=u=c=null,a=o;do{var b=a.lane,N=a.eventTime;if((s&b)===b){g!==null&&(g=g.next={eventTime:N,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var j=e,k=a;switch(b=t,N=n,k.tag){case 1:if(j=k.payload,typeof j=="function"){w=j.call(N,w,b);break e}w=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=k.payload,b=typeof j=="function"?j.call(N,w,b):j,b==null)break e;w=me({},w,b);break e;case 2:$t=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,b=l.effects,b===null?l.effects=[a]:b.push(a))}else N={eventTime:N,lane:b,tag:a.tag,payload:a.payload,callback:a.callback,next:null},g===null?(u=g=N,c=w):g=g.next=N,i|=b;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;b=a,a=b.next,b.next=null,l.lastBaseUpdate=b,l.shared.pending=null}}while(!0);if(g===null&&(c=w),l.baseState=c,l.firstBaseUpdate=u,l.lastBaseUpdate=g,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);kn|=i,e.lanes=i,e.memoizedState=w}}function gc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],l=s.callback;if(l!==null){if(s.callback=null,s=n,typeof l!="function")throw Error(_(191,l));l.call(s)}}}var ms={},wt=tn(ms),Yr=tn(ms),Xr=tn(ms);function fn(e){if(e===ms)throw Error(_(174));return e}function Zi(e,t){switch(se(Xr,t),se(Yr,e),se(wt,ms),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Io(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Io(t,e)}oe(wt),se(wt,t)}function tr(){oe(wt),oe(Yr),oe(Xr)}function hu(e){fn(Xr.current);var t=fn(wt.current),n=Io(t,e.type);t!==n&&(se(Yr,e),se(wt,n))}function Ki(e){Yr.current===e&&(oe(wt),oe(Yr))}var de=tn(0);function ml(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ao=[];function Yi(){for(var e=0;e<ao.length;e++)ao[e]._workInProgressVersionPrimary=null;ao.length=0}var Vs=Lt.ReactCurrentDispatcher,co=Lt.ReactCurrentBatchConfig,Nn=0,ue=null,ve=null,Ne=null,fl=!1,Pr=!1,es=0,Yf=0;function Ae(){throw Error(_(321))}function Xi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!mt(e[n],t[n]))return!1;return!0}function ea(e,t,n,s,l,o){if(Nn=o,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Vs.current=e===null||e.memoizedState===null?nh:rh,e=n(s,l),Pr){o=0;do{if(Pr=!1,es=0,25<=o)throw Error(_(301));o+=1,Ne=ve=null,t.updateQueue=null,Vs.current=sh,e=n(s,l)}while(Pr)}if(Vs.current=hl,t=ve!==null&&ve.next!==null,Nn=0,Ne=ve=ue=null,fl=!1,t)throw Error(_(300));return e}function ta(){var e=es!==0;return es=0,e}function gt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ne===null?ue.memoizedState=Ne=e:Ne=Ne.next=e,Ne}function rt(){if(ve===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=Ne===null?ue.memoizedState:Ne.next;if(t!==null)Ne=t,ve=e;else{if(e===null)throw Error(_(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},Ne===null?ue.memoizedState=Ne=e:Ne=Ne.next=e}return Ne}function ts(e,t){return typeof t=="function"?t(e):t}function uo(e){var t=rt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var s=ve,l=s.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}s.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,s=s.baseState;var a=i=null,c=null,u=o;do{var g=u.lane;if((Nn&g)===g)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),s=u.hasEagerState?u.eagerState:e(s,u.action);else{var w={lane:g,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(a=c=w,i=s):c=c.next=w,ue.lanes|=g,kn|=g}u=u.next}while(u!==null&&u!==o);c===null?i=s:c.next=a,mt(s,t.memoizedState)||(Be=!0),t.memoizedState=s,t.baseState=i,t.baseQueue=c,n.lastRenderedState=s}if(e=n.interleaved,e!==null){l=e;do o=l.lane,ue.lanes|=o,kn|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function mo(e){var t=rt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var s=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);mt(o,t.memoizedState)||(Be=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,s]}function pu(){}function gu(e,t){var n=ue,s=rt(),l=t(),o=!mt(s.memoizedState,l);if(o&&(s.memoizedState=l,Be=!0),s=s.queue,na(vu.bind(null,n,s,e),[e]),s.getSnapshot!==t||o||Ne!==null&&Ne.memoizedState.tag&1){if(n.flags|=2048,ns(9,yu.bind(null,n,s,l,t),void 0,null),ke===null)throw Error(_(349));Nn&30||xu(n,t,l)}return l}function xu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function yu(e,t,n,s){t.value=n,t.getSnapshot=s,wu(t)&&bu(e)}function vu(e,t,n){return n(function(){wu(t)&&bu(e)})}function wu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!mt(e,n)}catch{return!0}}function bu(e){var t=_t(e,1);t!==null&&ut(t,e,1,-1)}function xc(e){var t=gt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ts,lastRenderedState:e},t.queue=e,e=e.dispatch=th.bind(null,ue,e),[t.memoizedState,e]}function ns(e,t,n,s){return e={tag:e,create:t,destroy:n,deps:s,next:null},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(s=n.next,n.next=e,e.next=s,t.lastEffect=e)),e}function ju(){return rt().memoizedState}function qs(e,t,n,s){var l=gt();ue.flags|=e,l.memoizedState=ns(1|t,n,void 0,s===void 0?null:s)}function Ml(e,t,n,s){var l=rt();s=s===void 0?null:s;var o=void 0;if(ve!==null){var i=ve.memoizedState;if(o=i.destroy,s!==null&&Xi(s,i.deps)){l.memoizedState=ns(t,n,o,s);return}}ue.flags|=e,l.memoizedState=ns(1|t,n,o,s)}function yc(e,t){return qs(8390656,8,e,t)}function na(e,t){return Ml(2048,8,e,t)}function Nu(e,t){return Ml(4,2,e,t)}function ku(e,t){return Ml(4,4,e,t)}function Su(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Cu(e,t,n){return n=n!=null?n.concat([e]):null,Ml(4,4,Su.bind(null,t,e),n)}function ra(){}function Mu(e,t){var n=rt();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&Xi(t,s[1])?s[0]:(n.memoizedState=[e,t],e)}function Eu(e,t){var n=rt();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&Xi(t,s[1])?s[0]:(e=e(),n.memoizedState=[e,t],e)}function Iu(e,t,n){return Nn&21?(mt(n,t)||(n=Pd(),ue.lanes|=n,kn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Be=!0),e.memoizedState=n)}function Xf(e,t){var n=te;te=n!==0&&4>n?n:4,e(!0);var s=co.transition;co.transition={};try{e(!1),t()}finally{te=n,co.transition=s}}function Au(){return rt().memoizedState}function eh(e,t,n){var s=Zt(e);if(n={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null},_u(e))Tu(t,n);else if(n=mu(e,t,n,s),n!==null){var l=ze();ut(n,e,s,l),Lu(n,t,s)}}function th(e,t,n){var s=Zt(e),l={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null};if(_u(e))Tu(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(l.hasEagerState=!0,l.eagerState=a,mt(a,i)){var c=t.interleaved;c===null?(l.next=l,Di(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=mu(e,t,l,s),n!==null&&(l=ze(),ut(n,e,s,l),Lu(n,t,s))}}function _u(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Tu(e,t){Pr=fl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Lu(e,t,n){if(n&4194240){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,Pi(e,n)}}var hl={readContext:nt,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useInsertionEffect:Ae,useLayoutEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useMutableSource:Ae,useSyncExternalStore:Ae,useId:Ae,unstable_isNewReconciler:!1},nh={readContext:nt,useCallback:function(e,t){return gt().memoizedState=[e,t===void 0?null:t],e},useContext:nt,useEffect:yc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,qs(4194308,4,Su.bind(null,t,e),n)},useLayoutEffect:function(e,t){return qs(4194308,4,e,t)},useInsertionEffect:function(e,t){return qs(4,2,e,t)},useMemo:function(e,t){var n=gt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var s=gt();return t=n!==void 0?n(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=eh.bind(null,ue,e),[s.memoizedState,e]},useRef:function(e){var t=gt();return e={current:e},t.memoizedState=e},useState:xc,useDebugValue:ra,useDeferredValue:function(e){return gt().memoizedState=e},useTransition:function(){var e=xc(!1),t=e[0];return e=Xf.bind(null,e[1]),gt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var s=ue,l=gt();if(ae){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),ke===null)throw Error(_(349));Nn&30||xu(s,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,yc(vu.bind(null,s,o,e),[e]),s.flags|=2048,ns(9,yu.bind(null,s,o,n,t),void 0,null),n},useId:function(){var e=gt(),t=ke.identifierPrefix;if(ae){var n=Mt,s=Ct;n=(s&~(1<<32-dt(s)-1)).toString(32)+n,t=":"+t+"R"+n,n=es++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Yf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},rh={readContext:nt,useCallback:Mu,useContext:nt,useEffect:na,useImperativeHandle:Cu,useInsertionEffect:Nu,useLayoutEffect:ku,useMemo:Eu,useReducer:uo,useRef:ju,useState:function(){return uo(ts)},useDebugValue:ra,useDeferredValue:function(e){var t=rt();return Iu(t,ve.memoizedState,e)},useTransition:function(){var e=uo(ts)[0],t=rt().memoizedState;return[e,t]},useMutableSource:pu,useSyncExternalStore:gu,useId:Au,unstable_isNewReconciler:!1},sh={readContext:nt,useCallback:Mu,useContext:nt,useEffect:na,useImperativeHandle:Cu,useInsertionEffect:Nu,useLayoutEffect:ku,useMemo:Eu,useReducer:mo,useRef:ju,useState:function(){return mo(ts)},useDebugValue:ra,useDeferredValue:function(e){var t=rt();return ve===null?t.memoizedState=e:Iu(t,ve.memoizedState,e)},useTransition:function(){var e=mo(ts)[0],t=rt().memoizedState;return[e,t]},useMutableSource:pu,useSyncExternalStore:gu,useId:Au,unstable_isNewReconciler:!1};function it(e,t){if(e&&e.defaultProps){t=me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Zo(e,t,n,s){t=e.memoizedState,n=n(s,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var El={isMounted:function(e){return(e=e._reactInternals)?En(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var s=ze(),l=Zt(e),o=Et(s,l);o.payload=t,n!=null&&(o.callback=n),t=Dt(e,o,l),t!==null&&(ut(t,e,l,s),Bs(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var s=ze(),l=Zt(e),o=Et(s,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Dt(e,o,l),t!==null&&(ut(t,e,l,s),Bs(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ze(),s=Zt(e),l=Et(n,s);l.tag=2,t!=null&&(l.callback=t),t=Dt(e,l,s),t!==null&&(ut(t,e,s,n),Bs(t,e,s))}};function vc(e,t,n,s,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,o,i):t.prototype&&t.prototype.isPureReactComponent?!Dr(n,s)||!Dr(l,o):!0}function Pu(e,t,n){var s=!1,l=Xt,o=t.contextType;return typeof o=="object"&&o!==null?o=nt(o):(l=qe(t)?bn:Le.current,s=t.contextTypes,o=(s=s!=null)?Yn(e,l):Xt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=El,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function wc(e,t,n,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,s),t.state!==e&&El.enqueueReplaceState(t,t.state,null)}function Ko(e,t,n,s){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Ji(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=nt(o):(o=qe(t)?bn:Le.current,l.context=Yn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Zo(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&El.enqueueReplaceState(l,l.state,null),ul(e,n,l,s),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function nr(e,t){try{var n="",s=t;do n+=_m(s),s=s.return;while(s);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function fo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Yo(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var lh=typeof WeakMap=="function"?WeakMap:Map;function Ru(e,t,n){n=Et(-1,n),n.tag=3,n.payload={element:null};var s=t.value;return n.callback=function(){gl||(gl=!0,ai=s),Yo(e,t)},n}function zu(e,t,n){n=Et(-1,n),n.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var l=t.value;n.payload=function(){return s(l)},n.callback=function(){Yo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Yo(e,t),typeof s!="function"&&(Jt===null?Jt=new Set([this]):Jt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function bc(e,t,n){var s=e.pingCache;if(s===null){s=e.pingCache=new lh;var l=new Set;s.set(t,l)}else l=s.get(t),l===void 0&&(l=new Set,s.set(t,l));l.has(n)||(l.add(n),e=vh.bind(null,e,t,n),t.then(e,e))}function jc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Nc(e,t,n,s,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Et(-1,1),t.tag=2,Dt(n,t,1))),n.lanes|=1),e)}var oh=Lt.ReactCurrentOwner,Be=!1;function Re(e,t,n,s){t.child=e===null?uu(t,null,n,s):er(t,e.child,n,s)}function kc(e,t,n,s,l){n=n.render;var o=t.ref;return Jn(t,l),s=ea(e,t,n,s,o,l),n=ta(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Tt(e,t,l)):(ae&&n&&Vi(t),t.flags|=1,Re(e,t,s,l),t.child)}function Sc(e,t,n,s,l){if(e===null){var o=n.type;return typeof o=="function"&&!ua(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Ou(e,t,o,s,l)):(e=Gs(n.type,null,s,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:Dr,n(i,s)&&e.ref===t.ref)return Tt(e,t,l)}return t.flags|=1,e=Kt(o,s),e.ref=t.ref,e.return=t,t.child=e}function Ou(e,t,n,s,l){if(e!==null){var o=e.memoizedProps;if(Dr(o,s)&&e.ref===t.ref)if(Be=!1,t.pendingProps=s=o,(e.lanes&l)!==0)e.flags&131072&&(Be=!0);else return t.lanes=e.lanes,Tt(e,t,l)}return Xo(e,t,n,s,l)}function Uu(e,t,n){var s=t.pendingProps,l=s.children,o=e!==null?e.memoizedState:null;if(s.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},se(Hn,Qe),Qe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,se(Hn,Qe),Qe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=o!==null?o.baseLanes:n,se(Hn,Qe),Qe|=s}else o!==null?(s=o.baseLanes|n,t.memoizedState=null):s=n,se(Hn,Qe),Qe|=s;return Re(e,t,l,n),t.child}function $u(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Xo(e,t,n,s,l){var o=qe(n)?bn:Le.current;return o=Yn(t,o),Jn(t,l),n=ea(e,t,n,s,o,l),s=ta(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Tt(e,t,l)):(ae&&s&&Vi(t),t.flags|=1,Re(e,t,n,l),t.child)}function Cc(e,t,n,s,l){if(qe(n)){var o=!0;ol(t)}else o=!1;if(Jn(t,l),t.stateNode===null)Hs(e,t),Pu(t,n,s),Ko(t,n,s,l),s=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=nt(u):(u=qe(n)?bn:Le.current,u=Yn(t,u));var g=n.getDerivedStateFromProps,w=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function";w||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==s||c!==u)&&wc(t,i,s,u),$t=!1;var b=t.memoizedState;i.state=b,ul(t,s,i,l),c=t.memoizedState,a!==s||b!==c||Ve.current||$t?(typeof g=="function"&&(Zo(t,n,g,s),c=t.memoizedState),(a=$t||vc(t,n,a,s,b,c,u))?(w||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=c),i.props=s,i.state=c,i.context=u,s=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{i=t.stateNode,fu(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:it(t.type,a),i.props=u,w=t.pendingProps,b=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=nt(c):(c=qe(n)?bn:Le.current,c=Yn(t,c));var N=n.getDerivedStateFromProps;(g=typeof N=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==w||b!==c)&&wc(t,i,s,c),$t=!1,b=t.memoizedState,i.state=b,ul(t,s,i,l);var j=t.memoizedState;a!==w||b!==j||Ve.current||$t?(typeof N=="function"&&(Zo(t,n,N,s),j=t.memoizedState),(u=$t||vc(t,n,u,s,b,j,c)||!1)?(g||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(s,j,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(s,j,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=j),i.props=s,i.state=j,i.context=c,s=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=1024),s=!1)}return ei(e,t,n,s,o,l)}function ei(e,t,n,s,l,o){$u(e,t);var i=(t.flags&128)!==0;if(!s&&!i)return l&&uc(t,n,!1),Tt(e,t,o);s=t.stateNode,oh.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&i?(t.child=er(t,e.child,null,o),t.child=er(t,null,a,o)):Re(e,t,a,o),t.memoizedState=s.state,l&&uc(t,n,!0),t.child}function Fu(e){var t=e.stateNode;t.pendingContext?dc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&dc(e,t.context,!1),Zi(e,t.containerInfo)}function Mc(e,t,n,s,l){return Xn(),Hi(l),t.flags|=256,Re(e,t,n,s),t.child}var ti={dehydrated:null,treeContext:null,retryLane:0};function ni(e){return{baseLanes:e,cachePool:null,transitions:null}}function Bu(e,t,n){var s=t.pendingProps,l=de.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),se(de,l&1),e===null)return Do(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=s.children,e=s.fallback,o?(s=t.mode,o=t.child,i={mode:"hidden",children:i},!(s&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=_l(i,s,0,null),e=gn(e,s,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ni(n),t.memoizedState=ti,e):sa(t,i));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return ih(e,t,i,s,a,l,n);if(o){o=s.fallback,i=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:s.children};return!(i&1)&&t.child!==l?(s=t.child,s.childLanes=0,s.pendingProps=c,t.deletions=null):(s=Kt(l,c),s.subtreeFlags=l.subtreeFlags&14680064),a!==null?o=Kt(a,o):(o=gn(o,i,n,null),o.flags|=2),o.return=t,s.return=t,s.sibling=o,t.child=s,s=o,o=t.child,i=e.child.memoizedState,i=i===null?ni(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=ti,s}return o=e.child,e=o.sibling,s=Kt(o,{mode:"visible",children:s.children}),!(t.mode&1)&&(s.lanes=n),s.return=t,s.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=s,t.memoizedState=null,s}function sa(e,t){return t=_l({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function _s(e,t,n,s){return s!==null&&Hi(s),er(t,e.child,null,n),e=sa(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ih(e,t,n,s,l,o,i){if(n)return t.flags&256?(t.flags&=-257,s=fo(Error(_(422))),_s(e,t,i,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=s.fallback,l=t.mode,s=_l({mode:"visible",children:s.children},l,0,null),o=gn(o,l,i,null),o.flags|=2,s.return=t,o.return=t,s.sibling=o,t.child=s,t.mode&1&&er(t,e.child,null,i),t.child.memoizedState=ni(i),t.memoizedState=ti,o);if(!(t.mode&1))return _s(e,t,i,null);if(l.data==="$!"){if(s=l.nextSibling&&l.nextSibling.dataset,s)var a=s.dgst;return s=a,o=Error(_(419)),s=fo(o,s,void 0),_s(e,t,i,s)}if(a=(i&e.childLanes)!==0,Be||a){if(s=ke,s!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(s.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,_t(e,l),ut(s,e,l,-1))}return da(),s=fo(Error(_(421))),_s(e,t,i,s)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=wh.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,Ge=Gt(l.nextSibling),De=t,ae=!0,ct=null,e!==null&&(Ye[Xe++]=Ct,Ye[Xe++]=Mt,Ye[Xe++]=jn,Ct=e.id,Mt=e.overflow,jn=t),t=sa(t,s.children),t.flags|=4096,t)}function Ec(e,t,n){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Jo(e.return,t,n)}function ho(e,t,n,s,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=s,o.tail=n,o.tailMode=l)}function Vu(e,t,n){var s=t.pendingProps,l=s.revealOrder,o=s.tail;if(Re(e,t,s.children,n),s=de.current,s&2)s=s&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ec(e,n,t);else if(e.tag===19)Ec(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(se(de,s),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&ml(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),ho(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&ml(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}ho(t,!0,n,null,o);break;case"together":ho(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Tt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),kn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Kt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Kt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ah(e,t,n){switch(t.tag){case 3:Fu(t),Xn();break;case 5:hu(t);break;case 1:qe(t.type)&&ol(t);break;case 4:Zi(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,l=t.memoizedProps.value;se(cl,s._currentValue),s._currentValue=l;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(se(de,de.current&1),t.flags|=128,null):n&t.child.childLanes?Bu(e,t,n):(se(de,de.current&1),e=Tt(e,t,n),e!==null?e.sibling:null);se(de,de.current&1);break;case 19:if(s=(n&t.childLanes)!==0,e.flags&128){if(s)return Vu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),se(de,de.current),s)break;return null;case 22:case 23:return t.lanes=0,Uu(e,t,n)}return Tt(e,t,n)}var qu,ri,Hu,Wu;qu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ri=function(){};Hu=function(e,t,n,s){var l=e.memoizedProps;if(l!==s){e=t.stateNode,fn(wt.current);var o=null;switch(n){case"input":l=So(e,l),s=So(e,s),o=[];break;case"select":l=me({},l,{value:void 0}),s=me({},s,{value:void 0}),o=[];break;case"textarea":l=Eo(e,l),s=Eo(e,s),o=[];break;default:typeof l.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=sl)}Ao(n,s);var i;n=null;for(u in l)if(!s.hasOwnProperty(u)&&l.hasOwnProperty(u)&&l[u]!=null)if(u==="style"){var a=l[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Br.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in s){var c=s[u];if(a=l!=null?l[u]:void 0,s.hasOwnProperty(u)&&c!==a&&(c!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(o||(o=[]),o.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(o=o||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(o=o||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Br.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&le("scroll",e),o||a===c||(o=[])):(o=o||[]).push(u,c))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Wu=function(e,t,n,s){n!==s&&(t.flags|=4)};function Nr(e,t){if(!ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function _e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,s=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags&14680064,s|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags,s|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=s,e.childLanes=n,t}function ch(e,t,n){var s=t.pendingProps;switch(qi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _e(t),null;case 1:return qe(t.type)&&ll(),_e(t),null;case 3:return s=t.stateNode,tr(),oe(Ve),oe(Le),Yi(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(Is(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ct!==null&&(ui(ct),ct=null))),ri(e,t),_e(t),null;case 5:Ki(t);var l=fn(Xr.current);if(n=t.type,e!==null&&t.stateNode!=null)Hu(e,t,n,s,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(_(166));return _e(t),null}if(e=fn(wt.current),Is(t)){s=t.stateNode,n=t.type;var o=t.memoizedProps;switch(s[xt]=t,s[Kr]=o,e=(t.mode&1)!==0,n){case"dialog":le("cancel",s),le("close",s);break;case"iframe":case"object":case"embed":le("load",s);break;case"video":case"audio":for(l=0;l<Er.length;l++)le(Er[l],s);break;case"source":le("error",s);break;case"img":case"image":case"link":le("error",s),le("load",s);break;case"details":le("toggle",s);break;case"input":Oa(s,o),le("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!o.multiple},le("invalid",s);break;case"textarea":$a(s,o),le("invalid",s)}Ao(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?s.textContent!==a&&(o.suppressHydrationWarning!==!0&&Es(s.textContent,a,e),l=["children",a]):typeof a=="number"&&s.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&Es(s.textContent,a,e),l=["children",""+a]):Br.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&le("scroll",s)}switch(n){case"input":ws(s),Ua(s,o,!0);break;case"textarea":ws(s),Fa(s);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(s.onclick=sl)}s=l,t.updateQueue=s,s!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=vd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=i.createElement(n,{is:s.is}):(e=i.createElement(n),n==="select"&&(i=e,s.multiple?i.multiple=!0:s.size&&(i.size=s.size))):e=i.createElementNS(e,n),e[xt]=t,e[Kr]=s,qu(e,t,!1,!1),t.stateNode=e;e:{switch(i=_o(n,s),n){case"dialog":le("cancel",e),le("close",e),l=s;break;case"iframe":case"object":case"embed":le("load",e),l=s;break;case"video":case"audio":for(l=0;l<Er.length;l++)le(Er[l],e);l=s;break;case"source":le("error",e),l=s;break;case"img":case"image":case"link":le("error",e),le("load",e),l=s;break;case"details":le("toggle",e),l=s;break;case"input":Oa(e,s),l=So(e,s),le("invalid",e);break;case"option":l=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},l=me({},s,{value:void 0}),le("invalid",e);break;case"textarea":$a(e,s),l=Eo(e,s),le("invalid",e);break;default:l=s}Ao(n,l),a=l;for(o in a)if(a.hasOwnProperty(o)){var c=a[o];o==="style"?jd(e,c):o==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&wd(e,c)):o==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Vr(e,c):typeof c=="number"&&Vr(e,""+c):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Br.hasOwnProperty(o)?c!=null&&o==="onScroll"&&le("scroll",e):c!=null&&Ei(e,o,c,i))}switch(n){case"input":ws(e),Ua(e,s,!1);break;case"textarea":ws(e),Fa(e);break;case"option":s.value!=null&&e.setAttribute("value",""+Yt(s.value));break;case"select":e.multiple=!!s.multiple,o=s.value,o!=null?Wn(e,!!s.multiple,o,!1):s.defaultValue!=null&&Wn(e,!!s.multiple,s.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=sl)}switch(n){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return _e(t),null;case 6:if(e&&t.stateNode!=null)Wu(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(_(166));if(n=fn(Xr.current),fn(wt.current),Is(t)){if(s=t.stateNode,n=t.memoizedProps,s[xt]=t,(o=s.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:Es(s.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Es(s.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else s=(n.nodeType===9?n:n.ownerDocument).createTextNode(s),s[xt]=t,t.stateNode=s}return _e(t),null;case 13:if(oe(de),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ae&&Ge!==null&&t.mode&1&&!(t.flags&128))cu(),Xn(),t.flags|=98560,o=!1;else if(o=Is(t),s!==null&&s.dehydrated!==null){if(e===null){if(!o)throw Error(_(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(_(317));o[xt]=t}else Xn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;_e(t),o=!1}else ct!==null&&(ui(ct),ct=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,t.mode&1&&(e===null||de.current&1?we===0&&(we=3):da())),t.updateQueue!==null&&(t.flags|=4),_e(t),null);case 4:return tr(),ri(e,t),e===null&&Jr(t.stateNode.containerInfo),_e(t),null;case 10:return Gi(t.type._context),_e(t),null;case 17:return qe(t.type)&&ll(),_e(t),null;case 19:if(oe(de),o=t.memoizedState,o===null)return _e(t),null;if(s=(t.flags&128)!==0,i=o.rendering,i===null)if(s)Nr(o,!1);else{if(we!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=ml(e),i!==null){for(t.flags|=128,Nr(o,!1),s=i.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=n,n=t.child;n!==null;)o=n,e=s,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return se(de,de.current&1|2),t.child}e=e.sibling}o.tail!==null&&xe()>rr&&(t.flags|=128,s=!0,Nr(o,!1),t.lanes=4194304)}else{if(!s)if(e=ml(i),e!==null){if(t.flags|=128,s=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Nr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!ae)return _e(t),null}else 2*xe()-o.renderingStartTime>rr&&n!==1073741824&&(t.flags|=128,s=!0,Nr(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=xe(),t.sibling=null,n=de.current,se(de,s?n&1|2:n&1),t):(_e(t),null);case 22:case 23:return ca(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&t.mode&1?Qe&1073741824&&(_e(t),t.subtreeFlags&6&&(t.flags|=8192)):_e(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function dh(e,t){switch(qi(t),t.tag){case 1:return qe(t.type)&&ll(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return tr(),oe(Ve),oe(Le),Yi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ki(t),null;case 13:if(oe(de),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Xn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(de),null;case 4:return tr(),null;case 10:return Gi(t.type._context),null;case 22:case 23:return ca(),null;case 24:return null;default:return null}}var Ts=!1,Te=!1,uh=typeof WeakSet=="function"?WeakSet:Set,V=null;function qn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(s){pe(e,t,s)}else n.current=null}function si(e,t,n){try{n()}catch(s){pe(e,t,s)}}var Ic=!1;function mh(e,t){if(Bo=tl,e=Zd(),Bi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var l=s.anchorOffset,o=s.focusNode;s=s.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,c=-1,u=0,g=0,w=e,b=null;t:for(;;){for(var N;w!==n||l!==0&&w.nodeType!==3||(a=i+l),w!==o||s!==0&&w.nodeType!==3||(c=i+s),w.nodeType===3&&(i+=w.nodeValue.length),(N=w.firstChild)!==null;)b=w,w=N;for(;;){if(w===e)break t;if(b===n&&++u===l&&(a=i),b===o&&++g===s&&(c=i),(N=w.nextSibling)!==null)break;w=b,b=w.parentNode}w=N}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Vo={focusedElem:e,selectionRange:n},tl=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var k=j.memoizedProps,S=j.memoizedState,m=t.stateNode,d=m.getSnapshotBeforeUpdate(t.elementType===t.type?k:it(t.type,k),S);m.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(M){pe(t,t.return,M)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return j=Ic,Ic=!1,j}function Rr(e,t,n){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var l=s=s.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&si(t,n,o)}l=l.next}while(l!==s)}}function Il(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var s=n.create;n.destroy=s()}n=n.next}while(n!==t)}}function li(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Qu(e){var t=e.alternate;t!==null&&(e.alternate=null,Qu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[xt],delete t[Kr],delete t[Wo],delete t[Df],delete t[Jf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Gu(e){return e.tag===5||e.tag===3||e.tag===4}function Ac(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Gu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function oi(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=sl));else if(s!==4&&(e=e.child,e!==null))for(oi(e,t,n),e=e.sibling;e!==null;)oi(e,t,n),e=e.sibling}function ii(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(ii(e,t,n),e=e.sibling;e!==null;)ii(e,t,n),e=e.sibling}var Me=null,at=!1;function Ot(e,t,n){for(n=n.child;n!==null;)Du(e,t,n),n=n.sibling}function Du(e,t,n){if(vt&&typeof vt.onCommitFiberUnmount=="function")try{vt.onCommitFiberUnmount(bl,n)}catch{}switch(n.tag){case 5:Te||qn(n,t);case 6:var s=Me,l=at;Me=null,Ot(e,t,n),Me=s,at=l,Me!==null&&(at?(e=Me,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Me.removeChild(n.stateNode));break;case 18:Me!==null&&(at?(e=Me,n=n.stateNode,e.nodeType===8?oo(e.parentNode,n):e.nodeType===1&&oo(e,n),Qr(e)):oo(Me,n.stateNode));break;case 4:s=Me,l=at,Me=n.stateNode.containerInfo,at=!0,Ot(e,t,n),Me=s,at=l;break;case 0:case 11:case 14:case 15:if(!Te&&(s=n.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){l=s=s.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&si(n,t,i),l=l.next}while(l!==s)}Ot(e,t,n);break;case 1:if(!Te&&(qn(n,t),s=n.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=n.memoizedProps,s.state=n.memoizedState,s.componentWillUnmount()}catch(a){pe(n,t,a)}Ot(e,t,n);break;case 21:Ot(e,t,n);break;case 22:n.mode&1?(Te=(s=Te)||n.memoizedState!==null,Ot(e,t,n),Te=s):Ot(e,t,n);break;default:Ot(e,t,n)}}function _c(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new uh),t.forEach(function(s){var l=bh.bind(null,e,s);n.has(s)||(n.add(s),s.then(l,l))})}}function ot(e,t){var n=t.deletions;if(n!==null)for(var s=0;s<n.length;s++){var l=n[s];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Me=a.stateNode,at=!1;break e;case 3:Me=a.stateNode.containerInfo,at=!0;break e;case 4:Me=a.stateNode.containerInfo,at=!0;break e}a=a.return}if(Me===null)throw Error(_(160));Du(o,i,l),Me=null,at=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(u){pe(l,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ju(t,e),t=t.sibling}function Ju(e,t){var n=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ot(t,e),pt(e),s&4){try{Rr(3,e,e.return),Il(3,e)}catch(k){pe(e,e.return,k)}try{Rr(5,e,e.return)}catch(k){pe(e,e.return,k)}}break;case 1:ot(t,e),pt(e),s&512&&n!==null&&qn(n,n.return);break;case 5:if(ot(t,e),pt(e),s&512&&n!==null&&qn(n,n.return),e.flags&32){var l=e.stateNode;try{Vr(l,"")}catch(k){pe(e,e.return,k)}}if(s&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&xd(l,o),_o(a,i);var u=_o(a,o);for(i=0;i<c.length;i+=2){var g=c[i],w=c[i+1];g==="style"?jd(l,w):g==="dangerouslySetInnerHTML"?wd(l,w):g==="children"?Vr(l,w):Ei(l,g,w,u)}switch(a){case"input":Co(l,o);break;case"textarea":yd(l,o);break;case"select":var b=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var N=o.value;N!=null?Wn(l,!!o.multiple,N,!1):b!==!!o.multiple&&(o.defaultValue!=null?Wn(l,!!o.multiple,o.defaultValue,!0):Wn(l,!!o.multiple,o.multiple?[]:"",!1))}l[Kr]=o}catch(k){pe(e,e.return,k)}}break;case 6:if(ot(t,e),pt(e),s&4){if(e.stateNode===null)throw Error(_(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(k){pe(e,e.return,k)}}break;case 3:if(ot(t,e),pt(e),s&4&&n!==null&&n.memoizedState.isDehydrated)try{Qr(t.containerInfo)}catch(k){pe(e,e.return,k)}break;case 4:ot(t,e),pt(e);break;case 13:ot(t,e),pt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(ia=xe())),s&4&&_c(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(Te=(u=Te)||g,ot(t,e),Te=u):ot(t,e),pt(e),s&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!g&&e.mode&1)for(V=e,g=e.child;g!==null;){for(w=V=g;V!==null;){switch(b=V,N=b.child,b.tag){case 0:case 11:case 14:case 15:Rr(4,b,b.return);break;case 1:qn(b,b.return);var j=b.stateNode;if(typeof j.componentWillUnmount=="function"){s=b,n=b.return;try{t=s,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(k){pe(s,n,k)}}break;case 5:qn(b,b.return);break;case 22:if(b.memoizedState!==null){Lc(w);continue}}N!==null?(N.return=b,V=N):Lc(w)}g=g.sibling}e:for(g=null,w=e;;){if(w.tag===5){if(g===null){g=w;try{l=w.stateNode,u?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=w.stateNode,c=w.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=bd("display",i))}catch(k){pe(e,e.return,k)}}}else if(w.tag===6){if(g===null)try{w.stateNode.nodeValue=u?"":w.memoizedProps}catch(k){pe(e,e.return,k)}}else if((w.tag!==22&&w.tag!==23||w.memoizedState===null||w===e)&&w.child!==null){w.child.return=w,w=w.child;continue}if(w===e)break e;for(;w.sibling===null;){if(w.return===null||w.return===e)break e;g===w&&(g=null),w=w.return}g===w&&(g=null),w.sibling.return=w.return,w=w.sibling}}break;case 19:ot(t,e),pt(e),s&4&&_c(e);break;case 21:break;default:ot(t,e),pt(e)}}function pt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Gu(n)){var s=n;break e}n=n.return}throw Error(_(160))}switch(s.tag){case 5:var l=s.stateNode;s.flags&32&&(Vr(l,""),s.flags&=-33);var o=Ac(e);ii(e,o,l);break;case 3:case 4:var i=s.stateNode.containerInfo,a=Ac(e);oi(e,a,i);break;default:throw Error(_(161))}}catch(c){pe(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function fh(e,t,n){V=e,Zu(e)}function Zu(e,t,n){for(var s=(e.mode&1)!==0;V!==null;){var l=V,o=l.child;if(l.tag===22&&s){var i=l.memoizedState!==null||Ts;if(!i){var a=l.alternate,c=a!==null&&a.memoizedState!==null||Te;a=Ts;var u=Te;if(Ts=i,(Te=c)&&!u)for(V=l;V!==null;)i=V,c=i.child,i.tag===22&&i.memoizedState!==null?Pc(l):c!==null?(c.return=i,V=c):Pc(l);for(;o!==null;)V=o,Zu(o),o=o.sibling;V=l,Ts=a,Te=u}Tc(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,V=o):Tc(e)}}function Tc(e){for(;V!==null;){var t=V;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Te||Il(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!Te)if(n===null)s.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:it(t.type,n.memoizedProps);s.componentDidUpdate(l,n.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&gc(t,o,s);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}gc(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var g=u.memoizedState;if(g!==null){var w=g.dehydrated;w!==null&&Qr(w)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Te||t.flags&512&&li(t)}catch(b){pe(t,t.return,b)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function Lc(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function Pc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Il(4,t)}catch(c){pe(t,n,c)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var l=t.return;try{s.componentDidMount()}catch(c){pe(t,l,c)}}var o=t.return;try{li(t)}catch(c){pe(t,o,c)}break;case 5:var i=t.return;try{li(t)}catch(c){pe(t,i,c)}}}catch(c){pe(t,t.return,c)}if(t===e){V=null;break}var a=t.sibling;if(a!==null){a.return=t.return,V=a;break}V=t.return}}var hh=Math.ceil,pl=Lt.ReactCurrentDispatcher,la=Lt.ReactCurrentOwner,tt=Lt.ReactCurrentBatchConfig,ee=0,ke=null,ye=null,Ee=0,Qe=0,Hn=tn(0),we=0,rs=null,kn=0,Al=0,oa=0,zr=null,Fe=null,ia=0,rr=1/0,kt=null,gl=!1,ai=null,Jt=null,Ls=!1,qt=null,xl=0,Or=0,ci=null,Ws=-1,Qs=0;function ze(){return ee&6?xe():Ws!==-1?Ws:Ws=xe()}function Zt(e){return e.mode&1?ee&2&&Ee!==0?Ee&-Ee:Kf.transition!==null?(Qs===0&&(Qs=Pd()),Qs):(e=te,e!==0||(e=window.event,e=e===void 0?16:Bd(e.type)),e):1}function ut(e,t,n,s){if(50<Or)throw Or=0,ci=null,Error(_(185));cs(e,n,s),(!(ee&2)||e!==ke)&&(e===ke&&(!(ee&2)&&(Al|=n),we===4&&Bt(e,Ee)),He(e,s),n===1&&ee===0&&!(t.mode&1)&&(rr=xe()+500,Cl&&nn()))}function He(e,t){var n=e.callbackNode;Zm(e,t);var s=el(e,e===ke?Ee:0);if(s===0)n!==null&&qa(n),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(n!=null&&qa(n),t===1)e.tag===0?Zf(Rc.bind(null,e)):ou(Rc.bind(null,e)),Qf(function(){!(ee&6)&&nn()}),n=null;else{switch(Rd(s)){case 1:n=Li;break;case 4:n=Td;break;case 16:n=Xs;break;case 536870912:n=Ld;break;default:n=Xs}n=s0(n,Ku.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ku(e,t){if(Ws=-1,Qs=0,ee&6)throw Error(_(327));var n=e.callbackNode;if(Zn()&&e.callbackNode!==n)return null;var s=el(e,e===ke?Ee:0);if(s===0)return null;if(s&30||s&e.expiredLanes||t)t=yl(e,s);else{t=s;var l=ee;ee|=2;var o=Xu();(ke!==e||Ee!==t)&&(kt=null,rr=xe()+500,pn(e,t));do try{xh();break}catch(a){Yu(e,a)}while(!0);Qi(),pl.current=o,ee=l,ye!==null?t=0:(ke=null,Ee=0,t=we)}if(t!==0){if(t===2&&(l=zo(e),l!==0&&(s=l,t=di(e,l))),t===1)throw n=rs,pn(e,0),Bt(e,s),He(e,xe()),n;if(t===6)Bt(e,s);else{if(l=e.current.alternate,!(s&30)&&!ph(l)&&(t=yl(e,s),t===2&&(o=zo(e),o!==0&&(s=o,t=di(e,o))),t===1))throw n=rs,pn(e,0),Bt(e,s),He(e,xe()),n;switch(e.finishedWork=l,e.finishedLanes=s,t){case 0:case 1:throw Error(_(345));case 2:dn(e,Fe,kt);break;case 3:if(Bt(e,s),(s&130023424)===s&&(t=ia+500-xe(),10<t)){if(el(e,0)!==0)break;if(l=e.suspendedLanes,(l&s)!==s){ze(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Ho(dn.bind(null,e,Fe,kt),t);break}dn(e,Fe,kt);break;case 4:if(Bt(e,s),(s&4194240)===s)break;for(t=e.eventTimes,l=-1;0<s;){var i=31-dt(s);o=1<<i,i=t[i],i>l&&(l=i),s&=~o}if(s=l,s=xe()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*hh(s/1960))-s,10<s){e.timeoutHandle=Ho(dn.bind(null,e,Fe,kt),s);break}dn(e,Fe,kt);break;case 5:dn(e,Fe,kt);break;default:throw Error(_(329))}}}return He(e,xe()),e.callbackNode===n?Ku.bind(null,e):null}function di(e,t){var n=zr;return e.current.memoizedState.isDehydrated&&(pn(e,t).flags|=256),e=yl(e,t),e!==2&&(t=Fe,Fe=n,t!==null&&ui(t)),e}function ui(e){Fe===null?Fe=e:Fe.push.apply(Fe,e)}function ph(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var s=0;s<n.length;s++){var l=n[s],o=l.getSnapshot;l=l.value;try{if(!mt(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bt(e,t){for(t&=~oa,t&=~Al,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-dt(t),s=1<<n;e[n]=-1,t&=~s}}function Rc(e){if(ee&6)throw Error(_(327));Zn();var t=el(e,0);if(!(t&1))return He(e,xe()),null;var n=yl(e,t);if(e.tag!==0&&n===2){var s=zo(e);s!==0&&(t=s,n=di(e,s))}if(n===1)throw n=rs,pn(e,0),Bt(e,t),He(e,xe()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,dn(e,Fe,kt),He(e,xe()),null}function aa(e,t){var n=ee;ee|=1;try{return e(t)}finally{ee=n,ee===0&&(rr=xe()+500,Cl&&nn())}}function Sn(e){qt!==null&&qt.tag===0&&!(ee&6)&&Zn();var t=ee;ee|=1;var n=tt.transition,s=te;try{if(tt.transition=null,te=1,e)return e()}finally{te=s,tt.transition=n,ee=t,!(ee&6)&&nn()}}function ca(){Qe=Hn.current,oe(Hn)}function pn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Wf(n)),ye!==null)for(n=ye.return;n!==null;){var s=n;switch(qi(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&ll();break;case 3:tr(),oe(Ve),oe(Le),Yi();break;case 5:Ki(s);break;case 4:tr();break;case 13:oe(de);break;case 19:oe(de);break;case 10:Gi(s.type._context);break;case 22:case 23:ca()}n=n.return}if(ke=e,ye=e=Kt(e.current,null),Ee=Qe=t,we=0,rs=null,oa=Al=kn=0,Fe=zr=null,mn!==null){for(t=0;t<mn.length;t++)if(n=mn[t],s=n.interleaved,s!==null){n.interleaved=null;var l=s.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,s.next=i}n.pending=s}mn=null}return e}function Yu(e,t){do{var n=ye;try{if(Qi(),Vs.current=hl,fl){for(var s=ue.memoizedState;s!==null;){var l=s.queue;l!==null&&(l.pending=null),s=s.next}fl=!1}if(Nn=0,Ne=ve=ue=null,Pr=!1,es=0,la.current=null,n===null||n.return===null){we=1,rs=t,ye=null;break}e:{var o=e,i=n.return,a=n,c=t;if(t=Ee,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,g=a,w=g.tag;if(!(g.mode&1)&&(w===0||w===11||w===15)){var b=g.alternate;b?(g.updateQueue=b.updateQueue,g.memoizedState=b.memoizedState,g.lanes=b.lanes):(g.updateQueue=null,g.memoizedState=null)}var N=jc(i);if(N!==null){N.flags&=-257,Nc(N,i,a,o,t),N.mode&1&&bc(o,u,t),t=N,c=u;var j=t.updateQueue;if(j===null){var k=new Set;k.add(c),t.updateQueue=k}else j.add(c);break e}else{if(!(t&1)){bc(o,u,t),da();break e}c=Error(_(426))}}else if(ae&&a.mode&1){var S=jc(i);if(S!==null){!(S.flags&65536)&&(S.flags|=256),Nc(S,i,a,o,t),Hi(nr(c,a));break e}}o=c=nr(c,a),we!==4&&(we=2),zr===null?zr=[o]:zr.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Ru(o,c,t);pc(o,m);break e;case 1:a=c;var d=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof d.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Jt===null||!Jt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var M=zu(o,a,t);pc(o,M);break e}}o=o.return}while(o!==null)}t0(n)}catch(P){t=P,ye===n&&n!==null&&(ye=n=n.return);continue}break}while(!0)}function Xu(){var e=pl.current;return pl.current=hl,e===null?hl:e}function da(){(we===0||we===3||we===2)&&(we=4),ke===null||!(kn&268435455)&&!(Al&268435455)||Bt(ke,Ee)}function yl(e,t){var n=ee;ee|=2;var s=Xu();(ke!==e||Ee!==t)&&(kt=null,pn(e,t));do try{gh();break}catch(l){Yu(e,l)}while(!0);if(Qi(),ee=n,pl.current=s,ye!==null)throw Error(_(261));return ke=null,Ee=0,we}function gh(){for(;ye!==null;)e0(ye)}function xh(){for(;ye!==null&&!Bm();)e0(ye)}function e0(e){var t=r0(e.alternate,e,Qe);e.memoizedProps=e.pendingProps,t===null?t0(e):ye=t,la.current=null}function t0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=dh(n,t),n!==null){n.flags&=32767,ye=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{we=6,ye=null;return}}else if(n=ch(n,t,Qe),n!==null){ye=n;return}if(t=t.sibling,t!==null){ye=t;return}ye=t=e}while(t!==null);we===0&&(we=5)}function dn(e,t,n){var s=te,l=tt.transition;try{tt.transition=null,te=1,yh(e,t,n,s)}finally{tt.transition=l,te=s}return null}function yh(e,t,n,s){do Zn();while(qt!==null);if(ee&6)throw Error(_(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Km(e,o),e===ke&&(ye=ke=null,Ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ls||(Ls=!0,s0(Xs,function(){return Zn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=tt.transition,tt.transition=null;var i=te;te=1;var a=ee;ee|=4,la.current=null,mh(e,n),Ju(n,e),Uf(Vo),tl=!!Bo,Vo=Bo=null,e.current=n,fh(n),Vm(),ee=a,te=i,tt.transition=o}else e.current=n;if(Ls&&(Ls=!1,qt=e,xl=l),o=e.pendingLanes,o===0&&(Jt=null),Wm(n.stateNode),He(e,xe()),t!==null)for(s=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],s(l.value,{componentStack:l.stack,digest:l.digest});if(gl)throw gl=!1,e=ai,ai=null,e;return xl&1&&e.tag!==0&&Zn(),o=e.pendingLanes,o&1?e===ci?Or++:(Or=0,ci=e):Or=0,nn(),null}function Zn(){if(qt!==null){var e=Rd(xl),t=tt.transition,n=te;try{if(tt.transition=null,te=16>e?16:e,qt===null)var s=!1;else{if(e=qt,qt=null,xl=0,ee&6)throw Error(_(331));var l=ee;for(ee|=4,V=e.current;V!==null;){var o=V,i=o.child;if(V.flags&16){var a=o.deletions;if(a!==null){for(var c=0;c<a.length;c++){var u=a[c];for(V=u;V!==null;){var g=V;switch(g.tag){case 0:case 11:case 15:Rr(8,g,o)}var w=g.child;if(w!==null)w.return=g,V=w;else for(;V!==null;){g=V;var b=g.sibling,N=g.return;if(Qu(g),g===u){V=null;break}if(b!==null){b.return=N,V=b;break}V=N}}}var j=o.alternate;if(j!==null){var k=j.child;if(k!==null){j.child=null;do{var S=k.sibling;k.sibling=null,k=S}while(k!==null)}}V=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,V=i;else e:for(;V!==null;){if(o=V,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Rr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,V=m;break e}V=o.return}}var d=e.current;for(V=d;V!==null;){i=V;var p=i.child;if(i.subtreeFlags&2064&&p!==null)p.return=i,V=p;else e:for(i=d;V!==null;){if(a=V,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Il(9,a)}}catch(P){pe(a,a.return,P)}if(a===i){V=null;break e}var M=a.sibling;if(M!==null){M.return=a.return,V=M;break e}V=a.return}}if(ee=l,nn(),vt&&typeof vt.onPostCommitFiberRoot=="function")try{vt.onPostCommitFiberRoot(bl,e)}catch{}s=!0}return s}finally{te=n,tt.transition=t}}return!1}function zc(e,t,n){t=nr(n,t),t=Ru(e,t,1),e=Dt(e,t,1),t=ze(),e!==null&&(cs(e,1,t),He(e,t))}function pe(e,t,n){if(e.tag===3)zc(e,e,n);else for(;t!==null;){if(t.tag===3){zc(t,e,n);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(Jt===null||!Jt.has(s))){e=nr(n,e),e=zu(t,e,1),t=Dt(t,e,1),e=ze(),t!==null&&(cs(t,1,e),He(t,e));break}}t=t.return}}function vh(e,t,n){var s=e.pingCache;s!==null&&s.delete(t),t=ze(),e.pingedLanes|=e.suspendedLanes&n,ke===e&&(Ee&n)===n&&(we===4||we===3&&(Ee&130023424)===Ee&&500>xe()-ia?pn(e,0):oa|=n),He(e,t)}function n0(e,t){t===0&&(e.mode&1?(t=Ns,Ns<<=1,!(Ns&130023424)&&(Ns=4194304)):t=1);var n=ze();e=_t(e,t),e!==null&&(cs(e,t,n),He(e,n))}function wh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),n0(e,n)}function bh(e,t){var n=0;switch(e.tag){case 13:var s=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(_(314))}s!==null&&s.delete(t),n0(e,n)}var r0;r0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ve.current)Be=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Be=!1,ah(e,t,n);Be=!!(e.flags&131072)}else Be=!1,ae&&t.flags&1048576&&iu(t,al,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;Hs(e,t),e=t.pendingProps;var l=Yn(t,Le.current);Jn(t,n),l=ea(null,t,s,e,l,n);var o=ta();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qe(s)?(o=!0,ol(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Ji(t),l.updater=El,t.stateNode=l,l._reactInternals=t,Ko(t,s,e,n),t=ei(null,t,s,!0,o,n)):(t.tag=0,ae&&o&&Vi(t),Re(null,t,l,n),t=t.child),t;case 16:s=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,l=s._init,s=l(s._payload),t.type=s,l=t.tag=Nh(s),e=it(s,e),l){case 0:t=Xo(null,t,s,e,n);break e;case 1:t=Cc(null,t,s,e,n);break e;case 11:t=kc(null,t,s,e,n);break e;case 14:t=Sc(null,t,s,it(s.type,e),n);break e}throw Error(_(306,s,""))}return t;case 0:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),Xo(e,t,s,l,n);case 1:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),Cc(e,t,s,l,n);case 3:e:{if(Fu(t),e===null)throw Error(_(387));s=t.pendingProps,o=t.memoizedState,l=o.element,fu(e,t),ul(t,s,null,n);var i=t.memoizedState;if(s=i.element,o.isDehydrated)if(o={element:s,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=nr(Error(_(423)),t),t=Mc(e,t,s,n,l);break e}else if(s!==l){l=nr(Error(_(424)),t),t=Mc(e,t,s,n,l);break e}else for(Ge=Gt(t.stateNode.containerInfo.firstChild),De=t,ae=!0,ct=null,n=uu(t,null,s,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Xn(),s===l){t=Tt(e,t,n);break e}Re(e,t,s,n)}t=t.child}return t;case 5:return hu(t),e===null&&Do(t),s=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,qo(s,l)?i=null:o!==null&&qo(s,o)&&(t.flags|=32),$u(e,t),Re(e,t,i,n),t.child;case 6:return e===null&&Do(t),null;case 13:return Bu(e,t,n);case 4:return Zi(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=er(t,null,s,n):Re(e,t,s,n),t.child;case 11:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),kc(e,t,s,l,n);case 7:return Re(e,t,t.pendingProps,n),t.child;case 8:return Re(e,t,t.pendingProps.children,n),t.child;case 12:return Re(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(s=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,se(cl,s._currentValue),s._currentValue=i,o!==null)if(mt(o.value,i)){if(o.children===l.children&&!Ve.current){t=Tt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var c=a.firstContext;c!==null;){if(c.context===s){if(o.tag===1){c=Et(-1,n&-n),c.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var g=u.pending;g===null?c.next=c:(c.next=g.next,g.next=c),u.pending=c}}o.lanes|=n,c=o.alternate,c!==null&&(c.lanes|=n),Jo(o.return,n,t),a.lanes|=n;break}c=c.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(_(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Jo(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Re(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,s=t.pendingProps.children,Jn(t,n),l=nt(l),s=s(l),t.flags|=1,Re(e,t,s,n),t.child;case 14:return s=t.type,l=it(s,t.pendingProps),l=it(s.type,l),Sc(e,t,s,l,n);case 15:return Ou(e,t,t.type,t.pendingProps,n);case 17:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),Hs(e,t),t.tag=1,qe(s)?(e=!0,ol(t)):e=!1,Jn(t,n),Pu(t,s,l),Ko(t,s,l,n),ei(null,t,s,!0,e,n);case 19:return Vu(e,t,n);case 22:return Uu(e,t,n)}throw Error(_(156,t.tag))};function s0(e,t){return _d(e,t)}function jh(e,t,n,s){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function et(e,t,n,s){return new jh(e,t,n,s)}function ua(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Nh(e){if(typeof e=="function")return ua(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ai)return 11;if(e===_i)return 14}return 2}function Kt(e,t){var n=e.alternate;return n===null?(n=et(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Gs(e,t,n,s,l,o){var i=2;if(s=e,typeof e=="function")ua(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Pn:return gn(n.children,l,o,t);case Ii:i=8,l|=8;break;case bo:return e=et(12,n,t,l|2),e.elementType=bo,e.lanes=o,e;case jo:return e=et(13,n,t,l),e.elementType=jo,e.lanes=o,e;case No:return e=et(19,n,t,l),e.elementType=No,e.lanes=o,e;case hd:return _l(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case md:i=10;break e;case fd:i=9;break e;case Ai:i=11;break e;case _i:i=14;break e;case Ut:i=16,s=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=et(i,n,t,l),t.elementType=e,t.type=s,t.lanes=o,t}function gn(e,t,n,s){return e=et(7,e,s,t),e.lanes=n,e}function _l(e,t,n,s){return e=et(22,e,s,t),e.elementType=hd,e.lanes=n,e.stateNode={isHidden:!1},e}function po(e,t,n){return e=et(6,e,null,t),e.lanes=n,e}function go(e,t,n){return t=et(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function kh(e,t,n,s,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Jl(0),this.expirationTimes=Jl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Jl(0),this.identifierPrefix=s,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ma(e,t,n,s,l,o,i,a,c){return e=new kh(e,t,n,a,c),t===1?(t=1,o===!0&&(t|=8)):t=0,o=et(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:s,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ji(o),e}function Sh(e,t,n){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ln,key:s==null?null:""+s,children:e,containerInfo:t,implementation:n}}function l0(e){if(!e)return Xt;e=e._reactInternals;e:{if(En(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(qe(n))return lu(e,n,t)}return t}function o0(e,t,n,s,l,o,i,a,c){return e=ma(n,s,!0,e,l,o,i,a,c),e.context=l0(null),n=e.current,s=ze(),l=Zt(n),o=Et(s,l),o.callback=t??null,Dt(n,o,l),e.current.lanes=l,cs(e,l,s),He(e,s),e}function Tl(e,t,n,s){var l=t.current,o=ze(),i=Zt(l);return n=l0(n),t.context===null?t.context=n:t.pendingContext=n,t=Et(o,i),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=Dt(l,t,i),e!==null&&(ut(e,l,i,o),Bs(e,l,i)),i}function vl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Oc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function fa(e,t){Oc(e,t),(e=e.alternate)&&Oc(e,t)}function Ch(){return null}var i0=typeof reportError=="function"?reportError:function(e){console.error(e)};function ha(e){this._internalRoot=e}Ll.prototype.render=ha.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));Tl(e,t,null,null)};Ll.prototype.unmount=ha.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sn(function(){Tl(null,e,null,null)}),t[At]=null}};function Ll(e){this._internalRoot=e}Ll.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ud();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ft.length&&t!==0&&t<Ft[n].priority;n++);Ft.splice(n,0,e),n===0&&Fd(e)}};function pa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Pl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Uc(){}function Mh(e,t,n,s,l){if(l){if(typeof s=="function"){var o=s;s=function(){var u=vl(i);o.call(u)}}var i=o0(t,s,e,0,null,!1,!1,"",Uc);return e._reactRootContainer=i,e[At]=i.current,Jr(e.nodeType===8?e.parentNode:e),Sn(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof s=="function"){var a=s;s=function(){var u=vl(c);a.call(u)}}var c=ma(e,0,!1,null,null,!1,!1,"",Uc);return e._reactRootContainer=c,e[At]=c.current,Jr(e.nodeType===8?e.parentNode:e),Sn(function(){Tl(t,c,n,s)}),c}function Rl(e,t,n,s,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var a=l;l=function(){var c=vl(i);a.call(c)}}Tl(t,i,e,l)}else i=Mh(n,t,e,l,s);return vl(i)}zd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Mr(t.pendingLanes);n!==0&&(Pi(t,n|1),He(t,xe()),!(ee&6)&&(rr=xe()+500,nn()))}break;case 13:Sn(function(){var s=_t(e,1);if(s!==null){var l=ze();ut(s,e,1,l)}}),fa(e,1)}};Ri=function(e){if(e.tag===13){var t=_t(e,134217728);if(t!==null){var n=ze();ut(t,e,134217728,n)}fa(e,134217728)}};Od=function(e){if(e.tag===13){var t=Zt(e),n=_t(e,t);if(n!==null){var s=ze();ut(n,e,t,s)}fa(e,t)}};Ud=function(){return te};$d=function(e,t){var n=te;try{return te=e,t()}finally{te=n}};Lo=function(e,t,n){switch(t){case"input":if(Co(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var s=n[t];if(s!==e&&s.form===e.form){var l=Sl(s);if(!l)throw Error(_(90));gd(s),Co(s,l)}}}break;case"textarea":yd(e,n);break;case"select":t=n.value,t!=null&&Wn(e,!!n.multiple,t,!1)}};Sd=aa;Cd=Sn;var Eh={usingClientEntryPoint:!1,Events:[us,Un,Sl,Nd,kd,aa]},kr={findFiberByHostInstance:un,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ih={bundleType:kr.bundleType,version:kr.version,rendererPackageName:kr.rendererPackageName,rendererConfig:kr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Lt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Id(e),e===null?null:e.stateNode},findFiberByHostInstance:kr.findFiberByHostInstance||Ch,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ps=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ps.isDisabled&&Ps.supportsFiber)try{bl=Ps.inject(Ih),vt=Ps}catch{}}Ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Eh;Ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!pa(t))throw Error(_(200));return Sh(e,t,null,n)};Ze.createRoot=function(e,t){if(!pa(e))throw Error(_(299));var n=!1,s="",l=i0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ma(e,1,!1,null,null,n,!1,s,l),e[At]=t.current,Jr(e.nodeType===8?e.parentNode:e),new ha(t)};Ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Id(t),e=e===null?null:e.stateNode,e};Ze.flushSync=function(e){return Sn(e)};Ze.hydrate=function(e,t,n){if(!Pl(t))throw Error(_(200));return Rl(null,e,t,!0,n)};Ze.hydrateRoot=function(e,t,n){if(!pa(e))throw Error(_(405));var s=n!=null&&n.hydratedSources||null,l=!1,o="",i=i0;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=o0(t,null,e,1,n??null,l,!1,o,i),e[At]=t.current,Jr(e),s)for(e=0;e<s.length;e++)n=s[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Ll(t)};Ze.render=function(e,t,n){if(!Pl(t))throw Error(_(200));return Rl(null,e,t,!1,n)};Ze.unmountComponentAtNode=function(e){if(!Pl(e))throw Error(_(40));return e._reactRootContainer?(Sn(function(){Rl(null,null,e,!1,function(){e._reactRootContainer=null,e[At]=null})}),!0):!1};Ze.unstable_batchedUpdates=aa;Ze.unstable_renderSubtreeIntoContainer=function(e,t,n,s){if(!Pl(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return Rl(e,t,n,!1,s)};Ze.version="18.3.1-next-f1338f8080-20240426";function a0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a0)}catch(e){console.error(e)}}a0(),ad.exports=Ze;var Ah=ad.exports,c0,$c=Ah;c0=$c.createRoot,$c.hydrateRoot;/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _h=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),d0=(...e)=>e.filter((t,n,s)=>!!t&&s.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Th={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lh=x.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:l="",children:o,iconNode:i,...a},c)=>x.createElement("svg",{ref:c,...Th,width:t,height:t,stroke:e,strokeWidth:s?Number(n)*24/Number(t):n,className:d0("lucide",l),...a},[...i.map(([u,g])=>x.createElement(u,g)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=(e,t)=>{const n=x.forwardRef(({className:s,...l},o)=>x.createElement(Lh,{ref:o,iconNode:t,className:d0(`lucide-${_h(e)}`,s),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mi=F("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u0=F("Apple",[["path",{d:"M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z",key:"3s7exb"}],["path",{d:"M10 2c1 .5 2 2 2 5",key:"fcco2y"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph=F("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rh=F("ArrowLeftRight",[["path",{d:"M8 3 4 7l4 4",key:"9rb6wj"}],["path",{d:"M4 7h16",key:"6tx8e3"}],["path",{d:"m16 21 4-4-4-4",key:"siv7j2"}],["path",{d:"M20 17H4",key:"h6l3hr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zh=F("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oh=F("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=F("Box",[["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m0=F("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uh=F("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $h=F("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fh=F("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bh=F("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f0=F("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=F("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=F("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ds=F("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=F("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vh=F("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=F("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qh=F("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ur=F("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=F("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=F("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ss=F("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ga=F("Gamepad2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=F("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hh=F("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wh=F("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g0=F("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $r=F("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=F("Infinity",[["path",{d:"M12 12c-2-2.67-4-4-6-4a4 4 0 1 0 0 8c2 0 4-1.33 6-4Zm0 0c2 2.67 4 4 6 4a4 4 0 0 0 0-8c-2 0-4 1.33-6 4Z",key:"1z0uae"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=F("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gi=F("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=F("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xi=F("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=F("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rs=F("MessageSquareOff",[["path",{d:"M21 15V5a2 2 0 0 0-2-2H9",key:"43el77"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10",key:"pwpm4a"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bc=F("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ls=F("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=F("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=F("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qh=F("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w0=F("Puzzle",[["path",{d:"M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z",key:"i0oyt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vn=F("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=F("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yi=F("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fr=F("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vc=F("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b0=F("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hn=F("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j0=F("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dh=F("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N0=F("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k0=F("Trees",[["path",{d:"M10 10v.2A3 3 0 0 1 8.9 16H5a3 3 0 0 1-1-5.8V10a3 3 0 0 1 6 0Z",key:"1l6gj6"}],["path",{d:"M7 16v6",key:"1a82de"}],["path",{d:"M13 19v3",key:"13sx9i"}],["path",{d:"M12 19h8.3a1 1 0 0 0 .7-1.7L18 14h.3a1 1 0 0 0 .7-1.7L16 9h.2a1 1 0 0 0 .8-1.7L13 3l-1.4 1.5",key:"1sj9kv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vi=F("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const os=F("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jh=F("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zh=F("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=F("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wi=F("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wn=F("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cn=F("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kh=F("Volume1",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=F("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=F("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yh=F("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xh=F("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xa=F("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S0=F("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),yo={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},ep=(yo==null?void 0:yo.VITE_API_URL)||"";class tp{constructor(){this.token=null,this.token=localStorage.getItem("token")}async request(t,n={}){const s=`${ep}${t}`,l={headers:{"Content-Type":"application/json",...this.token&&{Authorization:`Bearer ${this.token}`},...n.headers},...n};try{const o=await fetch(s,l);if(!o.ok){const i=await o.json().catch(()=>({}));throw o.status===401&&i.code==="MULTIPLE_LOGIN"&&(this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),window.location.reload()),new Error(i.message||`HTTP error! status: ${o.status}`)}return await o.json()}catch(o){throw console.error("API request failed:",o),o}}async login(t,n){const s=await this.request("/api/auth/login",{method:"POST",body:JSON.stringify({username:t,password:n})});return this.token=s.token,localStorage.setItem("token",s.token),localStorage.setItem("username",s.user.username),localStorage.setItem("isAdmin",s.user.isAdmin?"true":"false"),{...s,username:s.user.username,isAdmin:s.user.isAdmin}}async register(t,n,s){return this.request("/api/auth/register",{method:"POST",body:JSON.stringify({username:t,email:n,password:s})})}async logout(){try{await this.request("/api/auth/logout",{method:"POST"})}catch{}this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}async getCurrentUser(){return this.request("/api/user")}async updateProfile(t){return this.request("/api/users/profile",{method:"PUT",body:JSON.stringify(t)})}async uploadAvatar(t){const n=new FormData;return n.append("avatar",t),this.request("/api/users/upload-avatar",{method:"POST",headers:{},body:n})}async getAllUsersAdmin(){return this.request("/api/admin/users")}async searchUsersAdmin(t){return this.request(`/api/users/admin/search?q=${encodeURIComponent(t)}`)}async updateUserAdmin(t,n){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(n)})}async updateCurrency(t,n){return this.request("/api/user/currency",{method:"PUT",body:JSON.stringify({goldCoins:t,pearls:n})})}async getNotifications(){return this.request("/api/notifications")}async markNotificationAsRead(t){return this.request(`/api/notifications/${t}/read`,{method:"PUT"})}async markAllNotificationsAsRead(){return this.request("/api/notifications/mark-all-read",{method:"PUT"})}async getMessages(t){return this.request(`/api/messages/${t}`)}async sendMessage(t,n,s="text"){return this.request("/api/messages",{method:"POST",body:JSON.stringify({recipientId:t,content:n,messageType:s})})}async getFriends(){return this.request("/api/profile/friends")}async getFriendRequests(){return this.request("/api/profile/friend-requests")}async sendFriendRequest(t){return this.request("/api/profile/friend-request",{method:"POST",body:JSON.stringify({friendId:t})})}async acceptFriendRequest(t){return this.request("/api/profile/accept-friend",{method:"POST",body:JSON.stringify({friendshipId:t})})}async checkFriendship(t){return this.request(`/api/friends/check/${t}`)}async searchUserById(t){return this.request(`/api/users/search-by-id/${t}`)}async exchangeGoldToPearls(t){return this.request("/api/profile/exchange-gold-to-pearls",{method:"POST",body:JSON.stringify({goldAmount:t})})}async sendItem(t,n,s){return this.request("/api/profile/send-item",{method:"POST",body:JSON.stringify({toUserId:t,itemType:n,message:s})})}async getGifts(){return this.request("/api/profile/gifts")}async sendGift(t,n,s,l){return this.request("/api/profile/send-gift",{method:"POST",body:JSON.stringify({toUserId:t,giftType:n,amount:s,message:l})})}async claimGift(t){return this.request("/api/profile/claim-gift",{method:"POST",body:JSON.stringify({giftId:t})})}async getItems(){return this.request("/api/profile/items")}async getVoiceRoom(){return this.request("/api/voice-room")}async joinVoiceSeat(t){return this.request("/api/voice-room/join-seat",{method:"POST",body:JSON.stringify({seatNumber:t})})}async leaveVoiceSeat(){return this.request("/api/voice-room/leave-seat",{method:"POST"})}async leaveSeat(){return this.leaveVoiceSeat()}async requestMic(){return this.request("/api/voice-room/request-mic",{method:"POST"})}async cancelMicRequest(){return this.request("/api/voice-room/cancel-mic-request",{method:"POST"})}async sendVoiceRoomMessage(t){return this.request("/api/voice-room/send-message",{method:"POST",body:JSON.stringify({content:t})})}async getVoiceRoomMessages(){return this.request("/api/voice-room/messages")}async toggleMute(t){return this.request("/api/voice-room/toggle-mute",{method:"POST",body:JSON.stringify({isMuted:t})})}async kickUserFromVoiceRoom(t,n){return this.request("/api/voice-room/admin/kick",{method:"POST",body:JSON.stringify({userId:t,durationInMinutes:n})})}async muteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/mute",{method:"POST",body:JSON.stringify({userId:t})})}async unmuteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/unmute",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromSeat(t){return this.request("/api/voice-room/admin/remove-seat",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromQueue(t){return this.request("/api/voice-room/admin/remove-queue",{method:"POST",body:JSON.stringify({userId:t})})}async banUserFromChat(t){return this.request("/api/voice-room/admin/ban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async unbanUserFromChat(t){return this.request("/api/voice-room/admin/unban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async getUserItems(t){return this.request(`/api/user-items/${t}`)}async getShield(t){return this.request(`/api/profile/shield/${t}`)}async activateShield(t){return this.request("/api/profile/activate-shield",{method:"POST",body:JSON.stringify({shieldType:t})})}async getTransactions(t=1,n=20){return this.request(`/api/profile/transactions?page=${t}&limit=${n}`)}async chargeBalance(t){return this.request("/api/profile/charge-balance",{method:"POST",body:JSON.stringify({amount:t})})}async activateItem(t){return this.request("/api/profile/activate-item",{method:"POST",body:JSON.stringify({itemId:t})})}async getGameSettings(){return this.request("/api/game/settings")}async updateGameSettings(t){return this.request("/api/game/settings",{method:"POST",body:t})}async getSuspiciousActivities(){return this.request("/api/admin/suspicious-activities")}async getPlayerId(t){return this.request(`/api/admin/users/${t}/player-id`)}async updatePlayerId(t,n){return this.request(`/api/admin/users/${t}/player-id`,{method:"PUT",body:JSON.stringify({playerId:n})})}async getUsersWithIds(t=1,n=12,s=""){return this.request(`/api/users/admin/users-with-ids?page=${t}&limit=${n}&search=${s}`)}async updateUser(t,n){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(n)})}async deleteUser(t){return this.request(`/api/users/admin/delete/${t}`,{method:"DELETE"})}async deleteUserImage(t){return this.request(`/api/users/admin/delete-image/${t}`,{method:"DELETE"})}async manageUserImage(t,n,s,l){return this.request("/api/users/admin/manage-user-image",{method:"PUT",body:JSON.stringify({targetUserId:t,action:n,imageData:s,imageType:l})})}async debugAllUsers(){return this.request("/api/admin/debug/all-users")}async updateBalance(t,n,s,l){return this.request("/api/users/update-balance",{method:"POST",body:JSON.stringify({balanceChange:t,gameType:n,sessionId:s,gameResult:l})})}async getGameProfile(){return this.request("/api/users/profile")}async endGameSession(t){return this.request("/api/games/session-end",{method:"POST",body:t})}async getPlayerStats(){return this.request("/api/games/player-stats")}clearLocalData(){localStorage.removeItem("token"),localStorage.removeItem("userData"),localStorage.removeItem("adminToken"),localStorage.removeItem("selectedUser"),localStorage.removeItem("userCache"),console.log("🧹 Cleared all local storage data")}}const X=new tp,qc={ar:{login:"تسجيل الدخول",username:"اسم المستخدم",password:"كلمة المرور",loginButton:"دخول",switchToRegister:"ليس لديك حساب؟ إنشاء حساب",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",requiredFields:"الرجاء إدخال اسم المستخدم وكلمة المرور",loginFailed:"فشل في تسجيل الدخول"},en:{login:"Login",username:"Username",password:"Password",loginButton:"Login",switchToRegister:"Don't have an account? Register",loginDesc:"Profitable activities that blend with fun and friendships",requiredFields:"Please enter username and password",loginFailed:"Login failed"},ur:{login:"لاگ ان",username:"صارف نام",password:"پاس ورڈ",loginButton:"داخل ہوں",switchToRegister:"اکاؤنٹ نہیں ہے؟ رجسٹر کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",requiredFields:"براہ کرم صارف نام اور پاس ورڈ درج کریں",loginFailed:"لاگ ان ناکام"},es:{login:"Iniciar Sesión",username:"Nombre de Usuario",password:"Contraseña",loginButton:"Entrar",switchToRegister:"¿No tienes cuenta? Regístrate",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",requiredFields:"Por favor ingresa nombre de usuario y contraseña",loginFailed:"Error al iniciar sesión"}},np=({onLoginSuccess:e,onSwitchToRegister:t})=>{const[n,s]=x.useState({username:"",password:""}),[l,o]=x.useState(!1),[i,a]=x.useState(!1),[c,u]=x.useState(""),g=localStorage.getItem("selectedLanguage")||"ar",w=j=>{var k;return((k=qc[g])==null?void 0:k[j])||qc.ar[j]||j},b=async j=>{if(j.preventDefault(),u(""),!n.username.trim()||!n.password.trim()){u(w("requiredFields"));return}a(!0);try{const k=await X.login(n.username,n.password);e(k)}catch(k){u(k.message||w("loginFailed"))}finally{a(!1)}},N=j=>{const{name:k,value:S}=j.target;s(m=>({...m,[k]:S})),c&&u("")};return r.jsx("div",{className:"w-full max-w-sm mx-auto",children:r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-800/20 via-blue-900/25 to-slate-800/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-500 animate-pulse"}),r.jsx("div",{className:"relative bg-gradient-to-br from-blue-900/40 via-blue-800/30 to-slate-800/40 backdrop-blur-2xl rounded-2xl p-5 border border-blue-400/30 shadow-xl hover:shadow-blue-500/30 transition-all duration-500 hover:border-blue-300/50",children:r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsxs("div",{className:"relative mx-auto mb-4",children:[r.jsxs("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full flex items-center justify-center mx-auto relative shadow-xl shadow-blue-500/40 hover:shadow-blue-500/60 transition-all duration-500 hover:scale-105 group",children:[r.jsx(fi,{className:"w-8 h-8 text-white drop-shadow-lg"}),r.jsx(Ol,{className:"w-4 h-4 text-white absolute -top-0.5 -right-0.5 animate-spin",style:{animationDuration:"8s"}})]}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full blur-lg opacity-30 animate-pulse"})]}),r.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-2 drop-shadow-lg",children:w("login")}),r.jsxs("p",{className:"text-purple-200 font-medium text-sm",children:["🌟 ",w("loginDesc")," 🌟"]})]}),c&&r.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:r.jsx("p",{className:"text-red-300 text-sm text-center",children:c})}),r.jsxs("form",{onSubmit:b,className:"space-y-4",children:[r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:r.jsx(xi,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),r.jsx("input",{type:"text",name:"username",value:n.username,onChange:N,placeholder:w("username"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"username",required:!0}),r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]}),r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:r.jsx(gi,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),r.jsx("input",{type:l?"text":"password",name:"password",value:n.password,onChange:N,placeholder:w("password"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 pl-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"current-password",required:!0}),r.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center z-10 group/eye",children:l?r.jsx(pi,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"}):r.jsx(ss,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"})}),r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]})]}),r.jsxs("button",{type:"submit",disabled:i,className:"relative w-full group overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl transition-all duration-500 group-hover:from-blue-600 group-hover:via-blue-700 group-hover:to-blue-800"}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl blur-md opacity-40 group-hover:opacity-60 transition-opacity duration-500"}),r.jsx("div",{className:"relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-500 hover:via-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 active:scale-95 flex items-center justify-center gap-2 text-sm shadow-xl",children:i?r.jsxs(r.Fragment,{children:[r.jsx(x0,{className:"w-4 h-4 animate-spin"}),r.jsx("span",{className:"animate-pulse",children:w("loggingIn")||"جاري تسجيل الدخول..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(fi,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),r.jsxs("span",{className:"group-hover:tracking-wider transition-all duration-300",children:["🚀 ",w("loginButton")]})]})}),r.jsx("div",{className:"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"})})]})]}),r.jsxs("div",{className:"mt-5 space-y-4",children:[r.jsx("div",{className:"text-center",children:r.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10",children:[r.jsx("p",{className:"text-blue-200 mb-2 text-sm",children:w("switchToRegister")}),r.jsxs("button",{onClick:t,className:"group relative inline-flex items-center gap-2 bg-gradient-to-r from-blue-800/20 to-blue-900/20 hover:from-blue-700/30 hover:to-blue-800/30 text-blue-300 hover:text-white font-bold py-2 px-4 rounded-lg border border-blue-400/30 hover:border-blue-300/60 transition-all duration-300 hover:scale-105",children:[r.jsx(wn,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),r.jsx("span",{className:"group-hover:tracking-wider transition-all duration-300 text-sm",children:"✨ إنشاء حساب جديد"})]})]})}),r.jsx("div",{className:"bg-gradient-to-br from-blue-800/20 via-blue-900/15 to-slate-800/20 backdrop-blur-sm rounded-xl p-4 border border-blue-400/20",children:r.jsxs("div",{className:"text-center space-y-3",children:[r.jsx("h3",{className:"text-blue-200 font-bold text-sm mb-3",children:"📞 للاستفسار وشحن العملات في INFINITY BOX"}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[r.jsx(xi,{className:"w-3 h-3"}),r.jsx("span",{className:"font-medium",children:"<EMAIL>"})]}),r.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[r.jsx(Qh,{className:"w-3 h-3"}),r.jsx("span",{className:"font-medium",children:"00966554593007"})]})]}),r.jsx("div",{className:"pt-2 border-t border-blue-400/20",children:r.jsx("p",{className:"text-blue-400/70 text-xs",children:"© 2024 INFINITY BOX - جميع الحقوق محفوظة"})})]})})]})]})})]})})},Hc={ar:{register:"إنشاء حساب",username:"اسم المستخدم",email:"البريد الإلكتروني",password:"كلمة المرور",confirmPassword:"تأكيد كلمة المرور",registerButton:"إنشاء حساب",switchToLogin:"لديك حساب؟ تسجيل الدخول",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",usernameRequired:"اسم المستخدم مطلوب",usernameMinLength:"اسم المستخدم يجب أن يكون 3 أحرف على الأقل",emailRequired:"البريد الإلكتروني مطلوب",emailInvalid:"البريد الإلكتروني غير صالح",passwordRequired:"كلمة المرور مطلوبة",passwordMinLength:"كلمة المرور يجب أن تكون 6 أحرف على الأقل",passwordMismatch:"كلمات المرور غير متطابقة",registering:"جاري إنشاء الحساب...",registerFailed:"فشل في إنشاء الحساب"},en:{register:"Register",username:"Username",email:"Email",password:"Password",confirmPassword:"Confirm Password",registerButton:"Create Account",switchToLogin:"Have an account? Login",registerDesc:"Join us and enjoy a unique experience",usernameRequired:"Username is required",usernameMinLength:"Username must be at least 3 characters",emailRequired:"Email is required",emailInvalid:"Invalid email address",passwordRequired:"Password is required",passwordMinLength:"Password must be at least 6 characters",passwordMismatch:"Passwords do not match",registering:"Creating account...",registerFailed:"Failed to create account"},ur:{register:"رجسٹر",username:"صارف نام",email:"ای میل",password:"پاس ورڈ",confirmPassword:"پاس ورڈ کی تصدیق",registerButton:"اکاؤنٹ بنائیں",switchToLogin:"اکاؤنٹ ہے؟ لاگ ان کریں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",usernameRequired:"صارف نام ضروری ہے",usernameMinLength:"صارف نام کم از کم 3 حروف کا ہونا چاہیے",emailRequired:"ای میل ضروری ہے",emailInvalid:"غلط ای میل ایڈریس",passwordRequired:"پاس ورڈ ضروری ہے",passwordMinLength:"پاس ورڈ کم از کم 6 حروف کا ہونا چاہیے",passwordMismatch:"پاس ورڈ میل نہیں کھاتے",registering:"اکاؤنٹ بنایا جا رہا ہے...",registerFailed:"اکاؤنٹ بنانے میں ناکامی"},es:{register:"Registrarse",username:"Nombre de Usuario",email:"Correo Electrónico",password:"Contraseña",confirmPassword:"Confirmar Contraseña",registerButton:"Crear Cuenta",switchToLogin:"¿Tienes cuenta? Inicia sesión",registerDesc:"Únete a nosotros y disfruta de una experiencia única",usernameRequired:"El nombre de usuario es requerido",usernameMinLength:"El nombre de usuario debe tener al menos 3 caracteres",emailRequired:"El correo electrónico es requerido",emailInvalid:"Dirección de correo electrónico inválida",passwordRequired:"La contraseña es requerida",passwordMinLength:"La contraseña debe tener al menos 6 caracteres",passwordMismatch:"Las contraseñas no coinciden",registering:"Creando cuenta...",registerFailed:"Error al crear la cuenta"}},rp=({onRegisterSuccess:e,onSwitchToLogin:t})=>{const[n,s]=x.useState({username:"",email:"",password:"",confirmPassword:""}),[l,o]=x.useState(!1),[i,a]=x.useState(!1),[c,u]=x.useState(!1),[g,w]=x.useState(""),b=localStorage.getItem("selectedLanguage")||"ar",N=m=>{var d;return((d=Hc[b])==null?void 0:d[m])||Hc.ar[m]||m},j=()=>n.username.trim()?n.username.length<3?N("usernameMinLength"):n.email.trim()?/\S+@\S+\.\S+/.test(n.email)?n.password?n.password.length<6?N("passwordMinLength"):n.password!==n.confirmPassword?N("passwordMismatch"):null:N("passwordRequired"):N("emailInvalid"):N("emailRequired"):N("usernameRequired"),k=async m=>{m.preventDefault(),w("");const d=j();if(d){w(d);return}u(!0);try{const p=await X.register(n.username,n.email,n.password);p.token&&p.user&&(localStorage.setItem("token",p.token),localStorage.setItem("userData",JSON.stringify(p.user))),p.isNewUser&&p.welcomeMessage?alert(p.welcomeMessage):p.rewards&&alert(`🎉 مرحباً بك في المنصة!

هدية الترحيب:
🪙 ${p.rewards.goldCoins.toLocaleString()} عملة ذهبية
🦪 ${p.rewards.pearls} لآلئ

استمتع باللعب واربح المزيد!`),e(p.user)}catch(p){w(p.message||N("registerFailed"))}finally{u(!1)}},S=m=>{const{name:d,value:p}=m.target;s(M=>({...M,[d]:p})),g&&w("")};return r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl",children:[r.jsxs("div",{className:"text-center mb-8",children:[r.jsxs("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 relative",children:[r.jsx(Ul,{className:"w-10 h-10 text-white"}),r.jsx(Ol,{className:"w-5 h-5 text-white absolute -top-1 -right-1"})]}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:N("register")}),r.jsx("p",{className:"text-gray-300",children:N("registerDesc")})]}),g&&r.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:r.jsx("p",{className:"text-red-300 text-sm text-center",children:g})}),r.jsxs("form",{onSubmit:k,className:"space-y-6",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(wn,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"text",name:"username",value:n.username,onChange:S,placeholder:N("username"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"username",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(xi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"email",name:"email",value:n.email,onChange:S,placeholder:N("email"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"email",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(gi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:l?"text":"password",name:"password",value:n.password,onChange:S,placeholder:N("password"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"new-password",required:!0}),r.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:l?r.jsx(pi,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):r.jsx(ss,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(gi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:i?"text":"password",name:"confirmPassword",value:n.confirmPassword,onChange:S,placeholder:N("confirmPassword"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",required:!0}),r.jsx("button",{type:"button",onClick:()=>a(!i),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:i?r.jsx(pi,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):r.jsx(ss,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]})]}),r.jsx("button",{type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx(x0,{className:"w-5 h-5 animate-spin"}),N("registering")]}):r.jsxs(r.Fragment,{children:[r.jsx(fi,{className:"w-5 h-5"}),N("registerButton")]})})]}),r.jsxs("div",{className:"mt-6 text-center",children:[r.jsx("p",{className:"text-gray-300",children:N("switchToLogin")}),r.jsx("button",{onClick:t,className:"text-blue-400 hover:text-blue-300 font-semibold transition-colors mt-2",children:N("login")})]})]})})},Wc={ar:{welcome:"مرحباً بك",login:"تسجيل الدخول",register:"إنشاء حساب",selectLanguage:"اختر اللغة",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",registerSuccess:"تم إنشاء الحساب بنجاح!",welcomeMessage:"مرحباً بك في INFINITY BOX - يمكنك الآن تسجيل الدخول"},en:{welcome:"Welcome",login:"Login",register:"Register",selectLanguage:"Select Language",loginDesc:"Profitable activities that blend with fun and friendships",registerDesc:"Join us and enjoy a unique experience",registerSuccess:"Account created successfully!",welcomeMessage:"Welcome to INFINITY BOX - you can now login"},ur:{welcome:"خوش آمدید",login:"لاگ ان",register:"رجسٹر",selectLanguage:"زبان منتخب کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",registerSuccess:"اکاؤنٹ کامیابی سے بن گیا!",welcomeMessage:"INFINITY BOX میں خوش آمدید - اب آپ لاگ ان کر سکتے ہیں"},es:{welcome:"Bienvenido",login:"Iniciar Sesión",register:"Registrarse",selectLanguage:"Seleccionar Idioma",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",registerDesc:"Únete a nosotros y disfruta de una experiencia única",registerSuccess:"¡Cuenta creada exitosamente!",welcomeMessage:"Bienvenido a INFINITY BOX - ahora puedes iniciar sesión"}},sp=({onAuthSuccess:e})=>{const[t,n]=x.useState("login"),[s,l]=x.useState(!1),[o,i]=x.useState(()=>localStorage.getItem("selectedLanguage")||"ar"),[a,c]=x.useState(!1),u=k=>({ar:"🇸🇦",en:"🇺🇸",ur:"🇵🇰",es:"🇪🇸"})[k]||"🌍",g=k=>({ar:"العربية",en:"English",ur:"اردو",es:"Español"})[k]||k,w=k=>{var S;return((S=Wc[o])==null?void 0:S[k])||Wc.ar[k]||k},b=k=>{i(k),localStorage.setItem("selectedLanguage",k),document.documentElement.dir=["ar","ur"].includes(k)?"rtl":"ltr",document.documentElement.lang=k,c(!1)};x.useEffect(()=>{document.documentElement.dir=["ar","ur"].includes(o)?"rtl":"ltr",document.documentElement.lang=o},[o]);const N=k=>{if(k.isAdmin&&window.confirm("مرحباً أيها المشرف! هل تريد الذهاب إلى لوحة التحكم؟ (إلغاء للذهاب إلى اللعبة)")){window.location.href="/admin.html";return}e(k)},j=k=>{k?N(k):(l(!0),setTimeout(()=>{l(!1),n("login")},3e3))};return r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white relative overflow-hidden",children:[r.jsx("div",{className:"absolute top-4 right-4 z-50",children:r.jsxs("button",{onClick:()=>c(!0),className:"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full hover:bg-white/20 transition-all duration-300",children:[r.jsx(Wh,{className:"w-5 h-5"}),r.jsx("span",{className:"text-2xl",children:u(o)}),r.jsx("span",{className:"hidden sm:inline",children:g(o)})]})}),a&&r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:r.jsxs("div",{className:"bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 max-w-md w-full",children:[r.jsx("h3",{className:"text-xl font-bold text-center mb-6",children:w("selectLanguage")}),r.jsx("div",{className:"grid grid-cols-2 gap-4",children:["ar","en","ur","es"].map(k=>r.jsxs("button",{onClick:()=>b(k),className:`flex items-center gap-3 p-4 rounded-xl transition-all duration-300 ${o===k?"bg-blue-500/30 border-2 border-blue-400":"bg-white/5 border border-white/10 hover:bg-white/10"}`,children:[r.jsx("span",{className:"text-3xl",children:u(k)}),r.jsx("span",{className:"font-medium",children:g(k)})]},k))}),r.jsx("button",{onClick:()=>c(!1),className:"w-full mt-6 py-3 bg-gray-500/20 hover:bg-gray-500/30 rounded-xl transition-all duration-300",children:"إغلاق / Close"})]})}),r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/30 to-indigo-900/50"}),r.jsxs("div",{className:"absolute -inset-10 opacity-30",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),r.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-72 h-72 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"}),r.jsx("div",{className:"absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-3000"})]}),r.jsxs("div",{className:"absolute inset-0 opacity-20",children:[r.jsx("div",{className:"absolute top-10 left-10 w-32 h-32 bg-white rounded-full filter blur-xl animate-bounce delay-500"}),r.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 bg-yellow-300 rounded-full filter blur-lg animate-bounce delay-1500"}),r.jsx("div",{className:"absolute top-1/3 right-10 w-20 h-20 bg-green-400 rounded-full filter blur-lg animate-bounce delay-2500"})]}),r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((k,S)=>r.jsx("div",{className:`${S%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${S*150}ms`,animationDuration:`${2+S%3}s`}},S))})}),r.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"})]}),r.jsxs("div",{className:"relative z-10 min-h-screen flex flex-col",children:[r.jsxs("header",{className:"text-center py-12 relative",children:[r.jsx("div",{className:"relative z-10",children:r.jsx("div",{className:"inline-flex items-center justify-center mb-8",children:r.jsxs("div",{className:"relative group",children:[r.jsxs("div",{className:"w-32 h-32 sphere-3d rounded-full flex items-center justify-center relative group-hover:scale-110 transition-all duration-500 rotate-y",children:[r.jsx("div",{className:"color-layer"}),r.jsxs("div",{className:"text-center relative z-10",children:[r.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight mb-1 drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"INFINITY"}),r.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"BOX"})]}),r.jsx(Ol,{className:"w-6 h-6 text-white absolute -top-2 -right-2 animate-spin bg-gradient-to-r from-yellow-600 to-blue-700 rounded-full p-1 z-20",style:{animationDuration:"12s"}})]}),r.jsx("div",{className:"absolute inset-0 sphere-glow rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 rotate-y-reverse"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-white/10 rotate-y-fast"}),r.jsx("div",{className:"absolute -inset-2 rounded-full border border-white/5 rotate-y-slow"})]})})}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-5",children:r.jsx("div",{className:"text-8xl font-black text-white animate-spin",style:{animationDuration:"25s"},children:"∞"})})]}),r.jsx("div",{className:"flex-1 flex items-start justify-center px-4 pt-8 pb-8",children:r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx("div",{className:"w-full",children:s?r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsxs("div",{className:"bg-green-500/20 border border-green-500/50 rounded-3xl p-8 text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:w("registerSuccess")}),r.jsx("p",{className:"text-green-300",children:w("welcomeMessage")})]})}):t==="login"?r.jsx(np,{onLoginSuccess:N,onSwitchToRegister:()=>n("register")}):r.jsx(rp,{onRegisterSuccess:j,onSwitchToLogin:()=>n("login")})})})})})]})]})};function lp(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,s,l,o,i=[],a="",c=e.split("/");for(c[0]||c.shift();l=c.shift();)n=l[0],n==="*"?(i.push(n),a+=l[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(s=l.indexOf("?",1),o=l.indexOf(".",1),i.push(l.substring(1,~s?s:~o?o:l.length)),a+=~s&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~s?"?":"")+"\\"+l.substring(o))):a+="/"+l;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var C0={exports:{}},M0={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sr=x;function op(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ip=typeof Object.is=="function"?Object.is:op,ap=sr.useState,cp=sr.useEffect,dp=sr.useLayoutEffect,up=sr.useDebugValue;function mp(e,t){var n=t(),s=ap({inst:{value:n,getSnapshot:t}}),l=s[0].inst,o=s[1];return dp(function(){l.value=n,l.getSnapshot=t,vo(l)&&o({inst:l})},[e,n,t]),cp(function(){return vo(l)&&o({inst:l}),e(function(){vo(l)&&o({inst:l})})},[e]),up(n),n}function vo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ip(e,n)}catch{return!0}}function fp(e,t){return t()}var hp=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?fp:mp;M0.useSyncExternalStore=sr.useSyncExternalStore!==void 0?sr.useSyncExternalStore:hp;C0.exports=M0;var pp=C0.exports;const gp=ym.useInsertionEffect,xp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",yp=xp?x.useLayoutEffect:x.useEffect,vp=gp||yp,E0=e=>{const t=x.useRef([e,(...n)=>t[0](...n)]).current;return vp(()=>{t[0]=e}),t[1]},wp="popstate",ya="pushState",va="replaceState",bp="hashchange",Qc=[wp,ya,va,bp],jp=e=>{for(const t of Qc)addEventListener(t,e);return()=>{for(const t of Qc)removeEventListener(t,e)}},I0=(e,t)=>pp.useSyncExternalStore(jp,e,t),Np=()=>location.search,kp=({ssrSearch:e=""}={})=>I0(Np,()=>e),Gc=()=>location.pathname,Sp=({ssrPath:e}={})=>I0(Gc,e?()=>e:Gc),Cp=(e,{replace:t=!1,state:n=null}={})=>history[t?va:ya](n,"",e),Mp=(e={})=>[Sp(e),Cp],Dc=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Dc]>"u"){for(const e of[ya,va]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),s=new Event(e);return s.arguments=arguments,dispatchEvent(s),n}}Object.defineProperty(window,Dc,{value:!0})}const Ep=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",A0=(e="")=>e==="/"?"":e,Ip=(e,t)=>e[0]==="~"?e.slice(1):A0(t)+e,Ap=(e="",t)=>Ep(Jc(A0(e)),Jc(t)),Jc=e=>{try{return decodeURI(e)}catch{return e}},_p={hook:Mp,searchHook:kp,parser:lp,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},Tp=x.createContext(_p),_0=()=>x.useContext(Tp),Lp={};x.createContext(Lp);const T0=e=>{const[t,n]=e.hook(e);return[Ap(e.base,t),E0((s,l)=>n(Ip(s,e.base),l))]},L0=()=>T0(_0());x.forwardRef((e,t)=>{const n=_0(),[s,l]=T0(n),{to:o="",href:i=o,onClick:a,asChild:c,children:u,className:g,replace:w,state:b,...N}=e,j=E0(S=>{S.ctrlKey||S.metaKey||S.altKey||S.shiftKey||S.button!==0||(a==null||a(S),S.defaultPrevented||(S.preventDefault(),l(i,e)))}),k=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return c&&x.isValidElement(u)?x.cloneElement(u,{onClick:j,href:k}):x.createElement("a",{...N,onClick:j,href:k,className:g!=null&&g.call?g(s===i):g,children:u,ref:t})});const Js=({setActiveTab:e})=>{L0();const t=[{icon:r.jsx(S0,{className:"w-8 h-8"}),title:"تحدي السرعة",description:"اختبر سرعة ردود أفعالك في تحدي مثير",color:"from-yellow-500 to-orange-500",onClick:()=>window.open("/speed-challenge.html","_blank")},{icon:r.jsx(j0,{className:"w-8 h-8"}),title:"صناديق الحظ",description:"اكسر الصناديق واجمع الكنوز والجواهر",color:"from-green-500 to-emerald-500",onClick:()=>window.open("/game8.html","_blank")},{icon:r.jsx(w0,{className:"w-8 h-8"}),title:"ألغاز العقل",description:"حل الألغاز المعقدة واختبر ذكاءك",color:"from-blue-500 to-cyan-500",onClick:()=>window.open("/mind-puzzles.html","_blank")},{icon:r.jsx(u0,{className:"w-8 h-8"}),title:"قطف الفواكه",description:"اقطف الفواكه الساقطة واجمع النقاط",color:"from-red-500 to-yellow-500",onClick:()=>window.open("/fruit-catching.html","_blank")},{icon:r.jsx(m0,{className:"w-8 h-8"}),title:"لعبة الذاكرة",description:"اختبر ذاكرتك وطابق البطاقات",color:"from-purple-500 to-pink-500",onClick:()=>window.open("/memory-match.html","_blank")},{icon:r.jsx(k0,{className:"w-8 h-8"}),title:"لعبة الغابة",description:"اكتشف الحيوانات وتعلم أسماءها",color:"from-green-600 to-emerald-600",onClick:()=>window.open("/forest-game.html","_blank")}];return r.jsx("div",{className:"space-y-8",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8",children:r.jsxs("section",{children:[r.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(ga,{className:"w-5 h-5 text-white"})}),r.jsx("h2",{className:"text-xl lg:text-2xl font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:"قاعة الألعاب"})]}),r.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:t.map((n,s)=>r.jsxs("button",{onClick:n.onClick,className:"group p-2 sm:p-3 crystal-game-card rounded-xl text-center",children:[r.jsx("div",{className:`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-r ${n.color} rounded-full flex items-center justify-center mb-2 sm:mb-3 group-hover:scale-110 transition-transform duration-300 mx-auto shadow-lg ring-4 ring-white/20`,children:n.icon}),r.jsx("h3",{className:"text-xs sm:text-sm font-semibold text-white mb-1",children:n.title}),r.jsx("p",{className:"text-gray-300 text-xs leading-tight",children:n.description})]},s))})]})})})},Pp=()=>{const[e,t]=x.useState([]),[n,s]=x.useState(1),[l,o]=x.useState(""),[i,a]=x.useState(null),[c,u]=x.useState(!1),[g,w]=x.useState(null),[b,N]=x.useState(""),[j,k]=x.useState(null),[S,m]=x.useState(null);x.useState(!1),x.useState(null);const[d,p]=x.useState(!1),[M,P]=x.useState(null);x.useEffect(()=>{z()},[]);const R=(h,C)=>{P({type:h,text:C}),setTimeout(()=>P(null),5e3)},z=async(h=1,C="")=>{var T;u(!0);try{const $=await X.getUsersWithImages(h,12,C);console.log("📥 Loaded users data:",$.users),(T=$.users)==null||T.forEach(L=>{L.profileImage&&console.log(`👤 User ${L.username} has profileImage:`,L.profileImage.substring(0,50)+"...")}),t($.users||[]),a($.pagination),s(h),o(C)}catch($){console.error("❌ Error loading users:",$),R("error","خطأ في تحميل المستخدمين: "+$.message)}finally{u(!1)}},U=()=>{const h=document.getElementById("searchInput"),C=(h==null?void 0:h.value.trim())||"";z(1,C)},D=(h,C)=>{console.log("🖼️ Image action for user:",h),console.log("🔍 User ID fields:",{id:h.id,userId:h.userId,_id:h._id});const T=h.id||h.userId||h._id;if(!T){R("error","خطأ: لا يمكن العثور على معرف المستخدم");return}const $={...h,id:T,userId:T};w($),N(C),k(null),m(null),p(!0)},y=async h=>{var T;const C=(T=h.target.files)==null?void 0:T[0];if(C){if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(C.type)){R("error","نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP");return}const L=5*1024*1024;if(C.size>L){R("error","حجم الصورة كبير جداً. الحد الأقصى 5MB");return}k(C);try{const G=await ne(C,400,.9);m(G),R("info","تم تحسين الصورة وضغطها للحصول على أفضل جودة")}catch(G){console.error("Error compressing preview:",G);const Y=new FileReader;Y.onload=fe=>{var ge;m((ge=fe.target)==null?void 0:ge.result)},Y.readAsDataURL(C),R("info","تم تحميل الصورة بنجاح")}}},O=h=>new Promise((C,T)=>{const $=new FileReader;$.readAsDataURL(h),$.onload=()=>C($.result),$.onerror=L=>T(L)}),Z=(h,C=500)=>new Promise((T,$)=>{try{if(console.log("📊 Original file size:",Math.round(h.size/1024),"KB"),h.size<=C*1024){console.log("✅ File size acceptable, using original"),O(h).then(T).catch($);return}const L=new FileReader;L.onload=()=>{try{const G=L.result;console.log("✅ File converted to base64, size:",Math.round(G.length*.75/1024),"KB (estimated)"),T(G)}catch(G){console.error("❌ Error processing file:",G),$(G)}},L.onerror=G=>{console.error("❌ FileReader error:",G),$(G)},L.readAsDataURL(h)}catch(L){console.error("❌ Error in compressImageSafe:",L),$(L)}}),ne=(h,C=800,T=.8)=>new Promise(($,L)=>{try{console.log("🔄 Attempting safe compression..."),Z(h,500).then($).catch(G=>{console.warn("⚠️ Safe compression failed, trying Canvas method:",G);try{const Y=(()=>{try{return document.createElement("canvas")}catch(ie){return console.warn("❌ Canvas creation failed:",ie),null}})();if(!Y){console.warn("❌ Canvas not available, using fallback"),O(h).then($).catch(L);return}const fe=Y.getContext("2d");if(!fe){console.warn("❌ Canvas context not available, using fallback"),O(h).then($).catch(L);return}const ge=new window.Image;ge.onload=()=>{try{let{width:ie,height:ft}=ge;ie>C&&(ft=ft*C/ie,ie=C),Y.width=ie,Y.height=ft,fe.imageSmoothingEnabled=!0,fe.imageSmoothingQuality="high",fe.drawImage(ge,0,0,ie,ft);const Pt=Y.toDataURL("image/jpeg",T);console.log("✅ Canvas compression successful"),$(Pt)}catch(ie){console.error("❌ Canvas processing error:",ie),O(h).then($).catch(L)}},ge.onerror=()=>{console.error("❌ Image load error, using fallback"),O(h).then($).catch(L)},ge.src=URL.createObjectURL(h)}catch(Y){console.error("❌ Canvas method failed:",Y),O(h).then($).catch(L)}})}catch(G){console.error("❌ Error in compressImage:",G),O(h).then($).catch(L)}}),We=async h=>{if(h.preventDefault(),!(!g||!b))try{const C=g.userId||g.id;if(!C){R("error","خطأ: معرف المستخدم غير صحيح");return}console.log("🔄 Managing image for user:",C,"action:",b);let T=null,$=null;if(b.startsWith("change_")&&j)try{R("info","جاري ضغط الصورة..."),T=await ne(j,800,.85),$="image/jpeg",console.log("✅ Image compressed successfully for upload"),R("info","جاري رفع الصورة المحسنة...")}catch(L){console.error("❌ Error compressing for upload:",L),R("info","فشل ضغط الصورة، جاري استخدام الصورة الأصلية...");try{T=await O(j),$=j.type,console.log("✅ Using original file as fallback")}catch(G){throw console.error("❌ Error with fallback method:",G),new Error("فشل في معالجة الصورة: "+G.message)}}console.log("📤 Sending image management request:",{userId:C,action:b,hasImageData:!!T,imageType:$}),await X.manageUserImage(C,b,T||void 0,$||void 0),R("success","تم تحديث الصورة بنجاح"),console.log("🔄 Reloading users data after image update..."),await z(n,l),Pe()}catch(C){console.error("❌ Image management error:",C),R("error","خطأ في إدارة الصورة: "+C.message)}},Pe=()=>{p(!1),w(null),N(""),k(null),m(null)},$e=h=>h.profileImage||h.avatar?"موجودة":"غير موجودة",be=h=>{var T;const C=h.profileImage||h.avatar;return console.log(`🖼️ Getting image for user ${h.username}:`,{hasProfileImage:!!h.profileImage,hasAvatar:!!h.avatar,profileImageLength:((T=h.profileImage)==null?void 0:T.length)||0,finalImageUrl:C?"HAS_IMAGE":"NO_IMAGE"}),C||"/images/default-avatar.png"};return r.jsxs("div",{className:"space-y-8",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center",children:r.jsx($r,{className:"w-5 h-5 text-white"})}),r.jsx("h2",{className:"text-2xl font-bold text-white",children:"إدارة صور المستخدمين"})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Cn,{className:"w-8 h-8 text-blue-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.length}),r.jsx("p",{className:"text-sm text-gray-400",children:"إجمالي المستخدمين"})]})]})}),r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx($r,{className:"w-8 h-8 text-green-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(h=>h.profileImage||h.avatar).length}),r.jsx("p",{className:"text-sm text-gray-400",children:"لديهم صور"})]})]})}),r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(ss,{className:"w-8 h-8 text-purple-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(h=>!h.profileImage&&!h.avatar).length}),r.jsx("p",{className:"text-sm text-gray-400",children:"بدون صور"})]})]})})]}),M&&r.jsx("div",{className:`p-4 rounded-2xl border ${M.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":M.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:r.jsxs("div",{className:"flex items-center gap-2",children:[M.type==="success"&&r.jsx(hi,{className:"w-5 h-5"}),M.type==="error"&&r.jsx(Ds,{className:"w-5 h-5"}),M.type==="info"&&r.jsx(vi,{className:"w-5 h-5"}),r.jsx("span",{children:M.text})]})}),r.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-3xl border border-white/10 shadow-2xl overflow-hidden",children:[r.jsx("div",{className:"p-6 border-b border-white/10",children:r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsxs("div",{className:"relative flex-1",children:[r.jsx(yi,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{id:"searchInput",type:"text",placeholder:"البحث عن مستخدم...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",onKeyPress:h=>h.key==="Enter"&&U()})]}),r.jsxs("button",{onClick:U,className:"bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-6 py-3 text-purple-300 transition-colors flex items-center gap-2",children:[r.jsx(yi,{className:"w-4 h-4"}),"بحث"]}),r.jsxs("button",{onClick:()=>z(),className:"bg-green-500/20 hover:bg-green-500/30 border border-green-500/50 rounded-xl px-6 py-3 text-green-300 transition-colors flex items-center gap-2",children:[r.jsx(vn,{className:"w-4 h-4"}),"تحديث"]})]})}),r.jsxs("div",{className:"p-6",children:[c?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(vn,{className:"w-8 h-8 text-purple-400 animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-gray-300",children:"جاري التحميل..."})]}):e.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(Cn,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد مستخدمين"}),r.jsx("p",{className:"text-gray-300",children:"لم يتم العثور على أي مستخدمين"})]}):r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(h=>r.jsxs("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-200",children:[r.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[r.jsx("img",{src:be(h),alt:"صورة المستخدم",className:"w-16 h-16 rounded-full object-cover border-2 border-purple-500"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-semibold text-white",children:h.displayName||h.username}),r.jsxs("p",{className:"text-sm text-gray-400",children:["المعرف: ",h.playerId||"غير محدد"]})]})]}),r.jsx("div",{className:"space-y-3 mb-4",children:r.jsxs("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("img",{src:be(h),alt:"الصورة الشخصية",className:"w-8 h-8 rounded-lg object-cover"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-sm font-medium text-white",children:"الصورة الشخصية"}),r.jsx("p",{className:"text-xs text-gray-400",children:$e(h)})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>D(h,"remove_avatar"),className:"p-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg text-red-300 transition-colors",title:"حذف",children:r.jsx(N0,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>D(h,"change_avatar"),className:"p-2 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/50 rounded-lg text-yellow-300 transition-colors",title:"تغيير",children:r.jsx(v0,{className:"w-4 h-4"})})]})]})})]},h.userId))}),i&&i.totalPages>1&&r.jsxs("div",{className:"flex justify-center items-center gap-2 mt-8",children:[i.hasPrevPage&&r.jsx("button",{onClick:()=>z(n-1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"السابق"}),Array.from({length:i.totalPages},(h,C)=>C+1).map(h=>r.jsx("button",{onClick:()=>z(h,l),className:`px-4 py-2 rounded-lg transition-colors ${h===n?"bg-purple-500 text-white":"bg-white/10 hover:bg-white/20 border border-white/20 text-white"}`,children:h},h)),i.hasNextPage&&r.jsx("button",{onClick:()=>z(n+1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"التالي"})]})]})]}),d&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h3",{className:"text-xl font-bold text-white",children:["إدارة صور المستخدم ",g==null?void 0:g.userId]}),r.jsx("button",{onClick:Pe,className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:r.jsx(xa,{className:"w-5 h-5 text-gray-400"})})]}),r.jsxs("form",{onSubmit:We,className:"space-y-6",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"الإجراء"}),r.jsxs("select",{value:b,onChange:h=>N(h.target.value),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0,children:[r.jsx("option",{value:"",children:"اختر الإجراء"}),r.jsx("option",{value:"remove_avatar",children:"حذف الصورة الشخصية"}),r.jsx("option",{value:"change_avatar",children:"تغيير الصورة الشخصية"})]})]}),b.startsWith("change_")&&r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اختر الصورة"}),r.jsxs("div",{className:"relative",children:[r.jsx("input",{type:"file",accept:"image/*",onChange:y,className:"hidden",id:"imageFile"}),r.jsxs("label",{htmlFor:"imageFile",className:"w-full bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-4 py-3 text-purple-300 cursor-pointer transition-colors flex items-center justify-center gap-2",children:[r.jsx(Jh,{className:"w-5 h-5"}),"اختيار ملف"]})]}),S&&r.jsx("div",{className:"mt-4",children:r.jsx("img",{src:S,alt:"معاينة الصورة",className:"w-full max-h-40 object-cover rounded-xl"})})]}),r.jsxs("div",{className:"flex gap-4",children:[r.jsx("button",{type:"submit",className:"flex-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),r.jsx("button",{type:"button",onClick:Pe,className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})]})})]})},bi=({userData:e,onLogout:t})=>{const[n,s]=x.useState("users"),[l,o]=x.useState([]),[i,a]=x.useState(null),[c,u]=x.useState(""),[g,w]=x.useState(!1),[b,N]=x.useState(null),[j,k]=x.useState({numBoxes:10,winRatio:.3}),[S,m]=x.useState([]);x.useEffect(()=>{e!=null&&e.isAdmin&&(p(),M(),P())},[e]);const d=(y,O)=>{N({type:y,text:O}),setTimeout(()=>N(null),5e3)},p=async()=>{w(!0);try{const y=await X.getAllUsersAdmin();console.log("📥 Loaded users:",y.users);const O=(y.users||[]).map(Z=>({...Z,id:Z.id||Z.userId||Z._id,userId:Z.userId||Z.id||Z._id}));console.log("✅ Processed users:",O),o(O)}catch(y){console.error("❌ Error loading users:",y),d("error","خطأ في تحميل المستخدمين")}finally{w(!1)}},M=async()=>{try{const y=await X.getGameSettings();k(y)}catch(y){console.error("خطأ في تحميل إعدادات اللعبة:",y)}},P=async()=>{try{const y=await X.getSuspiciousActivities();m(y||[])}catch(y){console.error("خطأ في تحميل النشاطات المشبوهة:",y),m([])}},R=async y=>{if(!y.trim()){p();return}w(!0);try{const O=await X.searchUsersAdmin(y);o(O.users||[])}catch{d("error","خطأ في البحث")}finally{w(!1)}},z=async(y,O)=>{try{if(!y||y==="undefined"||y===""){console.error("❌ Invalid userId:",y),console.error("❌ Updates object:",O),d("error","خطأ: معرف المستخدم غير صحيح. يرجى إعادة تحميل الصفحة.");return}if(console.log("🔄 Updating user:",y,"with updates:",O),O.playerId){if(!/^\d{6}$/.test(O.playerId)){d("error","معرف اللاعب يجب أن يكون 6 أرقام فقط");return}await X.updatePlayerId(y,O.playerId),d("success","تم تحديث معرف اللاعب بنجاح")}const{playerId:Z,...ne}=O;Object.keys(ne).length>0&&(await X.updateUserAdmin(y,ne),d("success","تم تحديث المستخدم بنجاح")),p(),a(null)}catch{d("error","خطأ في تحديث المستخدم")}},U=async()=>{try{await X.updateGameSettings(j),d("success","تم حفظ إعدادات اللعبة بنجاح")}catch{d("error","خطأ في حفظ الإعدادات")}},D=l.filter(y=>y.username.toLowerCase().includes(c.toLowerCase())||y.email.toLowerCase().includes(c.toLowerCase()));return e!=null&&e.isAdmin?r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white",children:[r.jsx("div",{className:"absolute inset-0 overflow-hidden",children:r.jsxs("div",{className:"absolute -inset-10 opacity-20",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-3/4 right-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),r.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"})]})}),r.jsxs("div",{className:"relative z-10 px-4 py-4",children:[r.jsxs("div",{className:"mb-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center relative",children:[r.jsx(Ur,{className:"w-6 h-6 text-white"}),r.jsx(Ol,{className:"w-3 h-3 text-white absolute -top-1 -right-1"})]}),r.jsxs("div",{children:[r.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"لوحة تحكم المشرف"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"INFINITY BOX"})]})]}),r.jsx("button",{onClick:t,className:"bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 hover:text-red-200 transition-all duration-200 text-sm",children:"خروج"})]}),r.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl px-3 py-2 border border-white/20 flex items-center gap-2",children:[r.jsx(Ur,{className:"w-4 h-4 text-yellow-400"}),r.jsxs("span",{className:"text-sm text-gray-300",children:["مرحباً، ",e.username]})]})]}),b&&r.jsx("div",{className:`mb-6 p-4 rounded-2xl border ${b.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":b.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:r.jsxs("div",{className:"flex items-center gap-2",children:[b.type==="success"&&r.jsx(hi,{className:"w-5 h-5"}),b.type==="error"&&r.jsx(Ds,{className:"w-5 h-5"}),b.type==="info"&&r.jsx(vi,{className:"w-5 h-5"}),r.jsx("span",{children:b.text})]})}),r.jsx("div",{className:"grid grid-cols-2 gap-2 mb-6",children:[{id:"users",label:"إدارة المستخدمين",shortLabel:"المستخدمين",icon:Cn,color:"from-blue-500 to-cyan-500"},{id:"game",label:"إعدادات اللعبة",shortLabel:"الألعاب",icon:Vc,color:"from-green-500 to-emerald-500"},{id:"suspicious",label:"النشاطات المشبوهة",shortLabel:"المشبوهة",icon:mi,color:"from-red-500 to-pink-500"},{id:"images",label:"إدارة الصور",shortLabel:"الصور",icon:$r,color:"from-purple-500 to-indigo-500"}].map(y=>r.jsxs("button",{onClick:()=>s(y.id),className:`flex flex-col items-center gap-2 px-3 py-3 rounded-xl transition-all duration-200 ${n===y.id?`bg-gradient-to-r ${y.color} text-white shadow-lg`:"bg-white/10 hover:bg-white/20 text-gray-300"}`,children:[r.jsx(y.icon,{className:"w-6 h-6"}),r.jsx("span",{className:"text-sm font-medium",children:y.shortLabel})]},y.id))}),r.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-2xl border border-white/10 shadow-2xl overflow-hidden",children:[n==="users"&&r.jsxs("div",{className:"p-4",children:[r.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[r.jsx("div",{className:"flex items-center justify-between",children:r.jsxs("h2",{className:"text-lg font-bold text-white flex items-center gap-2",children:[r.jsx(Cn,{className:"w-5 h-5 text-blue-400"}),"المستخدمين (",D.length,")"]})}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("button",{onClick:()=>{console.log("🧹 Clearing local data..."),X.clearLocalData(),d("info","تم تنظيف البيانات المحلية. سيتم إعادة تحميل الصفحة..."),setTimeout(()=>window.location.reload(),1e3)},className:"flex-1 flex items-center justify-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 transition-colors text-sm",children:[r.jsx(Ds,{className:"w-4 h-4"}),"تنظيف البيانات"]}),r.jsxs("button",{onClick:p,className:"flex-1 flex items-center justify-center gap-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-3 py-2 text-blue-300 transition-colors text-sm",children:[r.jsx(vn,{className:"w-4 h-4"}),"تحديث"]})]})]}),r.jsxs("div",{className:"relative mb-4",children:[r.jsx(yi,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),r.jsx("input",{type:"text",value:c,onChange:y=>{u(y.target.value),R(y.target.value)},placeholder:"البحث عن المستخدمين...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",autoComplete:"off"})]}),g?r.jsxs("div",{className:"text-center py-8",children:[r.jsx(vn,{className:"w-6 h-6 text-blue-400 animate-spin mx-auto mb-3"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"جاري التحميل..."})]}):r.jsx("div",{className:"space-y-3",children:D.map(y=>r.jsxs("div",{className:"bg-white/10 rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-200",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-sm font-bold",children:y.username.charAt(0).toUpperCase()}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("h3",{className:"font-semibold text-white flex items-center gap-2 text-sm",children:[r.jsx("span",{className:"truncate",children:y.username}),y.isAdmin&&r.jsx(Ur,{className:"w-3 h-3 text-yellow-400 flex-shrink-0"})]}),r.jsx("p",{className:"text-xs text-gray-400 truncate",children:y.email}),r.jsxs("p",{className:"text-xs text-blue-400",children:["المعرف: ",y.playerId]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[r.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[r.jsx(zl,{className:"w-3 h-3 text-yellow-400"}),r.jsx("span",{className:"text-yellow-400 font-semibold text-sm",children:y.goldCoins||y.coins||0})]}),r.jsx("p",{className:"text-xs text-gray-400",children:"العملات"})]}),r.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[r.jsx(hn,{className:"w-3 h-3 text-purple-400"}),r.jsx("span",{className:"text-purple-400 font-semibold text-sm",children:y.pearls||0})]}),r.jsx("p",{className:"text-xs text-gray-400",children:"اللآلئ"})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("button",{onClick:()=>a(y),className:"flex-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-2 py-2 text-blue-300 text-xs transition-colors flex items-center justify-center gap-1",children:[r.jsx(v0,{className:"w-3 h-3"}),"تعديل"]}),r.jsxs("button",{onClick:()=>z(y.id||y._id,{isAdmin:!y.isAdmin}),className:`flex-1 border rounded-lg px-2 py-2 text-xs transition-colors flex items-center justify-center gap-1 ${y.isAdmin?"bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300":"bg-green-500/20 hover:bg-green-500/30 border-green-500/50 text-green-300"}`,children:[y.isAdmin?r.jsx(wi,{className:"w-3 h-3"}):r.jsx(Zh,{className:"w-3 h-3"}),y.isAdmin?"إلغاء":"ترقية"]})]})]},y.id))})]}),n==="game"&&r.jsxs("div",{className:"p-4",children:[r.jsxs("h2",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(Vc,{className:"w-5 h-5 text-green-400"}),"إعدادات اللعبة"]}),r.jsx("div",{className:"space-y-4",children:r.jsxs("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:[r.jsx("h3",{className:"text-base font-semibold text-white mb-4",children:"إعدادات صناديق الحظ"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"عدد الصناديق في الجولة"}),r.jsx("input",{type:"number",min:"5",max:"100",value:j.numBoxes,onChange:y=>k(O=>({...O,numBoxes:parseInt(y.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"نسبة الفوز (من 0.1 إلى 0.9)"}),r.jsx("input",{type:"number",min:"0.1",max:"0.9",step:"0.1",value:j.winRatio,onChange:y=>k(O=>({...O,winRatio:parseFloat(y.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),r.jsxs("button",{onClick:U,className:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm",children:[r.jsx(Gh,{className:"w-4 h-4"}),"حفظ الإعدادات"]})]})]})})]}),n==="suspicious"&&r.jsxs("div",{className:"p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h2",{className:"text-2xl font-bold text-white flex items-center gap-2",children:[r.jsx(mi,{className:"w-6 h-6 text-red-400"}),"النشاطات المشبوهة"]}),r.jsxs("button",{onClick:P,className:"flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-xl px-4 py-2 text-red-300 transition-colors",children:[r.jsx(vn,{className:"w-4 h-4"}),"تحديث"]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[r.jsx("div",{className:"bg-red-500/20 rounded-2xl p-6 border border-red-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(vi,{className:"w-8 h-8 text-red-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-red-300",children:S.length}),r.jsx("p",{className:"text-sm text-red-400",children:"مستخدمين مشبوهين"})]})]})}),r.jsx("div",{className:"bg-orange-500/20 rounded-2xl p-6 border border-orange-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Fh,{className:"w-8 h-8 text-orange-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-orange-300",children:S.filter(y=>y.riskLevel==="high").length}),r.jsx("p",{className:"text-sm text-orange-400",children:"مخاطر عالية"})]})]})}),r.jsx("div",{className:"bg-yellow-500/20 rounded-2xl p-6 border border-yellow-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(ss,{className:"w-8 h-8 text-yellow-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-yellow-300",children:S.filter(y=>y.riskLevel==="medium").length}),r.jsx("p",{className:"text-sm text-yellow-400",children:"تحت المراقبة"})]})]})})]}),S.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(hi,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد نشاطات مشبوهة"}),r.jsx("p",{className:"text-gray-300",children:"جميع المستخدمين يتصرفون بشكل طبيعي"})]}):r.jsx("div",{className:"space-y-4",children:S.map((y,O)=>{var Z,ne;return r.jsx("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-lg font-bold",children:((ne=(Z=y.username)==null?void 0:Z.charAt(0))==null?void 0:ne.toUpperCase())||"?"}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-semibold text-white",children:y.username}),r.jsxs("p",{className:"text-sm text-gray-400",children:["آخر نشاط: ",y.lastActivity]})]})]}),r.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${y.riskLevel==="high"?"bg-red-500/20 text-red-300":y.riskLevel==="medium"?"bg-yellow-500/20 text-yellow-300":"bg-green-500/20 text-green-300"}`,children:y.riskLevel==="high"?"خطر عالي":y.riskLevel==="medium"?"خطر متوسط":"خطر منخفض"})]})},O)})})]}),n==="images"&&r.jsxs("div",{className:"p-8",children:[r.jsxs("div",{className:"mb-6",children:[r.jsxs("h2",{className:"text-2xl font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx($r,{className:"w-6 h-6 text-purple-400"}),"إدارة الصور"]}),r.jsxs("div",{className:"bg-purple-500/10 border border-purple-500/30 rounded-xl p-4 mb-6",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[r.jsx($r,{className:"w-5 h-5 text-purple-400"}),r.jsx("h3",{className:"font-semibold text-purple-300",children:"إدارة صور المستخدمين"})]}),r.jsx("p",{className:"text-gray-300 text-sm",children:"عرض وإدارة جميع صور المستخدمين، حذف الصور غير المناسبة، والبحث في المستخدمين."})]})]}),r.jsx(Pp,{})]})]})]}),i&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[r.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"تعديل المستخدم"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"معرف اللاعب"}),r.jsx("input",{type:"text",value:i.playerId,onChange:y=>a(O=>O?{...O,playerId:y.target.value}:null),maxLength:6,pattern:"\\d{6}",className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"6 أرقام فقط"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اسم المستخدم"}),r.jsx("input",{type:"text",value:i.username,onChange:y=>a(O=>O?{...O,username:y.target.value}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"username"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"العملات الذهبية"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"number",value:i.goldCoins||i.coins||0,onChange:y=>a(O=>O?{...O,goldCoins:parseInt(y.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),r.jsxs("div",{className:"flex gap-2 flex-wrap",children:[r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,goldCoins:(y.goldCoins||y.coins||0)+1e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+1,000"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,goldCoins:(y.goldCoins||y.coins||0)+5e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+5,000"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,goldCoins:(y.goldCoins||y.coins||0)+1e4}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+10,000"})]})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اللآلئ"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"number",value:i.pearls||0,onChange:y=>a(O=>O?{...O,pearls:parseInt(y.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),r.jsxs("div",{className:"flex gap-2 flex-wrap",children:[r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,pearls:(y.pearls||0)+1}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+1"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,pearls:(y.pearls||0)+5}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+5"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,pearls:(y.pearls||0)+10}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+10"})]})]})]}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("input",{type:"checkbox",id:"isAdmin",checked:i.isAdmin,onChange:y=>a(O=>O?{...O,isAdmin:y.target.checked}:null),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),r.jsx("label",{htmlFor:"isAdmin",className:"text-sm font-medium text-gray-300",children:"صلاحيات المشرف"})]})]}),r.jsxs("div",{className:"flex gap-4 mt-8",children:[r.jsx("button",{onClick:()=>{const y=i.id||i.userId;if(console.log("🔍 Selected user data:",i),console.log("🔍 Extracted userId:",y),!y){d("error","خطأ: لا يمكن العثور على معرف المستخدم. يرجى إعادة تحميل الصفحة.");return}z(y,i)},className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),r.jsx("button",{onClick:()=>a(null),className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})})]}):r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:r.jsxs("div",{className:"bg-red-500/20 border border-red-500/50 rounded-3xl p-8 text-center max-w-md",children:[r.jsx(Ds,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"غير مصرح"}),r.jsx("p",{className:"text-red-300 mb-6",children:"ليس لديك صلاحيات للوصول إلى لوحة تحكم المشرف"}),r.jsx("button",{onClick:t,className:"bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-xl transition-colors",children:"العودة لتسجيل الدخول"})]})})};class P0{constructor(t){this.url=t,this.ws=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.messageHandlers=new Map}connect(t){return new Promise((n,s)=>{try{this.ws=new WebSocket(`${this.url}?token=${t}`),this.ws.onopen=()=>{console.log("🔌 WebSocket connected"),this.reconnectAttempts=0,n()},this.ws.onmessage=l=>{try{const o=JSON.parse(l.data);this.handleMessage(o)}catch(o){console.error("Error parsing WebSocket message:",o)}},this.ws.onclose=()=>{console.log("🛑 WebSocket disconnected"),this.handleReconnect()},this.ws.onerror=l=>{console.error("WebSocket error:",l),s(l)}}catch(l){s(l)}})}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.connect(this.getTokenFromUrl()).catch(t=>{console.error("Reconnection failed:",t)})},this.reconnectDelay*this.reconnectAttempts)):console.error("❌ Max reconnection attempts reached")}getTokenFromUrl(){return new URLSearchParams(window.location.search).get("token")||""}handleMessage(t){const n=this.messageHandlers.get(t.type);n&&n.forEach(s=>s(t.data))}sendMessage(t,n){if(this.ws&&this.ws.readyState===WebSocket.OPEN){const s={type:t,data:n,timestamp:Date.now()};this.ws.send(JSON.stringify(s))}else console.warn("WebSocket is not connected")}sendPrivateMessage(t,n){this.sendMessage("private_message",{messageData:t,recipientId:n})}onMessage(t,n){this.messageHandlers.has(t)||this.messageHandlers.set(t,[]),this.messageHandlers.get(t).push(n)}offMessage(t,n){const s=this.messageHandlers.get(t);if(s){const l=s.indexOf(n);l>-1&&s.splice(l,1)}}addMessageListener(t){["voice_room_message","voice_room_update","admin_action_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.onMessage(s,t)})}removeMessageListener(t){["voice_room_message","voice_room_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.offMessage(s,t)})}send(t){this.sendMessage(t.type,t.data)}disconnect(){this.ws&&(this.ws.close(),this.ws=null)}}const Rp=`
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
`;if(typeof document<"u"){const e=document.createElement("style");e.textContent=Rp,document.head.appendChild(e)}const zp=({userData:e,isOwner:t,onUpdateProfile:n,onLogout:s})=>{var Ca,Ma,Ea,Ia,Aa;const[l,o]=x.useState("overview"),[i,a]=x.useState(!1),[c,u]=x.useState(""),[g,w]=x.useState((e==null?void 0:e.gender)||"male"),[b,N]=x.useState(!1),[j,k]=x.useState({gems:0,stars:0,coins:0,bombs:0,bats:0,snakes:0}),[S,m]=x.useState(!1),[d,p]=x.useState(!1),[M,P]=x.useState(""),[R,z]=x.useState(!1),[U,D]=x.useState(null),[y,O]=x.useState(""),[Z,ne]=x.useState(null),[We,Pe]=x.useState(!1),[$e,be]=x.useState(""),[h,C]=x.useState(null),[T,$]=x.useState(100),[L,G]=x.useState("gold"),[Y,fe]=x.useState(""),[ge,ie]=x.useState(!1),[ft,Pt]=x.useState([]),[In,ar]=x.useState([]),[$l,cr]=x.useState([]),[rn,Rt]=x.useState(!1),[st,Se]=x.useState(1e4),[sn,An]=x.useState(!1),[je,dr]=x.useState(250),[_n,ur]=x.useState({"1_dollar":!0,"5_dollar":!0}),[he,ht]=x.useState(""),[ln,fs]=x.useState(""),[hs,ps]=x.useState(!1),[bt,mr]=x.useState([]),[lt,fr]=x.useState(!1),[hr,jt]=x.useState(!1),[ce,Fl]=x.useState(null),[on,v]=x.useState([]),[I,W]=x.useState("");x.useState(!1);const[H,B]=x.useState(!1),[Ce,re]=x.useState(!1);(f=>f==="female"?{primary:"from-pink-500 to-red-400",secondary:"bg-pink-50",accent:"text-pink-600",button:"bg-pink-500 hover:bg-pink-600",border:"border-pink-200"}:{primary:"from-blue-500 to-yellow-400",secondary:"bg-blue-50",accent:"text-blue-600",button:"bg-blue-500 hover:bg-blue-600",border:"border-blue-200"})((e==null?void 0:e.gender)||"male"),x.useEffect(()=>{let f=!0;const A=setTimeout(async()=>{if(t&&(e!=null&&e.id)&&f)try{const q=localStorage.getItem("token");if(!q)return;const J=await fetch("/api/profile/me",{method:"GET",headers:{Authorization:`Bearer ${q}`,"Content-Type":"application/json"}});if(J.ok&&f){const Q=J.headers.get("content-type");if(Q&&Q.includes("application/json")){const an=await J.json();n&&f&&n(an)}}}catch(q){console.error("❌ Error fetching complete user data:",q)}},100);return()=>{f=!1,clearTimeout(A)}},[t,e==null?void 0:e.id]),x.useEffect(()=>{e!=null&&e.id&&(wa(),D0(),t&&pr())},[e==null?void 0:e.id,t]),x.useEffect(()=>{if(t&&(e!=null&&e.id))return Vl()},[t,e==null?void 0:e.id]);const Nt=new P0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`);x.useEffect(()=>{let f=!0;if(t&&(e!=null&&e.id)&&f){gr(),gs(),z0(),xs(),F0();const E=setInterval(()=>{f&&(gs(),xs())},3e5);return()=>{f=!1,clearInterval(E)}}},[t,e==null?void 0:e.id]);const Vl=()=>{console.log("🔧 Setting up message listener...");const f=E=>{if(console.log("📨 WebSocket message received:",E),E.messageData){const A=E.messageData.sender._id,q=E.messageData.recipient._id,J=e==null?void 0:e.id;if(console.log("📋 Message details:",{senderId:A,recipientId:q,currentUserId:J,showChat:hr,chatUserId:(ce==null?void 0:ce.id)||(ce==null?void 0:ce._id)}),q===J)if(console.log("✅ Message is for me, processing..."),hr&&ce&&(A===ce.id||A===ce._id)){console.log("💬 Adding message to open chat"),v(Q=>Q.some(X0=>X0._id===E.messageData._id)?(console.log("⚠️ Message already exists, skipping"),Q):(console.log("✅ Adding new message to chat"),[...Q,E.messageData])),setTimeout(ql,100);try{const Q=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");Q.volume=.5,Q.play().catch(()=>{})}catch{}}else{console.log("🔔 Chat not open, refreshing notifications"),xs();try{const Q=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");Q.volume=.3,Q.play().catch(()=>{})}catch{}}else console.log("ℹ️ Message not for me, ignoring")}else console.log("⚠️ No messageData in WebSocket message")};return Nt.offMessage("new_message",f),Nt.onMessage("new_message",f),console.log("✅ Message listener added"),()=>{console.log("🧹 Cleaning up message listener"),Nt.offMessage("new_message",f)}},wa=async()=>{try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for fetching user items");return}const E=await fetch(`/api/user-items/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const A=await E.json();k(A.items),console.log("✅ User items fetched successfully:",A.items)}else console.error("❌ Failed to fetch user items:",E.status,E.statusText)}catch(f){console.error("Error fetching user items:",f)}},pr=async()=>{try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for fetching user shield");return}const E=await fetch(`/api/profile/shield/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const A=await E.json();D(A.shield),console.log("✅ User shield fetched successfully:",A.shield)}else console.error("❌ Failed to fetch user shield:",E.status,E.statusText)}catch(f){console.error("Error fetching user shield:",f)}},zt=async(f,E)=>{if(!R){z(!0);try{const A=localStorage.getItem("token"),q=await fetch("/api/profile/activate-shield",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A}`},body:JSON.stringify({shieldType:f})}),J=await q.json();q.ok?(D(J.shield),n&&J.newBalance!==void 0&&n({goldCoins:J.newBalance}),alert(J.message),await pr()):alert(J.message||"فشل في تفعيل الدرع الواقي")}catch(A){console.error("Error activating shield:",A),alert("حدث خطأ في تفعيل الدرع الواقي")}finally{z(!1)}}},gr=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/friends",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();Pt(A),console.log("✅ Friends fetched:",A.length)}}catch(f){console.error("Error fetching friends:",f)}},gs=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/friend-requests",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();ar(A),console.log("✅ Friend requests fetched:",A.length)}}catch(f){console.error("Error fetching friend requests:",f)}},z0=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/gifts",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();cr(A),console.log("✅ Gifts fetched:",A.length)}}catch(f){console.error("Error fetching gifts:",f)}},ba=async()=>{if(!y.trim()){be("يرجى إدخال رقم اللاعب");return}if(y.length!==6){be("رقم اللاعب يجب أن يكون 6 أرقام");return}Pe(!0),be(""),ne(null);try{const f=localStorage.getItem("token"),E=await fetch(`/api/users/search-by-id/${y}`,{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();ne(A),console.log("✅ User found:",A.username)}else be("لم يتم العثور على لاعب بهذا الرقم")}catch(f){console.error("Error searching for friend:",f),be("حدث خطأ أثناء البحث")}finally{Pe(!1)}},O0=async f=>{try{const E=localStorage.getItem("token"),A=await fetch("/api/profile/friend-request",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({friendId:f})});if(A.ok){const q=await A.json();alert(q.message),ne(null),O(""),await xs()}else{const q=await A.json();alert(q.message||"فشل في إرسال طلب الصداقة")}}catch(E){console.error("Error sending friend request:",E),alert("حدث خطأ في إرسال طلب الصداقة")}},U0=async()=>{if(h){ie(!0);try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/send-gift",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({toUserId:h.id,giftType:L,amount:T,message:Y})});if(E.ok){const A=await E.json();alert(A.message),C(null),fe(""),$(100),n&&A.fromUserBalance&&n(A.fromUserBalance)}else{const A=await E.json();alert(A.message||"فشل في إرسال الهدية")}}catch(f){console.error("Error sending gift:",f),alert("حدث خطأ في إرسال الهدية")}finally{ie(!1)}}},$0=async f=>{try{const E=localStorage.getItem("token"),A=await fetch("/api/profile/accept-friend",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({friendshipId:f})});if(A.ok){const q=await A.json();alert(q.message),await gs(),await gr()}else{const q=await A.json();alert(q.message||"فشل في قبول طلب الصداقة")}}catch(E){console.error("Error accepting friend request:",E),alert("حدث خطأ في قبول طلب الصداقة")}},F0=async()=>{try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/free-charges",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();ur(A.availableCharges)}}catch(f){console.error("Error fetching free charges:",f)}},ja=async(f,E=!1,A)=>{Rt(!0);try{const q=localStorage.getItem("token"),J=await fetch("/api/profile/charge-balance",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${q}`},body:JSON.stringify({amount:f,isFree:E,chargeType:A})});if(J.ok){const Q=await J.json();alert(Q.message),n&&Q.newBalance!==void 0&&n({goldCoins:Q.newBalance}),E&&A&&ur(an=>({...an,[A]:!1}))}else{const Q=await J.json();alert(Q.message||"فشل في شحن الرصيد")}}catch(q){console.error("Error charging balance:",q),alert("حدث خطأ في شحن الرصيد")}finally{Rt(!1)}},B0=async()=>{if(st<1e4){alert("الحد الأدنى للتحويل هو 10,000 عملة ذهبية");return}if(st%1e4!==0){alert("يجب أن تكون الكمية مضاعفات 10,000");return}if(((e==null?void 0:e.goldCoins)||0)<st){alert("رصيد العملات الذهبية غير كافي");return}An(!0);try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/exchange-gold-to-pearls",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({goldAmount:st})});if(E.ok){const A=await E.json();alert(A.message),n&&A.newBalance&&n(A.newBalance),Se(1e4)}else{const A=await E.json();alert(A.message||"فشل في تحويل العملات")}}catch(f){console.error("Error exchanging gold to pearls:",f),alert("حدث خطأ في تحويل العملات")}finally{An(!1)}},V0=async()=>{if(je<250){alert("الحد الأدنى للسحب هو 250 لؤلؤة ($25)");return}if(((e==null?void 0:e.pearls)||0)<je){alert("رصيد اللآلئ غير كافي");return}const A=`https://wa.me/1234567890?text=${`طلب سحب دولارات%0Aالمبلغ: $${je/10}%0Aاللآلئ المطلوبة: ${je}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}`}`;window.open(A,"_blank")},q0=async()=>{if(!he){alert("يرجى اختيار عنصر للإرسال");return}if(!ln||ln.length!==6){alert("يرجى إدخال رقم لاعب صحيح (6 أرقام)");return}ps(!0);try{const f=localStorage.getItem("token"),E=await fetch(`/api/users/search-by-id/${ln}`,{headers:{Authorization:`Bearer ${f}`}});if(!E.ok){alert("لم يتم العثور على لاعب بهذا الرقم");return}const A=await E.json(),q=await fetch("/api/profile/send-item",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({toUserId:A.id,itemType:he,message:`عنصر ${Na(he)} من ${e==null?void 0:e.username}`})});if(q.ok){const J=await q.json();alert(J.message),ht(""),fs("")}else{const J=await q.json();alert(J.message||"فشل في إرسال العنصر")}}catch(f){console.error("Error sending item:",f),alert("حدث خطأ في إرسال العنصر")}finally{ps(!1)}},Na=f=>({bomb:"قنبلة مدمرة",bat:"خفاش مؤذي",snake:"ثعبان سام",gem:"جوهرة نادرة",star:"نجمة ذهبية",coin:"عملة خاصة",gold:"عملات ذهبية"})[f]||f,ka=(f,E)=>{const q=`https://wa.me/1234567890?text=${`طلب شحن رصيد%0Aالمبلغ المطلوب: ${f} عملة ذهبية%0Aالسعر: ${E}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}%0Aالرصيد الحالي: ${(e==null?void 0:e.goldCoins)||0} عملة`}`;window.open(q,"_blank")},xs=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/notifications",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();mr(A),console.log("✅ Notifications fetched:",A.length);const q=A.filter(Q=>{var an;return!Q.isRead&&Q.type==="item_received"&&((an=Q.data)==null?void 0:an.newBalance)});if(q.length>0&&n){const Q=q[0];n(Q.data.newBalance),console.log("💰 Balance updated from item notification:",Q.data.newBalance)}A.filter(Q=>Q.type==="friend_request"&&!Q.isRead).length>0&&(console.log("🤝 New friend request notifications found, refreshing friend requests"),await gs())}}catch(f){console.error("Error fetching notifications:",f)}},H0=async f=>{try{const E=localStorage.getItem("token");(await fetch(`/api/notifications/${f}/read`,{method:"PUT",headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}})).ok&&(mr(q=>q.map(J=>J._id===f?{...J,isRead:!0}:J)),console.log("✅ Notification marked as read:",f))}catch(E){console.error("Error marking notification as read:",E)}},W0=async()=>{try{const f=localStorage.getItem("token");(await fetch("/api/notifications/mark-all-read",{method:"PUT",headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}})).ok&&(mr(A=>A.map(q=>({...q,isRead:!0}))),console.log("✅ All notifications marked as read"))}catch(f){console.error("Error marking all notifications as read:",f)}},Q0=async f=>{try{const E=localStorage.getItem("token");if(!E){console.log("❌ No token found");return}console.log("📥 Fetching messages for user:",f);const A=await fetch(`/api/messages/${f}`,{headers:{Authorization:`Bearer ${E}`}});if(A.ok){const q=await A.json();console.log("✅ Messages fetched:",q.length),v(q)}else{const q=await A.json();console.error("❌ Failed to fetch messages:",q)}}catch(E){console.error("Error fetching messages:",E)}},Sa=async()=>{if(!I.trim()||!ce){console.log("❌ Missing message or chat user:",{newMessage:I,chatUser:ce});return}const f=ce.id||ce._id;if(!f){console.error("❌ No recipient ID found:",ce),alert("خطأ: لا يمكن تحديد المستقبل");return}console.log("📤 Sending message:",{recipientId:f,content:I});try{const E=localStorage.getItem("token"),A=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({recipientId:f,content:I})});if(A.ok){const q=await A.json();console.log("✅ Message sent successfully:",q),v([...on,q.messageData]),W(""),ql(),Nt&&Nt.sendPrivateMessage(q.messageData,f);try{const J=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");J.volume=.3,J.play().catch(()=>{})}catch{}}else{const q=await A.json();console.error("❌ Message send failed:",q),alert(q.message||"فشل في إرسال الرسالة")}}catch(E){console.error("Error sending message:",E),alert("حدث خطأ في إرسال الرسالة")}},ql=()=>{setTimeout(()=>{const f=document.getElementById("messages-container");f&&f.scrollTo({top:f.scrollHeight,behavior:"smooth"})},100)},G0=f=>{console.log("💬 Opening chat with user:",f);const E=f.id||f._id;console.log("📋 User ID for messages:",E),Fl(f),jt(!0),v([]),E?Q0(E).then(()=>{ql()}):console.error("❌ No user ID found for chat")},D0=async()=>{if(!t&&(e!=null&&e.id))try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for checking friendship");return}const E=await fetch(`/api/friends/check/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const A=await E.json();m(A.isFriend),console.log("✅ Friendship status checked:",A.isFriend)}else console.error("❌ Failed to check friendship:",E.status),m(!1)}catch(f){console.error("Error checking friendship:",f),m(!1)}},J0=async()=>{if(M.trim())try{const f=localStorage.getItem("token"),E=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({recipientId:e.id,content:M})});if(E.ok)P(""),p(!1),alert("تم إرسال الرسالة بنجاح!");else{const A=await E.json();alert(A.message||"فشل في إرسال الرسالة")}}catch(f){console.error("Error sending message:",f),alert("خطأ في إرسال الرسالة")}},Z0=f=>{var A;const E=(A=f.target.files)==null?void 0:A[0];if(E){if(E.size>5*1024*1024){alert("حجم الصورة كبير جداً. يجب أن يكون أقل من 5 ميجابايت");return}const q=new FileReader;q.onload=J=>{var Q;u((Q=J.target)==null?void 0:Q.result)},q.readAsDataURL(E)}},K0=async()=>{var f,E;N(!0);try{const A=localStorage.getItem("token"),q={gender:g};c&&c!==(e==null?void 0:e.profileImage)&&(q.profileImage=c),console.log("🔄 Updating profile with data:",{hasProfileImage:!!q.profileImage,gender:q.gender,selectedImageLength:(c==null?void 0:c.length)||0,currentImageLength:((f=e==null?void 0:e.profileImage)==null?void 0:f.length)||0});const J=await fetch("/api/profile/update",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A}`},body:JSON.stringify(q)});if(J.ok){const Q=await J.json();console.log("✅ Profile updated successfully:",{hasProfileImage:!!Q.profileImage,profileImageLength:((E=Q.profileImage)==null?void 0:E.length)||0}),n==null||n(Q),u(""),a(!1),alert("تم تحديث الملف الشخصي بنجاح!")}else{const Q=await J.json();console.error("❌ Profile update failed:",Q),alert(Q.message||"فشل في تحديث الملف الشخصي")}}catch(A){console.error("Error updating profile:",A),alert("حدث خطأ في تحديث الملف الشخصي")}finally{N(!1)}},Y0=()=>c||(e!=null&&e.profileImage?e.profileImage:(e==null?void 0:e.gender)==="female"?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNGRjY5QjQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNFMxMy4xIDYgMTIgNiAxMCA1LjEgMTAgNFMxMC45IDIgMTIgMlpNMjEgOVYxMUgyMFYxMkMxOS4xIDEyIDE4LjQgMTIuNiAxOC4xIDEzLjNDMTcuMSAxMS45IDE1LjEgMTEgMTMuOCAxMC43QzE0IDEwLjUgMTQuMSAxMC4yIDE0LjEgMTBDMTQgOS4xIDEzLjYgOC40IDEzIDhDMTMuNCA3LjYgMTMuNyA3IDE0IDYuOUMxNS40IDcuNyAxNi4yIDkuMSAxNiAzMEMxOC40IDI5IDEwLjUgMzAgOFMxMS42IDI5IDEwIDI5LjdIMThDMTggMjguNSAxOC4zIDI3LjUgMTguOSAyNi43QzE5LjMgMjcuMSAxOS44IDI3LjMgMjAuNSAyNy4zSDE5QzE5IDI3IDEwLjMgMjcgMTAuNSAyNy4zSDE5LjQgMjEgOVoiLz4KPHN2Zz4KPHN2Zz4K":"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQTMgMyAwIDAgMSAxNSA1QTMgMyAwIDAgMSAxMiA4QTMgMyAwIDAgMSA5IDVBMyAzIDAgMCAxIDEyIDJNMjEgMjFWMjBDMjEgMTYuMTMgMTcuODcgMTMgMTQgMTNIMTBDNi4xMyAxMyAzIDE2LjEzIDMgMjBWMjFIMjFaIi8+Cjwvc3ZnPgo=");return r.jsxs("div",{className:"max-w-md mx-auto bg-gradient-to-br from-gray-900 via-gray-800 to-black min-h-screen shadow-2xl overflow-hidden flex flex-col",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-4 text-white relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 opacity-20",children:[r.jsx("div",{className:"absolute top-0 left-0 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-10 right-0 w-16 h-16 bg-indigo-400/5 rounded-full blur-lg animate-bounce"}),r.jsx("div",{className:"absolute bottom-0 left-1/2 w-24 h-24 bg-slate-400/5 rounded-full blur-2xl animate-pulse delay-1000"})]}),r.jsxs("div",{className:"absolute inset-0 opacity-15",children:[r.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-white/30 rounded-full -translate-x-20 -translate-y-20 blur-xl"}),r.jsx("div",{className:"absolute bottom-0 right-0 w-32 h-32 bg-white/20 rounded-full translate-x-12 translate-y-12 blur-lg"}),r.jsx("div",{className:"absolute top-1/2 left-1/2 w-24 h-24 bg-white/10 rounded-full -translate-x-12 -translate-y-12 blur-md"})]}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"flex flex-col items-center mb-3",children:[r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 via-indigo-500 to-slate-500 animate-spin-slow opacity-60 blur-sm"}),r.jsxs("div",{className:"relative w-16 h-16 rounded-full overflow-hidden border-2 border-white/70 shadow-lg bg-gradient-to-br from-blue-400 to-indigo-500 ring-1 ring-white/40 backdrop-blur-sm transform group-hover:scale-105 transition-all duration-300",children:[r.jsx("img",{src:Y0(),alt:"الصورة الشخصية",className:"w-full h-full object-cover"}),r.jsx("div",{className:"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border border-white shadow-sm animate-pulse"})]}),t&&r.jsx("button",{onClick:()=>{var f;return i?(f=document.getElementById("imageUpload"))==null?void 0:f.click():a(!0)},className:"absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center shadow-lg border border-white/60 hover:scale-110 transition-all duration-300",children:r.jsx($h,{className:"w-3 h-3 text-white"})}),i&&r.jsx("input",{id:"imageUpload",type:"file",accept:"image/*",onChange:Z0,className:"hidden"})]}),r.jsxs("div",{className:"text-center mt-2",children:[r.jsx("h2",{className:"text-lg font-bold text-white mb-1 drop-shadow-md",children:e==null?void 0:e.username}),r.jsxs("p",{className:"text-white/60 text-xs bg-white/10 px-2 py-0.5 rounded-full backdrop-blur-sm",children:["ID: ",e==null?void 0:e.playerId]}),r.jsxs("div",{className:"flex items-center justify-center mt-1 gap-1",children:[r.jsxs("div",{className:"flex items-center bg-blue-500/20 px-1.5 py-0.5 rounded-full",children:[r.jsx(hn,{className:"w-2.5 h-2.5 text-blue-300 mr-1"}),r.jsxs("span",{className:"text-blue-200 text-xs font-medium",children:["Lv.",(e==null?void 0:e.level)||1]})]}),r.jsxs("div",{className:"flex items-center bg-indigo-500/20 px-1.5 py-0.5 rounded-full",children:[r.jsx(os,{className:"w-2.5 h-2.5 text-indigo-300 mr-1"}),r.jsx("span",{className:"text-indigo-200 text-xs font-medium",children:(e==null?void 0:e.points)||0})]})]})]}),i&&r.jsxs("div",{className:"mt-3 flex space-x-2 rtl:space-x-reverse",children:[r.jsx("button",{onClick:()=>w("male"),className:`px-3 py-1 rounded-full text-xs ${g==="male"?"bg-white text-blue-600":"bg-white/20 text-white"}`,children:"ذكر"}),r.jsx("button",{onClick:()=>w("female"),className:`px-3 py-1 rounded-full text-xs ${g==="female"?"bg-white text-pink-600":"bg-white/20 text-white"}`,children:"أنثى"})]})]}),t?r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"grid grid-cols-3 gap-1.5 text-center mt-3",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(zl,{className:"w-3 h-3 text-yellow-400"})}),r.jsx("div",{className:"text-sm font-bold text-yellow-200",children:(e==null?void 0:e.goldCoins)||0}),r.jsx("div",{className:"text-xs text-yellow-300/80",children:"ذهب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-indigo-700/40 rounded-lg p-2 backdrop-blur-sm border border-indigo-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(p0,{className:"w-3 h-3 text-purple-400"})}),r.jsx("div",{className:"text-sm font-bold text-purple-200",children:(e==null?void 0:e.pearls)||0}),r.jsx("div",{className:"text-xs text-purple-300/80",children:"لؤلؤ"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(hn,{className:"w-3 h-3 text-blue-400"})}),r.jsxs("div",{className:"text-sm font-bold text-blue-200",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-blue-300/80",children:"مستوى"})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-1.5 text-center mt-1.5",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-green-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-green-400/20",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(mi,{className:"w-3 h-3 text-green-400"})}),r.jsx("div",{className:"text-sm font-bold text-green-200",children:(e==null?void 0:e.gamesPlayed)||0}),r.jsx("div",{className:"text-xs text-green-300/80",children:"ألعاب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-red-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-red-400/20",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(g0,{className:"w-3 h-3 text-red-400"})}),r.jsx("div",{className:"text-sm font-bold text-red-200",children:((Ca=e==null?void 0:e.friends)==null?void 0:Ca.length)||0}),r.jsx("div",{className:"text-xs text-red-300/80",children:"أصدقاء"})]})]}),r.jsx("div",{className:"mt-3 flex justify-center",children:r.jsxs("button",{onClick:()=>fr(!0),className:"relative bg-white/20 hover:bg-white/30 rounded-lg p-3 backdrop-blur-sm transition-all duration-300 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"🔔"}),r.jsx("span",{className:"text-white text-sm font-medium",children:"الإشعارات"}),bt.filter(f=>!f.isRead).length>0&&r.jsx("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold",children:bt.filter(f=>!f.isRead).length})]})})]}):r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"bg-white/20 rounded-lg p-3 backdrop-blur-sm text-center",children:[r.jsxs("div",{className:"text-lg font-bold text-black",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-white/80",children:"مستوى"})]}),S&&r.jsxs("button",{onClick:()=>p(!0),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[r.jsx(yn,{className:"w-4 h-4"}),r.jsx("span",{children:"إرسال رسالة"})]})]})]}),i&&r.jsxs("div",{className:"absolute top-4 right-4 flex space-x-2 rtl:space-x-reverse",children:[r.jsx("button",{onClick:K0,disabled:b,className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:r.jsx(Bh,{className:"w-4 h-4 text-white"})}),r.jsx("button",{onClick:()=>{a(!1),u(""),w((e==null?void 0:e.gender)||"male")},className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:r.jsx(xa,{className:"w-4 h-4 text-white"})})]})]}),r.jsx("div",{className:"bg-gradient-to-r from-gray-900/95 via-purple-900/95 to-gray-900/95 backdrop-blur-md border-b border-purple-500/30 z-20 mx-4 rounded-xl mt-4 shadow-xl flex-shrink-0",children:r.jsx("div",{className:"flex overflow-x-auto scrollbar-hide p-2",children:[{id:"overview",label:"عام",icon:wn,color:"from-blue-500 to-cyan-500"},...t?[{id:"friends",label:"أصدقاء",icon:Cn,color:"from-green-500 to-emerald-500"},{id:"gifts",label:"هدايا",icon:Hh,color:"from-pink-500 to-rose-500"},{id:"items",label:"عناصر",icon:hn,color:"from-yellow-500 to-orange-500"},{id:"charge",label:"شحن",icon:qh,color:"from-purple-500 to-violet-500"},{id:"exchange",label:"تبديل",icon:Rh,color:"from-indigo-500 to-blue-500"}]:[]].map(f=>r.jsxs("button",{onClick:()=>o(f.id),className:`flex-shrink-0 flex flex-col items-center px-4 py-3 min-w-[70px] transition-all duration-500 rounded-xl relative overflow-hidden group ${l===f.id?`bg-gradient-to-br ${f.color} text-white shadow-2xl transform scale-110 animate-glow`:"text-gray-300 hover:bg-gray-800/60 hover:text-white hover:scale-105"}`,children:[l===f.id&&r.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${f.color} opacity-20 blur-xl`}),r.jsxs("div",{className:"relative z-10 flex flex-col items-center",children:[r.jsx(f.icon,{className:`w-5 h-5 mb-1 transition-all duration-300 ${l===f.id?"animate-bounce":"group-hover:scale-110"}`}),r.jsx("span",{className:"text-xs font-medium",children:f.label})]}),l===f.id&&r.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"})]},f.id))})}),r.jsxs("div",{className:"flex-1 flex flex-col",children:[l==="overview"&&r.jsxs("div",{className:"flex-1 flex flex-col p-4 gap-4",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/90 via-blue-800/90 to-indigo-800/90 rounded-xl p-3 border border-blue-400/30 shadow-lg backdrop-blur-sm relative overflow-hidden flex-shrink-0",children:[r.jsx("div",{className:"absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-full blur-xl animate-pulse"}),r.jsx("div",{className:"absolute bottom-0 left-0 w-12 h-12 bg-indigo-500/10 rounded-full blur-lg animate-float"}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("h3",{className:"font-bold text-blue-300 mb-3 text-base flex items-center gap-2",children:[r.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center",children:r.jsx(wn,{className:"w-3 h-3 text-white"})}),"معلومات الحساب"]}),r.jsxs("div",{className:"grid gap-2",children:[r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-pink-500 to-purple-500 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"👤"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الجنس"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:(e==null?void 0:e.gender)==="female"?"👩 أنثى":"👨 ذكر"})]}),r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-blue-500 to-indigo-500 rounded flex items-center justify-center",children:r.jsx(Uh,{className:"w-3 h-3 text-white"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الانضمام"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.joinedAt).toLocaleDateString("ar-EG")})]}),r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-500 rounded flex items-center justify-center",children:r.jsx(xn,{className:"w-3 h-3 text-white"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"آخر نشاط"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.lastActive).toLocaleDateString("ar-EG")})]})]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-3 flex-shrink-0",children:[r.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-blue-800/80 rounded-xl p-3 shadow-lg border border-blue-400/30 backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:r.jsx(os,{className:"w-4 h-4 text-white"})}),r.jsx("div",{className:"text-lg font-bold text-blue-200",children:(e==null?void 0:e.experience)||0}),r.jsx("div",{className:"text-xs text-blue-300 font-medium",children:"خبرة"})]})}),r.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-indigo-800/80 rounded-xl p-3 shadow-lg border border-indigo-400/30 backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:r.jsx(hn,{className:"w-4 h-4 text-white"})}),r.jsxs("div",{className:"text-lg font-bold text-indigo-200",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-indigo-300 font-medium",children:"مستوى"})]})})]}),!t&&r.jsx("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 rounded-xl p-3 border border-blue-400/30 shadow-md backdrop-blur-sm flex-shrink-0",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-sm",children:r.jsx(wn,{className:"w-3 h-3 text-white"})}),r.jsxs("div",{children:[r.jsx("h4",{className:"font-bold text-blue-300 text-sm",children:"ملف عام"}),r.jsx("p",{className:"text-xs text-blue-200",children:"معلومات أساسية فقط"})]})]})})]}),t&&l==="friends"&&r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"👥"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إدارة الأصدقاء"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"أضف وتفاعل مع أصدقائك في المنصة"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx(Ul,{className:"w-6 h-6 text-blue-300"}),r.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إضافة صديق جديد"})]}),r.jsxs("div",{className:"flex gap-3 mb-4",children:[r.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:y,onChange:f=>{const E=f.target.value.replace(/\D/g,"");O(E),be("")},className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:6,onKeyPress:f=>f.key==="Enter"&&ba()}),r.jsxs("button",{onClick:ba,disabled:We||y.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:[We?"⏳":"🔍"," ",We?"جاري البحث...":"بحث"]})]}),$e&&r.jsx("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-3 mb-4",children:r.jsx("p",{className:"text-red-200 text-sm text-center",children:$e})}),Z&&r.jsx("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-4",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:Z.profileImage?r.jsx("img",{src:Z.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(Ea=(Ma=Z.username)==null?void 0:Ma.charAt(0))==null?void 0:Ea.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-green-200 font-bold",children:Z.username}),r.jsxs("p",{className:"text-green-300 text-xs",children:["رقم اللاعب: ",Z.playerId]})]})]}),r.jsx("button",{onClick:()=>O0(Z.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"➕ إضافة صديق"})]})})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"👫"}),"قائمة الأصدقاء (",ft.length,")"]}),ft.length===0?r.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"😔"}),"لا توجد أصدقاء حالياً"]}):r.jsx("div",{className:"space-y-3",children:ft.map(f=>{var E,A;return r.jsxs("div",{className:"flex items-center justify-between p-3 bg-slate-900/40 rounded-xl",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:f.profileImage?r.jsx("img",{src:f.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(A=(E=f.username)==null?void 0:E.charAt(0))==null?void 0:A.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-slate-200 font-medium",children:f.username}),r.jsxs("p",{className:"text-slate-400 text-xs",children:["رقم اللاعب: ",f.playerId]})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>G0(f),className:"bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all",children:"💬 محادثة"}),r.jsx("button",{onClick:()=>C(f),className:"bg-gradient-to-r from-purple-500 to-pink-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-purple-600 hover:to-pink-700 transition-all",children:"🎁 هدية"})]})]},f.id)})})]}),r.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/60 to-green-800/60 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-emerald-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📩"}),"طلبات الصداقة (",In.length,")"]}),In.length===0?r.jsxs("div",{className:"text-center py-6 text-emerald-300 text-sm bg-emerald-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"📭"}),"لا توجد طلبات صداقة جديدة"]}):r.jsx("div",{className:"space-y-3",children:In.map(f=>{var E,A;return r.jsxs("div",{className:"flex items-center justify-between p-3 bg-emerald-900/40 rounded-xl",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:f.requester.profileImage?r.jsx("img",{src:f.requester.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(A=(E=f.requester.username)==null?void 0:E.charAt(0))==null?void 0:A.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-emerald-200 font-medium",children:f.requester.username}),r.jsxs("p",{className:"text-emerald-400 text-xs",children:["رقم اللاعب: ",f.requester.playerId]}),r.jsx("p",{className:"text-emerald-500 text-xs",children:new Date(f.requestedAt).toLocaleDateString("ar")})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>$0(f.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"✅ قبول"}),r.jsx("button",{className:"bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-red-600 hover:to-red-700 transition-all",children:"❌ رفض"})]})]},f.id)})})]})]}),t&&l==="gifts"&&r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🎁"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام إدارة الهدايا"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"أرسل واستقبل الهدايا المتنوعة مع الأصدقاء"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-5",children:[r.jsx(Fr,{className:"w-6 h-6 text-blue-300"}),r.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إرسال هدية جديدة"})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-yellow-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"💰"}),"العملات الذهبية"]}),r.jsx("div",{className:"grid grid-cols-1 gap-3",children:r.jsxs("button",{onClick:()=>ht("gold"),className:`p-4 border rounded-xl hover:bg-yellow-700/50 transition-all duration-300 flex items-center gap-4 shadow-lg ${he==="gold"?"bg-yellow-700/60 border-yellow-300":"bg-yellow-800/40 border-yellow-400/30"}`,children:[r.jsx("div",{className:"text-3xl drop-shadow-lg",children:"🪙"}),r.jsxs("div",{className:"text-right flex-1",children:[r.jsx("div",{className:"text-sm font-bold text-yellow-200",children:"عملات ذهبية"}),r.jsx("div",{className:"text-xs text-yellow-300",children:"للشراء والاستخدام في المنصة"})]}),he==="gold"&&r.jsx("div",{className:"text-yellow-300",children:"✓"})]})})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-red-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"⚠️"}),"العناصر الضارة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{onClick:()=>ht("bomb"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${he==="bomb"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💣"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"قنبلة مدمرة"}),he==="bomb"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ht("bat"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${he==="bat"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🦇"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"خفاش مؤذي"}),he==="bat"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ht("snake"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${he==="snake"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🐍"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"ثعبان سام"}),he==="snake"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]})]})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-emerald-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"✨"}),"العناصر المفيدة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{onClick:()=>ht("gem"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${he==="gem"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💎"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"جوهرة نادرة"}),he==="gem"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ht("star"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${he==="star"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"⭐"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"نجمة ذهبية"}),he==="star"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ht("coin"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${he==="coin"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🪙"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"عملة خاصة"}),he==="coin"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]})]})]}),r.jsxs("div",{className:"space-y-4",children:[he&&r.jsx("div",{className:"bg-blue-900/30 p-3 rounded-xl border border-blue-400/30",children:r.jsxs("p",{className:"text-blue-200 text-sm text-center",children:["العنصر المختار: ",r.jsx("span",{className:"font-bold text-blue-100",children:Na(he)})]})}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:ln,onChange:f=>{const E=f.target.value.replace(/\D/g,"");fs(E)},maxLength:6,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("button",{onClick:q0,disabled:hs||!he||ln.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:hs?"⏳ جاري الإرسال...":"🎁 إرسال"})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📦"}),"الهدايا المستلمة"]}),r.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"🎈"}),"لا توجد هدايا جديدة في الوقت الحالي"]})]})]}),t&&l==="items"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-4xl mb-3",children:"⚡"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"العناصر المجمعة"}),r.jsx("p",{className:"text-sm text-gray-500",children:"العناصر التي حصلت عليها من الألعاب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/80 to-green-800/80 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-emerald-300 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"⭐"}),"العناصر المفيدة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💎"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"جوهرة نادرة"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 500 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.gems})]}),r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"⭐"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"نجمة ذهبية"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 200 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.stars})]}),r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"عملة خاصة"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 100 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.coins})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-red-800/80 to-rose-800/80 p-6 rounded-2xl border border-red-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-red-300 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"💣"}),"العناصر الضارة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💣"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"قنبلة مدمرة"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 100 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bombs})]}),r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🦇"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"خفاش مؤذي"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 50 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bats})]}),r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🐍"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"ثعبان سام"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 75 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.snakes})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-amber-200 mb-3 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"💡"}),"نصائح مهمة"]}),r.jsx("p",{className:"text-amber-100 text-sm leading-relaxed",children:"اجمع العناصر المفيدة من الألعاب • أرسلها كهدايا للأصدقاء • بادلها بعملات ذهبية قيمة"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/80 to-indigo-800/80 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-3xl drop-shadow-lg",children:"🛡️"}),"نظام الحماية المتطور"]}),r.jsx("p",{className:"text-blue-100 text-sm mb-6 leading-relaxed",children:"احمِ نفسك من العناصر الضارة والهجمات الخطيرة في الألعاب والهدايا"}),r.jsxs("div",{className:"grid grid-cols-1 gap-5",children:[r.jsxs("div",{className:"bg-blue-800/40 p-5 rounded-xl border border-blue-400/30 backdrop-blur-sm shadow-lg",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-bold text-blue-200 text-base",children:"🥇 درع ذهبي أساسي"}),r.jsx("span",{className:"text-xs text-blue-100 bg-blue-600/40 px-3 py-1 rounded-full font-medium",children:"24 ساعة"})]}),r.jsx("p",{className:"text-sm text-blue-100 mb-4 leading-relaxed",children:"حماية قوية ضد القنابل المدمرة والخفافيش المؤذية والثعابين السامة"}),U!=null&&U.isActive?r.jsxs("div",{className:"bg-green-600/40 p-3 rounded-xl border border-green-400/30 text-center",children:[r.jsx("div",{className:"text-green-200 text-sm font-bold",children:"🛡️ الدرع نشط"}),r.jsxs("div",{className:"text-green-100 text-xs mt-1",children:["ينتهي في: ",U.expiresAt?new Date(U.expiresAt).toLocaleString("ar"):"غير محدد"]})]}):r.jsx("button",{onClick:()=>zt("gold"),disabled:R,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:R?"⏳ جاري التفعيل...":"🛡️ تفعيل الحماية (5,000 🪙)"})]}),r.jsxs("div",{className:"bg-purple-800/40 p-5 rounded-xl border border-purple-400/30 backdrop-blur-sm shadow-lg",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-bold text-purple-200 text-base",children:"👑 درع متقدم مميز"}),r.jsx("span",{className:"text-xs text-purple-100 bg-purple-600/40 px-3 py-1 rounded-full font-medium",children:"7 أيام"})]}),r.jsx("p",{className:"text-sm text-purple-100 mb-4 leading-relaxed",children:"حماية مميزة وشاملة لمدة أسبوع كامل ضد جميع العناصر الضارة والهجمات"}),r.jsx("button",{onClick:()=>alert("الدرع المميز غير متاح حالياً. استخدم الدرع الأساسي."),className:"w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 rounded-xl text-sm font-bold cursor-not-allowed opacity-60",disabled:!0,children:"👑 قريباً - الحماية المميزة"})]})]})]})]}),t&&l==="charge"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"💰"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"شحن الرصيد الذهبي"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"اشحن عملاتك الذهبية بأفضل الأسعار"})]}),r.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[r.jsx("div",{className:"bg-gradient-to-br from-yellow-800/60 to-amber-800/60 p-6 rounded-2xl border border-yellow-400/30 shadow-xl backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("h4",{className:"font-bold text-yellow-200 mb-2 text-lg",children:"5,000 عملة ذهبية"}),r.jsx("p",{className:"text-yellow-100 text-base mb-4 font-semibold",children:"💵 $1 USD فقط"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>ja(5e3,!0,"1_dollar"),disabled:rn||!_n["1_dollar"],className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:rn?"⏳":_n["1_dollar"]?"🆓 مجاني":"✅ مستخدم"}),r.jsx("button",{onClick:()=>ka(5e3,"$1 USD"),className:"flex-1 bg-gradient-to-r from-yellow-500 to-amber-600 text-white py-3 rounded-xl text-xs font-bold hover:from-yellow-600 hover:to-amber-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})}),r.jsx("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("h4",{className:"font-bold text-green-200 mb-2 text-lg",children:"27,200 عملة ذهبية"}),r.jsx("p",{className:"text-green-100 text-base mb-1 font-semibold",children:"💵 $5 USD"}),r.jsx("p",{className:"text-sm text-green-300 bg-green-900/30 px-3 py-1 rounded-lg mb-4 font-medium",children:"🎉 وفر 8% أكثر!"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>ja(27200,!0,"5_dollar"),disabled:rn||!_n["5_dollar"],className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-3 rounded-xl text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:rn?"⏳":_n["5_dollar"]?"🆓 مجاني":"✅ مستخدم"}),r.jsx("button",{onClick:()=>ka(27200,"$5 USD"),className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})})]})]}),t&&l==="exchange"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🔄"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام تبديل العملات"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"اللآلئ مخصصة حصرياً للتحويل إلى دولارات نقدية"})]}),r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🪙➡️🦪"}),"تحويل ذهب إلى لآلئ"]}),r.jsx("p",{className:"text-blue-100 text-sm mb-4 bg-blue-900/30 px-3 py-2 rounded-lg",children:"معدل التحويل: 10,000 🪙 = 1 🦪"}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("input",{type:"number",placeholder:"10000",value:st,onChange:f=>Se(Math.max(1e4,parseInt(f.target.value)||1e4)),min:"10000",step:"10000",max:(e==null?void 0:e.goldCoins)||0,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("span",{className:"text-blue-200 font-medium",children:"🪙 ➡️ 🦪"})]}),r.jsxs("div",{className:"text-center mb-4",children:[r.jsxs("p",{className:"text-blue-200 text-sm",children:["ستحصل على: ",r.jsxs("span",{className:"font-bold text-blue-100",children:[Math.floor(st/1e4)," 🦪"]})]}),r.jsxs("p",{className:"text-blue-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.goldCoins)||0," 🪙"]})]}),r.jsx("button",{onClick:B0,disabled:sn||st<1e4||((e==null?void 0:e.goldCoins)||0)<st,className:"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:sn?"⏳ جاري التحويل...":"🔄 تحويل إلى لآلئ"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-green-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🦪➡️💵"}),"سحب دولارات نقدية"]}),r.jsx("div",{className:"bg-green-900/30 p-4 rounded-xl mb-4",children:r.jsxs("p",{className:"text-green-100 text-sm leading-relaxed",children:[r.jsx("strong",{className:"text-green-200",children:"💰 معدل التحويل:"})," 10 🦪 = $1 USD",r.jsx("br",{}),r.jsx("strong",{className:"text-green-200",children:"🎯 الحد الأدنى للسحب:"})," $25 USD (250 🦪)"]})}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("input",{type:"number",placeholder:"250",value:je,onChange:f=>dr(Math.max(250,parseInt(f.target.value)||250)),min:"250",max:(e==null?void 0:e.pearls)||0,className:"flex-1 px-4 py-3 bg-green-900/30 border border-green-400/30 rounded-xl text-white placeholder-green-300 focus:outline-none focus:ring-2 focus:ring-green-500"}),r.jsx("span",{className:"text-green-200 font-medium",children:"🦪 ➡️ $"})]}),r.jsxs("div",{className:"text-center mb-4",children:[r.jsxs("p",{className:"text-green-200 text-sm",children:["ستحصل على: ",r.jsxs("span",{className:"font-bold text-green-100",children:["$",je/10," USD"]})]}),r.jsxs("p",{className:"text-green-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.pearls)||0," 🦪"]})]}),r.jsx("button",{onClick:V0,disabled:je<250||((e==null?void 0:e.pearls)||0)<je,className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:"📱 طلب سحب عبر واتساب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-amber-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📝"}),"معلومات مهمة"]}),r.jsxs("div",{className:"space-y-2 text-amber-100 text-sm leading-relaxed",children:[r.jsxs("p",{children:["• ",r.jsx("strong",{children:"اللآلئ 🦪"})," - مخصصة حصرياً للتحويل إلى دولارات نقدية"]}),r.jsxs("p",{children:["• ",r.jsx("strong",{children:"العملات الذهبية 🪙"})," - للشراء والتبادل داخل المنصة"]}),r.jsxs("p",{children:["• ",r.jsx("strong",{children:"العناصر الخاصة"})," - تُكسب من الألعاب والتحديات"]})]})]})]})]})]}),d&&r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-slate-800 to-blue-800 rounded-2xl p-6 w-full max-w-md shadow-2xl border border-blue-400/30",children:[r.jsxs("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:["إرسال رسالة إلى ",e==null?void 0:e.username]}),r.jsx("textarea",{value:M,onChange:f=>P(f.target.value),placeholder:"اكتب رسالتك هنا...",className:"w-full p-3 rounded-xl bg-slate-800/40 border border-blue-400/30 text-white placeholder-blue-300 resize-none h-32 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:500}),r.jsxs("div",{className:"text-right text-xs text-blue-300 mt-1 mb-4",children:[M.length,"/500"]}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>{p(!1),P("")},className:"flex-1 bg-slate-600 hover:bg-slate-700 text-white py-2 px-4 rounded-xl transition-colors",children:"إلغاء"}),r.jsxs("button",{onClick:J0,disabled:!M.trim(),className:"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-500 disabled:to-gray-600 text-white py-2 px-4 rounded-xl transition-all flex items-center justify-center gap-2",children:[r.jsx(Fr,{className:"w-4 h-4"}),"إرسال"]})]})]})}),h&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-purple-900 to-pink-900 rounded-2xl p-6 w-full max-w-md border border-purple-400/30 shadow-2xl",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-4xl mb-3",children:"🎁"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إرسال هدية"}),r.jsxs("p",{className:"text-purple-200 text-sm",children:["إلى: ",h.username]})]}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"نوع الهدية:"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>G("gold"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${L==="gold"?"bg-gradient-to-r from-yellow-500 to-orange-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"🪙 عملات ذهبية"}),r.jsx("button",{onClick:()=>G("pearls"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${L==="pearls"?"bg-gradient-to-r from-blue-500 to-cyan-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"💎 لآلئ"})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"الكمية:"}),r.jsx("input",{type:"number",value:T,onChange:f=>$(Math.max(1,parseInt(f.target.value)||1)),min:"1",max:L==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls,className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500"}),r.jsxs("p",{className:"text-purple-300 text-xs mt-1",children:["الحد الأقصى: ",L==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"رسالة (اختيارية):"}),r.jsx("textarea",{value:Y,onChange:f=>fe(f.target.value),placeholder:"اكتب رسالة مع الهدية...",className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none",rows:3,maxLength:200})]})]}),r.jsxs("div",{className:"flex gap-3 mt-6",children:[r.jsx("button",{onClick:()=>{C(null),fe(""),$(100)},className:"flex-1 bg-gray-600 text-white py-3 rounded-xl font-bold hover:bg-gray-700 transition-all",children:"إلغاء"}),r.jsx("button",{onClick:U0,disabled:ge||T<=0,className:"flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-xl font-bold hover:from-purple-600 hover:to-pink-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:ge?"⏳ جاري الإرسال...":"🎁 إرسال الهدية"})]})]})}),lt&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-slate-900 to-purple-900 rounded-2xl p-6 w-full max-w-md max-h-[80vh] overflow-hidden border border-purple-400/30 shadow-2xl",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("h3",{className:"text-xl font-bold text-white flex items-center gap-2",children:["🔔 الإشعارات",bt.filter(f=>!f.isRead).length>0&&r.jsx("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:bt.filter(f=>!f.isRead).length})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[bt.filter(f=>!f.isRead).length>0&&r.jsx("button",{onClick:W0,className:"text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors",children:"تحديد الكل كمقروء"}),r.jsx("button",{onClick:()=>fr(!1),className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]})]}),r.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:bt.length===0?r.jsxs("div",{className:"text-center py-8 text-gray-400",children:[r.jsx("div",{className:"text-4xl mb-2",children:"📭"}),r.jsx("p",{children:"لا توجد إشعارات"})]}):bt.map(f=>r.jsx("div",{onClick:()=>{f.isRead||H0(f._id)},className:`p-3 rounded-xl border transition-all cursor-pointer hover:bg-opacity-80 ${f.isRead?"bg-slate-800/50 border-slate-600/30":"bg-blue-900/30 border-blue-400/30 shadow-lg"}`,children:r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:"text-2xl",children:[f.type==="gift_received"&&"🎁",f.type==="item_received"&&"📦",f.type==="friend_request"&&"👥",f.type==="message"&&"💬"]}),!f.isRead&&r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-slate-900 animate-pulse"})]}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h4",{className:`font-bold text-sm ${f.isRead?"text-gray-300":"text-white"}`,children:f.title}),r.jsx("p",{className:`text-xs mt-1 ${f.isRead?"text-gray-400":"text-gray-200"}`,children:f.message}),r.jsxs("div",{className:"flex items-center justify-between mt-2",children:[r.jsx("span",{className:"text-gray-400 text-xs",children:new Date(f.createdAt).toLocaleString("ar")}),!f.isRead&&r.jsx("span",{className:"text-blue-400 text-xs font-bold",children:"جديد"})]})]})]})},f._id))})]})}),hr&&ce&&r.jsxs("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex flex-col",onClick:f=>{f.target===f.currentTarget&&re(!1)},children:[r.jsxs("div",{className:"bg-gradient-to-r from-green-600 to-green-700 px-4 py-3 flex items-center gap-3 shadow-lg",children:[r.jsx("button",{onClick:()=>jt(!1),className:"text-white hover:bg-white/20 rounded-full p-2 transition-colors",children:r.jsx(f0,{className:"w-5 h-5"})}),r.jsx("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-white/20 border-2 border-white/30",children:ce.profileImage?r.jsx("img",{src:ce.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold text-lg",children:(Aa=(Ia=ce.username)==null?void 0:Ia.charAt(0))==null?void 0:Aa.toUpperCase()})}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-bold text-white text-lg",children:ce.username}),r.jsxs("p",{className:"text-green-100 text-xs opacity-90",children:["رقم اللاعب: ",ce.playerId]}),r.jsx("p",{className:"text-green-100 text-xs opacity-75",children:"🕐 المحادثات تختفي بعد 3 أيام"})]}),r.jsx("div",{className:"text-green-100",children:r.jsx(yn,{className:"w-6 h-6"})})]}),r.jsxs("div",{className:"flex-1 overflow-y-auto px-4 py-6 space-y-4",id:"messages-container",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,backgroundColor:"#0f172a"},children:[on.length===0?r.jsxs("div",{className:"text-center py-20",children:[r.jsx("div",{className:"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(yn,{className:"w-10 h-10 text-white/50"})}),r.jsx("p",{className:"text-white/70 text-lg font-medium",children:"ابدأ محادثة جديدة"}),r.jsx("p",{className:"text-white/50 text-sm mt-2",children:"أرسل رسالة لبدء المحادثة"}),r.jsx("div",{className:"mt-4 p-3 bg-yellow-500/20 rounded-lg border border-yellow-500/30",children:r.jsx("p",{className:"text-yellow-200 text-xs",children:"🕐 تنبيه: المحادثات تُحذف تلقائياً بعد 3 أيام للحفاظ على الخصوصية"})})]}):on.map((f,E)=>{var J,Q;const A=((J=f.sender)==null?void 0:J._id)===(e==null?void 0:e.id),q=E===0||new Date(f.createdAt).getTime()-new Date((Q=on[E-1])==null?void 0:Q.createdAt).getTime()>3e5;return r.jsxs("div",{children:[q&&r.jsx("div",{className:"text-center my-4",children:r.jsx("span",{className:"bg-black/30 text-white/70 px-3 py-1 rounded-full text-xs",children:new Date(f.createdAt).toLocaleDateString("ar",{weekday:"short",hour:"2-digit",minute:"2-digit"})})}),r.jsx("div",{className:`flex ${A?"justify-end":"justify-start"} mb-2`,children:r.jsxs("div",{className:`max-w-[80%] px-4 py-3 rounded-2xl shadow-lg relative ${A?"bg-gradient-to-r from-green-500 to-green-600 text-white rounded-br-md":"bg-white text-gray-800 rounded-bl-md"}`,children:[r.jsx("p",{className:"text-sm leading-relaxed break-words whitespace-pre-wrap",children:f.content}),r.jsxs("div",{className:`flex items-center justify-end mt-1 text-xs ${A?"text-green-100":"text-gray-500"}`,children:[r.jsx("span",{children:new Date(f.createdAt).toLocaleTimeString("ar",{hour:"2-digit",minute:"2-digit"})}),A&&r.jsx("div",{className:"ml-1 text-green-200",children:"✓✓"})]}),r.jsx("div",{className:`absolute bottom-0 w-4 h-4 ${A?"-right-2 bg-gradient-to-r from-green-500 to-green-600":"-left-2 bg-white"}`,style:{clipPath:A?"polygon(0 0, 100% 0, 0 100%)":"polygon(100% 0, 0 0, 100% 100%)"}})]})})]},f._id)}),H&&r.jsx("div",{className:"flex justify-start mb-2",children:r.jsx("div",{className:"bg-white px-4 py-3 rounded-2xl rounded-bl-md shadow-lg",children:r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsxs("div",{className:"flex gap-1",children:[r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),r.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"يكتب..."})]})})})]}),r.jsxs("div",{className:"bg-gray-100 px-4 py-3 flex items-end gap-3 relative",children:[Ce&&r.jsx("div",{className:"absolute bottom-full left-4 right-4 mb-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-h-48 overflow-y-auto",children:r.jsx("div",{className:"grid grid-cols-8 gap-2",children:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","👍","👎","👌","🤌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤝","🙏","✍️","💪","🦾","🦿","🦵"].map((f,E)=>r.jsx("button",{onClick:()=>{W(A=>A+f),re(!1)},className:"text-xl hover:bg-gray-100 rounded-lg p-1 transition-colors",children:f},E))})}),r.jsx("button",{onClick:()=>re(!Ce),className:`text-gray-500 hover:text-gray-700 transition-colors p-2 ${Ce?"bg-gray-200 rounded-full":""}`,children:r.jsx("span",{className:"text-xl",children:"😊"})}),r.jsx("div",{className:"flex-1 relative",children:r.jsx("textarea",{value:I,onChange:f=>{W(f.target.value),f.target.style.height="auto",f.target.style.height=Math.min(f.target.scrollHeight,120)+"px"},placeholder:"اكتب رسالة...",className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-3xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none max-h-[120px] min-h-[48px]",onKeyPress:f=>{f.key==="Enter"&&!f.shiftKey&&(f.preventDefault(),I.trim()&&Sa())},rows:1,autoFocus:!0})}),r.jsx("button",{onClick:Sa,disabled:!I.trim(),className:`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${I.trim()?"bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:r.jsx(Fr,{className:"w-5 h-5"})})]})]})]})};class R0{constructor(t){this.localStream=null,this.peerConnections=new Map,this.remoteUsers=new Map,this.isJoined=!1,this.isMuted=!1,this.roomId=null,this.userId=null,this.processingOffers=new Set,this.connectionAttempts=new Map,this.connectionMonitorInterval=null,this.audioContext=null,this.analyser=null,this.voiceActivityThreshold=25,this.isMonitoringVoice=!1,this.isSpeaking=!1,this.lastVoiceActivitySent=0,this.voiceActivityDebounce=500,this.iceServers=[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"},{urls:"turn:openrelay.metered.ca:80",username:"openrelayproject",credential:"openrelayproject"}],this.wsService=t,this.setupWebSocketHandlers()}setupWebSocketHandlers(){this.wsService.onMessage("webrtc_offer",this.handleOffer.bind(this)),this.wsService.onMessage("webrtc_answer",this.handleAnswer.bind(this)),this.wsService.onMessage("webrtc_ice_candidate",this.handleIceCandidate.bind(this)),this.wsService.onMessage("user_joined_voice",this.handleUserJoined.bind(this)),this.wsService.onMessage("user_left_voice",this.handleUserLeft.bind(this))}async joinRoom(t,n){var s;try{this.roomId=t,this.userId=n,this.localStream=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0},video:!1}),this.startVoiceActivityDetection(),this.wsService.send({type:"join_voice_room",data:{roomId:t,userId:n}}),this.isJoined=!0,this.startConnectionMonitoring()}catch(l){throw console.error("❌ Error joining voice room:",l),(s=this.onError)==null||s.call(this,l),l}}async leaveRoom(){var t;try{this.peerConnections.forEach((n,s)=>{n.close()}),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(n=>n.stop()),this.localStream=null),this.stopVoiceActivityDetection(),this.roomId&&this.userId&&this.wsService.send({type:"leave_voice_room",data:{roomId:this.roomId,userId:this.userId}}),this.isJoined=!1,this.remoteUsers.clear(),this.processingOffers.clear(),this.connectionAttempts.clear(),this.stopConnectionMonitoring()}catch(n){console.error("❌ Error leaving voice room:",n),(t=this.onError)==null||t.call(this,n)}}async toggleMute(){if(!this.localStream)return!1;const t=this.localStream.getAudioTracks()[0];return t?(t.enabled=!t.enabled,this.isMuted=!t.enabled,this.isMuted):!1}setMute(t){if(!this.localStream)return;const n=this.localStream.getAudioTracks()[0];n&&(n.enabled=!t,this.isMuted=t,console.log(`🎤 Local audio ${t?"muted":"unmuted"}`))}setRemoteAudioMuted(t){this.peerConnections.forEach((n,s)=>{const l=n.getRemoteStreams()[0];l&&l.getAudioTracks().forEach(i=>{i.enabled=!t})})}async createPeerConnection(t){const n=new RTCPeerConnection({iceServers:this.iceServers});return this.localStream&&this.localStream.getTracks().forEach(s=>{n.addTrack(s,this.localStream)}),n.ontrack=s=>{var a;const[l]=s.streams;console.log("🎵 Received remote stream from:",t);const o=new Audio;o.srcObject=l,o.volume=.8,o.autoplay=!0,o.play().then(()=>{console.log("✅ Remote audio playing from:",t)}).catch(c=>{console.warn("⚠️ Audio play failed, trying user interaction:",c),document.addEventListener("click",()=>{o.play().catch(()=>{})},{once:!0})});const i=this.remoteUsers.get(t)||{id:t,isSpeaking:!1,isMuted:!1,audioLevel:0};this.remoteUsers.set(t,i),(a=this.onUserJoined)==null||a.call(this,i)},n.onicecandidate=s=>{s.candidate&&this.wsService.send({type:"webrtc_ice_candidate",data:{candidate:s.candidate,targetUserId:t,fromUserId:this.userId}})},n.onconnectionstatechange=()=>{console.log(`🔗 Connection state with ${t}: ${n.connectionState}`),n.connectionState==="connected"?(console.log(`✅ Successfully connected to ${t}`),this.processingOffers.delete(t),this.connectionAttempts.delete(t)):(n.connectionState==="failed"||n.connectionState==="disconnected")&&(console.log(`❌ Connection failed/disconnected with ${t}`),this.peerConnections.delete(t),this.processingOffers.delete(t))},n.oniceconnectionstatechange=()=>{console.log(`🧊 ICE state with ${t}: ${n.iceConnectionState}`),(n.iceConnectionState==="connected"||n.iceConnectionState==="completed")&&console.log(`🎉 ICE connection established with ${t}`)},this.peerConnections.set(t,n),n}async handleOffer(t){try{const{offer:n,fromUserId:s}=t;if(this.processingOffers.has(s)){console.log("⏭️ Already processing offer from:",s);return}const l=this.connectionAttempts.get(s)||0;if(l>=3){console.log("🛑 Too many connection attempts with:",s);return}this.processingOffers.add(s),this.connectionAttempts.set(s,l+1),console.log("📥 Received offer from:",s);const o=this.peerConnections.get(s);if(o&&o.connectionState==="connected"){console.log("✅ Connection already established with:",s),this.processingOffers.delete(s);return}o&&o.signalingState!=="closed"&&(console.log("🔄 Closing existing connection before creating new one"),o.close(),this.peerConnections.delete(s)),console.log("🔄 Creating peer connection and answer for:",s);const i=await this.createPeerConnection(s);try{await i.setRemoteDescription(n),console.log("✅ Set remote description (offer)");const a=await i.createAnswer();await i.setLocalDescription(a),console.log("✅ Created and set local description (answer)"),console.log("📤 Sending WebRTC answer to:",s),this.wsService.send({type:"webrtc_answer",data:{answer:a,targetUserId:s,fromUserId:this.userId}}),setTimeout(()=>{this.processingOffers.delete(s)},2e3)}catch(a){console.error("❌ SDP error in offer handling:",a),i.close(),this.peerConnections.delete(s),this.processingOffers.delete(s)}}catch(n){console.error("❌ Error handling offer:",n),this.processingOffers.delete(t.fromUserId)}}async handleAnswer(t){try{const{answer:n,fromUserId:s}=t;console.log("📥 Received answer from:",s);const l=this.peerConnections.get(s);if(!l){console.warn("⚠️ No peer connection found for:",s);return}if(l.signalingState==="have-local-offer")try{await l.setRemoteDescription(n),console.log("✅ Set remote description (answer) for:",s),console.log("🔗 WebRTC connection should be established with:",s),setTimeout(()=>{l.connectionState==="connected"?console.log("🎉 Connection confirmed with:",s):console.log("⏳ Waiting for connection to stabilize with:",s)},1e3)}catch(o){console.warn("⚠️ SDP error, recreating connection:",o.message),this.peerConnections.delete(s),this.processingOffers.delete(s),setTimeout(()=>{this.userId<s&&this.handleUserJoined({userId:s})},2e3)}else l.signalingState==="stable"?console.log("ℹ️ Connection already stable with:",s):(console.warn("⚠️ Peer connection not in correct state for answer:",l.signalingState),console.log("🔄 Current state:",l.signalingState,"Connection state:",l.connectionState))}catch(n){console.error("❌ Error handling answer:",n)}}async handleIceCandidate(t){try{const{candidate:n,fromUserId:s}=t,l=this.peerConnections.get(s);l&&l.remoteDescription?await l.addIceCandidate(n):l&&setTimeout(()=>this.handleIceCandidate(t),100)}catch(n){n.message.includes("ICE candidate")||console.error("❌ Error handling ICE candidate:",n)}}async handleUserJoined(t){try{const{userId:n}=t;if(n===this.userId)return;const s=this.peerConnections.get(n);if(s&&s.connectionState==="connected"){console.log("✅ Already connected to:",n);return}if(this.processingOffers.has(n)){console.log("⏭️ Already processing connection with:",n);return}if(console.log("👤 User joined voice room:",n),this.userId<n){const o=this.connectionAttempts.get(n)||0;if(o>=3){console.log("🛑 Too many offer attempts to:",n);return}this.connectionAttempts.set(n,o+1),console.log("🔄 Creating peer connection and offer for:",n);const i=await this.createPeerConnection(n),a=await i.createOffer();await i.setLocalDescription(a),console.log("📤 Sending WebRTC offer to:",n),this.wsService.send({type:"webrtc_offer",data:{offer:a,targetUserId:n,fromUserId:this.userId}})}else console.log("⏳ Waiting for offer from:",n),await this.createPeerConnection(n)}catch(n){console.error("❌ Error handling user joined:",n)}}handleUserLeft(t){var l;const{userId:n}=t;console.log("👋 User left voice room:",n);const s=this.peerConnections.get(n);s&&(s.close(),this.peerConnections.delete(n)),this.processingOffers.delete(n),this.connectionAttempts.delete(n),this.remoteUsers.delete(n),(l=this.onUserLeft)==null||l.call(this,n)}startVoiceActivityDetection(){if(!(!this.localStream||this.isMonitoringVoice))try{this.audioContext=new AudioContext;const t=this.audioContext.createMediaStreamSource(this.localStream);this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=256,t.connect(this.analyser),this.isMonitoringVoice=!0,this.monitorVoiceActivity()}catch(t){console.error("❌ Error starting voice activity detection:",t)}}monitorVoiceActivity(){if(!this.analyser||!this.isMonitoringVoice)return;const t=new Uint8Array(this.analyser.frequencyBinCount),n=()=>{if(!this.isMonitoringVoice)return;this.analyser.getByteFrequencyData(t);const s=t.reduce((u,g)=>u+g,0)/t.length,l=Math.round(s*10)/10,o=l>this.voiceActivityThreshold,i=Date.now(),a=o!==this.isSpeaking,c=i-this.lastVoiceActivitySent>this.voiceActivityDebounce;(a||c)&&this.onVoiceActivity&&this.userId&&(this.onVoiceActivity({userId:this.userId,level:l,isSpeaking:o}),this.lastVoiceActivitySent=i,this.isSpeaking=o),requestAnimationFrame(n)};n()}stopVoiceActivityDetection(){this.isMonitoringVoice=!1,this.audioContext&&(this.audioContext.close(),this.audioContext=null),this.analyser=null}get isConnected(){return this.isJoined}get mutedState(){return this.isMuted}get connectedUsers(){return Array.from(this.remoteUsers.values())}async sendOffer(t){try{if(t===this.userId)return;console.log("🔄 Creating peer connection and offer for:",t);const n=await this.createPeerConnection(t),s=await n.createOffer();await n.setLocalDescription(s),console.log("📤 Sending WebRTC offer to:",t),this.wsService.send({type:"webrtc_offer",data:{offer:s,targetUserId:t,fromUserId:this.userId}})}catch(n){console.error("❌ Error sending offer:",n)}}checkConnectionsAndRetry(){this.peerConnections.forEach((t,n)=>{(t.connectionState==="failed"||t.connectionState==="disconnected")&&(console.log("🔄 Retrying connection with:",n),this.peerConnections.delete(n),this.processingOffers.delete(n),setTimeout(()=>{this.userId<n&&this.handleUserJoined({userId:n})},1e3))})}startConnectionMonitoring(){this.connectionMonitorInterval||(this.connectionMonitorInterval=setInterval(()=>{this.checkConnectionsAndRetry()},5e3))}stopConnectionMonitoring(){this.connectionMonitorInterval&&(clearInterval(this.connectionMonitorInterval),this.connectionMonitorInterval=null)}cleanup(){this.stopConnectionMonitoring(),this.leaveRoom().catch(console.error)}}class Op{constructor(){this.commonPhrases=["السلام عليكم","وعليكم السلام","أهلاً وسهلاً","مرحباً بكم","حياكم الله","أهلاً بك","مساء الخير","صباح الخير","تصبحون على خير","ليلة سعيدة","كيف حالكم؟","كيف الأحوال؟","إن شاء الله","ما شاء الله","بارك الله فيك","جزاك الله خيراً","الله يعطيك العافية","تسلم إيدك","الله يبارك فيك","ربي يحفظك","ممتاز!","رائع جداً","أحسنت","بالتوفيق","الله يوفقك","نعم صحيح","أوافقك الرأي","هذا صحيح","بالضبط","تماماً","من أين أنت؟","كم عمرك؟","ما اسمك؟","أين تسكن؟","ما هو عملك؟","هل أنت متزوج؟","كم طفل لديك؟","ما هوايتك؟","مع السلامة","إلى اللقاء","نراكم قريباً","الله معكم","في أمان الله","وداعاً","إلى اللقاء قريباً","هيا نلعب","من يريد اللعب؟","لعبة جميلة","أحب هذه اللعبة","فزت!","خسرت","لعبة أخرى؟","تحدي جديد","لا تحزن","كله خير","الله معك","ستتحسن الأمور","لا تيأس","كن قوياً","أنا معك","سأساعدك","شكراً لك","عفواً","لا شكر على واجب","أعتذر","آسف","لا بأس","لا مشكلة","بالعكس","أنا جائع","ما رأيكم في الطعام؟","هل تناولتم الطعام؟","طعام لذيذ","أحب هذا الطبق","وجبة شهية","الجو جميل اليوم","الطقس حار","الطقس بارد","يبدو أنه سيمطر","الشمس مشرقة","الجو معتدل"],this.recentMessages=[]}addMessage(t){this.recentMessages.unshift(t),this.recentMessages.length>50&&(this.recentMessages=this.recentMessages.slice(0,50))}getSuggestions(t){if(!t||t.length<2)return[];const n=t.toLowerCase().trim(),s=[];return this.commonPhrases.forEach(l=>{l.toLowerCase().includes(n)&&s.push(l)}),this.recentMessages.forEach(l=>{l.toLowerCase().includes(n)&&!s.includes(l)&&l.length>t.length&&s.push(l)}),s.sort((l,o)=>{const i=l.length-o.length;if(i!==0)return i;const a=this.commonPhrases.includes(l),c=this.commonPhrases.includes(o);return a&&!c?-1:!a&&c?1:0}).slice(0,5)}getQuickSuggestions(){return["السلام عليكم","كيف حالكم؟","أهلاً وسهلاً","شكراً لك","مع السلامة"]}addCustomPhrase(t){this.commonPhrases.includes(t)||this.commonPhrases.push(t)}}const Zc=new Op,Up=({onEmojiSelect:e,onClose:t})=>{const n=x.useRef(null),[s,l]=x.useState("faces"),o={faces:{name:"الوجوه",icon:r.jsx(b0,{className:"w-4 h-4"}),emojis:["😀","😃","😄","😁","😆","😅","🤣","😂","🙂","🙃","😉","😊","😇","🥰","😍","🤩","😘","😗","😚","😙","😋","😛","😜","🤪","😝","🤑","🤗","🤭","🤫","🤔","🤐","🤨","😐","😑","😶","😏","😒","🙄","😬","🤥","😔","😪","🤤","😴","😷","🤒","🤕","🤢","🤮","🤧","🥵","🥶","🥴","😵","🤯","🤠","🥳","😎","🤓","🧐"]},hearts:{name:"القلوب",icon:r.jsx(g0,{className:"w-4 h-4"}),emojis:["❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","♥️","💋","💌","💐","🌹","🌺","🌻","🌷","🌸","💒","💍"]},gestures:{name:"الإيماءات",icon:r.jsx(Dh,{className:"w-4 h-4"}),emojis:["👍","👎","👌","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤲","🤝","🙏","✍️","💪","🦾","🦿","🦵","🦶"]},symbols:{name:"الرموز",icon:r.jsx(hn,{className:"w-4 h-4"}),emojis:["💯","💢","💥","💫","💦","💨","🕳️","💣","💬","👁️‍🗨️","🗨️","🗯️","💭","💤","💮","♨️","💈","🛑","⭐","🌟","✨","⚡","🔥","💎","🏆","🥇","🥈","🥉","🎖️","🏅"]},food:{name:"الطعام",icon:r.jsx(Vh,{className:"w-4 h-4"}),emojis:["🍎","🍊","🍋","🍌","🍉","🍇","🍓","🫐","🍈","🍒","🍑","🥭","🍍","🥥","🥝","🍅","🍆","🥑","🥦","🥬","☕","🍵","🧃","🥤","🍺","🍻","🥂","🍷","🥃","🍸"]},games:{name:"الألعاب",icon:r.jsx(ga,{className:"w-4 h-4"}),emojis:["🎮","🕹️","🎯","🎲","🃏","🀄","🎰","🎳","🏓","🏸","⚽","🏀","🏈","⚾","🥎","🎾","🏐","🏉","🥏","🎱"]}};return x.useEffect(()=>{const i=a=>{n.current&&!n.current.contains(a.target)&&t()};return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[t]),r.jsxs("div",{ref:n,onClick:i=>i.stopPropagation(),className:"absolute bottom-12 left-0 right-0 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-2xl z-50 max-w-sm",children:[r.jsxs("div",{className:"flex border-b border-gray-600 p-2 overflow-x-auto",children:[Object.entries(o).map(([i,a])=>r.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),l(i)},className:`p-2 rounded-lg transition-colors flex-shrink-0 ${s===i?"bg-purple-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,title:a.name,children:a.icon},i)),r.jsx("button",{onClick:i=>{i.preventDefault(),i.stopPropagation(),t()},className:"ml-auto p-2 text-gray-400 hover:text-white flex-shrink-0",children:"✕"})]}),r.jsx("div",{className:"p-3 max-h-48 overflow-y-auto",children:r.jsx("div",{className:"grid grid-cols-8 gap-1",children:o[s].emojis.map((i,a)=>r.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),e(i),t()},className:"w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-lg",title:i,children:i},a))})})]})},$p=({suggestions:e,onSuggestionSelect:t,isVisible:n})=>!n||e.length===0?null:r.jsx("div",{className:"absolute bottom-full left-0 right-0 mb-1 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-lg z-40 max-h-32 overflow-y-auto",children:e.map((s,l)=>r.jsx("button",{onClick:()=>t(s),className:"w-full text-left px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0",children:s},l))}),Fp=({user:e,wsService:t,onBack:n})=>{var on;const s=n||(()=>{window.history.back()}),[l,o]=x.useState(null),[i,a]=x.useState([]),[c,u]=x.useState(!0),[g,w]=x.useState(!1),[b,N]=x.useState(null),[j,k]=x.useState(!1),[S,m]=x.useState(null),[d,p]=x.useState(!1),[M,P]=x.useState(!1),[R,z]=x.useState(!1),[U,D]=x.useState(""),[y,O]=x.useState(!1),[Z,ne]=x.useState(null),[We,Pe]=x.useState(null),[$e,be]=x.useState("30"),[h,C]=x.useState(!1),[T,$]=x.useState(!1),[L,G]=x.useState("#ffffff"),[Y,fe]=x.useState([]),[ge,ie]=x.useState(!1),ft=[{name:"أبيض",value:"#ffffff"},{name:"أحمر",value:"#ef4444"},{name:"أزرق",value:"#3b82f6"},{name:"أخضر",value:"#10b981"},{name:"أصفر",value:"#f59e0b"},{name:"بنفسجي",value:"#8b5cf6"},{name:"وردي",value:"#ec4899"},{name:"برتقالي",value:"#f97316"},{name:"سماوي",value:"#06b6d4"},{name:"ذهبي",value:"#eab308"}],[Pt,In]=x.useState(null),[ar,$l]=x.useState(!1),[cr,rn]=x.useState("prompt"),[Rt,st]=x.useState(!1),Se=x.useRef(null),sn=x.useRef(new Map),An=x.useRef(null),je=x.useRef(null),dr=async(v=!1)=>{try{v?u(!0):w(!0);const[I,W]=await Promise.all([X.getVoiceRoom(),X.getVoiceRoomMessages()]);o(I);const H=W.map(re=>({...re,sender:{...re.sender,role:re.sender.role||(re.sender.isAdmin?"admin":"member"),isAdmin:re.sender.isAdmin||!1,gender:re.sender.gender||"male"}}));a(H);const B=I.seats.find(re=>re.user&&re.user._id===e.id);B?(console.log("✅ User is in seat:",B.seatNumber),k(!0),m(B.seatNumber),P(B.isMuted)):(console.log("❌ User is not in any seat"),k(!1),m(null));const Ce=I.waitingQueue.some(re=>re.user._id===e.id);p(Ce),N(null)}catch(I){if(console.error("Error loading voice room:",I),I.message&&I.message.includes("مطرود من الغرفة الصوتية")){N(I.message),o({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,maxUsers:100,seats:[],waitingQueue:[],listeners:[],settings:{},isActive:!1}),a([]);return}N(I.message||"خطأ في تحميل الغرفة الصوتية")}finally{u(!1),w(!1)}};x.useEffect(()=>(Se.current=new R0(t),Se.current.onRemoteStreamAdded=(v,I)=>{const W=new Audio;W.srcObject=I,W.autoplay=!0,W.muted=Rt,sn.current.set(v,W)},Se.current.onRemoteStreamRemoved=v=>{const I=sn.current.get(v);I&&(I.pause(),I.srcObject=null,sn.current.delete(v))},Se.current.onVoiceActivity=v=>{o(I=>({...I,seats:I.seats.map(W=>{var H;return((H=W.user)==null?void 0:H._id)===e.id?{...W,isSpeaking:v.isSpeaking}:W})})),j&&t.send({type:"voice_activity",data:{userId:e.id,username:e.username,role:e.role,isAdmin:e.isAdmin,level:v.level,isSpeaking:v.isSpeaking,isMuted:M,seatNumber:S}})},()=>{var v;(v=Se.current)==null||v.cleanup()}),[t,e.id]),x.useEffect(()=>{const v=B=>{const Ce={...B,sender:{...B.sender,role:B.senderRole||B.sender.role||(B.sender.isAdmin?"admin":"member"),isAdmin:B.senderIsAdmin||B.sender.isAdmin||!1,gender:B.senderGender||B.sender.gender||"male"},textColor:B.textColor||"#ffffff"};B.sender._id!==e.id&&(a(re=>[...re,Ce]),setTimeout(()=>{var re;(re=An.current)==null||re.scrollIntoView({behavior:"smooth"})},100))},I=B=>{dr(!1).then(()=>{B.action==="seat_joined"&&j&&B.userId!==e.id&&setTimeout(()=>{var Ce;(Ce=Se.current)==null||Ce.sendOffer(B.userId)},1e3)})},W=B=>{const{action:Ce,targetUserId:re,adminId:Bl,message:Nt}=B;fr(Ce,re),re===e.id&&(N(Nt||`تم تطبيق إجراء إداري: ${Ce}`),Ce==="kick"&&setTimeout(()=>{window.location.reload()},2e3))},H=B=>{const{userId:Ce,isSpeaking:re,role:Bl,isAdmin:Nt,isMuted:Vl,seatNumber:wa}=B;o(pr=>({...pr,seats:pr.seats.map(zt=>{var gr;return((gr=zt.user)==null?void 0:gr._id)===Ce?{...zt,isSpeaking:re,isMuted:Vl,user:{...zt.user,role:Bl||zt.user.role,isAdmin:Nt||zt.user.isAdmin}}:zt})}))};return t.onMessage("voice_room_message",v),t.onMessage("voice_room_update",I),t.onMessage("admin_action_update",W),t.onMessage("voice_activity",H),()=>{t.offMessage("voice_room_message",v),t.offMessage("voice_room_update",I),t.offMessage("admin_action_update",W)}},[t,j,e.id]),x.useEffect(()=>{var v;(v=An.current)==null||v.scrollIntoView({behavior:"smooth"})},[i]),x.useEffect(()=>{dr(!0),console.log("User role:",e.role),console.log("User isAdmin:",e.isAdmin),console.log("User object:",e)},[]),x.useEffect(()=>{const v=()=>{const H=window.innerHeight<window.screen.height*.75;O(H)},I=()=>O(!0),W=()=>O(!1);return window.addEventListener("resize",v),je.current&&(je.current.addEventListener("focus",I),je.current.addEventListener("blur",W)),()=>{window.removeEventListener("resize",v),je.current&&(je.current.removeEventListener("focus",I),je.current.removeEventListener("blur",W))}},[]),x.useEffect(()=>{const v=I=>{Z&&ne(null),h&&C(!1),T&&$(!1)};return document.addEventListener("click",v),()=>document.removeEventListener("click",v)},[Z,h,T]),x.useEffect(()=>{const v=W=>{if(j)return W.preventDefault(),W.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},I=()=>{j&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",v),window.addEventListener("unload",I),()=>{window.removeEventListener("beforeunload",v),window.removeEventListener("unload",I)}},[j,e.id]);const _n=async v=>{try{const I=await X.sendVoiceRoomMessage(v),W={_id:I.messageData._id,sender:{_id:e.id,username:e.username,role:e.role,isAdmin:e.isAdmin,gender:e.gender},content:v,timestamp:new Date().toISOString(),messageType:"text",textColor:L};a(H=>[...H,W]),t.send({type:"voice_room_message",data:{...I.messageData,textColor:L,senderRole:e.role,senderIsAdmin:e.isAdmin,senderGender:e.gender}})}catch(I){console.error("Error sending message:",I),N(I.message||"خطأ في إرسال الرسالة")}},ur=async()=>{try{z(!0),await X.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}})}catch(v){console.error("Error requesting mic:",v),N(v.message||"خطأ في طلب المايك")}finally{z(!1)}},he=async v=>{try{if(z(!0),await X.joinVoiceSeat(v),k(!0),m(v),P(!1),Se.current&&(e!=null&&e.id)){console.log("🎤 Starting WebRTC voice chat for user:",e.username);const I=`voice-room-${(l==null?void 0:l.id)||"default"}`;await Se.current.joinRoom(I,e.id),console.log("✅ WebRTC voice chat started successfully")}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",v.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:v}})}catch(I){console.error("Error joining seat:",I),N(I.message||"خطأ في الانضمام للمقعد")}finally{z(!1)}},ht=async()=>{var v;try{await X.leaveVoiceSeat(),k(!1),m(null),P(!1),Se.current&&Se.current.leaveRoom(),localStorage.removeItem("isInVoiceRoom"),localStorage.removeItem("voiceRoomSeat"),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:S}})}catch(I){console.error("Error leaving seat:",I),(v=I.message)!=null&&v.includes("لست في أي مقعد")||N(I.message||"خطأ في مغادرة المقعد")}},ln=async()=>{try{if(!j){N("يجب أن تكون في مقعد لاستخدام المايك");return}if(!Se.current){N("خدمة الصوت غير متاحة - جاري إعادة الاتصال..."),await initializeWebRTC();return}const v=!M;Se.current.setMute(v),P(v);try{await X.toggleMute(v)}catch(I){console.warn("Failed to update server mute state:",I)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:v}})}catch(v){console.error("Error toggling mute:",v),N("خطأ في تبديل كتم المايك"),P(!M)}},fs=()=>{try{const v=!Rt;st(v),sn.current.forEach(I=>{I.muted=v}),Se.current&&Se.current.setRemoteAudioMuted(v),localStorage.setItem("soundMuted",v.toString())}catch(v){console.error("Error toggling sound:",v),N("خطأ في تبديل كتم الصوت")}},hs=v=>{const I=v.target.value;if(D(I),I.length>=2){const W=Zc.getSuggestions(I);fe(W),ie(W.length>0)}else ie(!1)},ps=v=>{var I;D(v),ie(!1),(I=je.current)==null||I.focus()},bt=v=>{var I;D(W=>W+v),C(!1),(I=je.current)==null||I.focus()},mr=async v=>{if(v.preventDefault(),!U.trim())return;const I=U.trim();D(""),ie(!1),Zc.addMessage(I);try{await _n(I)}catch{D(I)}},lt=async(v,I,W)=>{try{let H;switch(v){case"kick":H=await X.kickUserFromVoiceRoom(I,W);break;case"mute":H=await X.muteUserInVoiceRoom(I);break;case"unmute":H=await X.unmuteUserInVoiceRoom(I);break;case"removeSeat":H=await X.removeUserFromSeat(I);break;case"removeQueue":H=await X.removeUserFromQueue(I);break;case"banChat":H=await X.banUserFromChat(I);break;case"unbanChat":H=await X.unbanUserFromChat(I);break}ne(null),Pe(null),fr(v,I),t.send({type:"admin_action_update",data:{action:v,targetUserId:I,adminId:e.id,duration:W,message:H==null?void 0:H.message}})}catch(H){console.error("Error performing admin action:",H),N(H.message||"خطأ في تنفيذ الإجراء الإداري")}},fr=(v,I)=>{o(W=>{if(!W)return W;const H={...W};switch(v){case"kick":case"removeSeat":H.seats=H.seats.map(B=>B.user&&B.user._id===I?{...B,user:null,isMuted:!1}:B),H.waitingQueue=H.waitingQueue.filter(B=>B.user._id!==I);break;case"removeQueue":H.waitingQueue=H.waitingQueue.filter(B=>B.user._id!==I);break;case"mute":H.seats=H.seats.map(B=>B.user&&B.user._id===I?{...B,isMuted:!0}:B);break;case"unmute":H.seats=H.seats.map(B=>B.user&&B.user._id===I?{...B,isMuted:!1}:B);break;case"banChat":H.seats=H.seats.map(B=>B.user&&B.user._id===I?{...B,user:{...B.user,isChatBanned:!0}}:B),H.waitingQueue=H.waitingQueue.map(B=>B.user._id===I?{...B,user:{...B.user,isChatBanned:!0}}:B);break;case"unbanChat":H.seats=H.seats.map(B=>B.user&&B.user._id===I?{...B,user:{...B.user,isChatBanned:!1}}:B),H.waitingQueue=H.waitingQueue.map(B=>B.user._id===I?{...B,user:{...B.user,isChatBanned:!1}}:B);break}return H})},hr=async()=>{const v=parseInt($e);await lt("kick",We,v)},jt=v=>{In(v),$l(!0)},ce=()=>{In(null),$l(!1)};x.useEffect(()=>{const v=I=>{I.key==="Escape"&&ar&&ce()};return document.addEventListener("keydown",v),()=>{document.removeEventListener("keydown",v)}},[ar]),x.useEffect(()=>{(async()=>{try{const W=await navigator.permissions.query({name:"microphone"});rn(W.state),W.addEventListener("change",()=>{rn(W.state)})}catch{console.log("Permission API not supported")}})(),localStorage.getItem("soundMuted")==="true"&&st(!0)},[]);const Fl=v=>new Date(v).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1});if(c)return r.jsx("div",{className:"flex items-center justify-center h-full",children:r.jsxs("div",{className:"text-center",children:[r.jsx(vn,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),r.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})});if(b){const v=b.includes("مطرود من الغرفة الصوتية");return r.jsx("div",{className:"p-4",children:r.jsxs("div",{className:`border rounded-lg p-6 text-center max-w-md mx-auto ${v?"bg-orange-900/20 border-orange-500/30":"bg-red-900/20 border-red-500/30"}`,children:[r.jsx(h0,{className:`w-12 h-12 mx-auto mb-4 ${v?"text-orange-400":"text-red-400"}`}),v?r.jsxs(r.Fragment,{children:[r.jsx("h3",{className:"text-orange-400 font-bold text-lg mb-2",children:"تم طردك من الغرفة الصوتية"}),r.jsx("p",{className:"text-orange-300 mb-4 text-sm leading-relaxed",children:b}),r.jsx("p",{className:"text-gray-400 text-xs",children:"سيتم السماح لك بالدخول مرة أخرى بعد انتهاء المدة المحددة"})]}):r.jsxs(r.Fragment,{children:[r.jsx("p",{className:"text-red-400 mb-4",children:b}),r.jsx("button",{onClick:dr,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white",children:"إعادة المحاولة"})]})]})})}return l?r.jsxs("div",{className:"h-screen flex flex-col bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white overflow-hidden",children:[r.jsxs("div",{className:"bg-black/30 backdrop-blur-sm border-b border-white/10 p-2 flex-shrink-0",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:s,className:"p-1.5 hover:bg-white/10 rounded-lg transition-colors",children:r.jsx(zh,{className:"w-4 h-4"})}),r.jsxs("div",{children:[r.jsxs("h1",{className:"text-sm font-bold flex items-center gap-1",children:[r.jsx(is,{className:"w-3 h-3 text-purple-400"}),"INFINITY ROOM"]}),r.jsx("p",{className:"text-xs text-gray-300",children:"غرفة صوتية للمحادثة"})]})]}),r.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[g&&r.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-1"}),r.jsx(Cn,{className:"w-3 h-3 text-gray-400"}),r.jsxs("span",{className:"text-gray-300",children:[((on=l.seats)==null?void 0:on.filter(v=>v.user).length)||0,"/5"]}),(e.role==="admin"||e.isAdmin)&&r.jsx("span",{className:"bg-red-600 text-white px-1.5 py-0.5 rounded text-xs ml-1",children:"ADMIN"})]})]}),j&&r.jsxs("div",{className:"bg-black/20 backdrop-blur-sm rounded-lg p-2 mt-2",children:[r.jsxs("div",{className:"flex items-center gap-1 mb-2",children:[r.jsx("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"}),r.jsxs("span",{className:"text-xs text-green-400 font-medium",children:["مقعد ",S]})]}),r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsxs("button",{onClick:ln,className:`flex-1 py-1.5 px-2 rounded-md transition-colors flex items-center justify-center gap-1 text-xs font-medium ${M?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,children:[M?r.jsx(ls,{className:"w-3 h-3"}):r.jsx(yt,{className:"w-3 h-3"}),r.jsx("span",{children:M?"إلغاء كتم":"كتم"})]}),r.jsxs("button",{onClick:fs,className:`flex-1 py-1.5 px-2 rounded-md transition-colors flex items-center justify-center gap-1 text-xs font-medium ${Rt?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:[Rt?r.jsx(xo,{className:"w-3 h-3"}):r.jsx(is,{className:"w-3 h-3"}),r.jsx("span",{children:Rt?"تشغيل":"صامت"})]}),r.jsx("button",{onClick:ht,className:"px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded-md text-white transition-colors text-xs font-medium",children:"مغادرة"})]})]}),r.jsxs("div",{className:"flex items-center gap-3 p-2 bg-gray-800/30 rounded-lg mb-3",children:[r.jsx("div",{className:"flex items-center gap-2",children:r.jsx("div",{className:`w-2 h-2 rounded-full ${cr==="granted"?"bg-green-500":cr==="denied"?"bg-red-500":"bg-yellow-500"}`})}),cr==="denied"&&r.jsx("span",{className:"text-xs text-red-400",children:"مرفوض"})]})]}),r.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[r.jsxs("div",{className:"p-2 border-b border-gray-700/50 flex-shrink-0",children:[r.jsx("div",{className:"flex justify-center gap-1.5 mb-1 overflow-x-auto px-1",children:l.seats.map(v=>r.jsx("div",{className:"flex flex-col items-center flex-shrink-0",children:v.user?r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:`relative w-12 h-12 rounded-full p-0.5 ${v.isSpeaking&&!v.isMuted?"bg-gradient-to-r from-green-400 to-green-500 shadow-md shadow-green-500/50 animate-pulse":v.user._id===e.id?"bg-gradient-to-r from-green-500 to-green-600":v.user.role==="admin"||v.user.isAdmin?"bg-gradient-to-r from-red-500 to-red-600 shadow-md shadow-red-500/50":"bg-gradient-to-r from-blue-500 to-purple-600"} shadow-md`,children:[r.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:v.user.profileImage?r.jsx("img",{src:v.user.profileImage,alt:v.user.username,className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-lg",children:v.user.username.charAt(0).toUpperCase()})})}),r.jsx("div",{className:`absolute -bottom-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center shadow-lg ${v.isMuted?"bg-red-600":v.isSpeaking?"bg-green-500 animate-pulse shadow-green-500/50":"bg-green-600"}`,children:v.isMuted?r.jsx(ls,{className:"w-3 h-3 text-white"}):r.jsx(yt,{className:`w-3 h-3 text-white ${v.isSpeaking?"animate-pulse":""}`})}),v.isSpeaking&&!v.isMuted&&r.jsxs("div",{className:"absolute inset-0 rounded-full",children:[r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),r.jsxs("div",{className:"text-center mt-1 relative",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1",children:[r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("h3",{className:"font-medium text-white text-xs mb-1 truncate max-w-12",children:v.user.username}),r.jsxs("div",{className:"flex gap-1",children:[v.isMuted&&r.jsx("div",{className:"bg-red-600 rounded-full p-1",title:"مكتوم",children:r.jsx(xo,{className:"w-2 h-2 text-white"})}),v.user.isChatBanned&&r.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:r.jsx(Rs,{className:"w-2 h-2 text-white"})})]})]}),(e.role==="admin"||e.isAdmin)&&v.user._id!==e.id&&r.jsx("button",{onClick:I=>{I.stopPropagation(),console.log("Admin menu clicked for user:",v.user.username),ne(Z===v.user._id?null:v.user._id)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:r.jsx(Fc,{className:"w-4 h-4"})})]}),Z===v.user._id&&(e.role==="admin"||e.isAdmin)&&r.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:r.jsxs("div",{className:"flex flex-col gap-1",children:[r.jsxs("button",{onClick:()=>lt("removeSeat",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[r.jsx(Ph,{className:"w-3 h-3"}),"إنزال"]}),v.isMuted?r.jsxs("button",{onClick:()=>lt("unmute",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Kh,{className:"w-3 h-3"}),"إلغاء كتم"]}):r.jsxs("button",{onClick:()=>lt("mute",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-yellow-400 hover:bg-yellow-900/30 rounded transition-colors",children:[r.jsx(xo,{className:"w-3 h-3"}),"كتم"]}),v.user.isChatBanned?r.jsxs("button",{onClick:()=>lt("unbanChat",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Bc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):r.jsxs("button",{onClick:()=>lt("banChat",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[r.jsx(Rs,{className:"w-3 h-3"}),"منع الكتابة"]}),r.jsxs("button",{onClick:()=>{ne(null),Pe(v.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[r.jsx(wi,{className:"w-3 h-3"}),"طرد"]})]})})]})]}):r.jsx("div",{className:"flex flex-col items-center",children:r.jsx("button",{onClick:()=>!j&&!d?he(v.seatNumber):null,disabled:R||j||d,className:"relative w-14 h-14 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:r.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center",children:r.jsx(Ul,{className:"w-5 h-5 text-gray-400"})})})})},v.seatNumber))}),!j&&!d&&l.seats.every(v=>v.user)&&r.jsxs("button",{onClick:ur,disabled:R,className:"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded-lg text-white font-medium transition-colors flex items-center justify-center gap-2 mb-4",children:[r.jsx(yt,{className:"w-4 h-4"}),R?"جاري الطلب...":"طلب المايك"]}),l.waitingQueue.length>0&&r.jsxs("div",{className:"bg-yellow-900/20 rounded-lg p-2 border border-yellow-500/20",children:[r.jsxs("h3",{className:"text-sm font-bold text-white mb-2 flex items-center gap-2",children:[r.jsx(xn,{className:"w-4 h-4 text-yellow-400"}),"قائمة الانتظار (",l.waitingQueue.length,")"]}),r.jsx("div",{className:"space-y-2",children:l.waitingQueue.map((v,I)=>r.jsxs("div",{className:"flex items-center gap-2 p-2 bg-gray-800/50 rounded text-sm relative",children:[r.jsx("div",{className:"w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-xs",children:I+1}),r.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[r.jsx("span",{className:"text-white",children:v.user.username}),r.jsx("div",{className:"flex gap-1",children:v.user.isChatBanned&&r.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:r.jsx(Rs,{className:"w-2 h-2 text-white"})})})]}),v.user._id===e.id&&r.jsx("span",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"}),(e.role==="admin"||e.isAdmin)&&v.user._id!==e.id&&r.jsx("button",{onClick:W=>{W.stopPropagation(),console.log("Admin menu clicked for queue user:",v.user.username),ne(Z===`queue_${v.user._id}`?null:`queue_${v.user._id}`)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:r.jsx(Fc,{className:"w-4 h-4"})}),Z===`queue_${v.user._id}`&&(e.role==="admin"||e.isAdmin)&&r.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:r.jsxs("div",{className:"flex flex-col gap-1",children:[r.jsxs("button",{onClick:()=>lt("removeQueue",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[r.jsx(N0,{className:"w-3 h-3"}),"إزالة"]}),v.user.isChatBanned?r.jsxs("button",{onClick:()=>lt("unbanChat",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Bc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):r.jsxs("button",{onClick:()=>lt("banChat",v.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[r.jsx(Rs,{className:"w-3 h-3"}),"منع الكتابة"]}),r.jsxs("button",{onClick:()=>{ne(null),Pe(v.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[r.jsx(wi,{className:"w-3 h-3"}),"طرد"]})]})})]},v.user._id))})]})]}),r.jsxs("div",{className:"h-96 flex flex-col",children:[r.jsx("div",{className:"flex-1 overflow-y-auto p-2",children:r.jsxs("div",{className:"flex flex-col space-y-1",children:[i.length===0?r.jsxs("div",{className:"text-center text-gray-400 py-4",children:[r.jsx(yn,{className:"w-6 h-6 mx-auto mb-2 opacity-50"}),r.jsx("p",{className:"text-xs",children:"لا توجد رسائل بعد"})]}):i.map(v=>{const I=H=>H.gender==="female"?"text-pink-400":H.gender==="male"?"text-blue-400":["فاطمة","عائشة","خديجة","زينب","مريم","سارة","نور","هند","ليلى","أمل","رنا","دانا","لينا","ريم","نادية","سلمى","ياسمين","روان","جنى","تالا"].some(re=>H.username.includes(re))?"text-pink-400":"text-blue-400",W=()=>v.messageType==="system"?"bg-blue-900/30 border border-blue-500/30 text-blue-200 ml-auto max-w-[85%]":v.sender.role==="admin"||v.sender.isAdmin?"bg-red-900/60 border-2 border-red-400/50 text-white ml-auto max-w-[75%] shadow-xl shadow-red-500/30 ring-1 ring-red-400/20":v.sender._id===e.id?"bg-purple-900/50 border border-purple-500/30 text-white ml-auto max-w-[75%]":"bg-gray-800/50 border border-gray-600/30 text-gray-200 ml-auto max-w-[75%]";return r.jsxs("div",{className:`px-3 py-1.5 rounded-xl text-sm ${W()}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-0.5 gap-2",children:[r.jsxs("span",{className:`font-medium text-xs flex-shrink-0 ${v.sender.role==="admin"||v.sender.isAdmin?"text-red-300 font-bold":I(v.sender)}`,children:[v.sender.role==="admin"||v.sender.isAdmin?"👑 ":"",v.sender.username]}),r.jsx("span",{className:"text-xs opacity-60 flex-shrink-0",children:Fl(v.timestamp)})]}),r.jsx("div",{className:"text-sm leading-snug",style:{color:v.textColor||"#ffffff"},children:v.content})]},v._id)}),r.jsx("div",{ref:An})]})}),r.jsx("div",{className:"px-4 py-2 border-t border-gray-700/50 bg-gray-900/30",children:r.jsxs("div",{className:"flex justify-center gap-4",children:[r.jsxs("button",{onClick:()=>jt("/speed-challenge.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"تحدي السرعة",children:[r.jsx(S0,{className:"w-6 h-6 text-yellow-400 group-hover:text-yellow-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"سرعة"})]}),r.jsxs("button",{onClick:()=>jt("/game8.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"صناديق الحظ",children:[r.jsx(j0,{className:"w-6 h-6 text-green-400 group-hover:text-green-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"صناديق"})]}),r.jsxs("button",{onClick:()=>jt("/mind-puzzles.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"ألغاز العقل",children:[r.jsx(w0,{className:"w-6 h-6 text-blue-400 group-hover:text-blue-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ألغاز"})]}),r.jsxs("button",{onClick:()=>jt("/fruit-catching.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"قطف الفواكه",children:[r.jsx(u0,{className:"w-6 h-6 text-red-400 group-hover:text-red-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"فواكه"})]}),r.jsxs("button",{onClick:()=>jt("/memory-match.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الذاكرة",children:[r.jsx(m0,{className:"w-6 h-6 text-purple-400 group-hover:text-purple-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ذاكرة"})]}),r.jsxs("button",{onClick:()=>jt("/forest-game.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الغابة",children:[r.jsx(k0,{className:"w-6 h-6 text-green-600 group-hover:text-green-500"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"غابة"})]})]})}),r.jsx("div",{className:"p-4 mb-4 border-t border-gray-700/50 bg-gray-900/50",children:r.jsxs("div",{className:"relative",children:[r.jsx($p,{suggestions:Y,onSuggestionSelect:ps,isVisible:ge}),h&&r.jsx(Up,{onEmojiSelect:bt,onClose:()=>C(!1)}),T&&r.jsxs("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-gray-800/95 backdrop-blur-sm border border-gray-600/50 rounded-lg p-3 shadow-xl",children:[r.jsx("div",{className:"text-xs text-gray-300 mb-2 font-medium",children:"اختر لون النص:"}),r.jsx("div",{className:"grid grid-cols-5 gap-2",children:ft.map(v=>r.jsx("button",{type:"button",onClick:I=>{I.preventDefault(),I.stopPropagation(),G(v.value),$(!1)},className:`w-8 h-8 rounded-full border-2 transition-all hover:scale-110 ${L===v.value?"border-white shadow-lg":"border-gray-500 hover:border-gray-300"}`,style:{backgroundColor:v.value},title:v.name,children:L===v.value&&r.jsx("div",{className:"w-full h-full rounded-full flex items-center justify-center",children:r.jsx("div",{className:"w-2 h-2 bg-black rounded-full opacity-50"})})},v.value))})]}),r.jsxs("form",{onSubmit:mr,className:"flex gap-2",children:[r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("input",{ref:je,type:"text",value:U,onChange:hs,placeholder:"اكتب رسالتك...",maxLength:500,style:{color:L},className:"w-full px-3 py-2 pr-16 bg-gray-800/50 border border-gray-600/50 rounded-lg placeholder-gray-400 focus:outline-none focus:border-purple-500/50 text-sm",onFocus:()=>ie(Y.length>0),onBlur:()=>setTimeout(()=>ie(!1),200)}),r.jsx("button",{type:"button",onClick:v=>{v.preventDefault(),v.stopPropagation(),$(!T),C(!1)},className:"absolute left-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-400 transition-colors",children:r.jsx("div",{className:"w-4 h-4 rounded-full border-2 border-gray-400",style:{backgroundColor:L}})}),r.jsx("button",{type:"button",onClick:v=>{v.preventDefault(),v.stopPropagation(),C(!h),$(!1)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-yellow-400 transition-colors",children:r.jsx(b0,{className:"w-4 h-4"})})]}),r.jsx("button",{type:"submit",disabled:!U.trim(),className:"px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-lg text-white transition-colors",children:r.jsx(Fr,{className:"w-4 h-4"})}),!d&&r.jsx("button",{type:"button",onClick:ur,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors",children:r.jsx(yt,{className:"w-4 h-4"})})]})]})})]})]}),We&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg border border-red-500 p-6 w-full max-w-sm",children:[r.jsx("h3",{className:"text-white font-bold text-lg mb-4 text-center",children:"اختر مدة الطرد"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-gray-300 text-sm mb-2",children:"المدة:"}),r.jsxs("select",{value:$e,onChange:v=>be(v.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500",children:[r.jsx("option",{value:"30",children:"30 دقيقة"}),r.jsx("option",{value:"60",children:"ساعة واحدة"}),r.jsx("option",{value:"180",children:"3 ساعات"}),r.jsx("option",{value:"360",children:"6 ساعات"}),r.jsx("option",{value:"720",children:"12 ساعة"}),r.jsx("option",{value:"1440",children:"يوم واحد"}),r.jsx("option",{value:"4320",children:"3 أيام"}),r.jsx("option",{value:"10080",children:"أسبوع واحد"}),r.jsx("option",{value:"43200",children:"شهر واحد"}),r.jsx("option",{value:"525600",children:"سنة واحدة"})]})]}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>Pe(null),className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"إلغاء"}),r.jsx("button",{onClick:hr,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors",children:"طرد"})]})]})]})}),ar&&Pt&&r.jsxs("div",{className:"absolute inset-0 bg-gray-900/95 backdrop-blur-sm z-50 flex flex-col",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-800/90 border-b border-gray-700",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),r.jsx("span",{className:"text-white font-medium",children:"اللعبة نشطة"})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:()=>{Pt&&window.open(Pt,"_blank")},className:"px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors",children:"فتح في نافذة جديدة"}),r.jsx("button",{onClick:ce,className:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",title:"إغلاق اللعبة",children:r.jsx(xa,{className:"w-5 h-5"})})]})]}),r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("iframe",{src:Pt,className:"w-full h-full border-0",title:"لعبة مدمجة",allow:"fullscreen; autoplay; microphone; camera",sandbox:"allow-scripts allow-same-origin allow-forms allow-popups allow-modals"}),r.jsx("div",{className:"absolute inset-0 bg-gray-900 flex items-center justify-center pointer-events-none opacity-0 transition-opacity duration-300",id:"game-loading",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-white",children:"جاري تحميل اللعبة..."})]})})]}),r.jsx("div",{className:"p-3 bg-gray-800/90 border-t border-gray-700",children:r.jsxs("div",{className:"flex items-center justify-between text-sm",children:[r.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[r.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),r.jsx("span",{children:"يمكنك الاستمرار في الدردشة أثناء اللعب"})]}),r.jsx("div",{className:"text-gray-500",children:"اضغط ESC للخروج السريع"})]})})]})]}):r.jsx("div",{className:"p-4 text-center text-gray-400",children:r.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},Bp=({userData:e,onLogout:t,onUpdateProfile:n,wsService:s})=>{const[l,o]=x.useState("games"),[i,a]=x.useState(e),[c,u]=x.useState(["games"]);x.useEffect(()=>{e&&(console.log("🔄 MobileDashboard: userData updated from parent:",e),a(e))},[e]);const g=S=>{console.log("🔄 MobileDashboard: Updating profile data:",S);const m={...i,...S};a(m),n&&n(m)};x.useEffect(()=>{a(e)},[e]);const w=S=>{console.log("🔄 Navigating to tab:",S),S!==l&&requestAnimationFrame(()=>{u(m=>[...m,S]),o(S),console.log("✅ Tab changed to:",S)})},b=()=>{if(c.length>1){const S=[...c];S.pop();const m=S[S.length-1];u(S),o(m)}},N=c.length>1,j=()=>{switch(l){case"games":return"مركز الألعاب";case"leaderboard":return"لوحة المتصدرين";case"voice":return"الغرفة الصوتية";case"profile":return"الملف الشخصي";case"admin":return"لوحة الإدارة";default:return"INFINITY BOX"}},k=()=>{switch(l){case"games":return r.jsx(Js,{setActiveTab:w});case"leaderboard":return r.jsx(Vp,{});case"voice":return s?r.jsx(Fp,{user:i,wsService:s,onBack:()=>w("games")}):r.jsx("div",{className:"p-4 text-center text-red-400",children:"خدمة WebSocket غير متاحة"});case"profile":return r.jsx(zp,{userData:i,onUpdateProfile:g,onLogout:t,isOwner:!0});case"admin":return e!=null&&e.isAdmin?r.jsx(bi,{userData:e,onLogout:t}):r.jsx(Js,{setActiveTab:w});default:return r.jsx(Js,{setActiveTab:w})}};return l==="admin"&&(e!=null&&e.isAdmin)?r.jsx(bi,{userData:e,onLogout:t}):r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-950 via-purple-950 to-slate-900 text-white flex flex-col relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute -top-24 -left-24 w-72 h-72 bg-green-800 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"}),r.jsx("div",{className:"absolute -bottom-24 -right-24 w-72 h-72 bg-red-900 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"}),r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((S,m)=>r.jsx("div",{className:`${m%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${m*150}ms`,animationDuration:`${2+m%3}s`}},m))})}),r.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-20",children:r.jsx("div",{className:"text-[18rem] md:text-[24rem] font-black text-cyan-500 animate-spin",style:{animationDuration:"40s"},children:"∞"})})]}),r.jsxs("div",{className:"relative z-10 flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-b border-purple-400/30 sticky top-0 z-40",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[N&&r.jsx("button",{onClick:b,className:"p-2 rounded-lg hover:bg-slate-700/50 transition-colors",children:r.jsx(f0,{className:"w-5 h-5 text-white"})}),r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:(i==null?void 0:i.profileImage)||"/images/default-avatar.png",alt:i==null?void 0:i.username,className:"w-9 h-9 rounded-full border-2 border-white/30 object-cover",onError:S=>{S.currentTarget.src="/images/default-avatar.png"}}),r.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),r.jsx("div",{children:r.jsx("h1",{className:"text-lg font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:l==="games"?(i==null?void 0:i.username)||"اللاعب":j()})})]}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[r.jsx(zl,{className:"w-5 h-5 text-yellow-300"}),r.jsx("span",{className:"text-yellow-300 text-sm font-bold",children:(i==null?void 0:i.goldCoins)||0})]}),r.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[r.jsx(hn,{className:"w-5 h-5 text-emerald-300"}),r.jsx("span",{className:"text-emerald-300 text-sm font-bold",children:(i==null?void 0:i.pearls)||0})]}),r.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-red-600/20 transition-colors",children:r.jsx(y0,{className:"w-5 h-5 text-red-400"})})]})]}),r.jsx("div",{className:"relative z-10 flex-1 overflow-hidden",children:k()}),r.jsx("div",{className:"relative z-10 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-t border-purple-400/30 px-2 py-2 sticky bottom-0 z-40",children:r.jsxs("div",{className:"flex items-center justify-around",children:[r.jsxs("button",{onClick:()=>w("games"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="games"?"bg-cyan-700/40 text-cyan-300":"text-gray-400 hover:text-cyan-200 hover:bg-slate-700/50"}`,children:[r.jsx(ga,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الألعاب"})]}),r.jsxs("button",{onClick:()=>w("voice"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="voice"?"bg-purple-700/40 text-purple-300":"text-gray-400 hover:text-purple-200 hover:bg-slate-700/50"}`,children:[r.jsx(is,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الغرفة الصوتية"})]}),r.jsxs("button",{onClick:()=>w("leaderboard"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="leaderboard"?"bg-amber-700/40 text-amber-300":"text-gray-400 hover:text-amber-200 hover:bg-slate-700/50"}`,children:[r.jsx(os,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"المتصدرين"})]}),r.jsxs("button",{onClick:()=>w("profile"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="profile"?"bg-emerald-700/40 text-emerald-300":"text-gray-400 hover:text-emerald-200 hover:bg-slate-700/50"}`,children:[r.jsx(wn,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الملف الشخصي"})]}),(e==null?void 0:e.isAdmin)&&r.jsxs("button",{onClick:()=>w("admin"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="admin"?"bg-rose-700/40 text-rose-300":"text-gray-400 hover:text-rose-200 hover:bg-slate-700/50"}`,children:[r.jsx(Ur,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الإدارة"})]})]})})]})},Vp=()=>r.jsx("div",{className:"p-4 h-full overflow-y-auto",children:r.jsxs("div",{className:"text-center py-20",children:[r.jsx(os,{className:"w-16 h-16 text-yellow-400 mx-auto mb-4"}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"لوحة المتصدرين"}),r.jsx("p",{className:"text-gray-400",children:"قريباً..."})]})}),qp=({seats:e,waitingQueue:t,currentUser:n,isInSeat:s,currentSeatNumber:l,isInWaitingQueue:o,isConnecting:i,onJoinSeat:a,onRequestMic:c,onCancelMicRequest:u})=>{const g=j=>{const k=new Date,S=new Date(j),m=k.getTime()-S.getTime(),d=Math.floor(m/(1e3*60));return d<1?"الآن":d<60?`${d} دقيقة`:`${Math.floor(d/60)} ساعة`},w=()=>{const j=t.findIndex(k=>k.user._id===n.id);return j>=0?j+1:0},b=e.filter(j=>!j.user),N=b.length>0&&!s&&!o;return r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-purple-900/30 rounded-xl p-6 border border-purple-500/20",children:[r.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(is,{className:"w-6 h-6 text-purple-400"}),"المقاعد الصوتية"]}),r.jsx("div",{className:"flex flex-wrap justify-center gap-6",children:e.map(j=>r.jsx("div",{className:"flex flex-col items-center",children:j.user?r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:`relative w-20 h-20 rounded-full p-1 transition-all duration-300 shadow-lg ${j.user._id===n.id?"bg-gradient-to-r from-green-500 to-green-600 shadow-green-500/30":"bg-gradient-to-r from-blue-500 to-purple-600 shadow-blue-500/30"}`,children:[r.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:j.user.profileImage?r.jsx("img",{src:j.user.profileImage,alt:j.user.username,className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-xl",children:j.user.username.charAt(0).toUpperCase()})})}),r.jsx("div",{className:`absolute -bottom-1 -right-1 w-7 h-7 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${j.isMuted?"bg-red-600":j.isSpeaking?"bg-green-600 animate-pulse shadow-green-500/50":"bg-gray-600"}`,children:j.isMuted?r.jsx(ls,{className:"w-4 h-4 text-white"}):r.jsx(yt,{className:"w-4 h-4 text-white"})}),j.isSpeaking&&!j.isMuted&&r.jsxs("div",{className:"absolute inset-0 rounded-full",children:[r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),r.jsxs("div",{className:"text-center mt-3 max-w-20",children:[r.jsx("h3",{className:"font-semibold text-white text-sm mb-1 truncate",children:j.user.username}),r.jsxs("p",{className:"text-xs text-gray-400 mb-2",children:["#",j.user.playerId]}),j.joinedAt&&r.jsxs("div",{className:"flex items-center justify-center gap-1 text-xs text-gray-400",children:[r.jsx(xn,{className:"w-3 h-3"}),r.jsx("span",{children:g(j.joinedAt)})]})]})]}):r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("button",{onClick:()=>N?a(j.seatNumber):null,disabled:i||!N,className:"relative w-20 h-20 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:r.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center hover:border-purple-400 transition-colors",children:r.jsx(Ul,{className:"w-8 h-8 text-gray-400"})})}),r.jsx("div",{className:"text-center mt-3",children:r.jsx("p",{className:"text-gray-400 text-sm",children:"مقعد فارغ"})})]})},j.seatNumber))}),r.jsxs("div",{className:"mt-6 flex flex-wrap gap-3 justify-center",children:[!s&&!o&&b.length===0&&r.jsxs("button",{onClick:c,disabled:i,className:"px-6 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white font-medium transition-colors flex items-center gap-2",children:[r.jsx(yt,{className:"w-4 h-4"}),i?"جاري الطلب...":"طلب المايك"]}),o&&r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"px-4 py-2 bg-yellow-900/50 border border-yellow-500/50 rounded-lg text-yellow-300 flex items-center gap-2",children:[r.jsx(xn,{className:"w-4 h-4"}),r.jsxs("span",{children:["في قائمة الانتظار (المركز ",w(),")"]})]}),r.jsx("button",{onClick:u,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors",children:"إلغاء الطلب"})]})]})]}),t.length>0&&r.jsxs("div",{className:"bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-xl p-6 border border-yellow-500/20",children:[r.jsxs("h3",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(xn,{className:"w-5 h-5 text-yellow-400"}),"قائمة انتظار المايك (",t.length,")"]}),r.jsx("div",{className:"space-y-3",children:t.map((j,k)=>r.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",children:[r.jsx("div",{className:"w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:k+1}),r.jsxs("div",{className:"flex-1",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"font-medium text-white",children:j.user.username}),r.jsxs("span",{className:"text-xs text-gray-400",children:["#",j.user.playerId]})]}),r.jsxs("div",{className:"text-xs text-gray-400",children:["طلب منذ ",g(j.requestedAt)]})]}),j.user._id===n.id&&r.jsx("div",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"})]},j.user._id))})]})]})},Hp=({messages:e,currentUser:t,isInWaitingQueue:n,onSendMessage:s,onRequestMic:l})=>{const[o,i]=x.useState(""),[a,c]=x.useState(!1),u=x.useRef(null),g=x.useRef(null);x.useEffect(()=>{var S;(S=u.current)==null||S.scrollIntoView({behavior:"smooth"})},[e]),x.useEffect(()=>{var S;(S=g.current)==null||S.focus()},[]);const w=async S=>{var d;if(S.preventDefault(),!o.trim()||a)return;const m=o.trim();i(""),c(!0);try{await s(m)}catch(p){console.error("Error sending message:",p),i(m)}finally{c(!1),(d=g.current)==null||d.focus()}},b=S=>{S.key==="Enter"&&!S.shiftKey&&(S.preventDefault(),w(S))},N=S=>new Date(S).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1}),j=S=>{switch(S){case"system":return r.jsx(h0,{className:"w-4 h-4 text-blue-400"});case"mic_request":return r.jsx(yt,{className:"w-4 h-4 text-yellow-400"});default:return r.jsx(yn,{className:"w-4 h-4 text-gray-400"})}},k=(S,m)=>{const d=m===t.id;switch(S){case"system":return"bg-blue-900/30 border-blue-500/30 text-blue-200";case"mic_request":return"bg-yellow-900/30 border-yellow-500/30 text-yellow-200";default:return d?"bg-purple-900/50 border-purple-500/30 text-white":"bg-gray-800/50 border-gray-600/30 text-gray-200"}};return r.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-blue-900/30 rounded-xl border border-blue-500/20 flex flex-col h-[600px]",children:[r.jsxs("div",{className:"p-4 border-b border-gray-700/50",children:[r.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[r.jsx(yn,{className:"w-5 h-5 text-blue-400"}),"المحادثة النصية"]}),r.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:[e.length," رسالة"]})]}),r.jsx("div",{className:"flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800",children:r.jsxs("div",{className:"flex flex-col space-y-3",children:[e.length===0?r.jsxs("div",{className:"text-center text-gray-400 py-8",children:[r.jsx(yn,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),r.jsx("p",{children:"لا توجد رسائل بعد"}),r.jsx("p",{className:"text-sm mt-1",children:"ابدأ المحادثة!"})]}):e.map(S=>r.jsxs("div",{className:`p-3 rounded-lg border ${k(S.messageType,S.sender._id)} ${S.messageType!=="system"&&S.sender._id===t.id?"max-w-[60%] self-end ml-auto":""}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[j(S.messageType),r.jsx("span",{className:"font-medium text-sm",children:S.sender.username}),r.jsxs("span",{className:"text-xs opacity-60",children:["#",S.sender.playerId]})]}),r.jsxs("div",{className:"flex items-center gap-1 text-xs opacity-60",children:[r.jsx(xn,{className:"w-3 h-3"}),r.jsx("span",{children:N(S.timestamp)})]})]}),r.jsx("div",{className:"text-sm leading-relaxed",children:S.content})]},S._id)),r.jsx("div",{ref:u})]})}),r.jsxs("div",{className:"p-4 border-t border-gray-700/50",children:[r.jsxs("form",{onSubmit:w,className:"flex gap-2",children:[r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("input",{ref:g,type:"text",value:o,onChange:S=>i(S.target.value),onKeyPress:b,placeholder:"اكتب رسالتك هنا...",maxLength:500,disabled:a,className:"w-full px-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-gray-800/70 transition-all disabled:opacity-50 disabled:cursor-not-allowed"}),r.jsxs("div",{className:"absolute bottom-1 left-2 text-xs text-gray-500",children:[o.length,"/500"]})]}),r.jsxs("button",{type:"submit",disabled:!o.trim()||a,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white transition-colors flex items-center gap-2",title:"إرسال الرسالة",children:[r.jsx(Fr,{className:"w-4 h-4"}),a?"جاري الإرسال...":"إرسال"]}),!n&&r.jsxs("button",{type:"button",onClick:l,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors flex items-center gap-1",title:"طلب المايك",children:[r.jsx(yt,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden sm:inline",children:"طلب المايك"})]})]}),n&&r.jsxs("div",{className:"mt-2 p-2 bg-yellow-900/30 border border-yellow-500/30 rounded-lg text-yellow-200 text-sm flex items-center gap-2",children:[r.jsx(xn,{className:"w-4 h-4"}),r.jsx("span",{children:"أنت في قائمة انتظار المايك"})]}),r.jsxs("div",{className:"mt-2 text-xs text-gray-500 flex items-center gap-4",children:[r.jsx("span",{children:"اضغط Enter للإرسال"}),r.jsx("span",{children:"الحد الأقصى: 500 حرف"})]})]})]})},Wp=({user:e,wsService:t})=>{const[n,s]=x.useState(null),[l,o]=x.useState([]),[i,a]=x.useState(!0),[c,u]=x.useState(null),[g,w]=x.useState(!1),[b,N]=x.useState(null),[j,k]=x.useState(!1),[S,m]=x.useState(!1),[d,p]=x.useState(!1),[M,P]=x.useState(!1),[R,z]=x.useState([]),[U,D]=x.useState(new Map),y=x.useRef(null),O=async()=>{try{a(!0);const[h,C]=await Promise.all([X.getVoiceRoom(),X.getVoiceRoomMessages()]);s(h),o(C);const T=h.seats.find(L=>L.user&&L.user._id===e.id);T?(w(!0),N(T.seatNumber),m(T.isMuted)):(w(!1),N(null));const $=h.waitingQueue.some(L=>L.user._id===e.id);k($),u(null)}catch(h){if(console.error("Error loading voice room:",h),h.message&&h.message.includes("مطرود من الغرفة الصوتية")){u(h.message),s({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,seats:[],waitingQueue:[],settings:{allowTextChat:!0,autoKickInactive:!1,inactiveTimeoutMinutes:30},isActive:!1});return}u(h.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1)}};x.useEffect(()=>{if(!(e!=null&&e.id)){console.warn("⚠️ No user ID available, skipping WebRTC service setup");return}console.log("🔧 Setting up WebRTC Voice Service with user ID:",e.id);try{y.current=new R0(t)}catch(h){console.error("❌ Error creating WebRTC service:",h);return}return y.current&&(y.current.onUserJoined=h=>{console.log(`👤 User joined voice chat: ${h.id}`),z(C=>[...C.filter(T=>T.id!==h.id),h])},y.current.onUserLeft=h=>{console.log(`👋 User left voice chat: ${h}`),z(C=>C.filter(T=>T.id!==h)),D(C=>{const T=new Map(C);return T.delete(h),T})},y.current.onVoiceActivity=h=>{console.log("🎤 Voice activity changed:",h.isSpeaking?"speaking":"silent",`(level: ${h.level})`),D(C=>{const T=new Map(C);return T.set(h.userId,h),T}),e!=null&&e.id&&g?(console.log("📤 Voice activity sent:",h.isSpeaking?"speaking":"silent",`(userId: ${e.id})`),t.send({type:"voice_activity",data:{userId:e.id,level:h.level,isSpeaking:h.isSpeaking,timestamp:Date.now()}})):(e!=null&&e.id||console.warn("⚠️ No currentUserId available for voice activity"),g||console.log("🔍 User not in seat, voice activity not sent"))},y.current.onError=h=>{console.error("❌ WebRTC error:",h),u(`خطأ في الصوت: ${h.message}`)}),()=>{y.current&&(console.log("🧹 Cleaning up WebRTC service"),y.current.leaveRoom().catch(console.error))}},[t,e==null?void 0:e.id]),x.useEffect(()=>{const h=L=>{o(G=>[...G,L])},C=L=>{L.action&&L.userId&&(L.action==="seat_joined"||L.action==="seat_left"||L.action==="mute_toggled")||O(),L.action==="seat_joined"&&g&&L.userId!==e.id&&setTimeout(()=>{var G;(G=y.current)==null||G.sendOffer(L.userId)},1e3)},T=L=>{L.userId&&L.userId!==e.id&&(D(G=>{const Y=new Map(G);return Y.set(L.userId.toString(),{userId:L.userId.toString(),level:L.level,isSpeaking:L.isSpeaking}),Y}),s(G=>({...G,seats:G.seats.map(Y=>{var fe;return((fe=Y.user)==null?void 0:fe._id)===L.userId?{...Y,isSpeaking:L.isSpeaking}:Y})})))};y.current.onVoiceActivity=L=>{s(G=>({...G,seats:G.seats.map(Y=>{var fe;return((fe=Y.user)==null?void 0:fe._id)===e.id?{...Y,isSpeaking:L.isSpeaking}:Y})})),g&&t.send({type:"voice_activity",data:{userId:e.id,level:L.level,isSpeaking:L.isSpeaking}})};const $=L=>{O()};return t.onMessage("voice_room_message",h),t.onMessage("voice_room_update",C),t.onMessage("voice_activity",T),t.onMessage("admin_action_update",$),()=>{t.offMessage("voice_room_message",h),t.offMessage("voice_room_update",C),t.offMessage("voice_activity",T),t.offMessage("admin_action_update",$)}},[t,g,e.id]),x.useEffect(()=>{O()},[]),x.useEffect(()=>{const h=T=>{if(g)return T.preventDefault(),T.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},C=()=>{g&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",h),window.addEventListener("unload",C),()=>{window.removeEventListener("beforeunload",h),window.removeEventListener("unload",C)}},[g,e.id]);const Z=async h=>{try{const C=await X.sendVoiceRoomMessage(h);t.send({type:"voice_room_message",data:C.messageData})}catch(C){console.error("Error sending message:",C),u(C.message||"خطأ في إرسال الرسالة")}},ne=async()=>{try{p(!0),await X.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}}),await O()}catch(h){console.error("Error requesting mic:",h),u(h.message||"خطأ في طلب المايك")}finally{p(!1)}},We=async()=>{try{await X.cancelMicRequest(),t.send({type:"voice_room_update",data:{action:"mic_request_cancelled",userId:e.id}}),await O()}catch(h){console.error("Error cancelling mic request:",h),u(h.message||"خطأ في إلغاء طلب المايك")}},Pe=async h=>{try{if(p(!0),u(null),await X.joinVoiceSeat(h),w(!0),N(h),m(!1),y.current&&(e!=null&&e.id))try{const C=`voice-room-${(n==null?void 0:n.id)||"default"}`;await y.current.joinRoom(C,e.id),P(!0)}catch(C){console.error("❌ WebRTC initialization failed:",C),u(`فشل في بدء المحادثة الصوتية: ${C.message}`)}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",h.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:h}}),await O()}catch(C){console.error("Error joining seat:",C),u(C.message||"خطأ في الانضمام للمقعد"),P(!1)}finally{p(!1)}},$e=async()=>{var h;try{p(!0),await X.leaveSeat(),w(!1),N(null),m(!1),y.current&&y.current.leaveRoom(),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:b}})}catch(C){console.error("Error leaving seat:",C),(h=C.message)!=null&&h.includes("لست في أي مقعد")||u(C.message||"خطأ في مغادرة المقعد")}finally{p(!1)}},be=async()=>{try{if(!g){u("يجب أن تكون في مقعد لاستخدام المايك");return}if(!y.current){u("خدمة الصوت غير متاحة - جاري إعادة الاتصال...");return}const h=!S;y.current.setMute(h),m(h);try{await X.toggleMute(h)}catch(C){console.warn("Failed to update server mute state:",C)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:h}})}catch(h){console.error("Error toggling mute:",h),u("خطأ في تبديل كتم المايك"),m(!S)}};return i?r.jsx("div",{className:"flex items-center justify-center h-96",children:r.jsxs("div",{className:"text-center",children:[r.jsx(vn,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),r.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})}):c?r.jsxs("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center",children:[r.jsx("p",{className:"text-red-400 mb-4",children:c}),r.jsx("button",{onClick:O,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors",children:"إعادة المحاولة"})]}):n?r.jsxs("div",{className:`max-w-7xl mx-auto p-4 sm:p-6 space-y-6 ${g?"pb-24 sm:pb-6":""}`,children:[r.jsx("div",{className:"bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-xl p-4 sm:p-6 border border-purple-500/20",children:r.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[r.jsxs("div",{children:[r.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-white mb-2 flex items-center gap-3",children:[r.jsx(is,{className:"w-6 sm:w-8 h-6 sm:h-8 text-purple-400"}),"INFINITY ROOM"]}),r.jsx("p",{className:"text-gray-300 text-sm sm:text-base",children:"غرفة صوتية للمحادثة مع الأصدقاء"})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[r.jsxs("div",{className:"flex items-center gap-4 text-gray-300",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(Cn,{className:"w-4 sm:w-5 h-4 sm:h-5"}),r.jsxs("span",{className:"text-sm",children:[n.seats.filter(h=>h.user).length,"/",n.maxSeats]})]}),r.jsxs("div",{className:"flex items-center gap-2 bg-black/20 px-2 sm:px-3 py-1 rounded-lg",children:[M?r.jsx(Xh,{className:"w-3 sm:w-4 h-3 sm:h-4 text-green-400"}):r.jsx(Yh,{className:"w-3 sm:w-4 h-3 sm:h-4 text-red-400"}),r.jsx("span",{className:"text-xs sm:text-sm",children:M?"متصل":"غير متصل"})]})]}),g&&r.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto",children:[r.jsx("button",{onClick:be,className:`w-full sm:w-auto p-3 sm:p-2 rounded-lg transition-colors text-sm font-medium ${S?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:r.jsxs("div",{className:"flex items-center justify-center gap-2",children:[S?r.jsx(ls,{className:"w-5 h-5"}):r.jsx(yt,{className:"w-5 h-5"}),r.jsx("span",{className:"sm:hidden",children:S?"إلغاء الكتم":"كتم المايك"})]})}),r.jsx("button",{onClick:$e,className:"w-full sm:w-auto px-4 py-3 sm:py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors text-sm font-medium",children:"مغادرة المقعد"})]})]})]})}),g&&r.jsx("div",{className:"hidden sm:block mb-6",children:r.jsx("div",{className:"bg-gradient-to-r from-gray-800/50 to-purple-900/30 rounded-xl p-4 border border-purple-500/20",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center gap-3",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),r.jsxs("span",{className:"text-green-400 font-medium",children:["متصل - مقعد ",b]})]})}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("button",{onClick:be,className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 font-medium ${S?"bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/25":"bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/25"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:[S?r.jsx(ls,{className:"w-4 h-4"}):r.jsx(yt,{className:"w-4 h-4"}),r.jsx("span",{children:S?"إلغاء كتم المايك":"كتم المايك"})]}),r.jsx("button",{onClick:$e,className:"flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-all duration-200 font-medium shadow-lg shadow-red-600/25",title:"مغادرة المقعد",disabled:d,children:r.jsx("span",{children:d?"جاري المغادرة...":"مغادرة المقعد"})})]})]})})}),r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx(qp,{seats:n.seats,waitingQueue:n.waitingQueue,currentUser:e,isInSeat:g,currentSeatNumber:b,isInWaitingQueue:j,isConnecting:d,onJoinSeat:Pe,onRequestMic:ne,onCancelMicRequest:We})}),r.jsx("div",{className:"lg:col-span-1",children:r.jsx(Hp,{messages:l,currentUser:e,isInWaitingQueue:j,onSendMessage:Z,onRequestMic:ne})})]})]}):r.jsx("div",{className:"text-center text-gray-400",children:r.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},Qp=({user:e,onLogout:t,wsService:n})=>{var b;const[,s]=L0(),[l,o]=x.useState(()=>{const N=localStorage.getItem("isInVoiceRoom")==="true",j=localStorage.getItem("activeTab");return N?"voice":j||"games"}),[i,a]=x.useState(!1),[c,u]=x.useState(!1);x.useEffect(()=>{const N=()=>{a(window.innerWidth<=768)};return N(),window.addEventListener("resize",N),()=>window.removeEventListener("resize",N)},[]);const g=N=>{o(N),localStorage.setItem("activeTab",N)},w=()=>{localStorage.removeItem("token"),s("/login"),t()};return console.log("USER DATA IN MAIN DASHBOARD:",e),!e||!e.username?r.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-red-900 via-yellow-900 to-black text-white",children:[r.jsx("h1",{className:"text-3xl font-bold mb-4",children:"⚠️ لا توجد بيانات لاعب!"}),r.jsx("pre",{className:"bg-black/60 rounded-lg p-4 text-left text-xs max-w-xl overflow-x-auto mb-4",children:JSON.stringify(e,null,2)}),r.jsx("p",{className:"mb-4",children:"يرجى التأكد من أن حسابك يحتوي على اسم مستخدم وصورة ورصيد."}),r.jsx("button",{onClick:t,className:"px-6 py-2 bg-red-600 rounded-lg text-white font-bold",children:"تسجيل الخروج"})]}):i?r.jsx(Bp,{userData:e,onLogout:w,wsService:n}):r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"}),r.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"}),r.jsx("div",{className:"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"})]}),r.jsx("header",{className:"relative z-10 bg-black/30 backdrop-blur-xl border-b border-white/10 shadow-2xl",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsxs("div",{className:"flex justify-between items-center h-20",children:[r.jsx("div",{className:"flex items-center space-x-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg",children:r.jsx(Ur,{className:"w-7 h-7 text-white"})}),r.jsxs("div",{children:[r.jsxs("h1",{className:"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:[e.username," - Infinity Box"]}),r.jsx("p",{className:"text-xs text-gray-400",children:"عالم الألعاب المثير"})]})]})}),r.jsx("div",{className:"flex items-center space-x-6",children:r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-yellow-500/50 shadow-lg hover:from-yellow-500/40 hover:to-orange-500/40 transition-all duration-300",children:[r.jsx(zl,{className:"w-6 h-6 text-yellow-300"}),r.jsx("span",{className:"text-yellow-300 font-bold text-lg",children:((b=e.goldCoins)==null?void 0:b.toLocaleString())||0})]}),r.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500/30 to-purple-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-blue-500/50 shadow-lg hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-300",children:[r.jsx(p0,{className:"w-6 h-6 text-blue-300"}),r.jsx("span",{className:"text-blue-300 font-bold text-lg",children:e.pearls||0})]})]})}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("button",{onClick:()=>u(!c),className:"relative p-2 bg-white/10 backdrop-blur-sm rounded-lg hover:bg-white/20 transition-all duration-300 border border-white/20",children:[r.jsx(Oh,{className:"w-5 h-5 text-white"}),r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),r.jsxs("div",{className:"flex items-center space-x-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-sm rounded-xl px-4 py-2 border border-purple-500/30 hover:from-purple-500/30 hover:to-blue-500/30 transition-all duration-300 shadow-lg",children:[r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:e.profileImage||"/images/default-avatar.png",alt:e.username,className:"w-12 h-12 rounded-full border-2 border-white/30 shadow-lg object-cover",onError:N=>{N.currentTarget.src="/images/default-avatar.png"}}),r.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),r.jsxs("div",{className:"text-right",children:[r.jsx("p",{className:"font-bold text-white text-lg",children:e.username}),r.jsxs("p",{className:"text-xs text-yellow-300",children:["مستوى ",e.level||1," - ",e.experience||0," XP"]})]})]}),r.jsxs("button",{onClick:w,className:"flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 backdrop-blur-sm rounded-lg transition-all duration-300 border border-red-500/30 text-red-400 hover:text-red-300",children:[r.jsx(y0,{className:"w-4 h-4"}),r.jsx("span",{className:"text-sm font-medium",children:"خروج"})]})]})]})})}),r.jsx("nav",{className:"relative z-10 bg-black/20 backdrop-blur-xl border-b border-white/10 shadow-lg",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsxs("div",{className:"flex space-x-8",children:[r.jsx("button",{onClick:()=>g("games"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="games"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎮 الألعاب"}),r.jsx("button",{onClick:()=>g("leaderboard"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="leaderboard"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🏆 المتصدرين"}),r.jsx("button",{onClick:()=>g("voice"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="voice"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎤 الغرفة الصوتية"}),r.jsx("button",{onClick:()=>g("profile"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="profile"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"👤 الملف الشخصي"}),e.isAdmin&&r.jsx("button",{onClick:()=>g("admin"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="admin"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"⚙️ الإدارة"})]})})}),r.jsxs("main",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[l==="games"&&r.jsx(Js,{setActiveTab:g}),l==="leaderboard"&&r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(os,{className:"w-12 h-12 text-yellow-400 mr-4"}),r.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"🏆 المتصدرين"})]}),r.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:r.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة قائمة المتصدرين..."})})]}),l==="voice"&&n&&r.jsx(Wp,{user:e,wsService:n}),l==="profile"&&r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(wn,{className:"w-12 h-12 text-blue-400 mr-4"}),r.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"👤 الملف الشخصي"})]}),r.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:r.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة الملف الشخصي..."})})]}),l==="admin"&&e.isAdmin&&r.jsx(bi,{userData:e,onLogout:w})]}),c&&r.jsxs("div",{className:"fixed top-20 right-4 z-50 w-80 bg-black/90 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl",children:[r.jsx("div",{className:"p-4 border-b border-white/10",children:r.jsx("h3",{className:"text-lg font-semibold text-white",children:"الإشعارات"})}),r.jsx("div",{className:"p-4",children:r.jsx("p",{className:"text-gray-400 text-center",children:"لا توجد إشعارات جديدة"})})]})]})};function Gp(){const[e,t]=x.useState(!1),[n,s]=x.useState(null),[l,o]=x.useState(!0),[i]=x.useState(()=>new P0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`));x.useEffect(()=>{localStorage.removeItem("activeTab");const u=localStorage.getItem("token");console.log("🔍 App: Checking token:",u?"Token exists":"No token found"),u?(console.log("🔄 App: Attempting to get current user..."),X.getCurrentUser().then(g=>{if(console.log("✅ App: User data received:",g),g&&typeof g=="object")s(g),localStorage.setItem("isAdmin",g.isAdmin?"true":"false");else{const b=localStorage.getItem("isAdmin")==="true";s({id:"",username:"",isAdmin:b})}console.log("🔓 App: Setting authenticated to true"),t(!0);const w=localStorage.getItem("token");w&&i.connect(w).then(()=>{console.log("✅ WebSocket connected on app load")}).catch(b=>{console.error("❌ Failed to connect to WebSocket on app load:",b)})}).catch(g=>{console.log("❌ App: Error getting user:",g),g.message.includes("MULTIPLE_LOGIN")&&alert("تم تسجيل الدخول من جهاز آخر. سيتم تسجيل خروجك من هذا الجهاز."),console.log("🔒 App: Setting authenticated to false"),t(!1),s(null),localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}).finally(()=>{console.log("⏹️ App: Loading finished"),o(!1)})):(console.log("🔒 App: No token found, setting authenticated to false"),o(!1))},[]);const a=async u=>{s(u),t(!0);try{const g=localStorage.getItem("token");g&&await i.connect(g)}catch(g){console.error("Failed to connect to WebSocket:",g)}},c=()=>{localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),i.disconnect(),t(!1),s(null)};return l?(console.log("⏳ App: Showing loading screen"),r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 flex items-center justify-center",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-white text-lg",children:"جاري التحميل..."})]})})):e?(console.log("🏠 App: Showing MainDashboard (authenticated)"),n?r.jsx(Qp,{user:n,onLogout:c,wsService:i}):null):(console.log("🔐 App: Showing AuthPage (not authenticated)"),r.jsx(sp,{onAuthSuccess:a}))}c0(document.getElementById("root")).render(r.jsx(x.StrictMode,{children:r.jsx(Gp,{})}));
