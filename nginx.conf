# تكوين Nginx للخادم العكسي
events {
    worker_connections 1024;
}

http {
    # إعدادات أساسية
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # إعدادات الأداء
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # إعدادات Gzip للضغط
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # إعدادات الأمان
    server_tokens off;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # الخادم الرئيسي
    server {
        listen 80;
        server_name localhost;
        
        # إعادة توجيه HTTP إلى HTTPS (اختياري)
        # return 301 https://$server_name$request_uri;
        
        # الملفات الثابتة
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            
            # إعدادات التخزين المؤقت للملفات الثابتة
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
        
        # الخادم العكسي للتطبيق
        location /api {
            proxy_pass http://app:5000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }
        
        # WebSocket للدردشة الصوتية
        location /ws {
            proxy_pass http://app:5000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # إعدادات الأمان الإضافية
        location ~ /\. {
            deny all;
        }
        
        # منع الوصول لملفات معينة
        location ~* \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
            deny all;
        }
    }
    
    # إعدادات HTTPS (اختياري)
    # server {
    #     listen 443 ssl http2;
    #     server_name localhost;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # نفس الإعدادات السابقة
    # }
} 