# متغيرات البيئة للتطبيق
# انسخ هذا الملف إلى .env واملأ القيم الصحيحة

# قاعدة البيانات
MONGODB_URI=mongodb+srv://aser20031:<EMAIL>/game_db?retryWrites=true&w=majority&appName=Cluster0

# JWT
JWT_SECRET=8110bb05094b7a3c5a5ae10a5d1d0f1abc3741b130075aee84af0dc260a8a0d13d36cb5d71d5ae1aaebb64abbe8e3da0f

# بيئة التطبيق
NODE_ENV=production

# منفذ الخادم
PORT=5000

# إعدادات CORS
CORS_ORIGIN=https://infinity-box25.onrender.com//,http://localhost:3000,http://localhost:5173

# إعدادات الجلسة
SESSION_SECRET=0b12402b2e60d96c5ecd9582306f2d3b8d8cb7a062254810f77377ad5646672f

# إعدادات الملفات
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# إعدادات الأمان
BCRYPT_ROUNDS=12

# إعدادات WebSocket
WS_HEARTBEAT_INTERVAL=30000
WS_HEARTBEAT_TIMEOUT=60000

# إعدادات التطبيق
DEFAULT_COINS=1000
DEFAULT_GOLD_COINS=10000
DEFAULT_PEARLS=10
MAX_VOICE_ROOM_CAPACITY=100

# إعدادات التنظيف التلقائي
MESSAGE_CLEANUP_INTERVAL=600000
MESSAGE_RETENTION_DAYS=3
VOICE_ROOM_CLEANUP_INTERVAL=600000

# إعدادات Agora للمحادثة الصوتية
VITE_AGORA_APP_ID=852ff5f55a7a49b081b799358f2fc329
