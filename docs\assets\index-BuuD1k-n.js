function Y0(e,t){for(var r=0;r<t.length;r++){const s=t[r];if(typeof s!="string"&&!Array.isArray(s)){for(const l in s)if(l!=="default"&&!(l in e)){const o=Object.getOwnPropertyDescriptor(s,l);o&&Object.defineProperty(e,l,o.get?o:{enumerable:!0,get:()=>s[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))s(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function r(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(l){if(l.ep)return;l.ep=!0;const o=r(l);fetch(l.href,o)}})();function X0(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Wc={exports:{}},hl={},Qc={exports:{}},Z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yn=Symbol.for("react.element"),em=Symbol.for("react.portal"),tm=Symbol.for("react.fragment"),rm=Symbol.for("react.strict_mode"),nm=Symbol.for("react.profiler"),sm=Symbol.for("react.provider"),lm=Symbol.for("react.context"),om=Symbol.for("react.forward_ref"),im=Symbol.for("react.suspense"),am=Symbol.for("react.memo"),cm=Symbol.for("react.lazy"),Sa=Symbol.iterator;function dm(e){return e===null||typeof e!="object"?null:(e=Sa&&e[Sa]||e["@@iterator"],typeof e=="function"?e:null)}var Gc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Dc=Object.assign,Jc={};function en(e,t,r){this.props=e,this.context=t,this.refs=Jc,this.updater=r||Gc}en.prototype.isReactComponent={};en.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};en.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Zc(){}Zc.prototype=en.prototype;function pi(e,t,r){this.props=e,this.context=t,this.refs=Jc,this.updater=r||Gc}var gi=pi.prototype=new Zc;gi.constructor=pi;Dc(gi,en.prototype);gi.isPureReactComponent=!0;var Ca=Array.isArray,Kc=Object.prototype.hasOwnProperty,xi={current:null},Yc={key:!0,ref:!0,__self:!0,__source:!0};function Xc(e,t,r){var s,l={},o=null,i=null;if(t!=null)for(s in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Kc.call(t,s)&&!Yc.hasOwnProperty(s)&&(l[s]=t[s]);var a=arguments.length-2;if(a===1)l.children=r;else if(1<a){for(var c=Array(a),d=0;d<a;d++)c[d]=arguments[d+2];l.children=c}if(e&&e.defaultProps)for(s in a=e.defaultProps,a)l[s]===void 0&&(l[s]=a[s]);return{$$typeof:Yn,type:e,key:o,ref:i,props:l,_owner:xi.current}}function um(e,t){return{$$typeof:Yn,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function yi(e){return typeof e=="object"&&e!==null&&e.$$typeof===Yn}function mm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Ea=/\/+/g;function Ol(e,t){return typeof e=="object"&&e!==null&&e.key!=null?mm(""+e.key):t.toString(36)}function Is(e,t,r,s,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Yn:case em:i=!0}}if(i)return i=e,l=l(i),e=s===""?"."+Ol(i,0):s,Ca(l)?(r="",e!=null&&(r=e.replace(Ea,"$&/")+"/"),Is(l,t,r,"",function(d){return d})):l!=null&&(yi(l)&&(l=um(l,r+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Ea,"$&/")+"/")+e)),t.push(l)),1;if(i=0,s=s===""?".":s+":",Ca(e))for(var a=0;a<e.length;a++){o=e[a];var c=s+Ol(o,a);i+=Is(o,t,r,c,l)}else if(c=dm(e),typeof c=="function")for(e=c.call(e),a=0;!(o=e.next()).done;)o=o.value,c=s+Ol(o,a++),i+=Is(o,t,r,c,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function us(e,t,r){if(e==null)return e;var s=[],l=0;return Is(e,s,"","",function(o){return t.call(r,o,l++)}),s}function hm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ze={current:null},As={transition:null},fm={ReactCurrentDispatcher:ze,ReactCurrentBatchConfig:As,ReactCurrentOwner:xi};function ed(){throw Error("act(...) is not supported in production builds of React.")}Z.Children={map:us,forEach:function(e,t,r){us(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return us(e,function(){t++}),t},toArray:function(e){return us(e,function(t){return t})||[]},only:function(e){if(!yi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Z.Component=en;Z.Fragment=tm;Z.Profiler=nm;Z.PureComponent=pi;Z.StrictMode=rm;Z.Suspense=im;Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fm;Z.act=ed;Z.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var s=Dc({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=xi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)Kc.call(t,c)&&!Yc.hasOwnProperty(c)&&(s[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)s.children=r;else if(1<c){a=Array(c);for(var d=0;d<c;d++)a[d]=arguments[d+2];s.children=a}return{$$typeof:Yn,type:e.type,key:l,ref:o,props:s,_owner:i}};Z.createContext=function(e){return e={$$typeof:lm,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:sm,_context:e},e.Consumer=e};Z.createElement=Xc;Z.createFactory=function(e){var t=Xc.bind(null,e);return t.type=e,t};Z.createRef=function(){return{current:null}};Z.forwardRef=function(e){return{$$typeof:om,render:e}};Z.isValidElement=yi;Z.lazy=function(e){return{$$typeof:cm,_payload:{_status:-1,_result:e},_init:hm}};Z.memo=function(e,t){return{$$typeof:am,type:e,compare:t===void 0?null:t}};Z.startTransition=function(e){var t=As.transition;As.transition={};try{e()}finally{As.transition=t}};Z.unstable_act=ed;Z.useCallback=function(e,t){return ze.current.useCallback(e,t)};Z.useContext=function(e){return ze.current.useContext(e)};Z.useDebugValue=function(){};Z.useDeferredValue=function(e){return ze.current.useDeferredValue(e)};Z.useEffect=function(e,t){return ze.current.useEffect(e,t)};Z.useId=function(){return ze.current.useId()};Z.useImperativeHandle=function(e,t,r){return ze.current.useImperativeHandle(e,t,r)};Z.useInsertionEffect=function(e,t){return ze.current.useInsertionEffect(e,t)};Z.useLayoutEffect=function(e,t){return ze.current.useLayoutEffect(e,t)};Z.useMemo=function(e,t){return ze.current.useMemo(e,t)};Z.useReducer=function(e,t,r){return ze.current.useReducer(e,t,r)};Z.useRef=function(e){return ze.current.useRef(e)};Z.useState=function(e){return ze.current.useState(e)};Z.useSyncExternalStore=function(e,t,r){return ze.current.useSyncExternalStore(e,t,r)};Z.useTransition=function(){return ze.current.useTransition()};Z.version="18.3.1";Qc.exports=Z;var y=Qc.exports;const pm=X0(y),gm=Y0({__proto__:null,default:pm},[y]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xm=y,ym=Symbol.for("react.element"),vm=Symbol.for("react.fragment"),wm=Object.prototype.hasOwnProperty,bm=xm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,jm={key:!0,ref:!0,__self:!0,__source:!0};function td(e,t,r){var s,l={},o=null,i=null;r!==void 0&&(o=""+r),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(s in t)wm.call(t,s)&&!jm.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)l[s]===void 0&&(l[s]=t[s]);return{$$typeof:ym,type:e,key:o,ref:i,props:l,_owner:bm.current}}hl.Fragment=vm;hl.jsx=td;hl.jsxs=td;Wc.exports=hl;var n=Wc.exports,rd={exports:{}},Je={},nd={exports:{}},sd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(f,C){var _=f.length;f.push(C);e:for(;0<_;){var T=_-1>>>1,F=f[T];if(0<l(F,C))f[T]=C,f[_]=F,_=T;else break e}}function r(f){return f.length===0?null:f[0]}function s(f){if(f.length===0)return null;var C=f[0],_=f.pop();if(_!==C){f[0]=_;e:for(var T=0,F=f.length,J=F>>>1;T<J;){var te=2*(T+1)-1,Ne=f[te],ne=te+1,ge=f[ne];if(0>l(Ne,_))ne<F&&0>l(ge,Ne)?(f[T]=ge,f[ne]=_,T=ne):(f[T]=Ne,f[te]=_,T=te);else if(ne<F&&0>l(ge,_))f[T]=ge,f[ne]=_,T=ne;else break e}}return C}function l(f,C){var _=f.sortIndex-C.sortIndex;return _!==0?_:f.id-C.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var c=[],d=[],g=1,v=null,b=3,N=!1,j=!1,k=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,u=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(f){for(var C=r(d);C!==null;){if(C.callback===null)s(d);else if(C.startTime<=f)s(d),C.sortIndex=C.expirationTime,t(c,C);else break;C=r(d)}}function E(f){if(k=!1,p(f),!j)if(r(c)!==null)j=!0,Ue(L);else{var C=r(d);C!==null&&we(E,C.startTime-f)}}function L(f,C){j=!1,k&&(k=!1,m($),$=-1),N=!0;var _=b;try{for(p(C),v=r(c);v!==null&&(!(v.expirationTime>C)||f&&!U());){var T=v.callback;if(typeof T=="function"){v.callback=null,b=v.priorityLevel;var F=T(v.expirationTime<=C);C=e.unstable_now(),typeof F=="function"?v.callback=F:v===r(c)&&s(c),p(C)}else s(c);v=r(c)}if(v!==null)var J=!0;else{var te=r(d);te!==null&&we(E,te.startTime-C),J=!1}return J}finally{v=null,b=_,N=!1}}var O=!1,z=null,$=-1,G=5,x=-1;function U(){return!(e.unstable_now()-x<G)}function Y(){if(z!==null){var f=e.unstable_now();x=f;var C=!0;try{C=z(!0,f)}finally{C?ae():(O=!1,z=null)}}else O=!1}var ae;if(typeof u=="function")ae=function(){u(Y)};else if(typeof MessageChannel<"u"){var Te=new MessageChannel,Le=Te.port2;Te.port1.onmessage=Y,ae=function(){Le.postMessage(null)}}else ae=function(){S(Y,0)};function Ue(f){z=f,O||(O=!0,ae())}function we(f,C){$=S(function(){f(e.unstable_now())},C)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(f){f.callback=null},e.unstable_continueExecution=function(){j||N||(j=!0,Ue(L))},e.unstable_forceFrameRate=function(f){0>f||125<f?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<f?Math.floor(1e3/f):5},e.unstable_getCurrentPriorityLevel=function(){return b},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(f){switch(b){case 1:case 2:case 3:var C=3;break;default:C=b}var _=b;b=C;try{return f()}finally{b=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(f,C){switch(f){case 1:case 2:case 3:case 4:case 5:break;default:f=3}var _=b;b=f;try{return C()}finally{b=_}},e.unstable_scheduleCallback=function(f,C,_){var T=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?T+_:T):_=T,f){case 1:var F=-1;break;case 2:F=250;break;case 5:F=**********;break;case 4:F=1e4;break;default:F=5e3}return F=_+F,f={id:g++,callback:C,priorityLevel:f,startTime:_,expirationTime:F,sortIndex:-1},_>T?(f.sortIndex=_,t(d,f),r(c)===null&&f===r(d)&&(k?(m($),$=-1):k=!0,we(E,_-T))):(f.sortIndex=F,t(c,f),j||N||(j=!0,Ue(L))),f},e.unstable_shouldYield=U,e.unstable_wrapCallback=function(f){var C=b;return function(){var _=b;b=C;try{return f.apply(this,arguments)}finally{b=_}}}})(sd);nd.exports=sd;var Nm=nd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var km=y,De=Nm;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ld=new Set,_n={};function jr(e,t){Qr(e,t),Qr(e+"Capture",t)}function Qr(e,t){for(_n[e]=t,e=0;e<t.length;e++)ld.add(t[e])}var Mt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ho=Object.prototype.hasOwnProperty,Sm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ma={},Ia={};function Cm(e){return ho.call(Ia,e)?!0:ho.call(Ma,e)?!1:Sm.test(e)?Ia[e]=!0:(Ma[e]=!0,!1)}function Em(e,t,r,s){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Mm(e,t,r,s){if(t===null||typeof t>"u"||Em(e,t,r,s))return!0;if(s)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Oe(e,t,r,s,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new Oe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new Oe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new Oe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new Oe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new Oe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new Oe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new Oe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new Oe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new Oe(e,5,!1,e.toLowerCase(),null,!1,!1)});var vi=/[\-:]([a-z])/g;function wi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(vi,wi);Ce[t]=new Oe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(vi,wi);Ce[t]=new Oe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(vi,wi);Ce[t]=new Oe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new Oe(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new Oe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new Oe(e,1,!1,e.toLowerCase(),null,!0,!0)});function bi(e,t,r,s){var l=Ce.hasOwnProperty(t)?Ce[t]:null;(l!==null?l.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Mm(t,r,l,s)&&(r=null),s||l===null?Cm(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,s=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,s?e.setAttributeNS(s,t,r):e.setAttribute(t,r))))}var Tt=km.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ms=Symbol.for("react.element"),Mr=Symbol.for("react.portal"),Ir=Symbol.for("react.fragment"),ji=Symbol.for("react.strict_mode"),fo=Symbol.for("react.profiler"),od=Symbol.for("react.provider"),id=Symbol.for("react.context"),Ni=Symbol.for("react.forward_ref"),po=Symbol.for("react.suspense"),go=Symbol.for("react.suspense_list"),ki=Symbol.for("react.memo"),zt=Symbol.for("react.lazy"),ad=Symbol.for("react.offscreen"),Aa=Symbol.iterator;function on(e){return e===null||typeof e!="object"?null:(e=Aa&&e[Aa]||e["@@iterator"],typeof e=="function"?e:null)}var he=Object.assign,Ul;function pn(e){if(Ul===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);Ul=t&&t[1]||""}return`
`+Ul+e}var $l=!1;function Fl(e,t){if(!e||$l)return"";$l=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(d){var s=d}Reflect.construct(e,[],t)}else{try{t.call()}catch(d){s=d}e.call(t.prototype)}else{try{throw Error()}catch(d){s=d}e()}}catch(d){if(d&&s&&typeof d.stack=="string"){for(var l=d.stack.split(`
`),o=s.stack.split(`
`),i=l.length-1,a=o.length-1;1<=i&&0<=a&&l[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(l[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||l[i]!==o[a]){var c=`
`+l[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{$l=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?pn(e):""}function Im(e){switch(e.tag){case 5:return pn(e.type);case 16:return pn("Lazy");case 13:return pn("Suspense");case 19:return pn("SuspenseList");case 0:case 2:case 15:return e=Fl(e.type,!1),e;case 11:return e=Fl(e.type.render,!1),e;case 1:return e=Fl(e.type,!0),e;default:return""}}function xo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ir:return"Fragment";case Mr:return"Portal";case fo:return"Profiler";case ji:return"StrictMode";case po:return"Suspense";case go:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case id:return(e.displayName||"Context")+".Consumer";case od:return(e._context.displayName||"Context")+".Provider";case Ni:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ki:return t=e.displayName||null,t!==null?t:xo(e.type)||"Memo";case zt:t=e._payload,e=e._init;try{return xo(e(t))}catch{}}return null}function Am(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xo(t);case 8:return t===ji?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function _m(e){var t=cd(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,o=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){s=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return s},setValue:function(i){s=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function hs(e){e._valueTracker||(e._valueTracker=_m(e))}function dd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),s="";return e&&(s=cd(e)?e.checked?"true":"false":e.value),e=s,e!==r?(t.setValue(e),!0):!1}function qs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function yo(e,t){var r=t.checked;return he({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function _a(e,t){var r=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;r=Zt(t.value!=null?t.value:r),e._wrapperState={initialChecked:s,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ud(e,t){t=t.checked,t!=null&&bi(e,"checked",t,!1)}function vo(e,t){ud(e,t);var r=Zt(t.value),s=t.type;if(r!=null)s==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?wo(e,t.type,r):t.hasOwnProperty("defaultValue")&&wo(e,t.type,Zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ta(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function wo(e,t,r){(t!=="number"||qs(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var gn=Array.isArray;function Fr(e,t,r,s){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&s&&(e[r].defaultSelected=!0)}else{for(r=""+Zt(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,s&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function bo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return he({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function La(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(A(92));if(gn(r)){if(1<r.length)throw Error(A(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Zt(r)}}function md(e,t){var r=Zt(t.value),s=Zt(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),s!=null&&(e.defaultValue=""+s)}function Pa(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function hd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function jo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?hd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var fs,fd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,s,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,s,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(fs=fs||document.createElement("div"),fs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=fs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Tn(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var vn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Tm=["Webkit","ms","Moz","O"];Object.keys(vn).forEach(function(e){Tm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),vn[t]=vn[e]})});function pd(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||vn.hasOwnProperty(e)&&vn[e]?(""+t).trim():t+"px"}function gd(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var s=r.indexOf("--")===0,l=pd(r,t[r],s);r==="float"&&(r="cssFloat"),s?e.setProperty(r,l):e[r]=l}}var Lm=he({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function No(e,t){if(t){if(Lm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function ko(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var So=null;function Si(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Co=null,Br=null,Vr=null;function Ra(e){if(e=ts(e)){if(typeof Co!="function")throw Error(A(280));var t=e.stateNode;t&&(t=yl(t),Co(e.stateNode,e.type,t))}}function xd(e){Br?Vr?Vr.push(e):Vr=[e]:Br=e}function yd(){if(Br){var e=Br,t=Vr;if(Vr=Br=null,Ra(e),t)for(e=0;e<t.length;e++)Ra(t[e])}}function vd(e,t){return e(t)}function wd(){}var Bl=!1;function bd(e,t,r){if(Bl)return e(t,r);Bl=!0;try{return vd(e,t,r)}finally{Bl=!1,(Br!==null||Vr!==null)&&(wd(),yd())}}function Ln(e,t){var r=e.stateNode;if(r===null)return null;var s=yl(r);if(s===null)return null;r=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(A(231,t,typeof r));return r}var Eo=!1;if(Mt)try{var an={};Object.defineProperty(an,"passive",{get:function(){Eo=!0}}),window.addEventListener("test",an,an),window.removeEventListener("test",an,an)}catch{Eo=!1}function Pm(e,t,r,s,l,o,i,a,c){var d=Array.prototype.slice.call(arguments,3);try{t.apply(r,d)}catch(g){this.onError(g)}}var wn=!1,Hs=null,Ws=!1,Mo=null,Rm={onError:function(e){wn=!0,Hs=e}};function zm(e,t,r,s,l,o,i,a,c){wn=!1,Hs=null,Pm.apply(Rm,arguments)}function Om(e,t,r,s,l,o,i,a,c){if(zm.apply(this,arguments),wn){if(wn){var d=Hs;wn=!1,Hs=null}else throw Error(A(198));Ws||(Ws=!0,Mo=d)}}function Nr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function jd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function za(e){if(Nr(e)!==e)throw Error(A(188))}function Um(e){var t=e.alternate;if(!t){if(t=Nr(e),t===null)throw Error(A(188));return t!==e?null:e}for(var r=e,s=t;;){var l=r.return;if(l===null)break;var o=l.alternate;if(o===null){if(s=l.return,s!==null){r=s;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===r)return za(l),e;if(o===s)return za(l),t;o=o.sibling}throw Error(A(188))}if(r.return!==s.return)r=l,s=o;else{for(var i=!1,a=l.child;a;){if(a===r){i=!0,r=l,s=o;break}if(a===s){i=!0,s=l,r=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===r){i=!0,r=o,s=l;break}if(a===s){i=!0,s=o,r=l;break}a=a.sibling}if(!i)throw Error(A(189))}}if(r.alternate!==s)throw Error(A(190))}if(r.tag!==3)throw Error(A(188));return r.stateNode.current===r?e:t}function Nd(e){return e=Um(e),e!==null?kd(e):null}function kd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=kd(e);if(t!==null)return t;e=e.sibling}return null}var Sd=De.unstable_scheduleCallback,Oa=De.unstable_cancelCallback,$m=De.unstable_shouldYield,Fm=De.unstable_requestPaint,pe=De.unstable_now,Bm=De.unstable_getCurrentPriorityLevel,Ci=De.unstable_ImmediatePriority,Cd=De.unstable_UserBlockingPriority,Qs=De.unstable_NormalPriority,Vm=De.unstable_LowPriority,Ed=De.unstable_IdlePriority,fl=null,bt=null;function qm(e){if(bt&&typeof bt.onCommitFiberRoot=="function")try{bt.onCommitFiberRoot(fl,e,void 0,(e.current.flags&128)===128)}catch{}}var dt=Math.clz32?Math.clz32:Qm,Hm=Math.log,Wm=Math.LN2;function Qm(e){return e>>>=0,e===0?32:31-(Hm(e)/Wm|0)|0}var ps=64,gs=4194304;function xn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Gs(e,t){var r=e.pendingLanes;if(r===0)return 0;var s=0,l=e.suspendedLanes,o=e.pingedLanes,i=r&268435455;if(i!==0){var a=i&~l;a!==0?s=xn(a):(o&=i,o!==0&&(s=xn(o)))}else i=r&~l,i!==0?s=xn(i):o!==0&&(s=xn(o));if(s===0)return 0;if(t!==0&&t!==s&&!(t&l)&&(l=s&-s,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(s&4&&(s|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)r=31-dt(t),l=1<<r,s|=e[r],t&=~l;return s}function Gm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Dm(e,t){for(var r=e.suspendedLanes,s=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-dt(o),a=1<<i,c=l[i];c===-1?(!(a&r)||a&s)&&(l[i]=Gm(a,t)):c<=t&&(e.expiredLanes|=a),o&=~a}}function Io(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Md(){var e=ps;return ps<<=1,!(ps&4194240)&&(ps=64),e}function Vl(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function Xn(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-dt(t),e[t]=r}function Jm(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-dt(r),o=1<<l;t[l]=0,s[l]=-1,e[l]=-1,r&=~o}}function Ei(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var s=31-dt(r),l=1<<s;l&t|e[s]&t&&(e[s]|=t),r&=~l}}var ee=0;function Id(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ad,Mi,_d,Td,Ld,Ao=!1,xs=[],Vt=null,qt=null,Ht=null,Pn=new Map,Rn=new Map,Ut=[],Zm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ua(e,t){switch(e){case"focusin":case"focusout":Vt=null;break;case"dragenter":case"dragleave":qt=null;break;case"mouseover":case"mouseout":Ht=null;break;case"pointerover":case"pointerout":Pn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rn.delete(t.pointerId)}}function cn(e,t,r,s,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:r,eventSystemFlags:s,nativeEvent:o,targetContainers:[l]},t!==null&&(t=ts(t),t!==null&&Mi(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Km(e,t,r,s,l){switch(t){case"focusin":return Vt=cn(Vt,e,t,r,s,l),!0;case"dragenter":return qt=cn(qt,e,t,r,s,l),!0;case"mouseover":return Ht=cn(Ht,e,t,r,s,l),!0;case"pointerover":var o=l.pointerId;return Pn.set(o,cn(Pn.get(o)||null,e,t,r,s,l)),!0;case"gotpointercapture":return o=l.pointerId,Rn.set(o,cn(Rn.get(o)||null,e,t,r,s,l)),!0}return!1}function Pd(e){var t=or(e.target);if(t!==null){var r=Nr(t);if(r!==null){if(t=r.tag,t===13){if(t=jd(r),t!==null){e.blockedOn=t,Ld(e.priority,function(){_d(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function _s(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=_o(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var s=new r.constructor(r.type,r);So=s,r.target.dispatchEvent(s),So=null}else return t=ts(r),t!==null&&Mi(t),e.blockedOn=r,!1;t.shift()}return!0}function $a(e,t,r){_s(e)&&r.delete(t)}function Ym(){Ao=!1,Vt!==null&&_s(Vt)&&(Vt=null),qt!==null&&_s(qt)&&(qt=null),Ht!==null&&_s(Ht)&&(Ht=null),Pn.forEach($a),Rn.forEach($a)}function dn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ao||(Ao=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Ym)))}function zn(e){function t(l){return dn(l,e)}if(0<xs.length){dn(xs[0],e);for(var r=1;r<xs.length;r++){var s=xs[r];s.blockedOn===e&&(s.blockedOn=null)}}for(Vt!==null&&dn(Vt,e),qt!==null&&dn(qt,e),Ht!==null&&dn(Ht,e),Pn.forEach(t),Rn.forEach(t),r=0;r<Ut.length;r++)s=Ut[r],s.blockedOn===e&&(s.blockedOn=null);for(;0<Ut.length&&(r=Ut[0],r.blockedOn===null);)Pd(r),r.blockedOn===null&&Ut.shift()}var qr=Tt.ReactCurrentBatchConfig,Ds=!0;function Xm(e,t,r,s){var l=ee,o=qr.transition;qr.transition=null;try{ee=1,Ii(e,t,r,s)}finally{ee=l,qr.transition=o}}function eh(e,t,r,s){var l=ee,o=qr.transition;qr.transition=null;try{ee=4,Ii(e,t,r,s)}finally{ee=l,qr.transition=o}}function Ii(e,t,r,s){if(Ds){var l=_o(e,t,r,s);if(l===null)Yl(e,t,s,Js,r),Ua(e,s);else if(Km(l,e,t,r,s))s.stopPropagation();else if(Ua(e,s),t&4&&-1<Zm.indexOf(e)){for(;l!==null;){var o=ts(l);if(o!==null&&Ad(o),o=_o(e,t,r,s),o===null&&Yl(e,t,s,Js,r),o===l)break;l=o}l!==null&&s.stopPropagation()}else Yl(e,t,s,null,r)}}var Js=null;function _o(e,t,r,s){if(Js=null,e=Si(s),e=or(e),e!==null)if(t=Nr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=jd(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Js=e,null}function Rd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Bm()){case Ci:return 1;case Cd:return 4;case Qs:case Vm:return 16;case Ed:return 536870912;default:return 16}default:return 16}}var Ft=null,Ai=null,Ts=null;function zd(){if(Ts)return Ts;var e,t=Ai,r=t.length,s,l="value"in Ft?Ft.value:Ft.textContent,o=l.length;for(e=0;e<r&&t[e]===l[e];e++);var i=r-e;for(s=1;s<=i&&t[r-s]===l[o-s];s++);return Ts=l.slice(e,1<s?1-s:void 0)}function Ls(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ys(){return!0}function Fa(){return!1}function Ze(e){function t(r,s,l,o,i){this._reactName=r,this._targetInst=l,this.type=s,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(r=e[a],this[a]=r?r(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?ys:Fa,this.isPropagationStopped=Fa,this}return he(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=ys)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=ys)},persist:function(){},isPersistent:ys}),t}var tn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_i=Ze(tn),es=he({},tn,{view:0,detail:0}),th=Ze(es),ql,Hl,un,pl=he({},es,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ti,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==un&&(un&&e.type==="mousemove"?(ql=e.screenX-un.screenX,Hl=e.screenY-un.screenY):Hl=ql=0,un=e),ql)},movementY:function(e){return"movementY"in e?e.movementY:Hl}}),Ba=Ze(pl),rh=he({},pl,{dataTransfer:0}),nh=Ze(rh),sh=he({},es,{relatedTarget:0}),Wl=Ze(sh),lh=he({},tn,{animationName:0,elapsedTime:0,pseudoElement:0}),oh=Ze(lh),ih=he({},tn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ah=Ze(ih),ch=he({},tn,{data:0}),Va=Ze(ch),dh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},uh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},mh={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=mh[e])?!!t[e]:!1}function Ti(){return hh}var fh=he({},es,{key:function(e){if(e.key){var t=dh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ls(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?uh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ti,charCode:function(e){return e.type==="keypress"?Ls(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ls(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ph=Ze(fh),gh=he({},pl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qa=Ze(gh),xh=he({},es,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ti}),yh=Ze(xh),vh=he({},tn,{propertyName:0,elapsedTime:0,pseudoElement:0}),wh=Ze(vh),bh=he({},pl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),jh=Ze(bh),Nh=[9,13,27,32],Li=Mt&&"CompositionEvent"in window,bn=null;Mt&&"documentMode"in document&&(bn=document.documentMode);var kh=Mt&&"TextEvent"in window&&!bn,Od=Mt&&(!Li||bn&&8<bn&&11>=bn),Ha=" ",Wa=!1;function Ud(e,t){switch(e){case"keyup":return Nh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $d(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ar=!1;function Sh(e,t){switch(e){case"compositionend":return $d(t);case"keypress":return t.which!==32?null:(Wa=!0,Ha);case"textInput":return e=t.data,e===Ha&&Wa?null:e;default:return null}}function Ch(e,t){if(Ar)return e==="compositionend"||!Li&&Ud(e,t)?(e=zd(),Ts=Ai=Ft=null,Ar=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Od&&t.locale!=="ko"?null:t.data;default:return null}}var Eh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Eh[e.type]:t==="textarea"}function Fd(e,t,r,s){xd(s),t=Zs(t,"onChange"),0<t.length&&(r=new _i("onChange","change",null,r,s),e.push({event:r,listeners:t}))}var jn=null,On=null;function Mh(e){Kd(e,0)}function gl(e){var t=Lr(e);if(dd(t))return e}function Ih(e,t){if(e==="change")return t}var Bd=!1;if(Mt){var Ql;if(Mt){var Gl="oninput"in document;if(!Gl){var Ga=document.createElement("div");Ga.setAttribute("oninput","return;"),Gl=typeof Ga.oninput=="function"}Ql=Gl}else Ql=!1;Bd=Ql&&(!document.documentMode||9<document.documentMode)}function Da(){jn&&(jn.detachEvent("onpropertychange",Vd),On=jn=null)}function Vd(e){if(e.propertyName==="value"&&gl(On)){var t=[];Fd(t,On,e,Si(e)),bd(Mh,t)}}function Ah(e,t,r){e==="focusin"?(Da(),jn=t,On=r,jn.attachEvent("onpropertychange",Vd)):e==="focusout"&&Da()}function _h(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return gl(On)}function Th(e,t){if(e==="click")return gl(t)}function Lh(e,t){if(e==="input"||e==="change")return gl(t)}function Ph(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var mt=typeof Object.is=="function"?Object.is:Ph;function Un(e,t){if(mt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(s=0;s<r.length;s++){var l=r[s];if(!ho.call(t,l)||!mt(e[l],t[l]))return!1}return!0}function Ja(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Za(e,t){var r=Ja(e);e=0;for(var s;r;){if(r.nodeType===3){if(s=e+r.textContent.length,e<=t&&s>=t)return{node:r,offset:t-e};e=s}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Ja(r)}}function qd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?qd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Hd(){for(var e=window,t=qs();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=qs(e.document)}return t}function Pi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Rh(e){var t=Hd(),r=e.focusedElem,s=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&qd(r.ownerDocument.documentElement,r)){if(s!==null&&Pi(r)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,o=Math.min(s.start,l);s=s.end===void 0?o:Math.min(s.end,l),!e.extend&&o>s&&(l=s,s=o,o=l),l=Za(r,o);var i=Za(r,s);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>s?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var zh=Mt&&"documentMode"in document&&11>=document.documentMode,_r=null,To=null,Nn=null,Lo=!1;function Ka(e,t,r){var s=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;Lo||_r==null||_r!==qs(s)||(s=_r,"selectionStart"in s&&Pi(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Nn&&Un(Nn,s)||(Nn=s,s=Zs(To,"onSelect"),0<s.length&&(t=new _i("onSelect","select",null,t,r),e.push({event:t,listeners:s}),t.target=_r)))}function vs(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Tr={animationend:vs("Animation","AnimationEnd"),animationiteration:vs("Animation","AnimationIteration"),animationstart:vs("Animation","AnimationStart"),transitionend:vs("Transition","TransitionEnd")},Dl={},Wd={};Mt&&(Wd=document.createElement("div").style,"AnimationEvent"in window||(delete Tr.animationend.animation,delete Tr.animationiteration.animation,delete Tr.animationstart.animation),"TransitionEvent"in window||delete Tr.transitionend.transition);function xl(e){if(Dl[e])return Dl[e];if(!Tr[e])return e;var t=Tr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in Wd)return Dl[e]=t[r];return e}var Qd=xl("animationend"),Gd=xl("animationiteration"),Dd=xl("animationstart"),Jd=xl("transitionend"),Zd=new Map,Ya="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Yt(e,t){Zd.set(e,t),jr(t,[e])}for(var Jl=0;Jl<Ya.length;Jl++){var Zl=Ya[Jl],Oh=Zl.toLowerCase(),Uh=Zl[0].toUpperCase()+Zl.slice(1);Yt(Oh,"on"+Uh)}Yt(Qd,"onAnimationEnd");Yt(Gd,"onAnimationIteration");Yt(Dd,"onAnimationStart");Yt("dblclick","onDoubleClick");Yt("focusin","onFocus");Yt("focusout","onBlur");Yt(Jd,"onTransitionEnd");Qr("onMouseEnter",["mouseout","mouseover"]);Qr("onMouseLeave",["mouseout","mouseover"]);Qr("onPointerEnter",["pointerout","pointerover"]);Qr("onPointerLeave",["pointerout","pointerover"]);jr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));jr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));jr("onBeforeInput",["compositionend","keypress","textInput","paste"]);jr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));jr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var yn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),$h=new Set("cancel close invalid load scroll toggle".split(" ").concat(yn));function Xa(e,t,r){var s=e.type||"unknown-event";e.currentTarget=r,Om(s,t,void 0,e),e.currentTarget=null}function Kd(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var s=e[r],l=s.event;s=s.listeners;e:{var o=void 0;if(t)for(var i=s.length-1;0<=i;i--){var a=s[i],c=a.instance,d=a.currentTarget;if(a=a.listener,c!==o&&l.isPropagationStopped())break e;Xa(l,a,d),o=c}else for(i=0;i<s.length;i++){if(a=s[i],c=a.instance,d=a.currentTarget,a=a.listener,c!==o&&l.isPropagationStopped())break e;Xa(l,a,d),o=c}}}if(Ws)throw e=Mo,Ws=!1,Mo=null,e}function le(e,t){var r=t[Uo];r===void 0&&(r=t[Uo]=new Set);var s=e+"__bubble";r.has(s)||(Yd(t,e,2,!1),r.add(s))}function Kl(e,t,r){var s=0;t&&(s|=4),Yd(r,e,s,t)}var ws="_reactListening"+Math.random().toString(36).slice(2);function $n(e){if(!e[ws]){e[ws]=!0,ld.forEach(function(r){r!=="selectionchange"&&($h.has(r)||Kl(r,!1,e),Kl(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ws]||(t[ws]=!0,Kl("selectionchange",!1,t))}}function Yd(e,t,r,s){switch(Rd(t)){case 1:var l=Xm;break;case 4:l=eh;break;default:l=Ii}r=l.bind(null,t,r,e),l=void 0,!Eo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),s?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function Yl(e,t,r,s,l){var o=s;if(!(t&1)&&!(t&2)&&s!==null)e:for(;;){if(s===null)return;var i=s.tag;if(i===3||i===4){var a=s.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(i===4)for(i=s.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;i=i.return}for(;a!==null;){if(i=or(a),i===null)return;if(c=i.tag,c===5||c===6){s=o=i;continue e}a=a.parentNode}}s=s.return}bd(function(){var d=o,g=Si(r),v=[];e:{var b=Zd.get(e);if(b!==void 0){var N=_i,j=e;switch(e){case"keypress":if(Ls(r)===0)break e;case"keydown":case"keyup":N=ph;break;case"focusin":j="focus",N=Wl;break;case"focusout":j="blur",N=Wl;break;case"beforeblur":case"afterblur":N=Wl;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=Ba;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=nh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=yh;break;case Qd:case Gd:case Dd:N=oh;break;case Jd:N=wh;break;case"scroll":N=th;break;case"wheel":N=jh;break;case"copy":case"cut":case"paste":N=ah;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=qa}var k=(t&4)!==0,S=!k&&e==="scroll",m=k?b!==null?b+"Capture":null:b;k=[];for(var u=d,p;u!==null;){p=u;var E=p.stateNode;if(p.tag===5&&E!==null&&(p=E,m!==null&&(E=Ln(u,m),E!=null&&k.push(Fn(u,E,p)))),S)break;u=u.return}0<k.length&&(b=new N(b,j,null,r,g),v.push({event:b,listeners:k}))}}if(!(t&7)){e:{if(b=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",b&&r!==So&&(j=r.relatedTarget||r.fromElement)&&(or(j)||j[It]))break e;if((N||b)&&(b=g.window===g?g:(b=g.ownerDocument)?b.defaultView||b.parentWindow:window,N?(j=r.relatedTarget||r.toElement,N=d,j=j?or(j):null,j!==null&&(S=Nr(j),j!==S||j.tag!==5&&j.tag!==6)&&(j=null)):(N=null,j=d),N!==j)){if(k=Ba,E="onMouseLeave",m="onMouseEnter",u="mouse",(e==="pointerout"||e==="pointerover")&&(k=qa,E="onPointerLeave",m="onPointerEnter",u="pointer"),S=N==null?b:Lr(N),p=j==null?b:Lr(j),b=new k(E,u+"leave",N,r,g),b.target=S,b.relatedTarget=p,E=null,or(g)===d&&(k=new k(m,u+"enter",j,r,g),k.target=p,k.relatedTarget=S,E=k),S=E,N&&j)t:{for(k=N,m=j,u=0,p=k;p;p=Er(p))u++;for(p=0,E=m;E;E=Er(E))p++;for(;0<u-p;)k=Er(k),u--;for(;0<p-u;)m=Er(m),p--;for(;u--;){if(k===m||m!==null&&k===m.alternate)break t;k=Er(k),m=Er(m)}k=null}else k=null;N!==null&&ec(v,b,N,k,!1),j!==null&&S!==null&&ec(v,S,j,k,!0)}}e:{if(b=d?Lr(d):window,N=b.nodeName&&b.nodeName.toLowerCase(),N==="select"||N==="input"&&b.type==="file")var L=Ih;else if(Qa(b))if(Bd)L=Lh;else{L=_h;var O=Ah}else(N=b.nodeName)&&N.toLowerCase()==="input"&&(b.type==="checkbox"||b.type==="radio")&&(L=Th);if(L&&(L=L(e,d))){Fd(v,L,r,g);break e}O&&O(e,b,d),e==="focusout"&&(O=b._wrapperState)&&O.controlled&&b.type==="number"&&wo(b,"number",b.value)}switch(O=d?Lr(d):window,e){case"focusin":(Qa(O)||O.contentEditable==="true")&&(_r=O,To=d,Nn=null);break;case"focusout":Nn=To=_r=null;break;case"mousedown":Lo=!0;break;case"contextmenu":case"mouseup":case"dragend":Lo=!1,Ka(v,r,g);break;case"selectionchange":if(zh)break;case"keydown":case"keyup":Ka(v,r,g)}var z;if(Li)e:{switch(e){case"compositionstart":var $="onCompositionStart";break e;case"compositionend":$="onCompositionEnd";break e;case"compositionupdate":$="onCompositionUpdate";break e}$=void 0}else Ar?Ud(e,r)&&($="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&($="onCompositionStart");$&&(Od&&r.locale!=="ko"&&(Ar||$!=="onCompositionStart"?$==="onCompositionEnd"&&Ar&&(z=zd()):(Ft=g,Ai="value"in Ft?Ft.value:Ft.textContent,Ar=!0)),O=Zs(d,$),0<O.length&&($=new Va($,e,null,r,g),v.push({event:$,listeners:O}),z?$.data=z:(z=$d(r),z!==null&&($.data=z)))),(z=kh?Sh(e,r):Ch(e,r))&&(d=Zs(d,"onBeforeInput"),0<d.length&&(g=new Va("onBeforeInput","beforeinput",null,r,g),v.push({event:g,listeners:d}),g.data=z))}Kd(v,t)})}function Fn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Zs(e,t){for(var r=t+"Capture",s=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Ln(e,r),o!=null&&s.unshift(Fn(e,o,l)),o=Ln(e,t),o!=null&&s.push(Fn(e,o,l))),e=e.return}return s}function Er(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ec(e,t,r,s,l){for(var o=t._reactName,i=[];r!==null&&r!==s;){var a=r,c=a.alternate,d=a.stateNode;if(c!==null&&c===s)break;a.tag===5&&d!==null&&(a=d,l?(c=Ln(r,o),c!=null&&i.unshift(Fn(r,c,a))):l||(c=Ln(r,o),c!=null&&i.push(Fn(r,c,a)))),r=r.return}i.length!==0&&e.push({event:t,listeners:i})}var Fh=/\r\n?/g,Bh=/\u0000|\uFFFD/g;function tc(e){return(typeof e=="string"?e:""+e).replace(Fh,`
`).replace(Bh,"")}function bs(e,t,r){if(t=tc(t),tc(e)!==t&&r)throw Error(A(425))}function Ks(){}var Po=null,Ro=null;function zo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Oo=typeof setTimeout=="function"?setTimeout:void 0,Vh=typeof clearTimeout=="function"?clearTimeout:void 0,rc=typeof Promise=="function"?Promise:void 0,qh=typeof queueMicrotask=="function"?queueMicrotask:typeof rc<"u"?function(e){return rc.resolve(null).then(e).catch(Hh)}:Oo;function Hh(e){setTimeout(function(){throw e})}function Xl(e,t){var r=t,s=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(s===0){e.removeChild(l),zn(t);return}s--}else r!=="$"&&r!=="$?"&&r!=="$!"||s++;r=l}while(r);zn(t)}function Wt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function nc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var rn=Math.random().toString(36).slice(2),vt="__reactFiber$"+rn,Bn="__reactProps$"+rn,It="__reactContainer$"+rn,Uo="__reactEvents$"+rn,Wh="__reactListeners$"+rn,Qh="__reactHandles$"+rn;function or(e){var t=e[vt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[It]||r[vt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=nc(e);e!==null;){if(r=e[vt])return r;e=nc(e)}return t}e=r,r=e.parentNode}return null}function ts(e){return e=e[vt]||e[It],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Lr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function yl(e){return e[Bn]||null}var $o=[],Pr=-1;function Xt(e){return{current:e}}function oe(e){0>Pr||(e.current=$o[Pr],$o[Pr]=null,Pr--)}function re(e,t){Pr++,$o[Pr]=e.current,e.current=t}var Kt={},_e=Xt(Kt),Ve=Xt(!1),gr=Kt;function Gr(e,t){var r=e.type.contextTypes;if(!r)return Kt;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in r)l[o]=t[o];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function qe(e){return e=e.childContextTypes,e!=null}function Ys(){oe(Ve),oe(_e)}function sc(e,t,r){if(_e.current!==Kt)throw Error(A(168));re(_e,t),re(Ve,r)}function Xd(e,t,r){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return r;s=s.getChildContext();for(var l in s)if(!(l in t))throw Error(A(108,Am(e)||"Unknown",l));return he({},r,s)}function Xs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kt,gr=_e.current,re(_e,e),re(Ve,Ve.current),!0}function lc(e,t,r){var s=e.stateNode;if(!s)throw Error(A(169));r?(e=Xd(e,t,gr),s.__reactInternalMemoizedMergedChildContext=e,oe(Ve),oe(_e),re(_e,e)):oe(Ve),re(Ve,r)}var kt=null,vl=!1,eo=!1;function eu(e){kt===null?kt=[e]:kt.push(e)}function Gh(e){vl=!0,eu(e)}function er(){if(!eo&&kt!==null){eo=!0;var e=0,t=ee;try{var r=kt;for(ee=1;e<r.length;e++){var s=r[e];do s=s(!0);while(s!==null)}kt=null,vl=!1}catch(l){throw kt!==null&&(kt=kt.slice(e+1)),Sd(Ci,er),l}finally{ee=t,eo=!1}}return null}var Rr=[],zr=0,el=null,tl=0,Xe=[],et=0,xr=null,St=1,Ct="";function sr(e,t){Rr[zr++]=tl,Rr[zr++]=el,el=e,tl=t}function tu(e,t,r){Xe[et++]=St,Xe[et++]=Ct,Xe[et++]=xr,xr=e;var s=St;e=Ct;var l=32-dt(s)-1;s&=~(1<<l),r+=1;var o=32-dt(t)+l;if(30<o){var i=l-l%5;o=(s&(1<<i)-1).toString(32),s>>=i,l-=i,St=1<<32-dt(t)+l|r<<l|s,Ct=o+e}else St=1<<o|r<<l|s,Ct=e}function Ri(e){e.return!==null&&(sr(e,1),tu(e,1,0))}function zi(e){for(;e===el;)el=Rr[--zr],Rr[zr]=null,tl=Rr[--zr],Rr[zr]=null;for(;e===xr;)xr=Xe[--et],Xe[et]=null,Ct=Xe[--et],Xe[et]=null,St=Xe[--et],Xe[et]=null}var Ge=null,Qe=null,ie=!1,ct=null;function ru(e,t){var r=tt(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function oc(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ge=e,Qe=Wt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ge=e,Qe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=xr!==null?{id:St,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=tt(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,Ge=e,Qe=null,!0):!1;default:return!1}}function Fo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bo(e){if(ie){var t=Qe;if(t){var r=t;if(!oc(e,t)){if(Fo(e))throw Error(A(418));t=Wt(r.nextSibling);var s=Ge;t&&oc(e,t)?ru(s,r):(e.flags=e.flags&-4097|2,ie=!1,Ge=e)}}else{if(Fo(e))throw Error(A(418));e.flags=e.flags&-4097|2,ie=!1,Ge=e}}}function ic(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ge=e}function js(e){if(e!==Ge)return!1;if(!ie)return ic(e),ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!zo(e.type,e.memoizedProps)),t&&(t=Qe)){if(Fo(e))throw nu(),Error(A(418));for(;t;)ru(e,t),t=Wt(t.nextSibling)}if(ic(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){Qe=Wt(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}Qe=null}}else Qe=Ge?Wt(e.stateNode.nextSibling):null;return!0}function nu(){for(var e=Qe;e;)e=Wt(e.nextSibling)}function Dr(){Qe=Ge=null,ie=!1}function Oi(e){ct===null?ct=[e]:ct.push(e)}var Dh=Tt.ReactCurrentBatchConfig;function mn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(A(309));var s=r.stateNode}if(!s)throw Error(A(147,e));var l=s,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=l.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(A(284));if(!r._owner)throw Error(A(290,e))}return e}function Ns(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ac(e){var t=e._init;return t(e._payload)}function su(e){function t(m,u){if(e){var p=m.deletions;p===null?(m.deletions=[u],m.flags|=16):p.push(u)}}function r(m,u){if(!e)return null;for(;u!==null;)t(m,u),u=u.sibling;return null}function s(m,u){for(m=new Map;u!==null;)u.key!==null?m.set(u.key,u):m.set(u.index,u),u=u.sibling;return m}function l(m,u){return m=Jt(m,u),m.index=0,m.sibling=null,m}function o(m,u,p){return m.index=p,e?(p=m.alternate,p!==null?(p=p.index,p<u?(m.flags|=2,u):p):(m.flags|=2,u)):(m.flags|=1048576,u)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,u,p,E){return u===null||u.tag!==6?(u=io(p,m.mode,E),u.return=m,u):(u=l(u,p),u.return=m,u)}function c(m,u,p,E){var L=p.type;return L===Ir?g(m,u,p.props.children,E,p.key):u!==null&&(u.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===zt&&ac(L)===u.type)?(E=l(u,p.props),E.ref=mn(m,u,p),E.return=m,E):(E=Fs(p.type,p.key,p.props,null,m.mode,E),E.ref=mn(m,u,p),E.return=m,E)}function d(m,u,p,E){return u===null||u.tag!==4||u.stateNode.containerInfo!==p.containerInfo||u.stateNode.implementation!==p.implementation?(u=ao(p,m.mode,E),u.return=m,u):(u=l(u,p.children||[]),u.return=m,u)}function g(m,u,p,E,L){return u===null||u.tag!==7?(u=ur(p,m.mode,E,L),u.return=m,u):(u=l(u,p),u.return=m,u)}function v(m,u,p){if(typeof u=="string"&&u!==""||typeof u=="number")return u=io(""+u,m.mode,p),u.return=m,u;if(typeof u=="object"&&u!==null){switch(u.$$typeof){case ms:return p=Fs(u.type,u.key,u.props,null,m.mode,p),p.ref=mn(m,null,u),p.return=m,p;case Mr:return u=ao(u,m.mode,p),u.return=m,u;case zt:var E=u._init;return v(m,E(u._payload),p)}if(gn(u)||on(u))return u=ur(u,m.mode,p,null),u.return=m,u;Ns(m,u)}return null}function b(m,u,p,E){var L=u!==null?u.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return L!==null?null:a(m,u,""+p,E);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ms:return p.key===L?c(m,u,p,E):null;case Mr:return p.key===L?d(m,u,p,E):null;case zt:return L=p._init,b(m,u,L(p._payload),E)}if(gn(p)||on(p))return L!==null?null:g(m,u,p,E,null);Ns(m,p)}return null}function N(m,u,p,E,L){if(typeof E=="string"&&E!==""||typeof E=="number")return m=m.get(p)||null,a(u,m,""+E,L);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case ms:return m=m.get(E.key===null?p:E.key)||null,c(u,m,E,L);case Mr:return m=m.get(E.key===null?p:E.key)||null,d(u,m,E,L);case zt:var O=E._init;return N(m,u,p,O(E._payload),L)}if(gn(E)||on(E))return m=m.get(p)||null,g(u,m,E,L,null);Ns(u,E)}return null}function j(m,u,p,E){for(var L=null,O=null,z=u,$=u=0,G=null;z!==null&&$<p.length;$++){z.index>$?(G=z,z=null):G=z.sibling;var x=b(m,z,p[$],E);if(x===null){z===null&&(z=G);break}e&&z&&x.alternate===null&&t(m,z),u=o(x,u,$),O===null?L=x:O.sibling=x,O=x,z=G}if($===p.length)return r(m,z),ie&&sr(m,$),L;if(z===null){for(;$<p.length;$++)z=v(m,p[$],E),z!==null&&(u=o(z,u,$),O===null?L=z:O.sibling=z,O=z);return ie&&sr(m,$),L}for(z=s(m,z);$<p.length;$++)G=N(z,m,$,p[$],E),G!==null&&(e&&G.alternate!==null&&z.delete(G.key===null?$:G.key),u=o(G,u,$),O===null?L=G:O.sibling=G,O=G);return e&&z.forEach(function(U){return t(m,U)}),ie&&sr(m,$),L}function k(m,u,p,E){var L=on(p);if(typeof L!="function")throw Error(A(150));if(p=L.call(p),p==null)throw Error(A(151));for(var O=L=null,z=u,$=u=0,G=null,x=p.next();z!==null&&!x.done;$++,x=p.next()){z.index>$?(G=z,z=null):G=z.sibling;var U=b(m,z,x.value,E);if(U===null){z===null&&(z=G);break}e&&z&&U.alternate===null&&t(m,z),u=o(U,u,$),O===null?L=U:O.sibling=U,O=U,z=G}if(x.done)return r(m,z),ie&&sr(m,$),L;if(z===null){for(;!x.done;$++,x=p.next())x=v(m,x.value,E),x!==null&&(u=o(x,u,$),O===null?L=x:O.sibling=x,O=x);return ie&&sr(m,$),L}for(z=s(m,z);!x.done;$++,x=p.next())x=N(z,m,$,x.value,E),x!==null&&(e&&x.alternate!==null&&z.delete(x.key===null?$:x.key),u=o(x,u,$),O===null?L=x:O.sibling=x,O=x);return e&&z.forEach(function(Y){return t(m,Y)}),ie&&sr(m,$),L}function S(m,u,p,E){if(typeof p=="object"&&p!==null&&p.type===Ir&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case ms:e:{for(var L=p.key,O=u;O!==null;){if(O.key===L){if(L=p.type,L===Ir){if(O.tag===7){r(m,O.sibling),u=l(O,p.props.children),u.return=m,m=u;break e}}else if(O.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===zt&&ac(L)===O.type){r(m,O.sibling),u=l(O,p.props),u.ref=mn(m,O,p),u.return=m,m=u;break e}r(m,O);break}else t(m,O);O=O.sibling}p.type===Ir?(u=ur(p.props.children,m.mode,E,p.key),u.return=m,m=u):(E=Fs(p.type,p.key,p.props,null,m.mode,E),E.ref=mn(m,u,p),E.return=m,m=E)}return i(m);case Mr:e:{for(O=p.key;u!==null;){if(u.key===O)if(u.tag===4&&u.stateNode.containerInfo===p.containerInfo&&u.stateNode.implementation===p.implementation){r(m,u.sibling),u=l(u,p.children||[]),u.return=m,m=u;break e}else{r(m,u);break}else t(m,u);u=u.sibling}u=ao(p,m.mode,E),u.return=m,m=u}return i(m);case zt:return O=p._init,S(m,u,O(p._payload),E)}if(gn(p))return j(m,u,p,E);if(on(p))return k(m,u,p,E);Ns(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,u!==null&&u.tag===6?(r(m,u.sibling),u=l(u,p),u.return=m,m=u):(r(m,u),u=io(p,m.mode,E),u.return=m,m=u),i(m)):r(m,u)}return S}var Jr=su(!0),lu=su(!1),rl=Xt(null),nl=null,Or=null,Ui=null;function $i(){Ui=Or=nl=null}function Fi(e){var t=rl.current;oe(rl),e._currentValue=t}function Vo(e,t,r){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===r)break;e=e.return}}function Hr(e,t){nl=e,Ui=Or=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Be=!0),e.firstContext=null)}function nt(e){var t=e._currentValue;if(Ui!==e)if(e={context:e,memoizedValue:t,next:null},Or===null){if(nl===null)throw Error(A(308));Or=e,nl.dependencies={lanes:0,firstContext:e}}else Or=Or.next=e;return t}var ir=null;function Bi(e){ir===null?ir=[e]:ir.push(e)}function ou(e,t,r,s){var l=t.interleaved;return l===null?(r.next=r,Bi(t)):(r.next=l.next,l.next=r),t.interleaved=r,At(e,s)}function At(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Ot=!1;function Vi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function iu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Et(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Qt(e,t,r){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,X&2){var l=s.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),s.pending=t,At(e,r)}return l=s.interleaved,l===null?(t.next=t,Bi(s)):(t.next=l.next,l.next=t),s.interleaved=t,At(e,r)}function Ps(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,r|=s,t.lanes=r,Ei(e,r)}}function cc(e,t){var r=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,r===s)){var l=null,o=null;if(r=r.firstBaseUpdate,r!==null){do{var i={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};o===null?l=o=i:o=o.next=i,r=r.next}while(r!==null);o===null?l=o=t:o=o.next=t}else l=o=t;r={baseState:s.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:s.shared,effects:s.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function sl(e,t,r,s){var l=e.updateQueue;Ot=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,d=c.next;c.next=null,i===null?o=d:i.next=d,i=c;var g=e.alternate;g!==null&&(g=g.updateQueue,a=g.lastBaseUpdate,a!==i&&(a===null?g.firstBaseUpdate=d:a.next=d,g.lastBaseUpdate=c))}if(o!==null){var v=l.baseState;i=0,g=d=c=null,a=o;do{var b=a.lane,N=a.eventTime;if((s&b)===b){g!==null&&(g=g.next={eventTime:N,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var j=e,k=a;switch(b=t,N=r,k.tag){case 1:if(j=k.payload,typeof j=="function"){v=j.call(N,v,b);break e}v=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=k.payload,b=typeof j=="function"?j.call(N,v,b):j,b==null)break e;v=he({},v,b);break e;case 2:Ot=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,b=l.effects,b===null?l.effects=[a]:b.push(a))}else N={eventTime:N,lane:b,tag:a.tag,payload:a.payload,callback:a.callback,next:null},g===null?(d=g=N,c=v):g=g.next=N,i|=b;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;b=a,a=b.next,b.next=null,l.lastBaseUpdate=b,l.shared.pending=null}}while(!0);if(g===null&&(c=v),l.baseState=c,l.firstBaseUpdate=d,l.lastBaseUpdate=g,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);vr|=i,e.lanes=i,e.memoizedState=v}}function dc(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],l=s.callback;if(l!==null){if(s.callback=null,s=r,typeof l!="function")throw Error(A(191,l));l.call(s)}}}var rs={},jt=Xt(rs),Vn=Xt(rs),qn=Xt(rs);function ar(e){if(e===rs)throw Error(A(174));return e}function qi(e,t){switch(re(qn,t),re(Vn,e),re(jt,rs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:jo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=jo(t,e)}oe(jt),re(jt,t)}function Zr(){oe(jt),oe(Vn),oe(qn)}function au(e){ar(qn.current);var t=ar(jt.current),r=jo(t,e.type);t!==r&&(re(Vn,e),re(jt,r))}function Hi(e){Vn.current===e&&(oe(jt),oe(Vn))}var ue=Xt(0);function ll(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function Wi(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var Rs=Tt.ReactCurrentDispatcher,ro=Tt.ReactCurrentBatchConfig,yr=0,me=null,ye=null,be=null,ol=!1,kn=!1,Hn=0,Jh=0;function Me(){throw Error(A(321))}function Qi(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!mt(e[r],t[r]))return!1;return!0}function Gi(e,t,r,s,l,o){if(yr=o,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Rs.current=e===null||e.memoizedState===null?Xh:ef,e=r(s,l),kn){o=0;do{if(kn=!1,Hn=0,25<=o)throw Error(A(301));o+=1,be=ye=null,t.updateQueue=null,Rs.current=tf,e=r(s,l)}while(kn)}if(Rs.current=il,t=ye!==null&&ye.next!==null,yr=0,be=ye=me=null,ol=!1,t)throw Error(A(300));return e}function Di(){var e=Hn!==0;return Hn=0,e}function yt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return be===null?me.memoizedState=be=e:be=be.next=e,be}function st(){if(ye===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=ye.next;var t=be===null?me.memoizedState:be.next;if(t!==null)be=t,ye=e;else{if(e===null)throw Error(A(310));ye=e,e={memoizedState:ye.memoizedState,baseState:ye.baseState,baseQueue:ye.baseQueue,queue:ye.queue,next:null},be===null?me.memoizedState=be=e:be=be.next=e}return be}function Wn(e,t){return typeof t=="function"?t(e):t}function no(e){var t=st(),r=t.queue;if(r===null)throw Error(A(311));r.lastRenderedReducer=e;var s=ye,l=s.baseQueue,o=r.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}s.baseQueue=l=o,r.pending=null}if(l!==null){o=l.next,s=s.baseState;var a=i=null,c=null,d=o;do{var g=d.lane;if((yr&g)===g)c!==null&&(c=c.next={lane:0,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null}),s=d.hasEagerState?d.eagerState:e(s,d.action);else{var v={lane:g,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null};c===null?(a=c=v,i=s):c=c.next=v,me.lanes|=g,vr|=g}d=d.next}while(d!==null&&d!==o);c===null?i=s:c.next=a,mt(s,t.memoizedState)||(Be=!0),t.memoizedState=s,t.baseState=i,t.baseQueue=c,r.lastRenderedState=s}if(e=r.interleaved,e!==null){l=e;do o=l.lane,me.lanes|=o,vr|=o,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function so(e){var t=st(),r=t.queue;if(r===null)throw Error(A(311));r.lastRenderedReducer=e;var s=r.dispatch,l=r.pending,o=t.memoizedState;if(l!==null){r.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);mt(o,t.memoizedState)||(Be=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),r.lastRenderedState=o}return[o,s]}function cu(){}function du(e,t){var r=me,s=st(),l=t(),o=!mt(s.memoizedState,l);if(o&&(s.memoizedState=l,Be=!0),s=s.queue,Ji(hu.bind(null,r,s,e),[e]),s.getSnapshot!==t||o||be!==null&&be.memoizedState.tag&1){if(r.flags|=2048,Qn(9,mu.bind(null,r,s,l,t),void 0,null),je===null)throw Error(A(349));yr&30||uu(r,t,l)}return l}function uu(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function mu(e,t,r,s){t.value=r,t.getSnapshot=s,fu(t)&&pu(e)}function hu(e,t,r){return r(function(){fu(t)&&pu(e)})}function fu(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!mt(e,r)}catch{return!0}}function pu(e){var t=At(e,1);t!==null&&ut(t,e,1,-1)}function uc(e){var t=yt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Wn,lastRenderedState:e},t.queue=e,e=e.dispatch=Yh.bind(null,me,e),[t.memoizedState,e]}function Qn(e,t,r,s){return e={tag:e,create:t,destroy:r,deps:s,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(s=r.next,r.next=e,e.next=s,t.lastEffect=e)),e}function gu(){return st().memoizedState}function zs(e,t,r,s){var l=yt();me.flags|=e,l.memoizedState=Qn(1|t,r,void 0,s===void 0?null:s)}function wl(e,t,r,s){var l=st();s=s===void 0?null:s;var o=void 0;if(ye!==null){var i=ye.memoizedState;if(o=i.destroy,s!==null&&Qi(s,i.deps)){l.memoizedState=Qn(t,r,o,s);return}}me.flags|=e,l.memoizedState=Qn(1|t,r,o,s)}function mc(e,t){return zs(8390656,8,e,t)}function Ji(e,t){return wl(2048,8,e,t)}function xu(e,t){return wl(4,2,e,t)}function yu(e,t){return wl(4,4,e,t)}function vu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function wu(e,t,r){return r=r!=null?r.concat([e]):null,wl(4,4,vu.bind(null,t,e),r)}function Zi(){}function bu(e,t){var r=st();t=t===void 0?null:t;var s=r.memoizedState;return s!==null&&t!==null&&Qi(t,s[1])?s[0]:(r.memoizedState=[e,t],e)}function ju(e,t){var r=st();t=t===void 0?null:t;var s=r.memoizedState;return s!==null&&t!==null&&Qi(t,s[1])?s[0]:(e=e(),r.memoizedState=[e,t],e)}function Nu(e,t,r){return yr&21?(mt(r,t)||(r=Md(),me.lanes|=r,vr|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Be=!0),e.memoizedState=r)}function Zh(e,t){var r=ee;ee=r!==0&&4>r?r:4,e(!0);var s=ro.transition;ro.transition={};try{e(!1),t()}finally{ee=r,ro.transition=s}}function ku(){return st().memoizedState}function Kh(e,t,r){var s=Dt(e);if(r={lane:s,action:r,hasEagerState:!1,eagerState:null,next:null},Su(e))Cu(t,r);else if(r=ou(e,t,r,s),r!==null){var l=Re();ut(r,e,s,l),Eu(r,t,s)}}function Yh(e,t,r){var s=Dt(e),l={lane:s,action:r,hasEagerState:!1,eagerState:null,next:null};if(Su(e))Cu(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,r);if(l.hasEagerState=!0,l.eagerState=a,mt(a,i)){var c=t.interleaved;c===null?(l.next=l,Bi(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}r=ou(e,t,l,s),r!==null&&(l=Re(),ut(r,e,s,l),Eu(r,t,s))}}function Su(e){var t=e.alternate;return e===me||t!==null&&t===me}function Cu(e,t){kn=ol=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Eu(e,t,r){if(r&4194240){var s=t.lanes;s&=e.pendingLanes,r|=s,t.lanes=r,Ei(e,r)}}var il={readContext:nt,useCallback:Me,useContext:Me,useEffect:Me,useImperativeHandle:Me,useInsertionEffect:Me,useLayoutEffect:Me,useMemo:Me,useReducer:Me,useRef:Me,useState:Me,useDebugValue:Me,useDeferredValue:Me,useTransition:Me,useMutableSource:Me,useSyncExternalStore:Me,useId:Me,unstable_isNewReconciler:!1},Xh={readContext:nt,useCallback:function(e,t){return yt().memoizedState=[e,t===void 0?null:t],e},useContext:nt,useEffect:mc,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,zs(4194308,4,vu.bind(null,t,e),r)},useLayoutEffect:function(e,t){return zs(4194308,4,e,t)},useInsertionEffect:function(e,t){return zs(4,2,e,t)},useMemo:function(e,t){var r=yt();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var s=yt();return t=r!==void 0?r(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=Kh.bind(null,me,e),[s.memoizedState,e]},useRef:function(e){var t=yt();return e={current:e},t.memoizedState=e},useState:uc,useDebugValue:Zi,useDeferredValue:function(e){return yt().memoizedState=e},useTransition:function(){var e=uc(!1),t=e[0];return e=Zh.bind(null,e[1]),yt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var s=me,l=yt();if(ie){if(r===void 0)throw Error(A(407));r=r()}else{if(r=t(),je===null)throw Error(A(349));yr&30||uu(s,t,r)}l.memoizedState=r;var o={value:r,getSnapshot:t};return l.queue=o,mc(hu.bind(null,s,o,e),[e]),s.flags|=2048,Qn(9,mu.bind(null,s,o,r,t),void 0,null),r},useId:function(){var e=yt(),t=je.identifierPrefix;if(ie){var r=Ct,s=St;r=(s&~(1<<32-dt(s)-1)).toString(32)+r,t=":"+t+"R"+r,r=Hn++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Jh++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ef={readContext:nt,useCallback:bu,useContext:nt,useEffect:Ji,useImperativeHandle:wu,useInsertionEffect:xu,useLayoutEffect:yu,useMemo:ju,useReducer:no,useRef:gu,useState:function(){return no(Wn)},useDebugValue:Zi,useDeferredValue:function(e){var t=st();return Nu(t,ye.memoizedState,e)},useTransition:function(){var e=no(Wn)[0],t=st().memoizedState;return[e,t]},useMutableSource:cu,useSyncExternalStore:du,useId:ku,unstable_isNewReconciler:!1},tf={readContext:nt,useCallback:bu,useContext:nt,useEffect:Ji,useImperativeHandle:wu,useInsertionEffect:xu,useLayoutEffect:yu,useMemo:ju,useReducer:so,useRef:gu,useState:function(){return so(Wn)},useDebugValue:Zi,useDeferredValue:function(e){var t=st();return ye===null?t.memoizedState=e:Nu(t,ye.memoizedState,e)},useTransition:function(){var e=so(Wn)[0],t=st().memoizedState;return[e,t]},useMutableSource:cu,useSyncExternalStore:du,useId:ku,unstable_isNewReconciler:!1};function it(e,t){if(e&&e.defaultProps){t=he({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function qo(e,t,r,s){t=e.memoizedState,r=r(s,t),r=r==null?t:he({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var bl={isMounted:function(e){return(e=e._reactInternals)?Nr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var s=Re(),l=Dt(e),o=Et(s,l);o.payload=t,r!=null&&(o.callback=r),t=Qt(e,o,l),t!==null&&(ut(t,e,l,s),Ps(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var s=Re(),l=Dt(e),o=Et(s,l);o.tag=1,o.payload=t,r!=null&&(o.callback=r),t=Qt(e,o,l),t!==null&&(ut(t,e,l,s),Ps(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=Re(),s=Dt(e),l=Et(r,s);l.tag=2,t!=null&&(l.callback=t),t=Qt(e,l,s),t!==null&&(ut(t,e,s,r),Ps(t,e,s))}};function hc(e,t,r,s,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,o,i):t.prototype&&t.prototype.isPureReactComponent?!Un(r,s)||!Un(l,o):!0}function Mu(e,t,r){var s=!1,l=Kt,o=t.contextType;return typeof o=="object"&&o!==null?o=nt(o):(l=qe(t)?gr:_e.current,s=t.contextTypes,o=(s=s!=null)?Gr(e,l):Kt),t=new t(r,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=bl,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function fc(e,t,r,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,s),t.state!==e&&bl.enqueueReplaceState(t,t.state,null)}function Ho(e,t,r,s){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},Vi(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=nt(o):(o=qe(t)?gr:_e.current,l.context=Gr(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(qo(e,t,o,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&bl.enqueueReplaceState(l,l.state,null),sl(e,r,l,s),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Kr(e,t){try{var r="",s=t;do r+=Im(s),s=s.return;while(s);var l=r}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function lo(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function Wo(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var rf=typeof WeakMap=="function"?WeakMap:Map;function Iu(e,t,r){r=Et(-1,r),r.tag=3,r.payload={element:null};var s=t.value;return r.callback=function(){cl||(cl=!0,ti=s),Wo(e,t)},r}function Au(e,t,r){r=Et(-1,r),r.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var l=t.value;r.payload=function(){return s(l)},r.callback=function(){Wo(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(r.callback=function(){Wo(e,t),typeof s!="function"&&(Gt===null?Gt=new Set([this]):Gt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),r}function pc(e,t,r){var s=e.pingCache;if(s===null){s=e.pingCache=new rf;var l=new Set;s.set(t,l)}else l=s.get(t),l===void 0&&(l=new Set,s.set(t,l));l.has(r)||(l.add(r),e=xf.bind(null,e,t,r),t.then(e,e))}function gc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xc(e,t,r,s,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Et(-1,1),t.tag=2,Qt(r,t,1))),r.lanes|=1),e)}var nf=Tt.ReactCurrentOwner,Be=!1;function Pe(e,t,r,s){t.child=e===null?lu(t,null,r,s):Jr(t,e.child,r,s)}function yc(e,t,r,s,l){r=r.render;var o=t.ref;return Hr(t,l),s=Gi(e,t,r,s,o,l),r=Di(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,_t(e,t,l)):(ie&&r&&Ri(t),t.flags|=1,Pe(e,t,s,l),t.child)}function vc(e,t,r,s,l){if(e===null){var o=r.type;return typeof o=="function"&&!sa(o)&&o.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=o,_u(e,t,o,s,l)):(e=Fs(r.type,null,s,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(r=r.compare,r=r!==null?r:Un,r(i,s)&&e.ref===t.ref)return _t(e,t,l)}return t.flags|=1,e=Jt(o,s),e.ref=t.ref,e.return=t,t.child=e}function _u(e,t,r,s,l){if(e!==null){var o=e.memoizedProps;if(Un(o,s)&&e.ref===t.ref)if(Be=!1,t.pendingProps=s=o,(e.lanes&l)!==0)e.flags&131072&&(Be=!0);else return t.lanes=e.lanes,_t(e,t,l)}return Qo(e,t,r,s,l)}function Tu(e,t,r){var s=t.pendingProps,l=s.children,o=e!==null?e.memoizedState:null;if(s.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},re($r,We),We|=r;else{if(!(r&1073741824))return e=o!==null?o.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,re($r,We),We|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=o!==null?o.baseLanes:r,re($r,We),We|=s}else o!==null?(s=o.baseLanes|r,t.memoizedState=null):s=r,re($r,We),We|=s;return Pe(e,t,l,r),t.child}function Lu(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Qo(e,t,r,s,l){var o=qe(r)?gr:_e.current;return o=Gr(t,o),Hr(t,l),r=Gi(e,t,r,s,o,l),s=Di(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,_t(e,t,l)):(ie&&s&&Ri(t),t.flags|=1,Pe(e,t,r,l),t.child)}function wc(e,t,r,s,l){if(qe(r)){var o=!0;Xs(t)}else o=!1;if(Hr(t,l),t.stateNode===null)Os(e,t),Mu(t,r,s),Ho(t,r,s,l),s=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,d=r.contextType;typeof d=="object"&&d!==null?d=nt(d):(d=qe(r)?gr:_e.current,d=Gr(t,d));var g=r.getDerivedStateFromProps,v=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function";v||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==s||c!==d)&&fc(t,i,s,d),Ot=!1;var b=t.memoizedState;i.state=b,sl(t,s,i,l),c=t.memoizedState,a!==s||b!==c||Ve.current||Ot?(typeof g=="function"&&(qo(t,r,g,s),c=t.memoizedState),(a=Ot||hc(t,r,a,s,b,c,d))?(v||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=c),i.props=s,i.state=c,i.context=d,s=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{i=t.stateNode,iu(e,t),a=t.memoizedProps,d=t.type===t.elementType?a:it(t.type,a),i.props=d,v=t.pendingProps,b=i.context,c=r.contextType,typeof c=="object"&&c!==null?c=nt(c):(c=qe(r)?gr:_e.current,c=Gr(t,c));var N=r.getDerivedStateFromProps;(g=typeof N=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==v||b!==c)&&fc(t,i,s,c),Ot=!1,b=t.memoizedState,i.state=b,sl(t,s,i,l);var j=t.memoizedState;a!==v||b!==j||Ve.current||Ot?(typeof N=="function"&&(qo(t,r,N,s),j=t.memoizedState),(d=Ot||hc(t,r,d,s,b,j,c)||!1)?(g||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(s,j,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(s,j,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=j),i.props=s,i.state=j,i.context=c,s=d):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=1024),s=!1)}return Go(e,t,r,s,o,l)}function Go(e,t,r,s,l,o){Lu(e,t);var i=(t.flags&128)!==0;if(!s&&!i)return l&&lc(t,r,!1),_t(e,t,o);s=t.stateNode,nf.current=t;var a=i&&typeof r.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&i?(t.child=Jr(t,e.child,null,o),t.child=Jr(t,null,a,o)):Pe(e,t,a,o),t.memoizedState=s.state,l&&lc(t,r,!0),t.child}function Pu(e){var t=e.stateNode;t.pendingContext?sc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&sc(e,t.context,!1),qi(e,t.containerInfo)}function bc(e,t,r,s,l){return Dr(),Oi(l),t.flags|=256,Pe(e,t,r,s),t.child}var Do={dehydrated:null,treeContext:null,retryLane:0};function Jo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ru(e,t,r){var s=t.pendingProps,l=ue.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),re(ue,l&1),e===null)return Bo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=s.children,e=s.fallback,o?(s=t.mode,o=t.child,i={mode:"hidden",children:i},!(s&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=kl(i,s,0,null),e=ur(e,s,r,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Jo(r),t.memoizedState=Do,e):Ki(t,i));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return sf(e,t,i,s,a,l,r);if(o){o=s.fallback,i=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:s.children};return!(i&1)&&t.child!==l?(s=t.child,s.childLanes=0,s.pendingProps=c,t.deletions=null):(s=Jt(l,c),s.subtreeFlags=l.subtreeFlags&14680064),a!==null?o=Jt(a,o):(o=ur(o,i,r,null),o.flags|=2),o.return=t,s.return=t,s.sibling=o,t.child=s,s=o,o=t.child,i=e.child.memoizedState,i=i===null?Jo(r):{baseLanes:i.baseLanes|r,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~r,t.memoizedState=Do,s}return o=e.child,e=o.sibling,s=Jt(o,{mode:"visible",children:s.children}),!(t.mode&1)&&(s.lanes=r),s.return=t,s.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=s,t.memoizedState=null,s}function Ki(e,t){return t=kl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ks(e,t,r,s){return s!==null&&Oi(s),Jr(t,e.child,null,r),e=Ki(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sf(e,t,r,s,l,o,i){if(r)return t.flags&256?(t.flags&=-257,s=lo(Error(A(422))),ks(e,t,i,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=s.fallback,l=t.mode,s=kl({mode:"visible",children:s.children},l,0,null),o=ur(o,l,i,null),o.flags|=2,s.return=t,o.return=t,s.sibling=o,t.child=s,t.mode&1&&Jr(t,e.child,null,i),t.child.memoizedState=Jo(i),t.memoizedState=Do,o);if(!(t.mode&1))return ks(e,t,i,null);if(l.data==="$!"){if(s=l.nextSibling&&l.nextSibling.dataset,s)var a=s.dgst;return s=a,o=Error(A(419)),s=lo(o,s,void 0),ks(e,t,i,s)}if(a=(i&e.childLanes)!==0,Be||a){if(s=je,s!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(s.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,At(e,l),ut(s,e,l,-1))}return na(),s=lo(Error(A(421))),ks(e,t,i,s)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=yf.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,Qe=Wt(l.nextSibling),Ge=t,ie=!0,ct=null,e!==null&&(Xe[et++]=St,Xe[et++]=Ct,Xe[et++]=xr,St=e.id,Ct=e.overflow,xr=t),t=Ki(t,s.children),t.flags|=4096,t)}function jc(e,t,r){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Vo(e.return,t,r)}function oo(e,t,r,s,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:r,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=s,o.tail=r,o.tailMode=l)}function zu(e,t,r){var s=t.pendingProps,l=s.revealOrder,o=s.tail;if(Pe(e,t,s.children,r),s=ue.current,s&2)s=s&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&jc(e,r,t);else if(e.tag===19)jc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(re(ue,s),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&ll(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),oo(t,!1,l,r,o);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&ll(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}oo(t,!0,r,null,o);break;case"together":oo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Os(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function _t(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),vr|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,r=Jt(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=Jt(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function lf(e,t,r){switch(t.tag){case 3:Pu(t),Dr();break;case 5:au(t);break;case 1:qe(t.type)&&Xs(t);break;case 4:qi(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,l=t.memoizedProps.value;re(rl,s._currentValue),s._currentValue=l;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(re(ue,ue.current&1),t.flags|=128,null):r&t.child.childLanes?Ru(e,t,r):(re(ue,ue.current&1),e=_t(e,t,r),e!==null?e.sibling:null);re(ue,ue.current&1);break;case 19:if(s=(r&t.childLanes)!==0,e.flags&128){if(s)return zu(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),re(ue,ue.current),s)break;return null;case 22:case 23:return t.lanes=0,Tu(e,t,r)}return _t(e,t,r)}var Ou,Zo,Uu,$u;Ou=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};Zo=function(){};Uu=function(e,t,r,s){var l=e.memoizedProps;if(l!==s){e=t.stateNode,ar(jt.current);var o=null;switch(r){case"input":l=yo(e,l),s=yo(e,s),o=[];break;case"select":l=he({},l,{value:void 0}),s=he({},s,{value:void 0}),o=[];break;case"textarea":l=bo(e,l),s=bo(e,s),o=[];break;default:typeof l.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=Ks)}No(r,s);var i;r=null;for(d in l)if(!s.hasOwnProperty(d)&&l.hasOwnProperty(d)&&l[d]!=null)if(d==="style"){var a=l[d];for(i in a)a.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else d!=="dangerouslySetInnerHTML"&&d!=="children"&&d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(_n.hasOwnProperty(d)?o||(o=[]):(o=o||[]).push(d,null));for(d in s){var c=s[d];if(a=l!=null?l[d]:void 0,s.hasOwnProperty(d)&&c!==a&&(c!=null||a!=null))if(d==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(r||(r={}),r[i]=c[i])}else r||(o||(o=[]),o.push(d,r)),r=c;else d==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(o=o||[]).push(d,c)):d==="children"?typeof c!="string"&&typeof c!="number"||(o=o||[]).push(d,""+c):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&(_n.hasOwnProperty(d)?(c!=null&&d==="onScroll"&&le("scroll",e),o||a===c||(o=[])):(o=o||[]).push(d,c))}r&&(o=o||[]).push("style",r);var d=o;(t.updateQueue=d)&&(t.flags|=4)}};$u=function(e,t,r,s){r!==s&&(t.flags|=4)};function hn(e,t){if(!ie)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var s=null;r!==null;)r.alternate!==null&&(s=r),r=r.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function Ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,s=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,s|=l.subtreeFlags&14680064,s|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,s|=l.subtreeFlags,s|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=s,e.childLanes=r,t}function of(e,t,r){var s=t.pendingProps;switch(zi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ie(t),null;case 1:return qe(t.type)&&Ys(),Ie(t),null;case 3:return s=t.stateNode,Zr(),oe(Ve),oe(_e),Wi(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(js(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ct!==null&&(si(ct),ct=null))),Zo(e,t),Ie(t),null;case 5:Hi(t);var l=ar(qn.current);if(r=t.type,e!==null&&t.stateNode!=null)Uu(e,t,r,s,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(A(166));return Ie(t),null}if(e=ar(jt.current),js(t)){s=t.stateNode,r=t.type;var o=t.memoizedProps;switch(s[vt]=t,s[Bn]=o,e=(t.mode&1)!==0,r){case"dialog":le("cancel",s),le("close",s);break;case"iframe":case"object":case"embed":le("load",s);break;case"video":case"audio":for(l=0;l<yn.length;l++)le(yn[l],s);break;case"source":le("error",s);break;case"img":case"image":case"link":le("error",s),le("load",s);break;case"details":le("toggle",s);break;case"input":_a(s,o),le("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!o.multiple},le("invalid",s);break;case"textarea":La(s,o),le("invalid",s)}No(r,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?s.textContent!==a&&(o.suppressHydrationWarning!==!0&&bs(s.textContent,a,e),l=["children",a]):typeof a=="number"&&s.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&bs(s.textContent,a,e),l=["children",""+a]):_n.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&le("scroll",s)}switch(r){case"input":hs(s),Ta(s,o,!0);break;case"textarea":hs(s),Pa(s);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(s.onclick=Ks)}s=l,t.updateQueue=s,s!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=hd(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=i.createElement(r,{is:s.is}):(e=i.createElement(r),r==="select"&&(i=e,s.multiple?i.multiple=!0:s.size&&(i.size=s.size))):e=i.createElementNS(e,r),e[vt]=t,e[Bn]=s,Ou(e,t,!1,!1),t.stateNode=e;e:{switch(i=ko(r,s),r){case"dialog":le("cancel",e),le("close",e),l=s;break;case"iframe":case"object":case"embed":le("load",e),l=s;break;case"video":case"audio":for(l=0;l<yn.length;l++)le(yn[l],e);l=s;break;case"source":le("error",e),l=s;break;case"img":case"image":case"link":le("error",e),le("load",e),l=s;break;case"details":le("toggle",e),l=s;break;case"input":_a(e,s),l=yo(e,s),le("invalid",e);break;case"option":l=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},l=he({},s,{value:void 0}),le("invalid",e);break;case"textarea":La(e,s),l=bo(e,s),le("invalid",e);break;default:l=s}No(r,l),a=l;for(o in a)if(a.hasOwnProperty(o)){var c=a[o];o==="style"?gd(e,c):o==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&fd(e,c)):o==="children"?typeof c=="string"?(r!=="textarea"||c!=="")&&Tn(e,c):typeof c=="number"&&Tn(e,""+c):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(_n.hasOwnProperty(o)?c!=null&&o==="onScroll"&&le("scroll",e):c!=null&&bi(e,o,c,i))}switch(r){case"input":hs(e),Ta(e,s,!1);break;case"textarea":hs(e),Pa(e);break;case"option":s.value!=null&&e.setAttribute("value",""+Zt(s.value));break;case"select":e.multiple=!!s.multiple,o=s.value,o!=null?Fr(e,!!s.multiple,o,!1):s.defaultValue!=null&&Fr(e,!!s.multiple,s.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Ks)}switch(r){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ie(t),null;case 6:if(e&&t.stateNode!=null)$u(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(A(166));if(r=ar(qn.current),ar(jt.current),js(t)){if(s=t.stateNode,r=t.memoizedProps,s[vt]=t,(o=s.nodeValue!==r)&&(e=Ge,e!==null))switch(e.tag){case 3:bs(s.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&bs(s.nodeValue,r,(e.mode&1)!==0)}o&&(t.flags|=4)}else s=(r.nodeType===9?r:r.ownerDocument).createTextNode(s),s[vt]=t,t.stateNode=s}return Ie(t),null;case 13:if(oe(ue),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ie&&Qe!==null&&t.mode&1&&!(t.flags&128))nu(),Dr(),t.flags|=98560,o=!1;else if(o=js(t),s!==null&&s.dehydrated!==null){if(e===null){if(!o)throw Error(A(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(A(317));o[vt]=t}else Dr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ie(t),o=!1}else ct!==null&&(si(ct),ct=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,t.mode&1&&(e===null||ue.current&1?ve===0&&(ve=3):na())),t.updateQueue!==null&&(t.flags|=4),Ie(t),null);case 4:return Zr(),Zo(e,t),e===null&&$n(t.stateNode.containerInfo),Ie(t),null;case 10:return Fi(t.type._context),Ie(t),null;case 17:return qe(t.type)&&Ys(),Ie(t),null;case 19:if(oe(ue),o=t.memoizedState,o===null)return Ie(t),null;if(s=(t.flags&128)!==0,i=o.rendering,i===null)if(s)hn(o,!1);else{if(ve!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=ll(e),i!==null){for(t.flags|=128,hn(o,!1),s=i.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=r,r=t.child;r!==null;)o=r,e=s,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return re(ue,ue.current&1|2),t.child}e=e.sibling}o.tail!==null&&pe()>Yr&&(t.flags|=128,s=!0,hn(o,!1),t.lanes=4194304)}else{if(!s)if(e=ll(i),e!==null){if(t.flags|=128,s=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),hn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!ie)return Ie(t),null}else 2*pe()-o.renderingStartTime>Yr&&r!==1073741824&&(t.flags|=128,s=!0,hn(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(r=o.last,r!==null?r.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=pe(),t.sibling=null,r=ue.current,re(ue,s?r&1|2:r&1),t):(Ie(t),null);case 22:case 23:return ra(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&t.mode&1?We&1073741824&&(Ie(t),t.subtreeFlags&6&&(t.flags|=8192)):Ie(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function af(e,t){switch(zi(t),t.tag){case 1:return qe(t.type)&&Ys(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Zr(),oe(Ve),oe(_e),Wi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Hi(t),null;case 13:if(oe(ue),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));Dr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(ue),null;case 4:return Zr(),null;case 10:return Fi(t.type._context),null;case 22:case 23:return ra(),null;case 24:return null;default:return null}}var Ss=!1,Ae=!1,cf=typeof WeakSet=="function"?WeakSet:Set,V=null;function Ur(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(s){fe(e,t,s)}else r.current=null}function Ko(e,t,r){try{r()}catch(s){fe(e,t,s)}}var Nc=!1;function df(e,t){if(Po=Ds,e=Hd(),Pi(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var s=r.getSelection&&r.getSelection();if(s&&s.rangeCount!==0){r=s.anchorNode;var l=s.anchorOffset,o=s.focusNode;s=s.focusOffset;try{r.nodeType,o.nodeType}catch{r=null;break e}var i=0,a=-1,c=-1,d=0,g=0,v=e,b=null;t:for(;;){for(var N;v!==r||l!==0&&v.nodeType!==3||(a=i+l),v!==o||s!==0&&v.nodeType!==3||(c=i+s),v.nodeType===3&&(i+=v.nodeValue.length),(N=v.firstChild)!==null;)b=v,v=N;for(;;){if(v===e)break t;if(b===r&&++d===l&&(a=i),b===o&&++g===s&&(c=i),(N=v.nextSibling)!==null)break;v=b,b=v.parentNode}v=N}r=a===-1||c===-1?null:{start:a,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(Ro={focusedElem:e,selectionRange:r},Ds=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var k=j.memoizedProps,S=j.memoizedState,m=t.stateNode,u=m.getSnapshotBeforeUpdate(t.elementType===t.type?k:it(t.type,k),S);m.__reactInternalSnapshotBeforeUpdate=u}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(E){fe(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return j=Nc,Nc=!1,j}function Sn(e,t,r){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var l=s=s.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Ko(t,r,o)}l=l.next}while(l!==s)}}function jl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var s=r.create;r.destroy=s()}r=r.next}while(r!==t)}}function Yo(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function Fu(e){var t=e.alternate;t!==null&&(e.alternate=null,Fu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[vt],delete t[Bn],delete t[Uo],delete t[Wh],delete t[Qh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Bu(e){return e.tag===5||e.tag===3||e.tag===4}function kc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Bu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xo(e,t,r){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Ks));else if(s!==4&&(e=e.child,e!==null))for(Xo(e,t,r),e=e.sibling;e!==null;)Xo(e,t,r),e=e.sibling}function ei(e,t,r){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(ei(e,t,r),e=e.sibling;e!==null;)ei(e,t,r),e=e.sibling}var ke=null,at=!1;function Rt(e,t,r){for(r=r.child;r!==null;)Vu(e,t,r),r=r.sibling}function Vu(e,t,r){if(bt&&typeof bt.onCommitFiberUnmount=="function")try{bt.onCommitFiberUnmount(fl,r)}catch{}switch(r.tag){case 5:Ae||Ur(r,t);case 6:var s=ke,l=at;ke=null,Rt(e,t,r),ke=s,at=l,ke!==null&&(at?(e=ke,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):ke.removeChild(r.stateNode));break;case 18:ke!==null&&(at?(e=ke,r=r.stateNode,e.nodeType===8?Xl(e.parentNode,r):e.nodeType===1&&Xl(e,r),zn(e)):Xl(ke,r.stateNode));break;case 4:s=ke,l=at,ke=r.stateNode.containerInfo,at=!0,Rt(e,t,r),ke=s,at=l;break;case 0:case 11:case 14:case 15:if(!Ae&&(s=r.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){l=s=s.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&Ko(r,t,i),l=l.next}while(l!==s)}Rt(e,t,r);break;case 1:if(!Ae&&(Ur(r,t),s=r.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=r.memoizedProps,s.state=r.memoizedState,s.componentWillUnmount()}catch(a){fe(r,t,a)}Rt(e,t,r);break;case 21:Rt(e,t,r);break;case 22:r.mode&1?(Ae=(s=Ae)||r.memoizedState!==null,Rt(e,t,r),Ae=s):Rt(e,t,r);break;default:Rt(e,t,r)}}function Sc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new cf),t.forEach(function(s){var l=vf.bind(null,e,s);r.has(s)||(r.add(s),s.then(l,l))})}}function ot(e,t){var r=t.deletions;if(r!==null)for(var s=0;s<r.length;s++){var l=r[s];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:ke=a.stateNode,at=!1;break e;case 3:ke=a.stateNode.containerInfo,at=!0;break e;case 4:ke=a.stateNode.containerInfo,at=!0;break e}a=a.return}if(ke===null)throw Error(A(160));Vu(o,i,l),ke=null,at=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(d){fe(l,t,d)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)qu(t,e),t=t.sibling}function qu(e,t){var r=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ot(t,e),xt(e),s&4){try{Sn(3,e,e.return),jl(3,e)}catch(k){fe(e,e.return,k)}try{Sn(5,e,e.return)}catch(k){fe(e,e.return,k)}}break;case 1:ot(t,e),xt(e),s&512&&r!==null&&Ur(r,r.return);break;case 5:if(ot(t,e),xt(e),s&512&&r!==null&&Ur(r,r.return),e.flags&32){var l=e.stateNode;try{Tn(l,"")}catch(k){fe(e,e.return,k)}}if(s&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=r!==null?r.memoizedProps:o,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&ud(l,o),ko(a,i);var d=ko(a,o);for(i=0;i<c.length;i+=2){var g=c[i],v=c[i+1];g==="style"?gd(l,v):g==="dangerouslySetInnerHTML"?fd(l,v):g==="children"?Tn(l,v):bi(l,g,v,d)}switch(a){case"input":vo(l,o);break;case"textarea":md(l,o);break;case"select":var b=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var N=o.value;N!=null?Fr(l,!!o.multiple,N,!1):b!==!!o.multiple&&(o.defaultValue!=null?Fr(l,!!o.multiple,o.defaultValue,!0):Fr(l,!!o.multiple,o.multiple?[]:"",!1))}l[Bn]=o}catch(k){fe(e,e.return,k)}}break;case 6:if(ot(t,e),xt(e),s&4){if(e.stateNode===null)throw Error(A(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(k){fe(e,e.return,k)}}break;case 3:if(ot(t,e),xt(e),s&4&&r!==null&&r.memoizedState.isDehydrated)try{zn(t.containerInfo)}catch(k){fe(e,e.return,k)}break;case 4:ot(t,e),xt(e);break;case 13:ot(t,e),xt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(ea=pe())),s&4&&Sc(e);break;case 22:if(g=r!==null&&r.memoizedState!==null,e.mode&1?(Ae=(d=Ae)||g,ot(t,e),Ae=d):ot(t,e),xt(e),s&8192){if(d=e.memoizedState!==null,(e.stateNode.isHidden=d)&&!g&&e.mode&1)for(V=e,g=e.child;g!==null;){for(v=V=g;V!==null;){switch(b=V,N=b.child,b.tag){case 0:case 11:case 14:case 15:Sn(4,b,b.return);break;case 1:Ur(b,b.return);var j=b.stateNode;if(typeof j.componentWillUnmount=="function"){s=b,r=b.return;try{t=s,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(k){fe(s,r,k)}}break;case 5:Ur(b,b.return);break;case 22:if(b.memoizedState!==null){Ec(v);continue}}N!==null?(N.return=b,V=N):Ec(v)}g=g.sibling}e:for(g=null,v=e;;){if(v.tag===5){if(g===null){g=v;try{l=v.stateNode,d?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=v.stateNode,c=v.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=pd("display",i))}catch(k){fe(e,e.return,k)}}}else if(v.tag===6){if(g===null)try{v.stateNode.nodeValue=d?"":v.memoizedProps}catch(k){fe(e,e.return,k)}}else if((v.tag!==22&&v.tag!==23||v.memoizedState===null||v===e)&&v.child!==null){v.child.return=v,v=v.child;continue}if(v===e)break e;for(;v.sibling===null;){if(v.return===null||v.return===e)break e;g===v&&(g=null),v=v.return}g===v&&(g=null),v.sibling.return=v.return,v=v.sibling}}break;case 19:ot(t,e),xt(e),s&4&&Sc(e);break;case 21:break;default:ot(t,e),xt(e)}}function xt(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(Bu(r)){var s=r;break e}r=r.return}throw Error(A(160))}switch(s.tag){case 5:var l=s.stateNode;s.flags&32&&(Tn(l,""),s.flags&=-33);var o=kc(e);ei(e,o,l);break;case 3:case 4:var i=s.stateNode.containerInfo,a=kc(e);Xo(e,a,i);break;default:throw Error(A(161))}}catch(c){fe(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function uf(e,t,r){V=e,Hu(e)}function Hu(e,t,r){for(var s=(e.mode&1)!==0;V!==null;){var l=V,o=l.child;if(l.tag===22&&s){var i=l.memoizedState!==null||Ss;if(!i){var a=l.alternate,c=a!==null&&a.memoizedState!==null||Ae;a=Ss;var d=Ae;if(Ss=i,(Ae=c)&&!d)for(V=l;V!==null;)i=V,c=i.child,i.tag===22&&i.memoizedState!==null?Mc(l):c!==null?(c.return=i,V=c):Mc(l);for(;o!==null;)V=o,Hu(o),o=o.sibling;V=l,Ss=a,Ae=d}Cc(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,V=o):Cc(e)}}function Cc(e){for(;V!==null;){var t=V;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ae||jl(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!Ae)if(r===null)s.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:it(t.type,r.memoizedProps);s.componentDidUpdate(l,r.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&dc(t,o,s);break;case 3:var i=t.updateQueue;if(i!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}dc(t,i,r)}break;case 5:var a=t.stateNode;if(r===null&&t.flags&4){r=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var d=t.alternate;if(d!==null){var g=d.memoizedState;if(g!==null){var v=g.dehydrated;v!==null&&zn(v)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}Ae||t.flags&512&&Yo(t)}catch(b){fe(t,t.return,b)}}if(t===e){V=null;break}if(r=t.sibling,r!==null){r.return=t.return,V=r;break}V=t.return}}function Ec(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var r=t.sibling;if(r!==null){r.return=t.return,V=r;break}V=t.return}}function Mc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{jl(4,t)}catch(c){fe(t,r,c)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var l=t.return;try{s.componentDidMount()}catch(c){fe(t,l,c)}}var o=t.return;try{Yo(t)}catch(c){fe(t,o,c)}break;case 5:var i=t.return;try{Yo(t)}catch(c){fe(t,i,c)}}}catch(c){fe(t,t.return,c)}if(t===e){V=null;break}var a=t.sibling;if(a!==null){a.return=t.return,V=a;break}V=t.return}}var mf=Math.ceil,al=Tt.ReactCurrentDispatcher,Yi=Tt.ReactCurrentOwner,rt=Tt.ReactCurrentBatchConfig,X=0,je=null,xe=null,Se=0,We=0,$r=Xt(0),ve=0,Gn=null,vr=0,Nl=0,Xi=0,Cn=null,Fe=null,ea=0,Yr=1/0,Nt=null,cl=!1,ti=null,Gt=null,Cs=!1,Bt=null,dl=0,En=0,ri=null,Us=-1,$s=0;function Re(){return X&6?pe():Us!==-1?Us:Us=pe()}function Dt(e){return e.mode&1?X&2&&Se!==0?Se&-Se:Dh.transition!==null?($s===0&&($s=Md()),$s):(e=ee,e!==0||(e=window.event,e=e===void 0?16:Rd(e.type)),e):1}function ut(e,t,r,s){if(50<En)throw En=0,ri=null,Error(A(185));Xn(e,r,s),(!(X&2)||e!==je)&&(e===je&&(!(X&2)&&(Nl|=r),ve===4&&$t(e,Se)),He(e,s),r===1&&X===0&&!(t.mode&1)&&(Yr=pe()+500,vl&&er()))}function He(e,t){var r=e.callbackNode;Dm(e,t);var s=Gs(e,e===je?Se:0);if(s===0)r!==null&&Oa(r),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(r!=null&&Oa(r),t===1)e.tag===0?Gh(Ic.bind(null,e)):eu(Ic.bind(null,e)),qh(function(){!(X&6)&&er()}),r=null;else{switch(Id(s)){case 1:r=Ci;break;case 4:r=Cd;break;case 16:r=Qs;break;case 536870912:r=Ed;break;default:r=Qs}r=Yu(r,Wu.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function Wu(e,t){if(Us=-1,$s=0,X&6)throw Error(A(327));var r=e.callbackNode;if(Wr()&&e.callbackNode!==r)return null;var s=Gs(e,e===je?Se:0);if(s===0)return null;if(s&30||s&e.expiredLanes||t)t=ul(e,s);else{t=s;var l=X;X|=2;var o=Gu();(je!==e||Se!==t)&&(Nt=null,Yr=pe()+500,dr(e,t));do try{pf();break}catch(a){Qu(e,a)}while(!0);$i(),al.current=o,X=l,xe!==null?t=0:(je=null,Se=0,t=ve)}if(t!==0){if(t===2&&(l=Io(e),l!==0&&(s=l,t=ni(e,l))),t===1)throw r=Gn,dr(e,0),$t(e,s),He(e,pe()),r;if(t===6)$t(e,s);else{if(l=e.current.alternate,!(s&30)&&!hf(l)&&(t=ul(e,s),t===2&&(o=Io(e),o!==0&&(s=o,t=ni(e,o))),t===1))throw r=Gn,dr(e,0),$t(e,s),He(e,pe()),r;switch(e.finishedWork=l,e.finishedLanes=s,t){case 0:case 1:throw Error(A(345));case 2:lr(e,Fe,Nt);break;case 3:if($t(e,s),(s&130023424)===s&&(t=ea+500-pe(),10<t)){if(Gs(e,0)!==0)break;if(l=e.suspendedLanes,(l&s)!==s){Re(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Oo(lr.bind(null,e,Fe,Nt),t);break}lr(e,Fe,Nt);break;case 4:if($t(e,s),(s&4194240)===s)break;for(t=e.eventTimes,l=-1;0<s;){var i=31-dt(s);o=1<<i,i=t[i],i>l&&(l=i),s&=~o}if(s=l,s=pe()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*mf(s/1960))-s,10<s){e.timeoutHandle=Oo(lr.bind(null,e,Fe,Nt),s);break}lr(e,Fe,Nt);break;case 5:lr(e,Fe,Nt);break;default:throw Error(A(329))}}}return He(e,pe()),e.callbackNode===r?Wu.bind(null,e):null}function ni(e,t){var r=Cn;return e.current.memoizedState.isDehydrated&&(dr(e,t).flags|=256),e=ul(e,t),e!==2&&(t=Fe,Fe=r,t!==null&&si(t)),e}function si(e){Fe===null?Fe=e:Fe.push.apply(Fe,e)}function hf(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var s=0;s<r.length;s++){var l=r[s],o=l.getSnapshot;l=l.value;try{if(!mt(o(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function $t(e,t){for(t&=~Xi,t&=~Nl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-dt(t),s=1<<r;e[r]=-1,t&=~s}}function Ic(e){if(X&6)throw Error(A(327));Wr();var t=Gs(e,0);if(!(t&1))return He(e,pe()),null;var r=ul(e,t);if(e.tag!==0&&r===2){var s=Io(e);s!==0&&(t=s,r=ni(e,s))}if(r===1)throw r=Gn,dr(e,0),$t(e,t),He(e,pe()),r;if(r===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,lr(e,Fe,Nt),He(e,pe()),null}function ta(e,t){var r=X;X|=1;try{return e(t)}finally{X=r,X===0&&(Yr=pe()+500,vl&&er())}}function wr(e){Bt!==null&&Bt.tag===0&&!(X&6)&&Wr();var t=X;X|=1;var r=rt.transition,s=ee;try{if(rt.transition=null,ee=1,e)return e()}finally{ee=s,rt.transition=r,X=t,!(X&6)&&er()}}function ra(){We=$r.current,oe($r)}function dr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Vh(r)),xe!==null)for(r=xe.return;r!==null;){var s=r;switch(zi(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&Ys();break;case 3:Zr(),oe(Ve),oe(_e),Wi();break;case 5:Hi(s);break;case 4:Zr();break;case 13:oe(ue);break;case 19:oe(ue);break;case 10:Fi(s.type._context);break;case 22:case 23:ra()}r=r.return}if(je=e,xe=e=Jt(e.current,null),Se=We=t,ve=0,Gn=null,Xi=Nl=vr=0,Fe=Cn=null,ir!==null){for(t=0;t<ir.length;t++)if(r=ir[t],s=r.interleaved,s!==null){r.interleaved=null;var l=s.next,o=r.pending;if(o!==null){var i=o.next;o.next=l,s.next=i}r.pending=s}ir=null}return e}function Qu(e,t){do{var r=xe;try{if($i(),Rs.current=il,ol){for(var s=me.memoizedState;s!==null;){var l=s.queue;l!==null&&(l.pending=null),s=s.next}ol=!1}if(yr=0,be=ye=me=null,kn=!1,Hn=0,Yi.current=null,r===null||r.return===null){ve=1,Gn=t,xe=null;break}e:{var o=e,i=r.return,a=r,c=t;if(t=Se,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var d=c,g=a,v=g.tag;if(!(g.mode&1)&&(v===0||v===11||v===15)){var b=g.alternate;b?(g.updateQueue=b.updateQueue,g.memoizedState=b.memoizedState,g.lanes=b.lanes):(g.updateQueue=null,g.memoizedState=null)}var N=gc(i);if(N!==null){N.flags&=-257,xc(N,i,a,o,t),N.mode&1&&pc(o,d,t),t=N,c=d;var j=t.updateQueue;if(j===null){var k=new Set;k.add(c),t.updateQueue=k}else j.add(c);break e}else{if(!(t&1)){pc(o,d,t),na();break e}c=Error(A(426))}}else if(ie&&a.mode&1){var S=gc(i);if(S!==null){!(S.flags&65536)&&(S.flags|=256),xc(S,i,a,o,t),Oi(Kr(c,a));break e}}o=c=Kr(c,a),ve!==4&&(ve=2),Cn===null?Cn=[o]:Cn.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Iu(o,c,t);cc(o,m);break e;case 1:a=c;var u=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof u.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Gt===null||!Gt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var E=Au(o,a,t);cc(o,E);break e}}o=o.return}while(o!==null)}Ju(r)}catch(L){t=L,xe===r&&r!==null&&(xe=r=r.return);continue}break}while(!0)}function Gu(){var e=al.current;return al.current=il,e===null?il:e}function na(){(ve===0||ve===3||ve===2)&&(ve=4),je===null||!(vr&268435455)&&!(Nl&268435455)||$t(je,Se)}function ul(e,t){var r=X;X|=2;var s=Gu();(je!==e||Se!==t)&&(Nt=null,dr(e,t));do try{ff();break}catch(l){Qu(e,l)}while(!0);if($i(),X=r,al.current=s,xe!==null)throw Error(A(261));return je=null,Se=0,ve}function ff(){for(;xe!==null;)Du(xe)}function pf(){for(;xe!==null&&!$m();)Du(xe)}function Du(e){var t=Ku(e.alternate,e,We);e.memoizedProps=e.pendingProps,t===null?Ju(e):xe=t,Yi.current=null}function Ju(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=af(r,t),r!==null){r.flags&=32767,xe=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ve=6,xe=null;return}}else if(r=of(r,t,We),r!==null){xe=r;return}if(t=t.sibling,t!==null){xe=t;return}xe=t=e}while(t!==null);ve===0&&(ve=5)}function lr(e,t,r){var s=ee,l=rt.transition;try{rt.transition=null,ee=1,gf(e,t,r,s)}finally{rt.transition=l,ee=s}return null}function gf(e,t,r,s){do Wr();while(Bt!==null);if(X&6)throw Error(A(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var o=r.lanes|r.childLanes;if(Jm(e,o),e===je&&(xe=je=null,Se=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||Cs||(Cs=!0,Yu(Qs,function(){return Wr(),null})),o=(r.flags&15990)!==0,r.subtreeFlags&15990||o){o=rt.transition,rt.transition=null;var i=ee;ee=1;var a=X;X|=4,Yi.current=null,df(e,r),qu(r,e),Rh(Ro),Ds=!!Po,Ro=Po=null,e.current=r,uf(r),Fm(),X=a,ee=i,rt.transition=o}else e.current=r;if(Cs&&(Cs=!1,Bt=e,dl=l),o=e.pendingLanes,o===0&&(Gt=null),qm(r.stateNode),He(e,pe()),t!==null)for(s=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],s(l.value,{componentStack:l.stack,digest:l.digest});if(cl)throw cl=!1,e=ti,ti=null,e;return dl&1&&e.tag!==0&&Wr(),o=e.pendingLanes,o&1?e===ri?En++:(En=0,ri=e):En=0,er(),null}function Wr(){if(Bt!==null){var e=Id(dl),t=rt.transition,r=ee;try{if(rt.transition=null,ee=16>e?16:e,Bt===null)var s=!1;else{if(e=Bt,Bt=null,dl=0,X&6)throw Error(A(331));var l=X;for(X|=4,V=e.current;V!==null;){var o=V,i=o.child;if(V.flags&16){var a=o.deletions;if(a!==null){for(var c=0;c<a.length;c++){var d=a[c];for(V=d;V!==null;){var g=V;switch(g.tag){case 0:case 11:case 15:Sn(8,g,o)}var v=g.child;if(v!==null)v.return=g,V=v;else for(;V!==null;){g=V;var b=g.sibling,N=g.return;if(Fu(g),g===d){V=null;break}if(b!==null){b.return=N,V=b;break}V=N}}}var j=o.alternate;if(j!==null){var k=j.child;if(k!==null){j.child=null;do{var S=k.sibling;k.sibling=null,k=S}while(k!==null)}}V=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,V=i;else e:for(;V!==null;){if(o=V,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Sn(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,V=m;break e}V=o.return}}var u=e.current;for(V=u;V!==null;){i=V;var p=i.child;if(i.subtreeFlags&2064&&p!==null)p.return=i,V=p;else e:for(i=u;V!==null;){if(a=V,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:jl(9,a)}}catch(L){fe(a,a.return,L)}if(a===i){V=null;break e}var E=a.sibling;if(E!==null){E.return=a.return,V=E;break e}V=a.return}}if(X=l,er(),bt&&typeof bt.onPostCommitFiberRoot=="function")try{bt.onPostCommitFiberRoot(fl,e)}catch{}s=!0}return s}finally{ee=r,rt.transition=t}}return!1}function Ac(e,t,r){t=Kr(r,t),t=Iu(e,t,1),e=Qt(e,t,1),t=Re(),e!==null&&(Xn(e,1,t),He(e,t))}function fe(e,t,r){if(e.tag===3)Ac(e,e,r);else for(;t!==null;){if(t.tag===3){Ac(t,e,r);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(Gt===null||!Gt.has(s))){e=Kr(r,e),e=Au(t,e,1),t=Qt(t,e,1),e=Re(),t!==null&&(Xn(t,1,e),He(t,e));break}}t=t.return}}function xf(e,t,r){var s=e.pingCache;s!==null&&s.delete(t),t=Re(),e.pingedLanes|=e.suspendedLanes&r,je===e&&(Se&r)===r&&(ve===4||ve===3&&(Se&130023424)===Se&&500>pe()-ea?dr(e,0):Xi|=r),He(e,t)}function Zu(e,t){t===0&&(e.mode&1?(t=gs,gs<<=1,!(gs&130023424)&&(gs=4194304)):t=1);var r=Re();e=At(e,t),e!==null&&(Xn(e,t,r),He(e,r))}function yf(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Zu(e,r)}function vf(e,t){var r=0;switch(e.tag){case 13:var s=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(A(314))}s!==null&&s.delete(t),Zu(e,r)}var Ku;Ku=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ve.current)Be=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return Be=!1,lf(e,t,r);Be=!!(e.flags&131072)}else Be=!1,ie&&t.flags&1048576&&tu(t,tl,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;Os(e,t),e=t.pendingProps;var l=Gr(t,_e.current);Hr(t,r),l=Gi(null,t,s,e,l,r);var o=Di();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qe(s)?(o=!0,Xs(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Vi(t),l.updater=bl,t.stateNode=l,l._reactInternals=t,Ho(t,s,e,r),t=Go(null,t,s,!0,o,r)):(t.tag=0,ie&&o&&Ri(t),Pe(null,t,l,r),t=t.child),t;case 16:s=t.elementType;e:{switch(Os(e,t),e=t.pendingProps,l=s._init,s=l(s._payload),t.type=s,l=t.tag=bf(s),e=it(s,e),l){case 0:t=Qo(null,t,s,e,r);break e;case 1:t=wc(null,t,s,e,r);break e;case 11:t=yc(null,t,s,e,r);break e;case 14:t=vc(null,t,s,it(s.type,e),r);break e}throw Error(A(306,s,""))}return t;case 0:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),Qo(e,t,s,l,r);case 1:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),wc(e,t,s,l,r);case 3:e:{if(Pu(t),e===null)throw Error(A(387));s=t.pendingProps,o=t.memoizedState,l=o.element,iu(e,t),sl(t,s,null,r);var i=t.memoizedState;if(s=i.element,o.isDehydrated)if(o={element:s,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=Kr(Error(A(423)),t),t=bc(e,t,s,r,l);break e}else if(s!==l){l=Kr(Error(A(424)),t),t=bc(e,t,s,r,l);break e}else for(Qe=Wt(t.stateNode.containerInfo.firstChild),Ge=t,ie=!0,ct=null,r=lu(t,null,s,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(Dr(),s===l){t=_t(e,t,r);break e}Pe(e,t,s,r)}t=t.child}return t;case 5:return au(t),e===null&&Bo(t),s=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,zo(s,l)?i=null:o!==null&&zo(s,o)&&(t.flags|=32),Lu(e,t),Pe(e,t,i,r),t.child;case 6:return e===null&&Bo(t),null;case 13:return Ru(e,t,r);case 4:return qi(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=Jr(t,null,s,r):Pe(e,t,s,r),t.child;case 11:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),yc(e,t,s,l,r);case 7:return Pe(e,t,t.pendingProps,r),t.child;case 8:return Pe(e,t,t.pendingProps.children,r),t.child;case 12:return Pe(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(s=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,re(rl,s._currentValue),s._currentValue=i,o!==null)if(mt(o.value,i)){if(o.children===l.children&&!Ve.current){t=_t(e,t,r);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var c=a.firstContext;c!==null;){if(c.context===s){if(o.tag===1){c=Et(-1,r&-r),c.tag=2;var d=o.updateQueue;if(d!==null){d=d.shared;var g=d.pending;g===null?c.next=c:(c.next=g.next,g.next=c),d.pending=c}}o.lanes|=r,c=o.alternate,c!==null&&(c.lanes|=r),Vo(o.return,r,t),a.lanes|=r;break}c=c.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(A(341));i.lanes|=r,a=i.alternate,a!==null&&(a.lanes|=r),Vo(i,r,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Pe(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,s=t.pendingProps.children,Hr(t,r),l=nt(l),s=s(l),t.flags|=1,Pe(e,t,s,r),t.child;case 14:return s=t.type,l=it(s,t.pendingProps),l=it(s.type,l),vc(e,t,s,l,r);case 15:return _u(e,t,t.type,t.pendingProps,r);case 17:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:it(s,l),Os(e,t),t.tag=1,qe(s)?(e=!0,Xs(t)):e=!1,Hr(t,r),Mu(t,s,l),Ho(t,s,l,r),Go(null,t,s,!0,e,r);case 19:return zu(e,t,r);case 22:return Tu(e,t,r)}throw Error(A(156,t.tag))};function Yu(e,t){return Sd(e,t)}function wf(e,t,r,s){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function tt(e,t,r,s){return new wf(e,t,r,s)}function sa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bf(e){if(typeof e=="function")return sa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ni)return 11;if(e===ki)return 14}return 2}function Jt(e,t){var r=e.alternate;return r===null?(r=tt(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function Fs(e,t,r,s,l,o){var i=2;if(s=e,typeof e=="function")sa(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Ir:return ur(r.children,l,o,t);case ji:i=8,l|=8;break;case fo:return e=tt(12,r,t,l|2),e.elementType=fo,e.lanes=o,e;case po:return e=tt(13,r,t,l),e.elementType=po,e.lanes=o,e;case go:return e=tt(19,r,t,l),e.elementType=go,e.lanes=o,e;case ad:return kl(r,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case od:i=10;break e;case id:i=9;break e;case Ni:i=11;break e;case ki:i=14;break e;case zt:i=16,s=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=tt(i,r,t,l),t.elementType=e,t.type=s,t.lanes=o,t}function ur(e,t,r,s){return e=tt(7,e,s,t),e.lanes=r,e}function kl(e,t,r,s){return e=tt(22,e,s,t),e.elementType=ad,e.lanes=r,e.stateNode={isHidden:!1},e}function io(e,t,r){return e=tt(6,e,null,t),e.lanes=r,e}function ao(e,t,r){return t=tt(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function jf(e,t,r,s,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Vl(0),this.expirationTimes=Vl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vl(0),this.identifierPrefix=s,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function la(e,t,r,s,l,o,i,a,c){return e=new jf(e,t,r,a,c),t===1?(t=1,o===!0&&(t|=8)):t=0,o=tt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:s,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Vi(o),e}function Nf(e,t,r){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Mr,key:s==null?null:""+s,children:e,containerInfo:t,implementation:r}}function Xu(e){if(!e)return Kt;e=e._reactInternals;e:{if(Nr(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var r=e.type;if(qe(r))return Xd(e,r,t)}return t}function e0(e,t,r,s,l,o,i,a,c){return e=la(r,s,!0,e,l,o,i,a,c),e.context=Xu(null),r=e.current,s=Re(),l=Dt(r),o=Et(s,l),o.callback=t??null,Qt(r,o,l),e.current.lanes=l,Xn(e,l,s),He(e,s),e}function Sl(e,t,r,s){var l=t.current,o=Re(),i=Dt(l);return r=Xu(r),t.context===null?t.context=r:t.pendingContext=r,t=Et(o,i),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=Qt(l,t,i),e!==null&&(ut(e,l,i,o),Ps(e,l,i)),i}function ml(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function _c(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function oa(e,t){_c(e,t),(e=e.alternate)&&_c(e,t)}function kf(){return null}var t0=typeof reportError=="function"?reportError:function(e){console.error(e)};function ia(e){this._internalRoot=e}Cl.prototype.render=ia.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));Sl(e,t,null,null)};Cl.prototype.unmount=ia.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wr(function(){Sl(null,e,null,null)}),t[It]=null}};function Cl(e){this._internalRoot=e}Cl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Td();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Ut.length&&t!==0&&t<Ut[r].priority;r++);Ut.splice(r,0,e),r===0&&Pd(e)}};function aa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function El(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Tc(){}function Sf(e,t,r,s,l){if(l){if(typeof s=="function"){var o=s;s=function(){var d=ml(i);o.call(d)}}var i=e0(t,s,e,0,null,!1,!1,"",Tc);return e._reactRootContainer=i,e[It]=i.current,$n(e.nodeType===8?e.parentNode:e),wr(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof s=="function"){var a=s;s=function(){var d=ml(c);a.call(d)}}var c=la(e,0,!1,null,null,!1,!1,"",Tc);return e._reactRootContainer=c,e[It]=c.current,$n(e.nodeType===8?e.parentNode:e),wr(function(){Sl(t,c,r,s)}),c}function Ml(e,t,r,s,l){var o=r._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var a=l;l=function(){var c=ml(i);a.call(c)}}Sl(t,i,e,l)}else i=Sf(r,t,e,l,s);return ml(i)}Ad=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=xn(t.pendingLanes);r!==0&&(Ei(t,r|1),He(t,pe()),!(X&6)&&(Yr=pe()+500,er()))}break;case 13:wr(function(){var s=At(e,1);if(s!==null){var l=Re();ut(s,e,1,l)}}),oa(e,1)}};Mi=function(e){if(e.tag===13){var t=At(e,134217728);if(t!==null){var r=Re();ut(t,e,134217728,r)}oa(e,134217728)}};_d=function(e){if(e.tag===13){var t=Dt(e),r=At(e,t);if(r!==null){var s=Re();ut(r,e,t,s)}oa(e,t)}};Td=function(){return ee};Ld=function(e,t){var r=ee;try{return ee=e,t()}finally{ee=r}};Co=function(e,t,r){switch(t){case"input":if(vo(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var s=r[t];if(s!==e&&s.form===e.form){var l=yl(s);if(!l)throw Error(A(90));dd(s),vo(s,l)}}}break;case"textarea":md(e,r);break;case"select":t=r.value,t!=null&&Fr(e,!!r.multiple,t,!1)}};vd=ta;wd=wr;var Cf={usingClientEntryPoint:!1,Events:[ts,Lr,yl,xd,yd,ta]},fn={findFiberByHostInstance:or,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ef={bundleType:fn.bundleType,version:fn.version,rendererPackageName:fn.rendererPackageName,rendererConfig:fn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Tt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Nd(e),e===null?null:e.stateNode},findFiberByHostInstance:fn.findFiberByHostInstance||kf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Es=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Es.isDisabled&&Es.supportsFiber)try{fl=Es.inject(Ef),bt=Es}catch{}}Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Cf;Je.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!aa(t))throw Error(A(200));return Nf(e,t,null,r)};Je.createRoot=function(e,t){if(!aa(e))throw Error(A(299));var r=!1,s="",l=t0;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=la(e,1,!1,null,null,r,!1,s,l),e[It]=t.current,$n(e.nodeType===8?e.parentNode:e),new ia(t)};Je.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=Nd(t),e=e===null?null:e.stateNode,e};Je.flushSync=function(e){return wr(e)};Je.hydrate=function(e,t,r){if(!El(t))throw Error(A(200));return Ml(null,e,t,!0,r)};Je.hydrateRoot=function(e,t,r){if(!aa(e))throw Error(A(405));var s=r!=null&&r.hydratedSources||null,l=!1,o="",i=t0;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(o=r.identifierPrefix),r.onRecoverableError!==void 0&&(i=r.onRecoverableError)),t=e0(t,null,e,1,r??null,l,!1,o,i),e[It]=t.current,$n(e),s)for(e=0;e<s.length;e++)r=s[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new Cl(t)};Je.render=function(e,t,r){if(!El(t))throw Error(A(200));return Ml(null,e,t,!1,r)};Je.unmountComponentAtNode=function(e){if(!El(e))throw Error(A(40));return e._reactRootContainer?(wr(function(){Ml(null,null,e,!1,function(){e._reactRootContainer=null,e[It]=null})}),!0):!1};Je.unstable_batchedUpdates=ta;Je.unstable_renderSubtreeIntoContainer=function(e,t,r,s){if(!El(r))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return Ml(e,t,r,!1,s)};Je.version="18.3.1-next-f1338f8080-20240426";function r0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r0)}catch(e){console.error(e)}}r0(),rd.exports=Je;var Mf=rd.exports,n0,Lc=Mf;n0=Lc.createRoot,Lc.hydrateRoot;/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const If=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s0=(...e)=>e.filter((t,r,s)=>!!t&&s.indexOf(t)===r).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Af={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _f=y.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:s,className:l="",children:o,iconNode:i,...a},c)=>y.createElement("svg",{ref:c,...Af,width:t,height:t,stroke:e,strokeWidth:s?Number(r)*24/Number(t):r,className:s0("lucide",l),...a},[...i.map(([d,g])=>y.createElement(d,g)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=(e,t)=>{const r=y.forwardRef(({className:s,...l},o)=>y.createElement(_f,{ref:o,iconNode:t,className:s0(`lucide-${If(e)}`,s),...l}));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=B("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l0=B("Apple",[["path",{d:"M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z",key:"3s7exb"}],["path",{d:"M10 2c1 .5 2 2 2 5",key:"fcco2y"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tf=B("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lf=B("ArrowLeftRight",[["path",{d:"M8 3 4 7l4 4",key:"9rb6wj"}],["path",{d:"M4 7h16",key:"6tx8e3"}],["path",{d:"m16 21 4-4-4-4",key:"siv7j2"}],["path",{d:"M20 17H4",key:"h6l3hr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pf=B("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oi=B("Box",[["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o0=B("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rf=B("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zf=B("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Of=B("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=B("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i0=B("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a0=B("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ii=B("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bs=B("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mr=B("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $f=B("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=B("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ff=B("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mn=B("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pc=B("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ai=B("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dn=B("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ca=B("Gamepad2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c0=B("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=B("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=B("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d0=B("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const In=B("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=B("Infinity",[["path",{d:"M12 12c-2-2.67-4-4-6-4a4 4 0 1 0 0 8c2 0 4-1.33 6-4Zm0 0c2 2.67 4 4 6 4a4 4 0 0 0 0-8c-2 0-4 1.33-6 4Z",key:"1z0uae"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u0=B("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=B("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m0=B("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=B("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hr=B("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ms=B("MessageSquareOff",[["path",{d:"M21 15V5a2 2 0 0 0-2-2H9",key:"43el77"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10",key:"pwpm4a"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rc=B("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jn=B("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wt=B("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=B("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=B("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f0=B("Puzzle",[["path",{d:"M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z",key:"i0oyt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=B("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hf=B("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ui=B("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const An=B("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=B("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=B("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=B("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g0=B("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wf=B("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=B("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=B("Trees",[["path",{d:"M10 10v.2A3 3 0 0 1 8.9 16H5a3 3 0 0 1-1-5.8V10a3 3 0 0 1 6 0Z",key:"1l6gj6"}],["path",{d:"M7 16v6",key:"1a82de"}],["path",{d:"M13 19v3",key:"13sx9i"}],["path",{d:"M12 19h8.3a1 1 0 0 0 .7-1.7L18 14h.3a1 1 0 0 0 .7-1.7L16 9h.2a1 1 0 0 0 .8-1.7L13 3l-1.4 1.5",key:"1sj9kv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mi=B("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zn=B("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qf=B("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gf=B("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=B("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=B("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pr=B("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const br=B("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Df=B("Volume1",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kn=B("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const co=B("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jf=B("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zf=B("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const da=B("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=B("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),uo={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},Kf=(uo==null?void 0:uo.VITE_API_URL)||"";class Yf{constructor(){this.token=null,this.token=localStorage.getItem("token")}async request(t,r={}){const s=`${Kf}${t}`,l={headers:{"Content-Type":"application/json",...this.token&&{Authorization:`Bearer ${this.token}`},...r.headers},...r};try{const o=await fetch(s,l);if(!o.ok){const i=await o.json().catch(()=>({}));throw o.status===401&&i.code==="MULTIPLE_LOGIN"&&(this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),window.location.reload()),new Error(i.message||`HTTP error! status: ${o.status}`)}return await o.json()}catch(o){throw console.error("API request failed:",o),o}}async login(t,r){const s=await this.request("/api/auth/login",{method:"POST",body:JSON.stringify({username:t,password:r})});return this.token=s.token,localStorage.setItem("token",s.token),localStorage.setItem("username",s.user.username),localStorage.setItem("isAdmin",s.user.isAdmin?"true":"false"),{...s,username:s.user.username,isAdmin:s.user.isAdmin}}async register(t,r,s){return this.request("/api/auth/register",{method:"POST",body:JSON.stringify({username:t,email:r,password:s})})}async logout(){try{await this.request("/api/auth/logout",{method:"POST"})}catch{}this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}async getCurrentUser(){return this.request("/api/user")}async updateProfile(t){return this.request("/api/users/profile",{method:"PUT",body:JSON.stringify(t)})}async uploadAvatar(t){const r=new FormData;return r.append("avatar",t),this.request("/api/users/upload-avatar",{method:"POST",headers:{},body:r})}async getAllUsersAdmin(){return this.request("/api/admin/users")}async searchUsersAdmin(t){return this.request(`/api/users/admin/search?q=${encodeURIComponent(t)}`)}async updateUserAdmin(t,r){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(r)})}async updateCurrency(t,r){return this.request("/api/user/currency",{method:"PUT",body:JSON.stringify({goldCoins:t,pearls:r})})}async getNotifications(){return this.request("/api/notifications")}async markNotificationAsRead(t){return this.request(`/api/notifications/${t}/read`,{method:"PUT"})}async markAllNotificationsAsRead(){return this.request("/api/notifications/mark-all-read",{method:"PUT"})}async getMessages(t){return this.request(`/api/messages/${t}`)}async sendMessage(t,r,s="text"){return this.request("/api/messages",{method:"POST",body:JSON.stringify({recipientId:t,content:r,messageType:s})})}async getFriends(){return this.request("/api/profile/friends")}async getFriendRequests(){return this.request("/api/profile/friend-requests")}async sendFriendRequest(t){return this.request("/api/profile/friend-request",{method:"POST",body:JSON.stringify({friendId:t})})}async acceptFriendRequest(t){return this.request("/api/profile/accept-friend",{method:"POST",body:JSON.stringify({friendshipId:t})})}async checkFriendship(t){return this.request(`/api/friends/check/${t}`)}async searchUserById(t){return this.request(`/api/users/search-by-id/${t}`)}async exchangeGoldToPearls(t){return this.request("/api/profile/exchange-gold-to-pearls",{method:"POST",body:JSON.stringify({goldAmount:t})})}async sendItem(t,r,s){return this.request("/api/profile/send-item",{method:"POST",body:JSON.stringify({toUserId:t,itemType:r,message:s})})}async getGifts(){return this.request("/api/profile/gifts")}async sendGift(t,r,s,l){return this.request("/api/profile/send-gift",{method:"POST",body:JSON.stringify({toUserId:t,giftType:r,amount:s,message:l})})}async claimGift(t){return this.request("/api/profile/claim-gift",{method:"POST",body:JSON.stringify({giftId:t})})}async getItems(){return this.request("/api/profile/items")}async getVoiceRoom(){return this.request("/api/voice-room")}async joinVoiceSeat(t){return this.request("/api/voice-room/join-seat",{method:"POST",body:JSON.stringify({seatNumber:t})})}async leaveVoiceSeat(){return this.request("/api/voice-room/leave-seat",{method:"POST"})}async requestMic(){return this.request("/api/voice-room/request-mic",{method:"POST"})}async cancelMicRequest(){return this.request("/api/voice-room/cancel-mic-request",{method:"POST"})}async sendVoiceRoomMessage(t){return this.request("/api/voice-room/send-message",{method:"POST",body:JSON.stringify({content:t})})}async getVoiceRoomMessages(){return this.request("/api/voice-room/messages")}async toggleMute(t){return this.request("/api/voice-room/toggle-mute",{method:"POST",body:JSON.stringify({isMuted:t})})}async kickUserFromVoiceRoom(t,r){return this.request("/api/voice-room/admin/kick",{method:"POST",body:JSON.stringify({userId:t,durationInMinutes:r})})}async muteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/mute",{method:"POST",body:JSON.stringify({userId:t})})}async unmuteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/unmute",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromSeat(t){return this.request("/api/voice-room/admin/remove-seat",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromQueue(t){return this.request("/api/voice-room/admin/remove-queue",{method:"POST",body:JSON.stringify({userId:t})})}async banUserFromChat(t){return this.request("/api/voice-room/admin/ban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async unbanUserFromChat(t){return this.request("/api/voice-room/admin/unban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async getUserItems(t){return this.request(`/api/user-items/${t}`)}async getShield(t){return this.request(`/api/profile/shield/${t}`)}async activateShield(t){return this.request("/api/profile/activate-shield",{method:"POST",body:JSON.stringify({shieldType:t})})}async getTransactions(t=1,r=20){return this.request(`/api/profile/transactions?page=${t}&limit=${r}`)}async chargeBalance(t){return this.request("/api/profile/charge-balance",{method:"POST",body:JSON.stringify({amount:t})})}async activateItem(t){return this.request("/api/profile/activate-item",{method:"POST",body:JSON.stringify({itemId:t})})}async getGameSettings(){return this.request("/api/game/settings")}async updateGameSettings(t){return this.request("/api/game/settings",{method:"POST",body:t})}async getSuspiciousActivities(){return this.request("/api/admin/suspicious-activities")}async getPlayerId(t){return this.request(`/api/admin/users/${t}/player-id`)}async updatePlayerId(t,r){return this.request(`/api/admin/users/${t}/player-id`,{method:"PUT",body:JSON.stringify({playerId:r})})}async getUsersWithIds(t=1,r=12,s=""){return this.request(`/api/users/admin/users-with-ids?page=${t}&limit=${r}&search=${s}`)}async updateUser(t,r){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(r)})}async deleteUser(t){return this.request(`/api/users/admin/delete/${t}`,{method:"DELETE"})}async deleteUserImage(t){return this.request(`/api/users/admin/delete-image/${t}`,{method:"DELETE"})}async manageUserImage(t,r,s,l){return this.request("/api/users/admin/manage-user-image",{method:"PUT",body:JSON.stringify({targetUserId:t,action:r,imageData:s,imageType:l})})}async debugAllUsers(){return this.request("/api/admin/debug/all-users")}async updateBalance(t,r,s,l){return this.request("/api/users/update-balance",{method:"POST",body:JSON.stringify({balanceChange:t,gameType:r,sessionId:s,gameResult:l})})}async getGameProfile(){return this.request("/api/users/profile")}async endGameSession(t){return this.request("/api/games/session-end",{method:"POST",body:t})}async getPlayerStats(){return this.request("/api/games/player-stats")}clearLocalData(){localStorage.removeItem("token"),localStorage.removeItem("userData"),localStorage.removeItem("adminToken"),localStorage.removeItem("selectedUser"),localStorage.removeItem("userCache"),console.log("🧹 Cleared all local storage data")}}const K=new Yf,Oc={ar:{login:"تسجيل الدخول",username:"اسم المستخدم",password:"كلمة المرور",loginButton:"دخول",switchToRegister:"ليس لديك حساب؟ إنشاء حساب",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",requiredFields:"الرجاء إدخال اسم المستخدم وكلمة المرور",loginFailed:"فشل في تسجيل الدخول"},en:{login:"Login",username:"Username",password:"Password",loginButton:"Login",switchToRegister:"Don't have an account? Register",loginDesc:"Profitable activities that blend with fun and friendships",requiredFields:"Please enter username and password",loginFailed:"Login failed"},ur:{login:"لاگ ان",username:"صارف نام",password:"پاس ورڈ",loginButton:"داخل ہوں",switchToRegister:"اکاؤنٹ نہیں ہے؟ رجسٹر کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",requiredFields:"براہ کرم صارف نام اور پاس ورڈ درج کریں",loginFailed:"لاگ ان ناکام"},es:{login:"Iniciar Sesión",username:"Nombre de Usuario",password:"Contraseña",loginButton:"Entrar",switchToRegister:"¿No tienes cuenta? Regístrate",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",requiredFields:"Por favor ingresa nombre de usuario y contraseña",loginFailed:"Error al iniciar sesión"}},Xf=({onLoginSuccess:e,onSwitchToRegister:t})=>{const[r,s]=y.useState({username:"",password:""}),[l,o]=y.useState(!1),[i,a]=y.useState(!1),[c,d]=y.useState(""),g=localStorage.getItem("selectedLanguage")||"ar",v=j=>{var k;return((k=Oc[g])==null?void 0:k[j])||Oc.ar[j]||j},b=async j=>{if(j.preventDefault(),d(""),!r.username.trim()||!r.password.trim()){d(v("requiredFields"));return}a(!0);try{const k=await K.login(r.username,r.password);e(k)}catch(k){d(k.message||v("loginFailed"))}finally{a(!1)}},N=j=>{const{name:k,value:S}=j.target;s(m=>({...m,[k]:S})),c&&d("")};return n.jsx("div",{className:"w-full max-w-sm mx-auto",children:n.jsxs("div",{className:"relative group",children:[n.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-800/20 via-blue-900/25 to-slate-800/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-500 animate-pulse"}),n.jsx("div",{className:"relative bg-gradient-to-br from-blue-900/40 via-blue-800/30 to-slate-800/40 backdrop-blur-2xl rounded-2xl p-5 border border-blue-400/30 shadow-xl hover:shadow-blue-500/30 transition-all duration-500 hover:border-blue-300/50",children:n.jsxs("div",{className:"relative z-10",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsxs("div",{className:"relative mx-auto mb-4",children:[n.jsxs("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full flex items-center justify-center mx-auto relative shadow-xl shadow-blue-500/40 hover:shadow-blue-500/60 transition-all duration-500 hover:scale-105 group",children:[n.jsx(oi,{className:"w-8 h-8 text-white drop-shadow-lg"}),n.jsx(Al,{className:"w-4 h-4 text-white absolute -top-0.5 -right-0.5 animate-spin",style:{animationDuration:"8s"}})]}),n.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full blur-lg opacity-30 animate-pulse"})]}),n.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-2 drop-shadow-lg",children:v("login")}),n.jsxs("p",{className:"text-purple-200 font-medium text-sm",children:["🌟 ",v("loginDesc")," 🌟"]})]}),c&&n.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:n.jsx("p",{className:"text-red-300 text-sm text-center",children:c})}),n.jsxs("form",{onSubmit:b,className:"space-y-4",children:[n.jsxs("div",{className:"space-y-3",children:[n.jsxs("div",{className:"relative group",children:[n.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:n.jsx(di,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),n.jsx("input",{type:"text",name:"username",value:r.username,onChange:N,placeholder:v("username"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"username",required:!0}),n.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]}),n.jsxs("div",{className:"relative group",children:[n.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:n.jsx(ci,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),n.jsx("input",{type:l?"text":"password",name:"password",value:r.password,onChange:N,placeholder:v("password"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 pl-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"current-password",required:!0}),n.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center z-10 group/eye",children:l?n.jsx(ai,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"}):n.jsx(Dn,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"})}),n.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]})]}),n.jsxs("button",{type:"submit",disabled:i,className:"relative w-full group overflow-hidden",children:[n.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl transition-all duration-500 group-hover:from-blue-600 group-hover:via-blue-700 group-hover:to-blue-800"}),n.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl blur-md opacity-40 group-hover:opacity-60 transition-opacity duration-500"}),n.jsx("div",{className:"relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-500 hover:via-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 active:scale-95 flex items-center justify-center gap-2 text-sm shadow-xl",children:i?n.jsxs(n.Fragment,{children:[n.jsx(u0,{className:"w-4 h-4 animate-spin"}),n.jsx("span",{className:"animate-pulse",children:v("loggingIn")||"جاري تسجيل الدخول..."})]}):n.jsxs(n.Fragment,{children:[n.jsx(oi,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),n.jsxs("span",{className:"group-hover:tracking-wider transition-all duration-300",children:["🚀 ",v("loginButton")]})]})}),n.jsx("div",{className:"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:n.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"})})]})]}),n.jsxs("div",{className:"mt-5 space-y-4",children:[n.jsx("div",{className:"text-center",children:n.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10",children:[n.jsx("p",{className:"text-blue-200 mb-2 text-sm",children:v("switchToRegister")}),n.jsxs("button",{onClick:t,className:"group relative inline-flex items-center gap-2 bg-gradient-to-r from-blue-800/20 to-blue-900/20 hover:from-blue-700/30 hover:to-blue-800/30 text-blue-300 hover:text-white font-bold py-2 px-4 rounded-lg border border-blue-400/30 hover:border-blue-300/60 transition-all duration-300 hover:scale-105",children:[n.jsx(pr,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),n.jsx("span",{className:"group-hover:tracking-wider transition-all duration-300 text-sm",children:"✨ إنشاء حساب جديد"})]})]})}),n.jsx("div",{className:"bg-gradient-to-br from-blue-800/20 via-blue-900/15 to-slate-800/20 backdrop-blur-sm rounded-xl p-4 border border-blue-400/20",children:n.jsxs("div",{className:"text-center space-y-3",children:[n.jsx("h3",{className:"text-blue-200 font-bold text-sm mb-3",children:"📞 للاستفسار وشحن العملات في INFINITY BOX"}),n.jsxs("div",{className:"space-y-2",children:[n.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[n.jsx(di,{className:"w-3 h-3"}),n.jsx("span",{className:"font-medium",children:"<EMAIL>"})]}),n.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[n.jsx(qf,{className:"w-3 h-3"}),n.jsx("span",{className:"font-medium",children:"00966554593007"})]})]}),n.jsx("div",{className:"pt-2 border-t border-blue-400/20",children:n.jsx("p",{className:"text-blue-400/70 text-xs",children:"© 2024 INFINITY BOX - جميع الحقوق محفوظة"})})]})})]})]})})]})})},Uc={ar:{register:"إنشاء حساب",username:"اسم المستخدم",email:"البريد الإلكتروني",password:"كلمة المرور",confirmPassword:"تأكيد كلمة المرور",registerButton:"إنشاء حساب",switchToLogin:"لديك حساب؟ تسجيل الدخول",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",usernameRequired:"اسم المستخدم مطلوب",usernameMinLength:"اسم المستخدم يجب أن يكون 3 أحرف على الأقل",emailRequired:"البريد الإلكتروني مطلوب",emailInvalid:"البريد الإلكتروني غير صالح",passwordRequired:"كلمة المرور مطلوبة",passwordMinLength:"كلمة المرور يجب أن تكون 6 أحرف على الأقل",passwordMismatch:"كلمات المرور غير متطابقة",registering:"جاري إنشاء الحساب...",registerFailed:"فشل في إنشاء الحساب"},en:{register:"Register",username:"Username",email:"Email",password:"Password",confirmPassword:"Confirm Password",registerButton:"Create Account",switchToLogin:"Have an account? Login",registerDesc:"Join us and enjoy a unique experience",usernameRequired:"Username is required",usernameMinLength:"Username must be at least 3 characters",emailRequired:"Email is required",emailInvalid:"Invalid email address",passwordRequired:"Password is required",passwordMinLength:"Password must be at least 6 characters",passwordMismatch:"Passwords do not match",registering:"Creating account...",registerFailed:"Failed to create account"},ur:{register:"رجسٹر",username:"صارف نام",email:"ای میل",password:"پاس ورڈ",confirmPassword:"پاس ورڈ کی تصدیق",registerButton:"اکاؤنٹ بنائیں",switchToLogin:"اکاؤنٹ ہے؟ لاگ ان کریں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",usernameRequired:"صارف نام ضروری ہے",usernameMinLength:"صارف نام کم از کم 3 حروف کا ہونا چاہیے",emailRequired:"ای میل ضروری ہے",emailInvalid:"غلط ای میل ایڈریس",passwordRequired:"پاس ورڈ ضروری ہے",passwordMinLength:"پاس ورڈ کم از کم 6 حروف کا ہونا چاہیے",passwordMismatch:"پاس ورڈ میل نہیں کھاتے",registering:"اکاؤنٹ بنایا جا رہا ہے...",registerFailed:"اکاؤنٹ بنانے میں ناکامی"},es:{register:"Registrarse",username:"Nombre de Usuario",email:"Correo Electrónico",password:"Contraseña",confirmPassword:"Confirmar Contraseña",registerButton:"Crear Cuenta",switchToLogin:"¿Tienes cuenta? Inicia sesión",registerDesc:"Únete a nosotros y disfruta de una experiencia única",usernameRequired:"El nombre de usuario es requerido",usernameMinLength:"El nombre de usuario debe tener al menos 3 caracteres",emailRequired:"El correo electrónico es requerido",emailInvalid:"Dirección de correo electrónico inválida",passwordRequired:"La contraseña es requerida",passwordMinLength:"La contraseña debe tener al menos 6 caracteres",passwordMismatch:"Las contraseñas no coinciden",registering:"Creando cuenta...",registerFailed:"Error al crear la cuenta"}},ep=({onRegisterSuccess:e,onSwitchToLogin:t})=>{const[r,s]=y.useState({username:"",email:"",password:"",confirmPassword:""}),[l,o]=y.useState(!1),[i,a]=y.useState(!1),[c,d]=y.useState(!1),[g,v]=y.useState(""),b=localStorage.getItem("selectedLanguage")||"ar",N=m=>{var u;return((u=Uc[b])==null?void 0:u[m])||Uc.ar[m]||m},j=()=>r.username.trim()?r.username.length<3?N("usernameMinLength"):r.email.trim()?/\S+@\S+\.\S+/.test(r.email)?r.password?r.password.length<6?N("passwordMinLength"):r.password!==r.confirmPassword?N("passwordMismatch"):null:N("passwordRequired"):N("emailInvalid"):N("emailRequired"):N("usernameRequired"),k=async m=>{m.preventDefault(),v("");const u=j();if(u){v(u);return}d(!0);try{const p=await K.register(r.username,r.email,r.password);p.token&&p.user&&(localStorage.setItem("token",p.token),localStorage.setItem("userData",JSON.stringify(p.user))),p.isNewUser&&p.welcomeMessage?alert(p.welcomeMessage):p.rewards&&alert(`🎉 مرحباً بك في المنصة!

هدية الترحيب:
🪙 ${p.rewards.goldCoins.toLocaleString()} عملة ذهبية
🦪 ${p.rewards.pearls} لآلئ

استمتع باللعب واربح المزيد!`),e(p.user)}catch(p){v(p.message||N("registerFailed"))}finally{d(!1)}},S=m=>{const{name:u,value:p}=m.target;s(E=>({...E,[u]:p})),g&&v("")};return n.jsx("div",{className:"w-full max-w-md mx-auto",children:n.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl",children:[n.jsxs("div",{className:"text-center mb-8",children:[n.jsxs("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 relative",children:[n.jsx(_l,{className:"w-10 h-10 text-white"}),n.jsx(Al,{className:"w-5 h-5 text-white absolute -top-1 -right-1"})]}),n.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:N("register")}),n.jsx("p",{className:"text-gray-300",children:N("registerDesc")})]}),g&&n.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:n.jsx("p",{className:"text-red-300 text-sm text-center",children:g})}),n.jsxs("form",{onSubmit:k,className:"space-y-6",children:[n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{className:"relative",children:[n.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:n.jsx(pr,{className:"h-5 w-5 text-gray-400"})}),n.jsx("input",{type:"text",name:"username",value:r.username,onChange:S,placeholder:N("username"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"username",required:!0})]}),n.jsxs("div",{className:"relative",children:[n.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:n.jsx(di,{className:"h-5 w-5 text-gray-400"})}),n.jsx("input",{type:"email",name:"email",value:r.email,onChange:S,placeholder:N("email"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"email",required:!0})]}),n.jsxs("div",{className:"relative",children:[n.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:n.jsx(ci,{className:"h-5 w-5 text-gray-400"})}),n.jsx("input",{type:l?"text":"password",name:"password",value:r.password,onChange:S,placeholder:N("password"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"new-password",required:!0}),n.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:l?n.jsx(ai,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):n.jsx(Dn,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]}),n.jsxs("div",{className:"relative",children:[n.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:n.jsx(ci,{className:"h-5 w-5 text-gray-400"})}),n.jsx("input",{type:i?"text":"password",name:"confirmPassword",value:r.confirmPassword,onChange:S,placeholder:N("confirmPassword"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",required:!0}),n.jsx("button",{type:"button",onClick:()=>a(!i),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:i?n.jsx(ai,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):n.jsx(Dn,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]})]}),n.jsx("button",{type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2",children:c?n.jsxs(n.Fragment,{children:[n.jsx(u0,{className:"w-5 h-5 animate-spin"}),N("registering")]}):n.jsxs(n.Fragment,{children:[n.jsx(oi,{className:"w-5 h-5"}),N("registerButton")]})})]}),n.jsxs("div",{className:"mt-6 text-center",children:[n.jsx("p",{className:"text-gray-300",children:N("switchToLogin")}),n.jsx("button",{onClick:t,className:"text-blue-400 hover:text-blue-300 font-semibold transition-colors mt-2",children:N("login")})]})]})})},$c={ar:{welcome:"مرحباً بك",login:"تسجيل الدخول",register:"إنشاء حساب",selectLanguage:"اختر اللغة",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",registerSuccess:"تم إنشاء الحساب بنجاح!",welcomeMessage:"مرحباً بك في INFINITY BOX - يمكنك الآن تسجيل الدخول"},en:{welcome:"Welcome",login:"Login",register:"Register",selectLanguage:"Select Language",loginDesc:"Profitable activities that blend with fun and friendships",registerDesc:"Join us and enjoy a unique experience",registerSuccess:"Account created successfully!",welcomeMessage:"Welcome to INFINITY BOX - you can now login"},ur:{welcome:"خوش آمدید",login:"لاگ ان",register:"رجسٹر",selectLanguage:"زبان منتخب کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",registerSuccess:"اکاؤنٹ کامیابی سے بن گیا!",welcomeMessage:"INFINITY BOX میں خوش آمدید - اب آپ لاگ ان کر سکتے ہیں"},es:{welcome:"Bienvenido",login:"Iniciar Sesión",register:"Registrarse",selectLanguage:"Seleccionar Idioma",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",registerDesc:"Únete a nosotros y disfruta de una experiencia única",registerSuccess:"¡Cuenta creada exitosamente!",welcomeMessage:"Bienvenido a INFINITY BOX - ahora puedes iniciar sesión"}},tp=({onAuthSuccess:e})=>{const[t,r]=y.useState("login"),[s,l]=y.useState(!1),[o,i]=y.useState(()=>localStorage.getItem("selectedLanguage")||"ar"),[a,c]=y.useState(!1),d=k=>({ar:"🇸🇦",en:"🇺🇸",ur:"🇵🇰",es:"🇪🇸"})[k]||"🌍",g=k=>({ar:"العربية",en:"English",ur:"اردو",es:"Español"})[k]||k,v=k=>{var S;return((S=$c[o])==null?void 0:S[k])||$c.ar[k]||k},b=k=>{i(k),localStorage.setItem("selectedLanguage",k),document.documentElement.dir=["ar","ur"].includes(k)?"rtl":"ltr",document.documentElement.lang=k,c(!1)};y.useEffect(()=>{document.documentElement.dir=["ar","ur"].includes(o)?"rtl":"ltr",document.documentElement.lang=o},[o]);const N=k=>{if(k.isAdmin&&window.confirm("مرحباً أيها المشرف! هل تريد الذهاب إلى لوحة التحكم؟ (إلغاء للذهاب إلى اللعبة)")){window.location.href="/admin.html";return}e(k)},j=k=>{k?N(k):(l(!0),setTimeout(()=>{l(!1),r("login")},3e3))};return n.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white relative overflow-hidden",children:[n.jsx("div",{className:"absolute top-4 right-4 z-50",children:n.jsxs("button",{onClick:()=>c(!0),className:"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full hover:bg-white/20 transition-all duration-300",children:[n.jsx(Vf,{className:"w-5 h-5"}),n.jsx("span",{className:"text-2xl",children:d(o)}),n.jsx("span",{className:"hidden sm:inline",children:g(o)})]})}),a&&n.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:n.jsxs("div",{className:"bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 max-w-md w-full",children:[n.jsx("h3",{className:"text-xl font-bold text-center mb-6",children:v("selectLanguage")}),n.jsx("div",{className:"grid grid-cols-2 gap-4",children:["ar","en","ur","es"].map(k=>n.jsxs("button",{onClick:()=>b(k),className:`flex items-center gap-3 p-4 rounded-xl transition-all duration-300 ${o===k?"bg-blue-500/30 border-2 border-blue-400":"bg-white/5 border border-white/10 hover:bg-white/10"}`,children:[n.jsx("span",{className:"text-3xl",children:d(k)}),n.jsx("span",{className:"font-medium",children:g(k)})]},k))}),n.jsx("button",{onClick:()=>c(!1),className:"w-full mt-6 py-3 bg-gray-500/20 hover:bg-gray-500/30 rounded-xl transition-all duration-300",children:"إغلاق / Close"})]})}),n.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[n.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/30 to-indigo-900/50"}),n.jsxs("div",{className:"absolute -inset-10 opacity-30",children:[n.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),n.jsx("div",{className:"absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),n.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-72 h-72 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"}),n.jsx("div",{className:"absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-3000"})]}),n.jsxs("div",{className:"absolute inset-0 opacity-20",children:[n.jsx("div",{className:"absolute top-10 left-10 w-32 h-32 bg-white rounded-full filter blur-xl animate-bounce delay-500"}),n.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 bg-yellow-300 rounded-full filter blur-lg animate-bounce delay-1500"}),n.jsx("div",{className:"absolute top-1/3 right-10 w-20 h-20 bg-green-400 rounded-full filter blur-lg animate-bounce delay-2500"})]}),n.jsx("div",{className:"absolute inset-0 opacity-20",children:n.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((k,S)=>n.jsx("div",{className:`${S%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${S*150}ms`,animationDuration:`${2+S%3}s`}},S))})}),n.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"})]}),n.jsxs("div",{className:"relative z-10 min-h-screen flex flex-col",children:[n.jsxs("header",{className:"text-center py-12 relative",children:[n.jsx("div",{className:"relative z-10",children:n.jsx("div",{className:"inline-flex items-center justify-center mb-8",children:n.jsxs("div",{className:"relative group",children:[n.jsxs("div",{className:"w-32 h-32 sphere-3d rounded-full flex items-center justify-center relative group-hover:scale-110 transition-all duration-500 rotate-y",children:[n.jsx("div",{className:"color-layer"}),n.jsxs("div",{className:"text-center relative z-10",children:[n.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight mb-1 drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"INFINITY"}),n.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"BOX"})]}),n.jsx(Al,{className:"w-6 h-6 text-white absolute -top-2 -right-2 animate-spin bg-gradient-to-r from-yellow-600 to-blue-700 rounded-full p-1 z-20",style:{animationDuration:"12s"}})]}),n.jsx("div",{className:"absolute inset-0 sphere-glow rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 rotate-y-reverse"}),n.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-white/10 rotate-y-fast"}),n.jsx("div",{className:"absolute -inset-2 rounded-full border border-white/5 rotate-y-slow"})]})})}),n.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-5",children:n.jsx("div",{className:"text-8xl font-black text-white animate-spin",style:{animationDuration:"25s"},children:"∞"})})]}),n.jsx("div",{className:"flex-1 flex items-start justify-center px-4 pt-8 pb-8",children:n.jsx("div",{className:"w-full max-w-md mx-auto",children:n.jsx("div",{className:"flex items-center justify-center",children:n.jsx("div",{className:"w-full",children:s?n.jsx("div",{className:"w-full max-w-md mx-auto",children:n.jsxs("div",{className:"bg-green-500/20 border border-green-500/50 rounded-3xl p-8 text-center",children:[n.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:n.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),n.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:v("registerSuccess")}),n.jsx("p",{className:"text-green-300",children:v("welcomeMessage")})]})}):t==="login"?n.jsx(Xf,{onLoginSuccess:N,onSwitchToRegister:()=>r("register")}):n.jsx(ep,{onRegisterSuccess:j,onSwitchToLogin:()=>r("login")})})})})})]})]})};function rp(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var r,s,l,o,i=[],a="",c=e.split("/");for(c[0]||c.shift();l=c.shift();)r=l[0],r==="*"?(i.push(r),a+=l[1]==="?"?"(?:/(.*))?":"/(.*)"):r===":"?(s=l.indexOf("?",1),o=l.indexOf(".",1),i.push(l.substring(1,~s?s:~o?o:l.length)),a+=~s&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~s?"?":"")+"\\"+l.substring(o))):a+="/"+l;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var w0={exports:{}},b0={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xr=y;function np(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var sp=typeof Object.is=="function"?Object.is:np,lp=Xr.useState,op=Xr.useEffect,ip=Xr.useLayoutEffect,ap=Xr.useDebugValue;function cp(e,t){var r=t(),s=lp({inst:{value:r,getSnapshot:t}}),l=s[0].inst,o=s[1];return ip(function(){l.value=r,l.getSnapshot=t,mo(l)&&o({inst:l})},[e,r,t]),op(function(){return mo(l)&&o({inst:l}),e(function(){mo(l)&&o({inst:l})})},[e]),ap(r),r}function mo(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!sp(e,r)}catch{return!0}}function dp(e,t){return t()}var up=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?dp:cp;b0.useSyncExternalStore=Xr.useSyncExternalStore!==void 0?Xr.useSyncExternalStore:up;w0.exports=b0;var mp=w0.exports;const hp=gm.useInsertionEffect,fp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",pp=fp?y.useLayoutEffect:y.useEffect,gp=hp||pp,j0=e=>{const t=y.useRef([e,(...r)=>t[0](...r)]).current;return gp(()=>{t[0]=e}),t[1]},xp="popstate",ua="pushState",ma="replaceState",yp="hashchange",Fc=[xp,ua,ma,yp],vp=e=>{for(const t of Fc)addEventListener(t,e);return()=>{for(const t of Fc)removeEventListener(t,e)}},N0=(e,t)=>mp.useSyncExternalStore(vp,e,t),wp=()=>location.search,bp=({ssrSearch:e=""}={})=>N0(wp,()=>e),Bc=()=>location.pathname,jp=({ssrPath:e}={})=>N0(Bc,e?()=>e:Bc),Np=(e,{replace:t=!1,state:r=null}={})=>history[t?ma:ua](r,"",e),kp=(e={})=>[jp(e),Np],Vc=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Vc]>"u"){for(const e of[ua,ma]){const t=history[e];history[e]=function(){const r=t.apply(this,arguments),s=new Event(e);return s.arguments=arguments,dispatchEvent(s),r}}Object.defineProperty(window,Vc,{value:!0})}const Sp=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",k0=(e="")=>e==="/"?"":e,Cp=(e,t)=>e[0]==="~"?e.slice(1):k0(t)+e,Ep=(e="",t)=>Sp(qc(k0(e)),qc(t)),qc=e=>{try{return decodeURI(e)}catch{return e}},Mp={hook:kp,searchHook:bp,parser:rp,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},Ip=y.createContext(Mp),S0=()=>y.useContext(Ip),Ap={};y.createContext(Ap);const C0=e=>{const[t,r]=e.hook(e);return[Ep(e.base,t),j0((s,l)=>r(Cp(s,e.base),l))]},E0=()=>C0(S0());y.forwardRef((e,t)=>{const r=S0(),[s,l]=C0(r),{to:o="",href:i=o,onClick:a,asChild:c,children:d,className:g,replace:v,state:b,...N}=e,j=j0(S=>{S.ctrlKey||S.metaKey||S.altKey||S.shiftKey||S.button!==0||(a==null||a(S),S.defaultPrevented||(S.preventDefault(),l(i,e)))}),k=r.hrefs(i[0]==="~"?i.slice(1):r.base+i,r);return c&&y.isValidElement(d)?y.cloneElement(d,{onClick:j,href:k}):y.createElement("a",{...N,onClick:j,href:k,className:g!=null&&g.call?g(s===i):g,children:d,ref:t})});const Vs=({setActiveTab:e})=>{E0();const t=[{icon:n.jsx(v0,{className:"w-8 h-8"}),title:"تحدي السرعة",description:"اختبر سرعة ردود أفعالك في تحدي مثير",color:"from-yellow-500 to-orange-500",onClick:()=>window.open("/speed-challenge.html","_blank")},{icon:n.jsx(g0,{className:"w-8 h-8"}),title:"صناديق الحظ",description:"اكسر الصناديق واجمع الكنوز والجواهر",color:"from-green-500 to-emerald-500",onClick:()=>window.open("/game8.html","_blank")},{icon:n.jsx(f0,{className:"w-8 h-8"}),title:"ألغاز العقل",description:"حل الألغاز المعقدة واختبر ذكاءك",color:"from-blue-500 to-cyan-500",onClick:()=>window.open("/mind-puzzles.html","_blank")},{icon:n.jsx(l0,{className:"w-8 h-8"}),title:"قطف الفواكه",description:"اقطف الفواكه الساقطة واجمع النقاط",color:"from-red-500 to-yellow-500",onClick:()=>window.open("/fruit-catching.html","_blank")},{icon:n.jsx(o0,{className:"w-8 h-8"}),title:"لعبة الذاكرة",description:"اختبر ذاكرتك وطابق البطاقات",color:"from-purple-500 to-pink-500",onClick:()=>window.open("/memory-match.html","_blank")},{icon:n.jsx(y0,{className:"w-8 h-8"}),title:"لعبة الغابة",description:"اكتشف الحيوانات وتعلم أسماءها",color:"from-green-600 to-emerald-600",onClick:()=>window.open("/forest-game.html","_blank")}];return n.jsx("div",{className:"space-y-8",children:n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8",children:n.jsxs("section",{children:[n.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[n.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-blue-600 rounded-lg flex items-center justify-center",children:n.jsx(ca,{className:"w-5 h-5 text-white"})}),n.jsx("h2",{className:"text-xl lg:text-2xl font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:"قاعة الألعاب"})]}),n.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:t.map((r,s)=>n.jsxs("button",{onClick:r.onClick,className:"group p-2 sm:p-3 crystal-game-card rounded-xl text-center",children:[n.jsx("div",{className:`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-r ${r.color} rounded-full flex items-center justify-center mb-2 sm:mb-3 group-hover:scale-110 transition-transform duration-300 mx-auto shadow-lg ring-4 ring-white/20`,children:r.icon}),n.jsx("h3",{className:"text-xs sm:text-sm font-semibold text-white mb-1",children:r.title}),n.jsx("p",{className:"text-gray-300 text-xs leading-tight",children:r.description})]},s))})]})})})},_p=()=>{const[e,t]=y.useState([]),[r,s]=y.useState(1),[l,o]=y.useState(""),[i,a]=y.useState(null),[c,d]=y.useState(!1),[g,v]=y.useState(null),[b,N]=y.useState(""),[j,k]=y.useState(null),[S,m]=y.useState(null);y.useState(!1),y.useState(null);const[u,p]=y.useState(!1),[E,L]=y.useState(null);y.useEffect(()=>{z()},[]);const O=(f,C)=>{L({type:f,text:C}),setTimeout(()=>L(null),5e3)},z=async(f=1,C="")=>{var _;d(!0);try{const T=await K.getUsersWithImages(f,12,C);console.log("📥 Loaded users data:",T.users),(_=T.users)==null||_.forEach(F=>{F.profileImage&&console.log(`👤 User ${F.username} has profileImage:`,F.profileImage.substring(0,50)+"...")}),t(T.users||[]),a(T.pagination),s(f),o(C)}catch(T){console.error("❌ Error loading users:",T),O("error","خطأ في تحميل المستخدمين: "+T.message)}finally{d(!1)}},$=()=>{const f=document.getElementById("searchInput"),C=(f==null?void 0:f.value.trim())||"";z(1,C)},G=(f,C)=>{console.log("🖼️ Image action for user:",f),console.log("🔍 User ID fields:",{id:f.id,userId:f.userId,_id:f._id});const _=f.id||f.userId||f._id;if(!_){O("error","خطأ: لا يمكن العثور على معرف المستخدم");return}const T={...f,id:_,userId:_};v(T),N(C),k(null),m(null),p(!0)},x=async f=>{var _;const C=(_=f.target.files)==null?void 0:_[0];if(C){if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(C.type)){O("error","نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP");return}const F=5*1024*1024;if(C.size>F){O("error","حجم الصورة كبير جداً. الحد الأقصى 5MB");return}k(C);try{const J=await ae(C,400,.9);m(J),O("info","تم تحسين الصورة وضغطها للحصول على أفضل جودة")}catch(J){console.error("Error compressing preview:",J);const te=new FileReader;te.onload=Ne=>{var ne;m((ne=Ne.target)==null?void 0:ne.result)},te.readAsDataURL(C),O("info","تم تحميل الصورة بنجاح")}}},U=f=>new Promise((C,_)=>{const T=new FileReader;T.readAsDataURL(f),T.onload=()=>C(T.result),T.onerror=F=>_(F)}),Y=(f,C=500)=>new Promise((_,T)=>{try{if(console.log("📊 Original file size:",Math.round(f.size/1024),"KB"),f.size<=C*1024){console.log("✅ File size acceptable, using original"),U(f).then(_).catch(T);return}const F=new FileReader;F.onload=()=>{try{const J=F.result;console.log("✅ File converted to base64, size:",Math.round(J.length*.75/1024),"KB (estimated)"),_(J)}catch(J){console.error("❌ Error processing file:",J),T(J)}},F.onerror=J=>{console.error("❌ FileReader error:",J),T(J)},F.readAsDataURL(f)}catch(F){console.error("❌ Error in compressImageSafe:",F),T(F)}}),ae=(f,C=800,_=.8)=>new Promise((T,F)=>{try{console.log("🔄 Attempting safe compression..."),Y(f,500).then(T).catch(J=>{console.warn("⚠️ Safe compression failed, trying Canvas method:",J);try{const te=(()=>{try{return document.createElement("canvas")}catch(ge){return console.warn("❌ Canvas creation failed:",ge),null}})();if(!te){console.warn("❌ Canvas not available, using fallback"),U(f).then(T).catch(F);return}const Ne=te.getContext("2d");if(!Ne){console.warn("❌ Canvas context not available, using fallback"),U(f).then(T).catch(F);return}const ne=new window.Image;ne.onload=()=>{try{let{width:ge,height:ce}=ne;ge>C&&(ce=ce*C/ge,ge=C),te.width=ge,te.height=ce,Ne.imageSmoothingEnabled=!0,Ne.imageSmoothingQuality="high",Ne.drawImage(ne,0,0,ge,ce);const Lt=te.toDataURL("image/jpeg",_);console.log("✅ Canvas compression successful"),T(Lt)}catch(ge){console.error("❌ Canvas processing error:",ge),U(f).then(T).catch(F)}},ne.onerror=()=>{console.error("❌ Image load error, using fallback"),U(f).then(T).catch(F)},ne.src=URL.createObjectURL(f)}catch(te){console.error("❌ Canvas method failed:",te),U(f).then(T).catch(F)}})}catch(J){console.error("❌ Error in compressImage:",J),U(f).then(T).catch(F)}}),Te=async f=>{if(f.preventDefault(),!(!g||!b))try{const C=g.userId||g.id;if(!C){O("error","خطأ: معرف المستخدم غير صحيح");return}console.log("🔄 Managing image for user:",C,"action:",b);let _=null,T=null;if(b.startsWith("change_")&&j)try{O("info","جاري ضغط الصورة..."),_=await ae(j,800,.85),T="image/jpeg",console.log("✅ Image compressed successfully for upload"),O("info","جاري رفع الصورة المحسنة...")}catch(F){console.error("❌ Error compressing for upload:",F),O("info","فشل ضغط الصورة، جاري استخدام الصورة الأصلية...");try{_=await U(j),T=j.type,console.log("✅ Using original file as fallback")}catch(J){throw console.error("❌ Error with fallback method:",J),new Error("فشل في معالجة الصورة: "+J.message)}}console.log("📤 Sending image management request:",{userId:C,action:b,hasImageData:!!_,imageType:T}),await K.manageUserImage(C,b,_||void 0,T||void 0),O("success","تم تحديث الصورة بنجاح"),console.log("🔄 Reloading users data after image update..."),await z(r,l),Le()}catch(C){console.error("❌ Image management error:",C),O("error","خطأ في إدارة الصورة: "+C.message)}},Le=()=>{p(!1),v(null),N(""),k(null),m(null)},Ue=f=>f.profileImage||f.avatar?"موجودة":"غير موجودة",we=f=>{var _;const C=f.profileImage||f.avatar;return console.log(`🖼️ Getting image for user ${f.username}:`,{hasProfileImage:!!f.profileImage,hasAvatar:!!f.avatar,profileImageLength:((_=f.profileImage)==null?void 0:_.length)||0,finalImageUrl:C?"HAS_IMAGE":"NO_IMAGE"}),C||"/images/default-avatar.png"};return n.jsxs("div",{className:"space-y-8",children:[n.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[n.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center",children:n.jsx(In,{className:"w-5 h-5 text-white"})}),n.jsx("h2",{className:"text-2xl font-bold text-white",children:"إدارة صور المستخدمين"})]}),n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[n.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx(br,{className:"w-8 h-8 text-blue-400"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-2xl font-bold text-white",children:e.length}),n.jsx("p",{className:"text-sm text-gray-400",children:"إجمالي المستخدمين"})]})]})}),n.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx(In,{className:"w-8 h-8 text-green-400"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(f=>f.profileImage||f.avatar).length}),n.jsx("p",{className:"text-sm text-gray-400",children:"لديهم صور"})]})]})}),n.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx(Dn,{className:"w-8 h-8 text-purple-400"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(f=>!f.profileImage&&!f.avatar).length}),n.jsx("p",{className:"text-sm text-gray-400",children:"بدون صور"})]})]})})]}),E&&n.jsx("div",{className:`p-4 rounded-2xl border ${E.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":E.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:n.jsxs("div",{className:"flex items-center gap-2",children:[E.type==="success"&&n.jsx(ii,{className:"w-5 h-5"}),E.type==="error"&&n.jsx(Bs,{className:"w-5 h-5"}),E.type==="info"&&n.jsx(mi,{className:"w-5 h-5"}),n.jsx("span",{children:E.text})]})}),n.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-3xl border border-white/10 shadow-2xl overflow-hidden",children:[n.jsx("div",{className:"p-6 border-b border-white/10",children:n.jsxs("div",{className:"flex items-center gap-4",children:[n.jsxs("div",{className:"relative flex-1",children:[n.jsx(ui,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),n.jsx("input",{id:"searchInput",type:"text",placeholder:"البحث عن مستخدم...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",onKeyPress:f=>f.key==="Enter"&&$()})]}),n.jsxs("button",{onClick:$,className:"bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-6 py-3 text-purple-300 transition-colors flex items-center gap-2",children:[n.jsx(ui,{className:"w-4 h-4"}),"بحث"]}),n.jsxs("button",{onClick:()=>z(),className:"bg-green-500/20 hover:bg-green-500/30 border border-green-500/50 rounded-xl px-6 py-3 text-green-300 transition-colors flex items-center gap-2",children:[n.jsx(fr,{className:"w-4 h-4"}),"تحديث"]})]})}),n.jsxs("div",{className:"p-6",children:[c?n.jsxs("div",{className:"text-center py-12",children:[n.jsx(fr,{className:"w-8 h-8 text-purple-400 animate-spin mx-auto mb-4"}),n.jsx("p",{className:"text-gray-300",children:"جاري التحميل..."})]}):e.length===0?n.jsxs("div",{className:"text-center py-12",children:[n.jsx(br,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),n.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد مستخدمين"}),n.jsx("p",{className:"text-gray-300",children:"لم يتم العثور على أي مستخدمين"})]}):n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(f=>n.jsxs("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-200",children:[n.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[n.jsx("img",{src:we(f),alt:"صورة المستخدم",className:"w-16 h-16 rounded-full object-cover border-2 border-purple-500"}),n.jsxs("div",{className:"flex-1",children:[n.jsx("h3",{className:"font-semibold text-white",children:f.displayName||f.username}),n.jsxs("p",{className:"text-sm text-gray-400",children:["المعرف: ",f.playerId||"غير محدد"]})]})]}),n.jsx("div",{className:"space-y-3 mb-4",children:n.jsxs("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("img",{src:we(f),alt:"الصورة الشخصية",className:"w-8 h-8 rounded-lg object-cover"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-sm font-medium text-white",children:"الصورة الشخصية"}),n.jsx("p",{className:"text-xs text-gray-400",children:Ue(f)})]})]}),n.jsxs("div",{className:"flex gap-2",children:[n.jsx("button",{onClick:()=>G(f,"remove_avatar"),className:"p-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg text-red-300 transition-colors",title:"حذف",children:n.jsx(x0,{className:"w-4 h-4"})}),n.jsx("button",{onClick:()=>G(f,"change_avatar"),className:"p-2 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/50 rounded-lg text-yellow-300 transition-colors",title:"تغيير",children:n.jsx(h0,{className:"w-4 h-4"})})]})]})})]},f.userId))}),i&&i.totalPages>1&&n.jsxs("div",{className:"flex justify-center items-center gap-2 mt-8",children:[i.hasPrevPage&&n.jsx("button",{onClick:()=>z(r-1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"السابق"}),Array.from({length:i.totalPages},(f,C)=>C+1).map(f=>n.jsx("button",{onClick:()=>z(f,l),className:`px-4 py-2 rounded-lg transition-colors ${f===r?"bg-purple-500 text-white":"bg-white/10 hover:bg-white/20 border border-white/20 text-white"}`,children:f},f)),i.hasNextPage&&n.jsx("button",{onClick:()=>z(r+1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"التالي"})]})]})]}),u&&n.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[n.jsxs("div",{className:"flex items-center justify-between mb-6",children:[n.jsxs("h3",{className:"text-xl font-bold text-white",children:["إدارة صور المستخدم ",g==null?void 0:g.userId]}),n.jsx("button",{onClick:Le,className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:n.jsx(da,{className:"w-5 h-5 text-gray-400"})})]}),n.jsxs("form",{onSubmit:Te,className:"space-y-6",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"الإجراء"}),n.jsxs("select",{value:b,onChange:f=>N(f.target.value),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0,children:[n.jsx("option",{value:"",children:"اختر الإجراء"}),n.jsx("option",{value:"remove_avatar",children:"حذف الصورة الشخصية"}),n.jsx("option",{value:"change_avatar",children:"تغيير الصورة الشخصية"})]})]}),b.startsWith("change_")&&n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اختر الصورة"}),n.jsxs("div",{className:"relative",children:[n.jsx("input",{type:"file",accept:"image/*",onChange:x,className:"hidden",id:"imageFile"}),n.jsxs("label",{htmlFor:"imageFile",className:"w-full bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-4 py-3 text-purple-300 cursor-pointer transition-colors flex items-center justify-center gap-2",children:[n.jsx(Qf,{className:"w-5 h-5"}),"اختيار ملف"]})]}),S&&n.jsx("div",{className:"mt-4",children:n.jsx("img",{src:S,alt:"معاينة الصورة",className:"w-full max-h-40 object-cover rounded-xl"})})]}),n.jsxs("div",{className:"flex gap-4",children:[n.jsx("button",{type:"submit",className:"flex-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),n.jsx("button",{type:"button",onClick:Le,className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})]})})]})},fi=({userData:e,onLogout:t})=>{const[r,s]=y.useState("users"),[l,o]=y.useState([]),[i,a]=y.useState(null),[c,d]=y.useState(""),[g,v]=y.useState(!1),[b,N]=y.useState(null),[j,k]=y.useState({numBoxes:10,winRatio:.3}),[S,m]=y.useState([]);y.useEffect(()=>{e!=null&&e.isAdmin&&(p(),E(),L())},[e]);const u=(x,U)=>{N({type:x,text:U}),setTimeout(()=>N(null),5e3)},p=async()=>{v(!0);try{const x=await K.getAllUsersAdmin();console.log("📥 Loaded users:",x.users);const U=(x.users||[]).map(Y=>({...Y,id:Y.id||Y.userId||Y._id,userId:Y.userId||Y.id||Y._id}));console.log("✅ Processed users:",U),o(U)}catch(x){console.error("❌ Error loading users:",x),u("error","خطأ في تحميل المستخدمين")}finally{v(!1)}},E=async()=>{try{const x=await K.getGameSettings();k(x)}catch(x){console.error("خطأ في تحميل إعدادات اللعبة:",x)}},L=async()=>{try{const x=await K.getSuspiciousActivities();m(x||[])}catch(x){console.error("خطأ في تحميل النشاطات المشبوهة:",x),m([])}},O=async x=>{if(!x.trim()){p();return}v(!0);try{const U=await K.searchUsersAdmin(x);o(U.users||[])}catch{u("error","خطأ في البحث")}finally{v(!1)}},z=async(x,U)=>{try{if(!x||x==="undefined"||x===""){console.error("❌ Invalid userId:",x),console.error("❌ Updates object:",U),u("error","خطأ: معرف المستخدم غير صحيح. يرجى إعادة تحميل الصفحة.");return}if(console.log("🔄 Updating user:",x,"with updates:",U),U.playerId){if(!/^\d{6}$/.test(U.playerId)){u("error","معرف اللاعب يجب أن يكون 6 أرقام فقط");return}await K.updatePlayerId(x,U.playerId),u("success","تم تحديث معرف اللاعب بنجاح")}const{playerId:Y,...ae}=U;Object.keys(ae).length>0&&(await K.updateUserAdmin(x,ae),u("success","تم تحديث المستخدم بنجاح")),p(),a(null)}catch{u("error","خطأ في تحديث المستخدم")}},$=async()=>{try{await K.updateGameSettings(j),u("success","تم حفظ إعدادات اللعبة بنجاح")}catch{u("error","خطأ في حفظ الإعدادات")}},G=l.filter(x=>x.username.toLowerCase().includes(c.toLowerCase())||x.email.toLowerCase().includes(c.toLowerCase()));return e!=null&&e.isAdmin?n.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white",children:[n.jsx("div",{className:"absolute inset-0 overflow-hidden",children:n.jsxs("div",{className:"absolute -inset-10 opacity-20",children:[n.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),n.jsx("div",{className:"absolute top-3/4 right-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),n.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"})]})}),n.jsxs("div",{className:"relative z-10 px-4 py-4",children:[n.jsxs("div",{className:"mb-6",children:[n.jsxs("div",{className:"flex items-center justify-between mb-4",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsxs("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center relative",children:[n.jsx(Mn,{className:"w-6 h-6 text-white"}),n.jsx(Al,{className:"w-3 h-3 text-white absolute -top-1 -right-1"})]}),n.jsxs("div",{children:[n.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"لوحة تحكم المشرف"}),n.jsx("p",{className:"text-gray-300 text-sm",children:"INFINITY BOX"})]})]}),n.jsx("button",{onClick:t,className:"bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 hover:text-red-200 transition-all duration-200 text-sm",children:"خروج"})]}),n.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl px-3 py-2 border border-white/20 flex items-center gap-2",children:[n.jsx(Mn,{className:"w-4 h-4 text-yellow-400"}),n.jsxs("span",{className:"text-sm text-gray-300",children:["مرحباً، ",e.username]})]})]}),b&&n.jsx("div",{className:`mb-6 p-4 rounded-2xl border ${b.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":b.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:n.jsxs("div",{className:"flex items-center gap-2",children:[b.type==="success"&&n.jsx(ii,{className:"w-5 h-5"}),b.type==="error"&&n.jsx(Bs,{className:"w-5 h-5"}),b.type==="info"&&n.jsx(mi,{className:"w-5 h-5"}),n.jsx("span",{children:b.text})]})}),n.jsx("div",{className:"grid grid-cols-2 gap-2 mb-6",children:[{id:"users",label:"إدارة المستخدمين",shortLabel:"المستخدمين",icon:br,color:"from-blue-500 to-cyan-500"},{id:"game",label:"إعدادات اللعبة",shortLabel:"الألعاب",icon:zc,color:"from-green-500 to-emerald-500"},{id:"suspicious",label:"النشاطات المشبوهة",shortLabel:"المشبوهة",icon:li,color:"from-red-500 to-pink-500"},{id:"images",label:"إدارة الصور",shortLabel:"الصور",icon:In,color:"from-purple-500 to-indigo-500"}].map(x=>n.jsxs("button",{onClick:()=>s(x.id),className:`flex flex-col items-center gap-2 px-3 py-3 rounded-xl transition-all duration-200 ${r===x.id?`bg-gradient-to-r ${x.color} text-white shadow-lg`:"bg-white/10 hover:bg-white/20 text-gray-300"}`,children:[n.jsx(x.icon,{className:"w-6 h-6"}),n.jsx("span",{className:"text-sm font-medium",children:x.shortLabel})]},x.id))}),n.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-2xl border border-white/10 shadow-2xl overflow-hidden",children:[r==="users"&&n.jsxs("div",{className:"p-4",children:[n.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[n.jsx("div",{className:"flex items-center justify-between",children:n.jsxs("h2",{className:"text-lg font-bold text-white flex items-center gap-2",children:[n.jsx(br,{className:"w-5 h-5 text-blue-400"}),"المستخدمين (",G.length,")"]})}),n.jsxs("div",{className:"flex gap-2",children:[n.jsxs("button",{onClick:()=>{console.log("🧹 Clearing local data..."),K.clearLocalData(),u("info","تم تنظيف البيانات المحلية. سيتم إعادة تحميل الصفحة..."),setTimeout(()=>window.location.reload(),1e3)},className:"flex-1 flex items-center justify-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 transition-colors text-sm",children:[n.jsx(Bs,{className:"w-4 h-4"}),"تنظيف البيانات"]}),n.jsxs("button",{onClick:p,className:"flex-1 flex items-center justify-center gap-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-3 py-2 text-blue-300 transition-colors text-sm",children:[n.jsx(fr,{className:"w-4 h-4"}),"تحديث"]})]})]}),n.jsxs("div",{className:"relative mb-4",children:[n.jsx(ui,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),n.jsx("input",{type:"text",value:c,onChange:x=>{d(x.target.value),O(x.target.value)},placeholder:"البحث عن المستخدمين...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",autoComplete:"off"})]}),g?n.jsxs("div",{className:"text-center py-8",children:[n.jsx(fr,{className:"w-6 h-6 text-blue-400 animate-spin mx-auto mb-3"}),n.jsx("p",{className:"text-gray-300 text-sm",children:"جاري التحميل..."})]}):n.jsx("div",{className:"space-y-3",children:G.map(x=>n.jsxs("div",{className:"bg-white/10 rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-200",children:[n.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[n.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-sm font-bold",children:x.username.charAt(0).toUpperCase()}),n.jsxs("div",{className:"flex-1 min-w-0",children:[n.jsxs("h3",{className:"font-semibold text-white flex items-center gap-2 text-sm",children:[n.jsx("span",{className:"truncate",children:x.username}),x.isAdmin&&n.jsx(Mn,{className:"w-3 h-3 text-yellow-400 flex-shrink-0"})]}),n.jsx("p",{className:"text-xs text-gray-400 truncate",children:x.email}),n.jsxs("p",{className:"text-xs text-blue-400",children:["المعرف: ",x.playerId]})]})]}),n.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[n.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[n.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[n.jsx(Il,{className:"w-3 h-3 text-yellow-400"}),n.jsx("span",{className:"text-yellow-400 font-semibold text-sm",children:x.goldCoins||x.coins||0})]}),n.jsx("p",{className:"text-xs text-gray-400",children:"العملات"})]}),n.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[n.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[n.jsx(cr,{className:"w-3 h-3 text-purple-400"}),n.jsx("span",{className:"text-purple-400 font-semibold text-sm",children:x.pearls||0})]}),n.jsx("p",{className:"text-xs text-gray-400",children:"اللآلئ"})]})]}),n.jsxs("div",{className:"flex gap-2",children:[n.jsxs("button",{onClick:()=>a(x),className:"flex-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-2 py-2 text-blue-300 text-xs transition-colors flex items-center justify-center gap-1",children:[n.jsx(h0,{className:"w-3 h-3"}),"تعديل"]}),n.jsxs("button",{onClick:()=>z(x.id||x._id,{isAdmin:!x.isAdmin}),className:`flex-1 border rounded-lg px-2 py-2 text-xs transition-colors flex items-center justify-center gap-1 ${x.isAdmin?"bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300":"bg-green-500/20 hover:bg-green-500/30 border-green-500/50 text-green-300"}`,children:[x.isAdmin?n.jsx(hi,{className:"w-3 h-3"}):n.jsx(Gf,{className:"w-3 h-3"}),x.isAdmin?"إلغاء":"ترقية"]})]})]},x.id))})]}),r==="game"&&n.jsxs("div",{className:"p-4",children:[n.jsxs("h2",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[n.jsx(zc,{className:"w-5 h-5 text-green-400"}),"إعدادات اللعبة"]}),n.jsx("div",{className:"space-y-4",children:n.jsxs("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:[n.jsx("h3",{className:"text-base font-semibold text-white mb-4",children:"إعدادات صناديق الحظ"}),n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"عدد الصناديق في الجولة"}),n.jsx("input",{type:"number",min:"5",max:"100",value:j.numBoxes,onChange:x=>k(U=>({...U,numBoxes:parseInt(x.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"نسبة الفوز (من 0.1 إلى 0.9)"}),n.jsx("input",{type:"number",min:"0.1",max:"0.9",step:"0.1",value:j.winRatio,onChange:x=>k(U=>({...U,winRatio:parseFloat(x.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),n.jsxs("button",{onClick:$,className:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm",children:[n.jsx(Hf,{className:"w-4 h-4"}),"حفظ الإعدادات"]})]})]})})]}),r==="suspicious"&&n.jsxs("div",{className:"p-8",children:[n.jsxs("div",{className:"flex items-center justify-between mb-6",children:[n.jsxs("h2",{className:"text-2xl font-bold text-white flex items-center gap-2",children:[n.jsx(li,{className:"w-6 h-6 text-red-400"}),"النشاطات المشبوهة"]}),n.jsxs("button",{onClick:L,className:"flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-xl px-4 py-2 text-red-300 transition-colors",children:[n.jsx(fr,{className:"w-4 h-4"}),"تحديث"]})]}),n.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[n.jsx("div",{className:"bg-red-500/20 rounded-2xl p-6 border border-red-500/50",children:n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx(mi,{className:"w-8 h-8 text-red-400"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-2xl font-bold text-red-300",children:S.length}),n.jsx("p",{className:"text-sm text-red-400",children:"مستخدمين مشبوهين"})]})]})}),n.jsx("div",{className:"bg-orange-500/20 rounded-2xl p-6 border border-orange-500/50",children:n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx(Of,{className:"w-8 h-8 text-orange-400"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-2xl font-bold text-orange-300",children:S.filter(x=>x.riskLevel==="high").length}),n.jsx("p",{className:"text-sm text-orange-400",children:"مخاطر عالية"})]})]})}),n.jsx("div",{className:"bg-yellow-500/20 rounded-2xl p-6 border border-yellow-500/50",children:n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx(Dn,{className:"w-8 h-8 text-yellow-400"}),n.jsxs("div",{children:[n.jsx("p",{className:"text-2xl font-bold text-yellow-300",children:S.filter(x=>x.riskLevel==="medium").length}),n.jsx("p",{className:"text-sm text-yellow-400",children:"تحت المراقبة"})]})]})})]}),S.length===0?n.jsxs("div",{className:"text-center py-12",children:[n.jsx(ii,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),n.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد نشاطات مشبوهة"}),n.jsx("p",{className:"text-gray-300",children:"جميع المستخدمين يتصرفون بشكل طبيعي"})]}):n.jsx("div",{className:"space-y-4",children:S.map((x,U)=>{var Y,ae;return n.jsx("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{className:"flex items-center gap-4",children:[n.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-lg font-bold",children:((ae=(Y=x.username)==null?void 0:Y.charAt(0))==null?void 0:ae.toUpperCase())||"?"}),n.jsxs("div",{children:[n.jsx("h3",{className:"font-semibold text-white",children:x.username}),n.jsxs("p",{className:"text-sm text-gray-400",children:["آخر نشاط: ",x.lastActivity]})]})]}),n.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${x.riskLevel==="high"?"bg-red-500/20 text-red-300":x.riskLevel==="medium"?"bg-yellow-500/20 text-yellow-300":"bg-green-500/20 text-green-300"}`,children:x.riskLevel==="high"?"خطر عالي":x.riskLevel==="medium"?"خطر متوسط":"خطر منخفض"})]})},U)})})]}),r==="images"&&n.jsxs("div",{className:"p-8",children:[n.jsxs("div",{className:"mb-6",children:[n.jsxs("h2",{className:"text-2xl font-bold text-white mb-4 flex items-center gap-2",children:[n.jsx(In,{className:"w-6 h-6 text-purple-400"}),"إدارة الصور"]}),n.jsxs("div",{className:"bg-purple-500/10 border border-purple-500/30 rounded-xl p-4 mb-6",children:[n.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[n.jsx(In,{className:"w-5 h-5 text-purple-400"}),n.jsx("h3",{className:"font-semibold text-purple-300",children:"إدارة صور المستخدمين"})]}),n.jsx("p",{className:"text-gray-300 text-sm",children:"عرض وإدارة جميع صور المستخدمين، حذف الصور غير المناسبة، والبحث في المستخدمين."})]})]}),n.jsx(_p,{})]})]})]}),i&&n.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[n.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"تعديل المستخدم"}),n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"معرف اللاعب"}),n.jsx("input",{type:"text",value:i.playerId,onChange:x=>a(U=>U?{...U,playerId:x.target.value}:null),maxLength:6,pattern:"\\d{6}",className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"6 أرقام فقط"})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اسم المستخدم"}),n.jsx("input",{type:"text",value:i.username,onChange:x=>a(U=>U?{...U,username:x.target.value}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"username"})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"العملات الذهبية"}),n.jsxs("div",{className:"space-y-3",children:[n.jsx("input",{type:"number",value:i.goldCoins||i.coins||0,onChange:x=>a(U=>U?{...U,goldCoins:parseInt(x.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),n.jsxs("div",{className:"flex gap-2 flex-wrap",children:[n.jsx("button",{type:"button",onClick:()=>a(x=>x?{...x,goldCoins:(x.goldCoins||x.coins||0)+1e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+1,000"}),n.jsx("button",{type:"button",onClick:()=>a(x=>x?{...x,goldCoins:(x.goldCoins||x.coins||0)+5e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+5,000"}),n.jsx("button",{type:"button",onClick:()=>a(x=>x?{...x,goldCoins:(x.goldCoins||x.coins||0)+1e4}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+10,000"})]})]})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اللآلئ"}),n.jsxs("div",{className:"space-y-3",children:[n.jsx("input",{type:"number",value:i.pearls||0,onChange:x=>a(U=>U?{...U,pearls:parseInt(x.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),n.jsxs("div",{className:"flex gap-2 flex-wrap",children:[n.jsx("button",{type:"button",onClick:()=>a(x=>x?{...x,pearls:(x.pearls||0)+1}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+1"}),n.jsx("button",{type:"button",onClick:()=>a(x=>x?{...x,pearls:(x.pearls||0)+5}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+5"}),n.jsx("button",{type:"button",onClick:()=>a(x=>x?{...x,pearls:(x.pearls||0)+10}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+10"})]})]})]}),n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("input",{type:"checkbox",id:"isAdmin",checked:i.isAdmin,onChange:x=>a(U=>U?{...U,isAdmin:x.target.checked}:null),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),n.jsx("label",{htmlFor:"isAdmin",className:"text-sm font-medium text-gray-300",children:"صلاحيات المشرف"})]})]}),n.jsxs("div",{className:"flex gap-4 mt-8",children:[n.jsx("button",{onClick:()=>{const x=i.id||i.userId;if(console.log("🔍 Selected user data:",i),console.log("🔍 Extracted userId:",x),!x){u("error","خطأ: لا يمكن العثور على معرف المستخدم. يرجى إعادة تحميل الصفحة.");return}z(x,i)},className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),n.jsx("button",{onClick:()=>a(null),className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})})]}):n.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:n.jsxs("div",{className:"bg-red-500/20 border border-red-500/50 rounded-3xl p-8 text-center max-w-md",children:[n.jsx(Bs,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),n.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"غير مصرح"}),n.jsx("p",{className:"text-red-300 mb-6",children:"ليس لديك صلاحيات للوصول إلى لوحة تحكم المشرف"}),n.jsx("button",{onClick:t,className:"bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-xl transition-colors",children:"العودة لتسجيل الدخول"})]})})};class M0{constructor(t){this.url=t,this.ws=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.messageHandlers=new Map}connect(t){return new Promise((r,s)=>{try{this.ws=new WebSocket(`${this.url}?token=${t}`),this.ws.onopen=()=>{console.log("🔌 WebSocket connected"),this.reconnectAttempts=0,r()},this.ws.onmessage=l=>{try{const o=JSON.parse(l.data);this.handleMessage(o)}catch(o){console.error("Error parsing WebSocket message:",o)}},this.ws.onclose=()=>{console.log("🛑 WebSocket disconnected"),this.handleReconnect()},this.ws.onerror=l=>{console.error("WebSocket error:",l),s(l)}}catch(l){s(l)}})}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.connect(this.getTokenFromUrl()).catch(t=>{console.error("Reconnection failed:",t)})},this.reconnectDelay*this.reconnectAttempts)):console.error("❌ Max reconnection attempts reached")}getTokenFromUrl(){return new URLSearchParams(window.location.search).get("token")||""}handleMessage(t){const r=this.messageHandlers.get(t.type);r&&r.forEach(s=>s(t.data))}sendMessage(t,r){if(this.ws&&this.ws.readyState===WebSocket.OPEN){const s={type:t,data:r,timestamp:Date.now()};this.ws.send(JSON.stringify(s))}else console.warn("WebSocket is not connected")}sendPrivateMessage(t,r){this.sendMessage("private_message",{messageData:t,recipientId:r})}onMessage(t,r){this.messageHandlers.has(t)||this.messageHandlers.set(t,[]),this.messageHandlers.get(t).push(r)}offMessage(t,r){const s=this.messageHandlers.get(t);if(s){const l=s.indexOf(r);l>-1&&s.splice(l,1)}}addMessageListener(t){["voice_room_message","voice_room_update","admin_action_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.onMessage(s,t)})}removeMessageListener(t){["voice_room_message","voice_room_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.offMessage(s,t)})}send(t){this.sendMessage(t.type,t.data)}disconnect(){this.ws&&(this.ws.close(),this.ws=null)}}const Tp=`
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
`;if(typeof document<"u"){const e=document.createElement("style");e.textContent=Tp,document.head.appendChild(e)}const Lp=({userData:e,isOwner:t,onUpdateProfile:r,onLogout:s})=>{var wa,ba,ja,Na,ka;const[l,o]=y.useState("overview"),[i,a]=y.useState(!1),[c,d]=y.useState(""),[g,v]=y.useState((e==null?void 0:e.gender)||"male"),[b,N]=y.useState(!1),[j,k]=y.useState({gems:0,stars:0,coins:0,bombs:0,bats:0,snakes:0}),[S,m]=y.useState(!1),[u,p]=y.useState(!1),[E,L]=y.useState(""),[O,z]=y.useState(!1),[$,G]=y.useState(null),[x,U]=y.useState(""),[Y,ae]=y.useState(null),[Te,Le]=y.useState(!1),[Ue,we]=y.useState(""),[f,C]=y.useState(null),[_,T]=y.useState(100),[F,J]=y.useState("gold"),[te,Ne]=y.useState(""),[ne,ge]=y.useState(!1),[ce,Lt]=y.useState([]),[tr,lt]=y.useState([]),[ns,Tl]=y.useState([]),[rr,ss]=y.useState(!1),[ht,ls]=y.useState(1e4),[os,is]=y.useState(!1),[ft,Ll]=y.useState(250),[kr,Ke]=y.useState({"1_dollar":!0,"5_dollar":!0}),[de,pt]=y.useState(""),[Ye,nn]=y.useState(""),[as,sn]=y.useState(!1),[gt,Sr]=y.useState([]),[w,P]=y.useState(!1),[W,H]=y.useState(!1),[R,se]=y.useState(null),[Ee,Pt]=y.useState([]),[$e,Cr]=y.useState("");y.useState(!1);const[A0,Wp]=y.useState(!1),[Pl,Rl]=y.useState(!1);(h=>h==="female"?{primary:"from-pink-500 to-red-400",secondary:"bg-pink-50",accent:"text-pink-600",button:"bg-pink-500 hover:bg-pink-600",border:"border-pink-200"}:{primary:"from-blue-500 to-yellow-400",secondary:"bg-blue-50",accent:"text-blue-600",button:"bg-blue-500 hover:bg-blue-600",border:"border-blue-200"})((e==null?void 0:e.gender)||"male"),y.useEffect(()=>{let h=!0;const I=setTimeout(async()=>{if(t&&(e!=null&&e.id)&&h)try{const q=localStorage.getItem("token");if(!q)return;const D=await fetch("/api/profile/me",{method:"GET",headers:{Authorization:`Bearer ${q}`,"Content-Type":"application/json"}});if(D.ok&&h){const Q=D.headers.get("content-type");if(Q&&Q.includes("application/json")){const nr=await D.json();r&&h&&r(nr)}}}catch(q){console.error("❌ Error fetching complete user data:",q)}},100);return()=>{h=!1,clearTimeout(I)}},[t,e==null?void 0:e.id]),y.useEffect(()=>{e!=null&&e.id&&(T0(),Q0(),t&&ha())},[e==null?void 0:e.id,t]),y.useEffect(()=>{if(t&&(e!=null&&e.id))return _0()},[t,e==null?void 0:e.id]);const ln=new M0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`);y.useEffect(()=>{let h=!0;if(t&&(e!=null&&e.id)&&h){fa(),cs(),P0(),ds(),U0();const M=setInterval(()=>{h&&(cs(),ds())},3e5);return()=>{h=!1,clearInterval(M)}}},[t,e==null?void 0:e.id]);const _0=()=>{console.log("🔧 Setting up message listener...");const h=M=>{if(console.log("📨 WebSocket message received:",M),M.messageData){const I=M.messageData.sender._id,q=M.messageData.recipient._id,D=e==null?void 0:e.id;if(console.log("📋 Message details:",{senderId:I,recipientId:q,currentUserId:D,showChat:W,chatUserId:(R==null?void 0:R.id)||(R==null?void 0:R._id)}),q===D)if(console.log("✅ Message is for me, processing..."),W&&R&&(I===R.id||I===R._id)){console.log("💬 Adding message to open chat"),Pt(Q=>Q.some(K0=>K0._id===M.messageData._id)?(console.log("⚠️ Message already exists, skipping"),Q):(console.log("✅ Adding new message to chat"),[...Q,M.messageData])),setTimeout(zl,100);try{const Q=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");Q.volume=.5,Q.play().catch(()=>{})}catch{}}else{console.log("🔔 Chat not open, refreshing notifications"),ds();try{const Q=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");Q.volume=.3,Q.play().catch(()=>{})}catch{}}else console.log("ℹ️ Message not for me, ignoring")}else console.log("⚠️ No messageData in WebSocket message")};return ln.offMessage("new_message",h),ln.onMessage("new_message",h),console.log("✅ Message listener added"),()=>{console.log("🧹 Cleaning up message listener"),ln.offMessage("new_message",h)}},T0=async()=>{try{const h=localStorage.getItem("token");if(!h){console.warn("No token found for fetching user items");return}const M=await fetch(`/api/user-items/${e.id}`,{headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}});if(M.ok){const I=await M.json();k(I.items),console.log("✅ User items fetched successfully:",I.items)}else console.error("❌ Failed to fetch user items:",M.status,M.statusText)}catch(h){console.error("Error fetching user items:",h)}},ha=async()=>{try{const h=localStorage.getItem("token");if(!h){console.warn("No token found for fetching user shield");return}const M=await fetch(`/api/profile/shield/${e.id}`,{headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}});if(M.ok){const I=await M.json();G(I.shield),console.log("✅ User shield fetched successfully:",I.shield)}else console.error("❌ Failed to fetch user shield:",M.status,M.statusText)}catch(h){console.error("Error fetching user shield:",h)}},L0=async(h,M)=>{if(!O){z(!0);try{const I=localStorage.getItem("token"),q=await fetch("/api/profile/activate-shield",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify({shieldType:h})}),D=await q.json();q.ok?(G(D.shield),r&&D.newBalance!==void 0&&r({goldCoins:D.newBalance}),alert(D.message),await ha()):alert(D.message||"فشل في تفعيل الدرع الواقي")}catch(I){console.error("Error activating shield:",I),alert("حدث خطأ في تفعيل الدرع الواقي")}finally{z(!1)}}},fa=async()=>{try{const h=localStorage.getItem("token");if(!h)return;const M=await fetch("/api/profile/friends",{headers:{Authorization:`Bearer ${h}`}});if(M.ok){const I=await M.json();Lt(I),console.log("✅ Friends fetched:",I.length)}}catch(h){console.error("Error fetching friends:",h)}},cs=async()=>{try{const h=localStorage.getItem("token");if(!h)return;const M=await fetch("/api/profile/friend-requests",{headers:{Authorization:`Bearer ${h}`}});if(M.ok){const I=await M.json();lt(I),console.log("✅ Friend requests fetched:",I.length)}}catch(h){console.error("Error fetching friend requests:",h)}},P0=async()=>{try{const h=localStorage.getItem("token");if(!h)return;const M=await fetch("/api/profile/gifts",{headers:{Authorization:`Bearer ${h}`}});if(M.ok){const I=await M.json();Tl(I),console.log("✅ Gifts fetched:",I.length)}}catch(h){console.error("Error fetching gifts:",h)}},pa=async()=>{if(!x.trim()){we("يرجى إدخال رقم اللاعب");return}if(x.length!==6){we("رقم اللاعب يجب أن يكون 6 أرقام");return}Le(!0),we(""),ae(null);try{const h=localStorage.getItem("token"),M=await fetch(`/api/users/search-by-id/${x}`,{headers:{Authorization:`Bearer ${h}`}});if(M.ok){const I=await M.json();ae(I),console.log("✅ User found:",I.username)}else we("لم يتم العثور على لاعب بهذا الرقم")}catch(h){console.error("Error searching for friend:",h),we("حدث خطأ أثناء البحث")}finally{Le(!1)}},R0=async h=>{try{const M=localStorage.getItem("token"),I=await fetch("/api/profile/friend-request",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${M}`},body:JSON.stringify({friendId:h})});if(I.ok){const q=await I.json();alert(q.message),ae(null),U(""),await ds()}else{const q=await I.json();alert(q.message||"فشل في إرسال طلب الصداقة")}}catch(M){console.error("Error sending friend request:",M),alert("حدث خطأ في إرسال طلب الصداقة")}},z0=async()=>{if(f){ge(!0);try{const h=localStorage.getItem("token"),M=await fetch("/api/profile/send-gift",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${h}`},body:JSON.stringify({toUserId:f.id,giftType:F,amount:_,message:te})});if(M.ok){const I=await M.json();alert(I.message),C(null),Ne(""),T(100),r&&I.fromUserBalance&&r(I.fromUserBalance)}else{const I=await M.json();alert(I.message||"فشل في إرسال الهدية")}}catch(h){console.error("Error sending gift:",h),alert("حدث خطأ في إرسال الهدية")}finally{ge(!1)}}},O0=async h=>{try{const M=localStorage.getItem("token"),I=await fetch("/api/profile/accept-friend",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${M}`},body:JSON.stringify({friendshipId:h})});if(I.ok){const q=await I.json();alert(q.message),await cs(),await fa()}else{const q=await I.json();alert(q.message||"فشل في قبول طلب الصداقة")}}catch(M){console.error("Error accepting friend request:",M),alert("حدث خطأ في قبول طلب الصداقة")}},U0=async()=>{try{const h=localStorage.getItem("token"),M=await fetch("/api/profile/free-charges",{headers:{Authorization:`Bearer ${h}`}});if(M.ok){const I=await M.json();Ke(I.availableCharges)}}catch(h){console.error("Error fetching free charges:",h)}},ga=async(h,M=!1,I)=>{ss(!0);try{const q=localStorage.getItem("token"),D=await fetch("/api/profile/charge-balance",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${q}`},body:JSON.stringify({amount:h,isFree:M,chargeType:I})});if(D.ok){const Q=await D.json();alert(Q.message),r&&Q.newBalance!==void 0&&r({goldCoins:Q.newBalance}),M&&I&&Ke(nr=>({...nr,[I]:!1}))}else{const Q=await D.json();alert(Q.message||"فشل في شحن الرصيد")}}catch(q){console.error("Error charging balance:",q),alert("حدث خطأ في شحن الرصيد")}finally{ss(!1)}},$0=async()=>{if(ht<1e4){alert("الحد الأدنى للتحويل هو 10,000 عملة ذهبية");return}if(ht%1e4!==0){alert("يجب أن تكون الكمية مضاعفات 10,000");return}if(((e==null?void 0:e.goldCoins)||0)<ht){alert("رصيد العملات الذهبية غير كافي");return}is(!0);try{const h=localStorage.getItem("token"),M=await fetch("/api/profile/exchange-gold-to-pearls",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${h}`},body:JSON.stringify({goldAmount:ht})});if(M.ok){const I=await M.json();alert(I.message),r&&I.newBalance&&r(I.newBalance),ls(1e4)}else{const I=await M.json();alert(I.message||"فشل في تحويل العملات")}}catch(h){console.error("Error exchanging gold to pearls:",h),alert("حدث خطأ في تحويل العملات")}finally{is(!1)}},F0=async()=>{if(ft<250){alert("الحد الأدنى للسحب هو 250 لؤلؤة ($25)");return}if(((e==null?void 0:e.pearls)||0)<ft){alert("رصيد اللآلئ غير كافي");return}const I=`https://wa.me/1234567890?text=${`طلب سحب دولارات%0Aالمبلغ: $${ft/10}%0Aاللآلئ المطلوبة: ${ft}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}`}`;window.open(I,"_blank")},B0=async()=>{if(!de){alert("يرجى اختيار عنصر للإرسال");return}if(!Ye||Ye.length!==6){alert("يرجى إدخال رقم لاعب صحيح (6 أرقام)");return}sn(!0);try{const h=localStorage.getItem("token"),M=await fetch(`/api/users/search-by-id/${Ye}`,{headers:{Authorization:`Bearer ${h}`}});if(!M.ok){alert("لم يتم العثور على لاعب بهذا الرقم");return}const I=await M.json(),q=await fetch("/api/profile/send-item",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${h}`},body:JSON.stringify({toUserId:I.id,itemType:de,message:`عنصر ${xa(de)} من ${e==null?void 0:e.username}`})});if(q.ok){const D=await q.json();alert(D.message),pt(""),nn("")}else{const D=await q.json();alert(D.message||"فشل في إرسال العنصر")}}catch(h){console.error("Error sending item:",h),alert("حدث خطأ في إرسال العنصر")}finally{sn(!1)}},xa=h=>({bomb:"قنبلة مدمرة",bat:"خفاش مؤذي",snake:"ثعبان سام",gem:"جوهرة نادرة",star:"نجمة ذهبية",coin:"عملة خاصة",gold:"عملات ذهبية"})[h]||h,ya=(h,M)=>{const q=`https://wa.me/1234567890?text=${`طلب شحن رصيد%0Aالمبلغ المطلوب: ${h} عملة ذهبية%0Aالسعر: ${M}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}%0Aالرصيد الحالي: ${(e==null?void 0:e.goldCoins)||0} عملة`}`;window.open(q,"_blank")},ds=async()=>{try{const h=localStorage.getItem("token");if(!h)return;const M=await fetch("/api/notifications",{headers:{Authorization:`Bearer ${h}`}});if(M.ok){const I=await M.json();Sr(I),console.log("✅ Notifications fetched:",I.length);const q=I.filter(Q=>{var nr;return!Q.isRead&&Q.type==="item_received"&&((nr=Q.data)==null?void 0:nr.newBalance)});if(q.length>0&&r){const Q=q[0];r(Q.data.newBalance),console.log("💰 Balance updated from item notification:",Q.data.newBalance)}I.filter(Q=>Q.type==="friend_request"&&!Q.isRead).length>0&&(console.log("🤝 New friend request notifications found, refreshing friend requests"),await cs())}}catch(h){console.error("Error fetching notifications:",h)}},V0=async h=>{try{const M=localStorage.getItem("token");(await fetch(`/api/notifications/${h}/read`,{method:"PUT",headers:{Authorization:`Bearer ${M}`,"Content-Type":"application/json"}})).ok&&(Sr(q=>q.map(D=>D._id===h?{...D,isRead:!0}:D)),console.log("✅ Notification marked as read:",h))}catch(M){console.error("Error marking notification as read:",M)}},q0=async()=>{try{const h=localStorage.getItem("token");(await fetch("/api/notifications/mark-all-read",{method:"PUT",headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}})).ok&&(Sr(I=>I.map(q=>({...q,isRead:!0}))),console.log("✅ All notifications marked as read"))}catch(h){console.error("Error marking all notifications as read:",h)}},H0=async h=>{try{const M=localStorage.getItem("token");if(!M){console.log("❌ No token found");return}console.log("📥 Fetching messages for user:",h);const I=await fetch(`/api/messages/${h}`,{headers:{Authorization:`Bearer ${M}`}});if(I.ok){const q=await I.json();console.log("✅ Messages fetched:",q.length),Pt(q)}else{const q=await I.json();console.error("❌ Failed to fetch messages:",q)}}catch(M){console.error("Error fetching messages:",M)}},va=async()=>{if(!$e.trim()||!R){console.log("❌ Missing message or chat user:",{newMessage:$e,chatUser:R});return}const h=R.id||R._id;if(!h){console.error("❌ No recipient ID found:",R),alert("خطأ: لا يمكن تحديد المستقبل");return}console.log("📤 Sending message:",{recipientId:h,content:$e});try{const M=localStorage.getItem("token"),I=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${M}`},body:JSON.stringify({recipientId:h,content:$e})});if(I.ok){const q=await I.json();console.log("✅ Message sent successfully:",q),Pt([...Ee,q.messageData]),Cr(""),zl(),ln&&ln.sendPrivateMessage(q.messageData,h);try{const D=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");D.volume=.3,D.play().catch(()=>{})}catch{}}else{const q=await I.json();console.error("❌ Message send failed:",q),alert(q.message||"فشل في إرسال الرسالة")}}catch(M){console.error("Error sending message:",M),alert("حدث خطأ في إرسال الرسالة")}},zl=()=>{setTimeout(()=>{const h=document.getElementById("messages-container");h&&h.scrollTo({top:h.scrollHeight,behavior:"smooth"})},100)},W0=h=>{console.log("💬 Opening chat with user:",h);const M=h.id||h._id;console.log("📋 User ID for messages:",M),se(h),H(!0),Pt([]),M?H0(M).then(()=>{zl()}):console.error("❌ No user ID found for chat")},Q0=async()=>{if(!t&&(e!=null&&e.id))try{const h=localStorage.getItem("token");if(!h){console.warn("No token found for checking friendship");return}const M=await fetch(`/api/friends/check/${e.id}`,{headers:{Authorization:`Bearer ${h}`,"Content-Type":"application/json"}});if(M.ok){const I=await M.json();m(I.isFriend),console.log("✅ Friendship status checked:",I.isFriend)}else console.error("❌ Failed to check friendship:",M.status),m(!1)}catch(h){console.error("Error checking friendship:",h),m(!1)}},G0=async()=>{if(E.trim())try{const h=localStorage.getItem("token"),M=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${h}`},body:JSON.stringify({recipientId:e.id,content:E})});if(M.ok)L(""),p(!1),alert("تم إرسال الرسالة بنجاح!");else{const I=await M.json();alert(I.message||"فشل في إرسال الرسالة")}}catch(h){console.error("Error sending message:",h),alert("خطأ في إرسال الرسالة")}},D0=h=>{var I;const M=(I=h.target.files)==null?void 0:I[0];if(M){if(M.size>5*1024*1024){alert("حجم الصورة كبير جداً. يجب أن يكون أقل من 5 ميجابايت");return}const q=new FileReader;q.onload=D=>{var Q;d((Q=D.target)==null?void 0:Q.result)},q.readAsDataURL(M)}},J0=async()=>{var h,M;N(!0);try{const I=localStorage.getItem("token"),q={gender:g};c&&c!==(e==null?void 0:e.profileImage)&&(q.profileImage=c),console.log("🔄 Updating profile with data:",{hasProfileImage:!!q.profileImage,gender:q.gender,selectedImageLength:(c==null?void 0:c.length)||0,currentImageLength:((h=e==null?void 0:e.profileImage)==null?void 0:h.length)||0});const D=await fetch("/api/profile/update",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify(q)});if(D.ok){const Q=await D.json();console.log("✅ Profile updated successfully:",{hasProfileImage:!!Q.profileImage,profileImageLength:((M=Q.profileImage)==null?void 0:M.length)||0}),r==null||r(Q),d(""),a(!1),alert("تم تحديث الملف الشخصي بنجاح!")}else{const Q=await D.json();console.error("❌ Profile update failed:",Q),alert(Q.message||"فشل في تحديث الملف الشخصي")}}catch(I){console.error("Error updating profile:",I),alert("حدث خطأ في تحديث الملف الشخصي")}finally{N(!1)}},Z0=()=>c||(e!=null&&e.profileImage?e.profileImage:(e==null?void 0:e.gender)==="female"?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNGRjY5QjQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNFMxMy4xIDYgMTIgNiAxMCA1LjEgMTAgNFMxMC45IDIgMTIgMlpNMjEgOVYxMUgyMFYxMkMxOS4xIDEyIDE4LjQgMTIuNiAxOC4xIDEzLjNDMTcuMSAxMS45IDE1LjEgMTEgMTMuOCAxMC43QzE0IDEwLjUgMTQuMSAxMC4yIDE0LjEgMTBDMTQgOS4xIDEzLjYgOC40IDEzIDhDMTMuNCA3LjYgMTMuNyA3IDE0IDYuOUMxNS40IDcuNyAxNi4yIDkuMSAxNiAzMEMxOC40IDI5IDEwLjUgMzAgOFMxMS42IDI5IDEwIDI5LjdIMThDMTggMjguNSAxOC4zIDI3LjUgMTguOSAyNi43QzE5LjMgMjcuMSAxOS44IDI3LjMgMjAuNSAyNy4zSDE5QzE5IDI3IDEwLjMgMjcgMTAuNSAyNy4zSDE5LjQgMjEgOVoiLz4KPHN2Zz4KPHN2Zz4K":"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQTMgMyAwIDAgMSAxNSA1QTMgMyAwIDAgMSAxMiA4QTMgMyAwIDAgMSA5IDVBMyAzIDAgMCAxIDEyIDJNMjEgMjFWMjBDMjEgMTYuMTMgMTcuODcgMTMgMTQgMTNIMTBDNi4xMyAxMyAzIDE2LjEzIDMgMjBWMjFIMjFaIi8+Cjwvc3ZnPgo=");return n.jsxs("div",{className:"max-w-md mx-auto bg-gradient-to-br from-gray-900 via-gray-800 to-black min-h-screen shadow-2xl overflow-hidden flex flex-col",children:[n.jsxs("div",{className:"bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-4 text-white relative overflow-hidden",children:[n.jsxs("div",{className:"absolute inset-0 opacity-20",children:[n.jsx("div",{className:"absolute top-0 left-0 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"}),n.jsx("div",{className:"absolute top-10 right-0 w-16 h-16 bg-indigo-400/5 rounded-full blur-lg animate-bounce"}),n.jsx("div",{className:"absolute bottom-0 left-1/2 w-24 h-24 bg-slate-400/5 rounded-full blur-2xl animate-pulse delay-1000"})]}),n.jsxs("div",{className:"absolute inset-0 opacity-15",children:[n.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-white/30 rounded-full -translate-x-20 -translate-y-20 blur-xl"}),n.jsx("div",{className:"absolute bottom-0 right-0 w-32 h-32 bg-white/20 rounded-full translate-x-12 translate-y-12 blur-lg"}),n.jsx("div",{className:"absolute top-1/2 left-1/2 w-24 h-24 bg-white/10 rounded-full -translate-x-12 -translate-y-12 blur-md"})]}),n.jsxs("div",{className:"relative z-10",children:[n.jsxs("div",{className:"flex flex-col items-center mb-3",children:[n.jsxs("div",{className:"relative group",children:[n.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 via-indigo-500 to-slate-500 animate-spin-slow opacity-60 blur-sm"}),n.jsxs("div",{className:"relative w-16 h-16 rounded-full overflow-hidden border-2 border-white/70 shadow-lg bg-gradient-to-br from-blue-400 to-indigo-500 ring-1 ring-white/40 backdrop-blur-sm transform group-hover:scale-105 transition-all duration-300",children:[n.jsx("img",{src:Z0(),alt:"الصورة الشخصية",className:"w-full h-full object-cover"}),n.jsx("div",{className:"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border border-white shadow-sm animate-pulse"})]}),t&&n.jsx("button",{onClick:()=>{var h;return i?(h=document.getElementById("imageUpload"))==null?void 0:h.click():a(!0)},className:"absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center shadow-lg border border-white/60 hover:scale-110 transition-all duration-300",children:n.jsx(zf,{className:"w-3 h-3 text-white"})}),i&&n.jsx("input",{id:"imageUpload",type:"file",accept:"image/*",onChange:D0,className:"hidden"})]}),n.jsxs("div",{className:"text-center mt-2",children:[n.jsx("h2",{className:"text-lg font-bold text-white mb-1 drop-shadow-md",children:e==null?void 0:e.username}),n.jsxs("p",{className:"text-white/60 text-xs bg-white/10 px-2 py-0.5 rounded-full backdrop-blur-sm",children:["ID: ",e==null?void 0:e.playerId]}),n.jsxs("div",{className:"flex items-center justify-center mt-1 gap-1",children:[n.jsxs("div",{className:"flex items-center bg-blue-500/20 px-1.5 py-0.5 rounded-full",children:[n.jsx(cr,{className:"w-2.5 h-2.5 text-blue-300 mr-1"}),n.jsxs("span",{className:"text-blue-200 text-xs font-medium",children:["Lv.",(e==null?void 0:e.level)||1]})]}),n.jsxs("div",{className:"flex items-center bg-indigo-500/20 px-1.5 py-0.5 rounded-full",children:[n.jsx(Zn,{className:"w-2.5 h-2.5 text-indigo-300 mr-1"}),n.jsx("span",{className:"text-indigo-200 text-xs font-medium",children:(e==null?void 0:e.points)||0})]})]})]}),i&&n.jsxs("div",{className:"mt-3 flex space-x-2 rtl:space-x-reverse",children:[n.jsx("button",{onClick:()=>v("male"),className:`px-3 py-1 rounded-full text-xs ${g==="male"?"bg-white text-blue-600":"bg-white/20 text-white"}`,children:"ذكر"}),n.jsx("button",{onClick:()=>v("female"),className:`px-3 py-1 rounded-full text-xs ${g==="female"?"bg-white text-pink-600":"bg-white/20 text-white"}`,children:"أنثى"})]})]}),t?n.jsxs(n.Fragment,{children:[n.jsxs("div",{className:"grid grid-cols-3 gap-1.5 text-center mt-3",children:[n.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[n.jsx("div",{className:"flex items-center justify-center mb-0.5",children:n.jsx(Il,{className:"w-3 h-3 text-yellow-400"})}),n.jsx("div",{className:"text-sm font-bold text-yellow-200",children:(e==null?void 0:e.goldCoins)||0}),n.jsx("div",{className:"text-xs text-yellow-300/80",children:"ذهب"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-indigo-700/40 rounded-lg p-2 backdrop-blur-sm border border-indigo-400/20 hover:scale-105 transition-all duration-300",children:[n.jsx("div",{className:"flex items-center justify-center mb-0.5",children:n.jsx(c0,{className:"w-3 h-3 text-purple-400"})}),n.jsx("div",{className:"text-sm font-bold text-purple-200",children:(e==null?void 0:e.pearls)||0}),n.jsx("div",{className:"text-xs text-purple-300/80",children:"لؤلؤ"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[n.jsx("div",{className:"flex items-center justify-center mb-0.5",children:n.jsx(cr,{className:"w-3 h-3 text-blue-400"})}),n.jsxs("div",{className:"text-sm font-bold text-blue-200",children:["Lv.",(e==null?void 0:e.level)||1]}),n.jsx("div",{className:"text-xs text-blue-300/80",children:"مستوى"})]})]}),n.jsxs("div",{className:"grid grid-cols-2 gap-1.5 text-center mt-1.5",children:[n.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-green-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-green-400/20",children:[n.jsx("div",{className:"flex items-center justify-center mb-0.5",children:n.jsx(li,{className:"w-3 h-3 text-green-400"})}),n.jsx("div",{className:"text-sm font-bold text-green-200",children:(e==null?void 0:e.gamesPlayed)||0}),n.jsx("div",{className:"text-xs text-green-300/80",children:"ألعاب"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-red-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-red-400/20",children:[n.jsx("div",{className:"flex items-center justify-center mb-0.5",children:n.jsx(d0,{className:"w-3 h-3 text-red-400"})}),n.jsx("div",{className:"text-sm font-bold text-red-200",children:((wa=e==null?void 0:e.friends)==null?void 0:wa.length)||0}),n.jsx("div",{className:"text-xs text-red-300/80",children:"أصدقاء"})]})]}),n.jsx("div",{className:"mt-3 flex justify-center",children:n.jsxs("button",{onClick:()=>P(!0),className:"relative bg-white/20 hover:bg-white/30 rounded-lg p-3 backdrop-blur-sm transition-all duration-300 flex items-center gap-2",children:[n.jsx("span",{className:"text-xl",children:"🔔"}),n.jsx("span",{className:"text-white text-sm font-medium",children:"الإشعارات"}),gt.filter(h=>!h.isRead).length>0&&n.jsx("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold",children:gt.filter(h=>!h.isRead).length})]})})]}):n.jsxs("div",{className:"space-y-3",children:[n.jsxs("div",{className:"bg-white/20 rounded-lg p-3 backdrop-blur-sm text-center",children:[n.jsxs("div",{className:"text-lg font-bold text-black",children:["Lv.",(e==null?void 0:e.level)||1]}),n.jsx("div",{className:"text-xs text-white/80",children:"مستوى"})]}),S&&n.jsxs("button",{onClick:()=>p(!0),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[n.jsx(hr,{className:"w-4 h-4"}),n.jsx("span",{children:"إرسال رسالة"})]})]})]}),i&&n.jsxs("div",{className:"absolute top-4 right-4 flex space-x-2 rtl:space-x-reverse",children:[n.jsx("button",{onClick:J0,disabled:b,className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:n.jsx(Uf,{className:"w-4 h-4 text-white"})}),n.jsx("button",{onClick:()=>{a(!1),d(""),v((e==null?void 0:e.gender)||"male")},className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:n.jsx(da,{className:"w-4 h-4 text-white"})})]})]}),n.jsx("div",{className:"bg-gradient-to-r from-gray-900/95 via-purple-900/95 to-gray-900/95 backdrop-blur-md border-b border-purple-500/30 z-20 mx-4 rounded-xl mt-4 shadow-xl flex-shrink-0",children:n.jsx("div",{className:"flex overflow-x-auto scrollbar-hide p-2",children:[{id:"overview",label:"عام",icon:pr,color:"from-blue-500 to-cyan-500"},...t?[{id:"friends",label:"أصدقاء",icon:br,color:"from-green-500 to-emerald-500"},{id:"gifts",label:"هدايا",icon:Bf,color:"from-pink-500 to-rose-500"},{id:"items",label:"عناصر",icon:cr,color:"from-yellow-500 to-orange-500"},{id:"charge",label:"شحن",icon:Ff,color:"from-purple-500 to-violet-500"},{id:"exchange",label:"تبديل",icon:Lf,color:"from-indigo-500 to-blue-500"}]:[]].map(h=>n.jsxs("button",{onClick:()=>o(h.id),className:`flex-shrink-0 flex flex-col items-center px-4 py-3 min-w-[70px] transition-all duration-500 rounded-xl relative overflow-hidden group ${l===h.id?`bg-gradient-to-br ${h.color} text-white shadow-2xl transform scale-110 animate-glow`:"text-gray-300 hover:bg-gray-800/60 hover:text-white hover:scale-105"}`,children:[l===h.id&&n.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${h.color} opacity-20 blur-xl`}),n.jsxs("div",{className:"relative z-10 flex flex-col items-center",children:[n.jsx(h.icon,{className:`w-5 h-5 mb-1 transition-all duration-300 ${l===h.id?"animate-bounce":"group-hover:scale-110"}`}),n.jsx("span",{className:"text-xs font-medium",children:h.label})]}),l===h.id&&n.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"})]},h.id))})}),n.jsxs("div",{className:"flex-1 flex flex-col",children:[l==="overview"&&n.jsxs("div",{className:"flex-1 flex flex-col p-4 gap-4",children:[n.jsxs("div",{className:"bg-gradient-to-br from-slate-800/90 via-blue-800/90 to-indigo-800/90 rounded-xl p-3 border border-blue-400/30 shadow-lg backdrop-blur-sm relative overflow-hidden flex-shrink-0",children:[n.jsx("div",{className:"absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-full blur-xl animate-pulse"}),n.jsx("div",{className:"absolute bottom-0 left-0 w-12 h-12 bg-indigo-500/10 rounded-full blur-lg animate-float"}),n.jsxs("div",{className:"relative z-10",children:[n.jsxs("h3",{className:"font-bold text-blue-300 mb-3 text-base flex items-center gap-2",children:[n.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center",children:n.jsx(pr,{className:"w-3 h-3 text-white"})}),"معلومات الحساب"]}),n.jsxs("div",{className:"grid gap-2",children:[n.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-pink-500 to-purple-500 rounded flex items-center justify-center",children:n.jsx("span",{className:"text-white text-xs",children:"👤"})}),n.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الجنس"})]}),n.jsx("span",{className:"text-blue-200 font-bold text-sm",children:(e==null?void 0:e.gender)==="female"?"👩 أنثى":"👨 ذكر"})]}),n.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-blue-500 to-indigo-500 rounded flex items-center justify-center",children:n.jsx(Rf,{className:"w-3 h-3 text-white"})}),n.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الانضمام"})]}),n.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.joinedAt).toLocaleDateString("ar-EG")})]}),n.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-500 rounded flex items-center justify-center",children:n.jsx(mr,{className:"w-3 h-3 text-white"})}),n.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"آخر نشاط"})]}),n.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.lastActive).toLocaleDateString("ar-EG")})]})]})]})]}),n.jsxs("div",{className:"grid grid-cols-2 gap-3 flex-shrink-0",children:[n.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-blue-800/80 rounded-xl p-3 shadow-lg border border-blue-400/30 backdrop-blur-sm",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:n.jsx(Zn,{className:"w-4 h-4 text-white"})}),n.jsx("div",{className:"text-lg font-bold text-blue-200",children:(e==null?void 0:e.experience)||0}),n.jsx("div",{className:"text-xs text-blue-300 font-medium",children:"خبرة"})]})}),n.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-indigo-800/80 rounded-xl p-3 shadow-lg border border-indigo-400/30 backdrop-blur-sm",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:n.jsx(cr,{className:"w-4 h-4 text-white"})}),n.jsxs("div",{className:"text-lg font-bold text-indigo-200",children:["Lv.",(e==null?void 0:e.level)||1]}),n.jsx("div",{className:"text-xs text-indigo-300 font-medium",children:"مستوى"})]})})]}),!t&&n.jsx("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 rounded-xl p-3 border border-blue-400/30 shadow-md backdrop-blur-sm flex-shrink-0",children:n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-sm",children:n.jsx(pr,{className:"w-3 h-3 text-white"})}),n.jsxs("div",{children:[n.jsx("h4",{className:"font-bold text-blue-300 text-sm",children:"ملف عام"}),n.jsx("p",{className:"text-xs text-blue-200",children:"معلومات أساسية فقط"})]})]})})]}),t&&l==="friends"&&n.jsxs("div",{className:"space-y-5",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"👥"}),n.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إدارة الأصدقاء"}),n.jsx("p",{className:"text-gray-300 text-sm",children:"أضف وتفاعل مع أصدقائك في المنصة"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[n.jsx(_l,{className:"w-6 h-6 text-blue-300"}),n.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إضافة صديق جديد"})]}),n.jsxs("div",{className:"flex gap-3 mb-4",children:[n.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:x,onChange:h=>{const M=h.target.value.replace(/\D/g,"");U(M),we("")},className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:6,onKeyPress:h=>h.key==="Enter"&&pa()}),n.jsxs("button",{onClick:pa,disabled:Te||x.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:[Te?"⏳":"🔍"," ",Te?"جاري البحث...":"بحث"]})]}),Ue&&n.jsx("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-3 mb-4",children:n.jsx("p",{className:"text-red-200 text-sm text-center",children:Ue})}),Y&&n.jsx("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-4",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:Y.profileImage?n.jsx("img",{src:Y.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(ja=(ba=Y.username)==null?void 0:ba.charAt(0))==null?void 0:ja.toUpperCase()}),n.jsxs("div",{children:[n.jsx("p",{className:"text-green-200 font-bold",children:Y.username}),n.jsxs("p",{className:"text-green-300 text-xs",children:["رقم اللاعب: ",Y.playerId]})]})]}),n.jsx("button",{onClick:()=>R0(Y.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"➕ إضافة صديق"})]})})]}),n.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"👫"}),"قائمة الأصدقاء (",ce.length,")"]}),ce.length===0?n.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[n.jsx("div",{className:"text-3xl mb-2",children:"😔"}),"لا توجد أصدقاء حالياً"]}):n.jsx("div",{className:"space-y-3",children:ce.map(h=>{var M,I;return n.jsxs("div",{className:"flex items-center justify-between p-3 bg-slate-900/40 rounded-xl",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:h.profileImage?n.jsx("img",{src:h.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(I=(M=h.username)==null?void 0:M.charAt(0))==null?void 0:I.toUpperCase()}),n.jsxs("div",{children:[n.jsx("p",{className:"text-slate-200 font-medium",children:h.username}),n.jsxs("p",{className:"text-slate-400 text-xs",children:["رقم اللاعب: ",h.playerId]})]})]}),n.jsxs("div",{className:"flex gap-2",children:[n.jsx("button",{onClick:()=>W0(h),className:"bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all",children:"💬 محادثة"}),n.jsx("button",{onClick:()=>C(h),className:"bg-gradient-to-r from-purple-500 to-pink-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-purple-600 hover:to-pink-700 transition-all",children:"🎁 هدية"})]})]},h.id)})})]}),n.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/60 to-green-800/60 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-emerald-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"📩"}),"طلبات الصداقة (",tr.length,")"]}),tr.length===0?n.jsxs("div",{className:"text-center py-6 text-emerald-300 text-sm bg-emerald-900/30 rounded-xl",children:[n.jsx("div",{className:"text-3xl mb-2",children:"📭"}),"لا توجد طلبات صداقة جديدة"]}):n.jsx("div",{className:"space-y-3",children:tr.map(h=>{var M,I;return n.jsxs("div",{className:"flex items-center justify-between p-3 bg-emerald-900/40 rounded-xl",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:h.requester.profileImage?n.jsx("img",{src:h.requester.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(I=(M=h.requester.username)==null?void 0:M.charAt(0))==null?void 0:I.toUpperCase()}),n.jsxs("div",{children:[n.jsx("p",{className:"text-emerald-200 font-medium",children:h.requester.username}),n.jsxs("p",{className:"text-emerald-400 text-xs",children:["رقم اللاعب: ",h.requester.playerId]}),n.jsx("p",{className:"text-emerald-500 text-xs",children:new Date(h.requestedAt).toLocaleDateString("ar")})]})]}),n.jsxs("div",{className:"flex gap-2",children:[n.jsx("button",{onClick:()=>O0(h.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"✅ قبول"}),n.jsx("button",{className:"bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-red-600 hover:to-red-700 transition-all",children:"❌ رفض"})]})]},h.id)})})]})]}),t&&l==="gifts"&&n.jsxs("div",{className:"space-y-5",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🎁"}),n.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام إدارة الهدايا"}),n.jsx("p",{className:"text-gray-300 text-sm",children:"أرسل واستقبل الهدايا المتنوعة مع الأصدقاء"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("div",{className:"flex items-center gap-3 mb-5",children:[n.jsx(An,{className:"w-6 h-6 text-blue-300"}),n.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إرسال هدية جديدة"})]}),n.jsxs("div",{className:"mb-5",children:[n.jsxs("h5",{className:"text-base font-bold text-yellow-300 mb-3 flex items-center gap-2",children:[n.jsx("span",{className:"text-xl",children:"💰"}),"العملات الذهبية"]}),n.jsx("div",{className:"grid grid-cols-1 gap-3",children:n.jsxs("button",{onClick:()=>pt("gold"),className:`p-4 border rounded-xl hover:bg-yellow-700/50 transition-all duration-300 flex items-center gap-4 shadow-lg ${de==="gold"?"bg-yellow-700/60 border-yellow-300":"bg-yellow-800/40 border-yellow-400/30"}`,children:[n.jsx("div",{className:"text-3xl drop-shadow-lg",children:"🪙"}),n.jsxs("div",{className:"text-right flex-1",children:[n.jsx("div",{className:"text-sm font-bold text-yellow-200",children:"عملات ذهبية"}),n.jsx("div",{className:"text-xs text-yellow-300",children:"للشراء والاستخدام في المنصة"})]}),de==="gold"&&n.jsx("div",{className:"text-yellow-300",children:"✓"})]})})]}),n.jsxs("div",{className:"mb-5",children:[n.jsxs("h5",{className:"text-base font-bold text-red-300 mb-3 flex items-center gap-2",children:[n.jsx("span",{className:"text-xl",children:"⚠️"}),"العناصر الضارة"]}),n.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[n.jsxs("button",{onClick:()=>pt("bomb"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${de==="bomb"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[n.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💣"}),n.jsx("div",{className:"text-xs font-bold text-red-200",children:"قنبلة مدمرة"}),de==="bomb"&&n.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),n.jsxs("button",{onClick:()=>pt("bat"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${de==="bat"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[n.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🦇"}),n.jsx("div",{className:"text-xs font-bold text-red-200",children:"خفاش مؤذي"}),de==="bat"&&n.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),n.jsxs("button",{onClick:()=>pt("snake"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${de==="snake"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[n.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🐍"}),n.jsx("div",{className:"text-xs font-bold text-red-200",children:"ثعبان سام"}),de==="snake"&&n.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]})]})]}),n.jsxs("div",{className:"mb-5",children:[n.jsxs("h5",{className:"text-base font-bold text-emerald-300 mb-3 flex items-center gap-2",children:[n.jsx("span",{className:"text-xl",children:"✨"}),"العناصر المفيدة"]}),n.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[n.jsxs("button",{onClick:()=>pt("gem"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${de==="gem"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[n.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💎"}),n.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"جوهرة نادرة"}),de==="gem"&&n.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),n.jsxs("button",{onClick:()=>pt("star"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${de==="star"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[n.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"⭐"}),n.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"نجمة ذهبية"}),de==="star"&&n.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),n.jsxs("button",{onClick:()=>pt("coin"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${de==="coin"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[n.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🪙"}),n.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"عملة خاصة"}),de==="coin"&&n.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]})]})]}),n.jsxs("div",{className:"space-y-4",children:[de&&n.jsx("div",{className:"bg-blue-900/30 p-3 rounded-xl border border-blue-400/30",children:n.jsxs("p",{className:"text-blue-200 text-sm text-center",children:["العنصر المختار: ",n.jsx("span",{className:"font-bold text-blue-100",children:xa(de)})]})}),n.jsxs("div",{className:"flex gap-3",children:[n.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:Ye,onChange:h=>{const M=h.target.value.replace(/\D/g,"");nn(M)},maxLength:6,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),n.jsx("button",{onClick:B0,disabled:as||!de||Ye.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:as?"⏳ جاري الإرسال...":"🎁 إرسال"})]})]})]}),n.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"📦"}),"الهدايا المستلمة"]}),n.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[n.jsx("div",{className:"text-3xl mb-2",children:"🎈"}),"لا توجد هدايا جديدة في الوقت الحالي"]})]})]}),t&&l==="items"&&n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"text-4xl mb-3",children:"⚡"}),n.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"العناصر المجمعة"}),n.jsx("p",{className:"text-sm text-gray-500",children:"العناصر التي حصلت عليها من الألعاب"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/80 to-green-800/80 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-emerald-300 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"⭐"}),"العناصر المفيدة"]}),n.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[n.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💎"}),n.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"جوهرة نادرة"}),n.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 500 🪙"}),n.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.gems})]}),n.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"⭐"}),n.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"نجمة ذهبية"}),n.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 200 🪙"}),n.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.stars})]}),n.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),n.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"عملة خاصة"}),n.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 100 🪙"}),n.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.coins})]})]})]}),n.jsxs("div",{className:"bg-gradient-to-br from-red-800/80 to-rose-800/80 p-6 rounded-2xl border border-red-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-red-300 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"💣"}),"العناصر الضارة"]}),n.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[n.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💣"}),n.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"قنبلة مدمرة"}),n.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 100 🪙"}),n.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bombs})]}),n.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🦇"}),n.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"خفاش مؤذي"}),n.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 50 🪙"}),n.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bats})]}),n.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🐍"}),n.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"ثعبان سام"}),n.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 75 🪙"}),n.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.snakes})]})]})]}),n.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-amber-200 mb-3 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"💡"}),"نصائح مهمة"]}),n.jsx("p",{className:"text-amber-100 text-sm leading-relaxed",children:"اجمع العناصر المفيدة من الألعاب • أرسلها كهدايا للأصدقاء • بادلها بعملات ذهبية قيمة"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-blue-800/80 to-indigo-800/80 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-3xl drop-shadow-lg",children:"🛡️"}),"نظام الحماية المتطور"]}),n.jsx("p",{className:"text-blue-100 text-sm mb-6 leading-relaxed",children:"احمِ نفسك من العناصر الضارة والهجمات الخطيرة في الألعاب والهدايا"}),n.jsxs("div",{className:"grid grid-cols-1 gap-5",children:[n.jsxs("div",{className:"bg-blue-800/40 p-5 rounded-xl border border-blue-400/30 backdrop-blur-sm shadow-lg",children:[n.jsxs("div",{className:"flex items-center justify-between mb-3",children:[n.jsx("h5",{className:"font-bold text-blue-200 text-base",children:"🥇 درع ذهبي أساسي"}),n.jsx("span",{className:"text-xs text-blue-100 bg-blue-600/40 px-3 py-1 rounded-full font-medium",children:"24 ساعة"})]}),n.jsx("p",{className:"text-sm text-blue-100 mb-4 leading-relaxed",children:"حماية قوية ضد القنابل المدمرة والخفافيش المؤذية والثعابين السامة"}),$!=null&&$.isActive?n.jsxs("div",{className:"bg-green-600/40 p-3 rounded-xl border border-green-400/30 text-center",children:[n.jsx("div",{className:"text-green-200 text-sm font-bold",children:"🛡️ الدرع نشط"}),n.jsxs("div",{className:"text-green-100 text-xs mt-1",children:["ينتهي في: ",$.expiresAt?new Date($.expiresAt).toLocaleString("ar"):"غير محدد"]})]}):n.jsx("button",{onClick:()=>L0("gold"),disabled:O,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:O?"⏳ جاري التفعيل...":"🛡️ تفعيل الحماية (5,000 🪙)"})]}),n.jsxs("div",{className:"bg-purple-800/40 p-5 rounded-xl border border-purple-400/30 backdrop-blur-sm shadow-lg",children:[n.jsxs("div",{className:"flex items-center justify-between mb-3",children:[n.jsx("h5",{className:"font-bold text-purple-200 text-base",children:"👑 درع متقدم مميز"}),n.jsx("span",{className:"text-xs text-purple-100 bg-purple-600/40 px-3 py-1 rounded-full font-medium",children:"7 أيام"})]}),n.jsx("p",{className:"text-sm text-purple-100 mb-4 leading-relaxed",children:"حماية مميزة وشاملة لمدة أسبوع كامل ضد جميع العناصر الضارة والهجمات"}),n.jsx("button",{onClick:()=>alert("الدرع المميز غير متاح حالياً. استخدم الدرع الأساسي."),className:"w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 rounded-xl text-sm font-bold cursor-not-allowed opacity-60",disabled:!0,children:"👑 قريباً - الحماية المميزة"})]})]})]})]}),t&&l==="charge"&&n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"💰"}),n.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"شحن الرصيد الذهبي"}),n.jsx("p",{className:"text-gray-300 text-sm",children:"اشحن عملاتك الذهبية بأفضل الأسعار"})]}),n.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[n.jsx("div",{className:"bg-gradient-to-br from-yellow-800/60 to-amber-800/60 p-6 rounded-2xl border border-yellow-400/30 shadow-xl backdrop-blur-sm",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),n.jsx("h4",{className:"font-bold text-yellow-200 mb-2 text-lg",children:"5,000 عملة ذهبية"}),n.jsx("p",{className:"text-yellow-100 text-base mb-4 font-semibold",children:"💵 $1 USD فقط"}),n.jsxs("div",{className:"flex gap-2",children:[n.jsx("button",{onClick:()=>ga(5e3,!0,"1_dollar"),disabled:rr||!kr["1_dollar"],className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:rr?"⏳":kr["1_dollar"]?"🆓 مجاني":"✅ مستخدم"}),n.jsx("button",{onClick:()=>ya(5e3,"$1 USD"),className:"flex-1 bg-gradient-to-r from-yellow-500 to-amber-600 text-white py-3 rounded-xl text-xs font-bold hover:from-yellow-600 hover:to-amber-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})}),n.jsx("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),n.jsx("h4",{className:"font-bold text-green-200 mb-2 text-lg",children:"27,200 عملة ذهبية"}),n.jsx("p",{className:"text-green-100 text-base mb-1 font-semibold",children:"💵 $5 USD"}),n.jsx("p",{className:"text-sm text-green-300 bg-green-900/30 px-3 py-1 rounded-lg mb-4 font-medium",children:"🎉 وفر 8% أكثر!"}),n.jsxs("div",{className:"flex gap-2",children:[n.jsx("button",{onClick:()=>ga(27200,!0,"5_dollar"),disabled:rr||!kr["5_dollar"],className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-3 rounded-xl text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:rr?"⏳":kr["5_dollar"]?"🆓 مجاني":"✅ مستخدم"}),n.jsx("button",{onClick:()=>ya(27200,"$5 USD"),className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})})]})]}),t&&l==="exchange"&&n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🔄"}),n.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام تبديل العملات"}),n.jsx("p",{className:"text-gray-300 text-sm",children:"اللآلئ مخصصة حصرياً للتحويل إلى دولارات نقدية"})]}),n.jsxs("div",{className:"space-y-5",children:[n.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"🪙➡️🦪"}),"تحويل ذهب إلى لآلئ"]}),n.jsx("p",{className:"text-blue-100 text-sm mb-4 bg-blue-900/30 px-3 py-2 rounded-lg",children:"معدل التحويل: 10,000 🪙 = 1 🦪"}),n.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[n.jsx("input",{type:"number",placeholder:"10000",value:ht,onChange:h=>ls(Math.max(1e4,parseInt(h.target.value)||1e4)),min:"10000",step:"10000",max:(e==null?void 0:e.goldCoins)||0,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),n.jsx("span",{className:"text-blue-200 font-medium",children:"🪙 ➡️ 🦪"})]}),n.jsxs("div",{className:"text-center mb-4",children:[n.jsxs("p",{className:"text-blue-200 text-sm",children:["ستحصل على: ",n.jsxs("span",{className:"font-bold text-blue-100",children:[Math.floor(ht/1e4)," 🦪"]})]}),n.jsxs("p",{className:"text-blue-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.goldCoins)||0," 🪙"]})]}),n.jsx("button",{onClick:$0,disabled:os||ht<1e4||((e==null?void 0:e.goldCoins)||0)<ht,className:"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:os?"⏳ جاري التحويل...":"🔄 تحويل إلى لآلئ"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-green-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"🦪➡️💵"}),"سحب دولارات نقدية"]}),n.jsx("div",{className:"bg-green-900/30 p-4 rounded-xl mb-4",children:n.jsxs("p",{className:"text-green-100 text-sm leading-relaxed",children:[n.jsx("strong",{className:"text-green-200",children:"💰 معدل التحويل:"})," 10 🦪 = $1 USD",n.jsx("br",{}),n.jsx("strong",{className:"text-green-200",children:"🎯 الحد الأدنى للسحب:"})," $25 USD (250 🦪)"]})}),n.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[n.jsx("input",{type:"number",placeholder:"250",value:ft,onChange:h=>Ll(Math.max(250,parseInt(h.target.value)||250)),min:"250",max:(e==null?void 0:e.pearls)||0,className:"flex-1 px-4 py-3 bg-green-900/30 border border-green-400/30 rounded-xl text-white placeholder-green-300 focus:outline-none focus:ring-2 focus:ring-green-500"}),n.jsx("span",{className:"text-green-200 font-medium",children:"🦪 ➡️ $"})]}),n.jsxs("div",{className:"text-center mb-4",children:[n.jsxs("p",{className:"text-green-200 text-sm",children:["ستحصل على: ",n.jsxs("span",{className:"font-bold text-green-100",children:["$",ft/10," USD"]})]}),n.jsxs("p",{className:"text-green-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.pearls)||0," 🦪"]})]}),n.jsx("button",{onClick:F0,disabled:ft<250||((e==null?void 0:e.pearls)||0)<ft,className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:"📱 طلب سحب عبر واتساب"})]}),n.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[n.jsxs("h4",{className:"font-bold text-amber-200 mb-4 text-lg flex items-center gap-3",children:[n.jsx("span",{className:"text-2xl",children:"📝"}),"معلومات مهمة"]}),n.jsxs("div",{className:"space-y-2 text-amber-100 text-sm leading-relaxed",children:[n.jsxs("p",{children:["• ",n.jsx("strong",{children:"اللآلئ 🦪"})," - مخصصة حصرياً للتحويل إلى دولارات نقدية"]}),n.jsxs("p",{children:["• ",n.jsx("strong",{children:"العملات الذهبية 🪙"})," - للشراء والتبادل داخل المنصة"]}),n.jsxs("p",{children:["• ",n.jsx("strong",{children:"العناصر الخاصة"})," - تُكسب من الألعاب والتحديات"]})]})]})]})]})]}),u&&n.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-gradient-to-br from-slate-800 to-blue-800 rounded-2xl p-6 w-full max-w-md shadow-2xl border border-blue-400/30",children:[n.jsxs("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:["إرسال رسالة إلى ",e==null?void 0:e.username]}),n.jsx("textarea",{value:E,onChange:h=>L(h.target.value),placeholder:"اكتب رسالتك هنا...",className:"w-full p-3 rounded-xl bg-slate-800/40 border border-blue-400/30 text-white placeholder-blue-300 resize-none h-32 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:500}),n.jsxs("div",{className:"text-right text-xs text-blue-300 mt-1 mb-4",children:[E.length,"/500"]}),n.jsxs("div",{className:"flex gap-3",children:[n.jsx("button",{onClick:()=>{p(!1),L("")},className:"flex-1 bg-slate-600 hover:bg-slate-700 text-white py-2 px-4 rounded-xl transition-colors",children:"إلغاء"}),n.jsxs("button",{onClick:G0,disabled:!E.trim(),className:"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-500 disabled:to-gray-600 text-white py-2 px-4 rounded-xl transition-all flex items-center justify-center gap-2",children:[n.jsx(An,{className:"w-4 h-4"}),"إرسال"]})]})]})}),f&&n.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-gradient-to-br from-purple-900 to-pink-900 rounded-2xl p-6 w-full max-w-md border border-purple-400/30 shadow-2xl",children:[n.jsxs("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"text-4xl mb-3",children:"🎁"}),n.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إرسال هدية"}),n.jsxs("p",{className:"text-purple-200 text-sm",children:["إلى: ",f.username]})]}),n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"نوع الهدية:"}),n.jsxs("div",{className:"flex gap-2",children:[n.jsx("button",{onClick:()=>J("gold"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${F==="gold"?"bg-gradient-to-r from-yellow-500 to-orange-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"🪙 عملات ذهبية"}),n.jsx("button",{onClick:()=>J("pearls"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${F==="pearls"?"bg-gradient-to-r from-blue-500 to-cyan-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"💎 لآلئ"})]})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"الكمية:"}),n.jsx("input",{type:"number",value:_,onChange:h=>T(Math.max(1,parseInt(h.target.value)||1)),min:"1",max:F==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls,className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500"}),n.jsxs("p",{className:"text-purple-300 text-xs mt-1",children:["الحد الأقصى: ",F==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls]})]}),n.jsxs("div",{children:[n.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"رسالة (اختيارية):"}),n.jsx("textarea",{value:te,onChange:h=>Ne(h.target.value),placeholder:"اكتب رسالة مع الهدية...",className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none",rows:3,maxLength:200})]})]}),n.jsxs("div",{className:"flex gap-3 mt-6",children:[n.jsx("button",{onClick:()=>{C(null),Ne(""),T(100)},className:"flex-1 bg-gray-600 text-white py-3 rounded-xl font-bold hover:bg-gray-700 transition-all",children:"إلغاء"}),n.jsx("button",{onClick:z0,disabled:ne||_<=0,className:"flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-xl font-bold hover:from-purple-600 hover:to-pink-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:ne?"⏳ جاري الإرسال...":"🎁 إرسال الهدية"})]})]})}),w&&n.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:n.jsxs("div",{className:"bg-gradient-to-br from-slate-900 to-purple-900 rounded-2xl p-6 w-full max-w-md max-h-[80vh] overflow-hidden border border-purple-400/30 shadow-2xl",children:[n.jsxs("div",{className:"flex items-center justify-between mb-4",children:[n.jsxs("h3",{className:"text-xl font-bold text-white flex items-center gap-2",children:["🔔 الإشعارات",gt.filter(h=>!h.isRead).length>0&&n.jsx("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:gt.filter(h=>!h.isRead).length})]}),n.jsxs("div",{className:"flex items-center gap-2",children:[gt.filter(h=>!h.isRead).length>0&&n.jsx("button",{onClick:q0,className:"text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors",children:"تحديد الكل كمقروء"}),n.jsx("button",{onClick:()=>P(!1),className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]})]}),n.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:gt.length===0?n.jsxs("div",{className:"text-center py-8 text-gray-400",children:[n.jsx("div",{className:"text-4xl mb-2",children:"📭"}),n.jsx("p",{children:"لا توجد إشعارات"})]}):gt.map(h=>n.jsx("div",{onClick:()=>{h.isRead||V0(h._id)},className:`p-3 rounded-xl border transition-all cursor-pointer hover:bg-opacity-80 ${h.isRead?"bg-slate-800/50 border-slate-600/30":"bg-blue-900/30 border-blue-400/30 shadow-lg"}`,children:n.jsxs("div",{className:"flex items-start gap-3",children:[n.jsxs("div",{className:"relative",children:[n.jsxs("div",{className:"text-2xl",children:[h.type==="gift_received"&&"🎁",h.type==="item_received"&&"📦",h.type==="friend_request"&&"👥",h.type==="message"&&"💬"]}),!h.isRead&&n.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-slate-900 animate-pulse"})]}),n.jsxs("div",{className:"flex-1",children:[n.jsx("h4",{className:`font-bold text-sm ${h.isRead?"text-gray-300":"text-white"}`,children:h.title}),n.jsx("p",{className:`text-xs mt-1 ${h.isRead?"text-gray-400":"text-gray-200"}`,children:h.message}),n.jsxs("div",{className:"flex items-center justify-between mt-2",children:[n.jsx("span",{className:"text-gray-400 text-xs",children:new Date(h.createdAt).toLocaleString("ar")}),!h.isRead&&n.jsx("span",{className:"text-blue-400 text-xs font-bold",children:"جديد"})]})]})]})},h._id))})]})}),W&&R&&n.jsxs("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex flex-col",onClick:h=>{h.target===h.currentTarget&&Rl(!1)},children:[n.jsxs("div",{className:"bg-gradient-to-r from-green-600 to-green-700 px-4 py-3 flex items-center gap-3 shadow-lg",children:[n.jsx("button",{onClick:()=>H(!1),className:"text-white hover:bg-white/20 rounded-full p-2 transition-colors",children:n.jsx(i0,{className:"w-5 h-5"})}),n.jsx("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-white/20 border-2 border-white/30",children:R.profileImage?n.jsx("img",{src:R.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):n.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold text-lg",children:(ka=(Na=R.username)==null?void 0:Na.charAt(0))==null?void 0:ka.toUpperCase()})}),n.jsxs("div",{className:"flex-1",children:[n.jsx("h3",{className:"font-bold text-white text-lg",children:R.username}),n.jsxs("p",{className:"text-green-100 text-xs opacity-90",children:["رقم اللاعب: ",R.playerId]}),n.jsx("p",{className:"text-green-100 text-xs opacity-75",children:"🕐 المحادثات تختفي بعد 3 أيام"})]}),n.jsx("div",{className:"text-green-100",children:n.jsx(hr,{className:"w-6 h-6"})})]}),n.jsxs("div",{className:"flex-1 overflow-y-auto px-4 py-6 space-y-4",id:"messages-container",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,backgroundColor:"#0f172a"},children:[Ee.length===0?n.jsxs("div",{className:"text-center py-20",children:[n.jsx("div",{className:"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4",children:n.jsx(hr,{className:"w-10 h-10 text-white/50"})}),n.jsx("p",{className:"text-white/70 text-lg font-medium",children:"ابدأ محادثة جديدة"}),n.jsx("p",{className:"text-white/50 text-sm mt-2",children:"أرسل رسالة لبدء المحادثة"}),n.jsx("div",{className:"mt-4 p-3 bg-yellow-500/20 rounded-lg border border-yellow-500/30",children:n.jsx("p",{className:"text-yellow-200 text-xs",children:"🕐 تنبيه: المحادثات تُحذف تلقائياً بعد 3 أيام للحفاظ على الخصوصية"})})]}):Ee.map((h,M)=>{var D,Q;const I=((D=h.sender)==null?void 0:D._id)===(e==null?void 0:e.id),q=M===0||new Date(h.createdAt).getTime()-new Date((Q=Ee[M-1])==null?void 0:Q.createdAt).getTime()>3e5;return n.jsxs("div",{children:[q&&n.jsx("div",{className:"text-center my-4",children:n.jsx("span",{className:"bg-black/30 text-white/70 px-3 py-1 rounded-full text-xs",children:new Date(h.createdAt).toLocaleDateString("ar",{weekday:"short",hour:"2-digit",minute:"2-digit"})})}),n.jsx("div",{className:`flex ${I?"justify-end":"justify-start"} mb-2`,children:n.jsxs("div",{className:`max-w-[80%] px-4 py-3 rounded-2xl shadow-lg relative ${I?"bg-gradient-to-r from-green-500 to-green-600 text-white rounded-br-md":"bg-white text-gray-800 rounded-bl-md"}`,children:[n.jsx("p",{className:"text-sm leading-relaxed break-words whitespace-pre-wrap",children:h.content}),n.jsxs("div",{className:`flex items-center justify-end mt-1 text-xs ${I?"text-green-100":"text-gray-500"}`,children:[n.jsx("span",{children:new Date(h.createdAt).toLocaleTimeString("ar",{hour:"2-digit",minute:"2-digit"})}),I&&n.jsx("div",{className:"ml-1 text-green-200",children:"✓✓"})]}),n.jsx("div",{className:`absolute bottom-0 w-4 h-4 ${I?"-right-2 bg-gradient-to-r from-green-500 to-green-600":"-left-2 bg-white"}`,style:{clipPath:I?"polygon(0 0, 100% 0, 0 100%)":"polygon(100% 0, 0 0, 100% 100%)"}})]})})]},h._id)}),A0&&n.jsx("div",{className:"flex justify-start mb-2",children:n.jsx("div",{className:"bg-white px-4 py-3 rounded-2xl rounded-bl-md shadow-lg",children:n.jsxs("div",{className:"flex items-center gap-1",children:[n.jsxs("div",{className:"flex gap-1",children:[n.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),n.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),n.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),n.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"يكتب..."})]})})})]}),n.jsxs("div",{className:"bg-gray-100 px-4 py-3 flex items-end gap-3 relative",children:[Pl&&n.jsx("div",{className:"absolute bottom-full left-4 right-4 mb-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-h-48 overflow-y-auto",children:n.jsx("div",{className:"grid grid-cols-8 gap-2",children:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","👍","👎","👌","🤌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤝","🙏","✍️","💪","🦾","🦿","🦵"].map((h,M)=>n.jsx("button",{onClick:()=>{Cr(I=>I+h),Rl(!1)},className:"text-xl hover:bg-gray-100 rounded-lg p-1 transition-colors",children:h},M))})}),n.jsx("button",{onClick:()=>Rl(!Pl),className:`text-gray-500 hover:text-gray-700 transition-colors p-2 ${Pl?"bg-gray-200 rounded-full":""}`,children:n.jsx("span",{className:"text-xl",children:"😊"})}),n.jsx("div",{className:"flex-1 relative",children:n.jsx("textarea",{value:$e,onChange:h=>{Cr(h.target.value),h.target.style.height="auto",h.target.style.height=Math.min(h.target.scrollHeight,120)+"px"},placeholder:"اكتب رسالة...",className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-3xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none max-h-[120px] min-h-[48px]",onKeyPress:h=>{h.key==="Enter"&&!h.shiftKey&&(h.preventDefault(),$e.trim()&&va())},rows:1,autoFocus:!0})}),n.jsx("button",{onClick:va,disabled:!$e.trim(),className:`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${$e.trim()?"bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:n.jsx(An,{className:"w-5 h-5"})})]})]})]})};class I0{constructor(t){this.localStream=null,this.peerConnections=new Map,this.remoteUsers=new Map,this.isJoined=!1,this.isMuted=!1,this.roomId=null,this.userId=null,this.audioContext=null,this.analyser=null,this.voiceActivityThreshold=25,this.isMonitoringVoice=!1,this.isSpeaking=!1,this.lastVoiceActivitySent=0,this.voiceActivityDebounce=500,this.iceServers=[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"},{urls:"turn:openrelay.metered.ca:80",username:"openrelayproject",credential:"openrelayproject"}],this.wsService=t,this.setupWebSocketHandlers()}setupWebSocketHandlers(){this.wsService.onMessage("webrtc_offer",this.handleOffer.bind(this)),this.wsService.onMessage("webrtc_answer",this.handleAnswer.bind(this)),this.wsService.onMessage("webrtc_ice_candidate",this.handleIceCandidate.bind(this)),this.wsService.onMessage("user_joined_voice",this.handleUserJoined.bind(this)),this.wsService.onMessage("user_left_voice",this.handleUserLeft.bind(this))}async joinRoom(t,r){var s;try{this.roomId=t,this.userId=r,this.localStream=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0},video:!1}),console.log("✅ Got local audio stream"),this.startVoiceActivityDetection(),console.log("📤 Sending join_voice_room message to server"),this.wsService.send({type:"join_voice_room",data:{roomId:t,userId:r}}),console.log("✅ join_voice_room message sent"),this.isJoined=!0,console.log("✅ Joined voice room successfully")}catch(l){throw console.error("❌ Error joining voice room:",l),(s=this.onError)==null||s.call(this,l),l}}async leaveRoom(){var t;try{console.log("🔄 Leaving voice room..."),this.peerConnections.forEach((r,s)=>{r.close()}),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(r=>r.stop()),this.localStream=null),this.stopVoiceActivityDetection(),this.roomId&&this.userId&&this.wsService.send({type:"leave_voice_room",data:{roomId:this.roomId,userId:this.userId}}),this.isJoined=!1,this.remoteUsers.clear(),console.log("✅ Left voice room successfully")}catch(r){console.error("❌ Error leaving voice room:",r),(t=this.onError)==null||t.call(this,r)}}async toggleMute(){if(!this.localStream)return!1;const t=this.localStream.getAudioTracks()[0];return t?(t.enabled=!t.enabled,this.isMuted=!t.enabled,this.isMuted):!1}setMute(t){if(!this.localStream)return;const r=this.localStream.getAudioTracks()[0];r&&(r.enabled=!t,this.isMuted=t,console.log(`🎤 Local audio ${t?"muted":"unmuted"}`))}setRemoteAudioMuted(t){this.peerConnections.forEach((r,s)=>{const l=r.getRemoteStreams()[0];l&&l.getAudioTracks().forEach(i=>{i.enabled=!t})})}async createPeerConnection(t){const r=new RTCPeerConnection({iceServers:this.iceServers});return this.localStream&&this.localStream.getTracks().forEach(s=>{r.addTrack(s,this.localStream)}),r.ontrack=s=>{var a;const[l]=s.streams,o=new Audio;o.srcObject=l,o.volume=.8,o.autoplay=!0,o.play().catch(()=>{});const i=this.remoteUsers.get(t)||{id:t,isSpeaking:!1,isMuted:!1,audioLevel:0};this.remoteUsers.set(t,i),(a=this.onUserJoined)==null||a.call(this,i)},r.onicecandidate=s=>{s.candidate&&this.wsService.send({type:"webrtc_ice_candidate",data:{candidate:s.candidate,targetUserId:t,fromUserId:this.userId}})},this.peerConnections.set(t,r),r}async handleOffer(t){try{const{offer:r,fromUserId:s}=t;console.log("📥 Received offer from:",s),console.log("🔄 Creating peer connection and answer for:",s);const l=await this.createPeerConnection(s);await l.setRemoteDescription(r),console.log("✅ Set remote description (offer)");const o=await l.createAnswer();await l.setLocalDescription(o),console.log("✅ Created and set local description (answer)"),console.log("📤 Sending WebRTC answer to:",s),this.wsService.send({type:"webrtc_answer",data:{answer:o,targetUserId:s,fromUserId:this.userId}})}catch(r){console.error("❌ Error handling offer:",r)}}async handleAnswer(t){try{const{answer:r,fromUserId:s}=t;console.log("📥 Received answer from:",s);const l=this.peerConnections.get(s);l&&l.signalingState==="have-local-offer"?(await l.setRemoteDescription(r),console.log("✅ Set remote description (answer) for:",s),console.log("🔗 WebRTC connection should be established with:",s)):l?console.warn("⚠️ Peer connection not in correct state for answer:",l.signalingState):console.warn("⚠️ No peer connection found for:",s)}catch(r){console.error("❌ Error handling answer:",r)}}async handleIceCandidate(t){try{const{candidate:r,fromUserId:s}=t,l=this.peerConnections.get(s);l&&await l.addIceCandidate(r)}catch(r){console.error("❌ Error handling ICE candidate:",r)}}async handleUserJoined(t){try{const{userId:r}=t;if(r===this.userId)return;console.log("👤 User joined voice room:",r),console.log("🔄 Creating peer connection and offer for:",r);const s=await this.createPeerConnection(r),l=await s.createOffer();await s.setLocalDescription(l),console.log("📤 Sending WebRTC offer to:",r),this.wsService.send({type:"webrtc_offer",data:{offer:l,targetUserId:r,fromUserId:this.userId}})}catch(r){console.error("❌ Error handling user joined:",r)}}handleUserLeft(t){var l;const{userId:r}=t;console.log("👋 User left voice room:",r);const s=this.peerConnections.get(r);s&&(s.close(),this.peerConnections.delete(r)),this.remoteUsers.delete(r),(l=this.onUserLeft)==null||l.call(this,r)}startVoiceActivityDetection(){if(!(!this.localStream||this.isMonitoringVoice))try{this.audioContext=new AudioContext;const t=this.audioContext.createMediaStreamSource(this.localStream);this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=256,t.connect(this.analyser),this.isMonitoringVoice=!0,this.monitorVoiceActivity()}catch(t){console.error("❌ Error starting voice activity detection:",t)}}monitorVoiceActivity(){if(!this.analyser||!this.isMonitoringVoice)return;const t=new Uint8Array(this.analyser.frequencyBinCount),r=()=>{if(!this.isMonitoringVoice)return;this.analyser.getByteFrequencyData(t);const s=t.reduce((d,g)=>d+g,0)/t.length,l=Math.round(s*10)/10,o=l>this.voiceActivityThreshold,i=Date.now(),a=o!==this.isSpeaking,c=i-this.lastVoiceActivitySent>this.voiceActivityDebounce;(a||c)&&this.onVoiceActivity&&this.userId&&(this.onVoiceActivity({userId:this.userId,level:l,isSpeaking:o}),this.lastVoiceActivitySent=i,this.isSpeaking=o),requestAnimationFrame(r)};r()}stopVoiceActivityDetection(){this.isMonitoringVoice=!1,this.audioContext&&(this.audioContext.close(),this.audioContext=null),this.analyser=null}get isConnected(){return this.isJoined}get mutedState(){return this.isMuted}get connectedUsers(){return Array.from(this.remoteUsers.values())}cleanup(){this.leaveRoom().catch(console.error)}}class Pp{constructor(){this.commonPhrases=["السلام عليكم","وعليكم السلام","أهلاً وسهلاً","مرحباً بكم","حياكم الله","أهلاً بك","مساء الخير","صباح الخير","تصبحون على خير","ليلة سعيدة","كيف حالكم؟","كيف الأحوال؟","إن شاء الله","ما شاء الله","بارك الله فيك","جزاك الله خيراً","الله يعطيك العافية","تسلم إيدك","الله يبارك فيك","ربي يحفظك","ممتاز!","رائع جداً","أحسنت","بالتوفيق","الله يوفقك","نعم صحيح","أوافقك الرأي","هذا صحيح","بالضبط","تماماً","من أين أنت؟","كم عمرك؟","ما اسمك؟","أين تسكن؟","ما هو عملك؟","هل أنت متزوج؟","كم طفل لديك؟","ما هوايتك؟","مع السلامة","إلى اللقاء","نراكم قريباً","الله معكم","في أمان الله","وداعاً","إلى اللقاء قريباً","هيا نلعب","من يريد اللعب؟","لعبة جميلة","أحب هذه اللعبة","فزت!","خسرت","لعبة أخرى؟","تحدي جديد","لا تحزن","كله خير","الله معك","ستتحسن الأمور","لا تيأس","كن قوياً","أنا معك","سأساعدك","شكراً لك","عفواً","لا شكر على واجب","أعتذر","آسف","لا بأس","لا مشكلة","بالعكس","أنا جائع","ما رأيكم في الطعام؟","هل تناولتم الطعام؟","طعام لذيذ","أحب هذا الطبق","وجبة شهية","الجو جميل اليوم","الطقس حار","الطقس بارد","يبدو أنه سيمطر","الشمس مشرقة","الجو معتدل"],this.recentMessages=[]}addMessage(t){this.recentMessages.unshift(t),this.recentMessages.length>50&&(this.recentMessages=this.recentMessages.slice(0,50))}getSuggestions(t){if(!t||t.length<2)return[];const r=t.toLowerCase().trim(),s=[];return this.commonPhrases.forEach(l=>{l.toLowerCase().includes(r)&&s.push(l)}),this.recentMessages.forEach(l=>{l.toLowerCase().includes(r)&&!s.includes(l)&&l.length>t.length&&s.push(l)}),s.sort((l,o)=>{const i=l.length-o.length;if(i!==0)return i;const a=this.commonPhrases.includes(l),c=this.commonPhrases.includes(o);return a&&!c?-1:!a&&c?1:0}).slice(0,5)}getQuickSuggestions(){return["السلام عليكم","كيف حالكم؟","أهلاً وسهلاً","شكراً لك","مع السلامة"]}addCustomPhrase(t){this.commonPhrases.includes(t)||this.commonPhrases.push(t)}}const Hc=new Pp,Rp=({onEmojiSelect:e,onClose:t})=>{const r=y.useRef(null),[s,l]=y.useState("faces"),o={faces:{name:"الوجوه",icon:n.jsx(p0,{className:"w-4 h-4"}),emojis:["😀","😃","😄","😁","😆","😅","🤣","😂","🙂","🙃","😉","😊","😇","🥰","😍","🤩","😘","😗","😚","😙","😋","😛","😜","🤪","😝","🤑","🤗","🤭","🤫","🤔","🤐","🤨","😐","😑","😶","😏","😒","🙄","😬","🤥","😔","😪","🤤","😴","😷","🤒","🤕","🤢","🤮","🤧","🥵","🥶","🥴","😵","🤯","🤠","🥳","😎","🤓","🧐"]},hearts:{name:"القلوب",icon:n.jsx(d0,{className:"w-4 h-4"}),emojis:["❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","♥️","💋","💌","💐","🌹","🌺","🌻","🌷","🌸","💒","💍"]},gestures:{name:"الإيماءات",icon:n.jsx(Wf,{className:"w-4 h-4"}),emojis:["👍","👎","👌","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤲","🤝","🙏","✍️","💪","🦾","🦿","🦵","🦶"]},symbols:{name:"الرموز",icon:n.jsx(cr,{className:"w-4 h-4"}),emojis:["💯","💢","💥","💫","💦","💨","🕳️","💣","💬","👁️‍🗨️","🗨️","🗯️","💭","💤","💮","♨️","💈","🛑","⭐","🌟","✨","⚡","🔥","💎","🏆","🥇","🥈","🥉","🎖️","🏅"]},food:{name:"الطعام",icon:n.jsx($f,{className:"w-4 h-4"}),emojis:["🍎","🍊","🍋","🍌","🍉","🍇","🍓","🫐","🍈","🍒","🍑","🥭","🍍","🥥","🥝","🍅","🍆","🥑","🥦","🥬","☕","🍵","🧃","🥤","🍺","🍻","🥂","🍷","🥃","🍸"]},games:{name:"الألعاب",icon:n.jsx(ca,{className:"w-4 h-4"}),emojis:["🎮","🕹️","🎯","🎲","🃏","🀄","🎰","🎳","🏓","🏸","⚽","🏀","🏈","⚾","🥎","🎾","🏐","🏉","🥏","🎱"]}};return y.useEffect(()=>{const i=a=>{r.current&&!r.current.contains(a.target)&&t()};return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[t]),n.jsxs("div",{ref:r,onClick:i=>i.stopPropagation(),className:"absolute bottom-12 left-0 right-0 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-2xl z-50 max-w-sm",children:[n.jsxs("div",{className:"flex border-b border-gray-600 p-2 overflow-x-auto",children:[Object.entries(o).map(([i,a])=>n.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),l(i)},className:`p-2 rounded-lg transition-colors flex-shrink-0 ${s===i?"bg-purple-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,title:a.name,children:a.icon},i)),n.jsx("button",{onClick:i=>{i.preventDefault(),i.stopPropagation(),t()},className:"ml-auto p-2 text-gray-400 hover:text-white flex-shrink-0",children:"✕"})]}),n.jsx("div",{className:"p-3 max-h-48 overflow-y-auto",children:n.jsx("div",{className:"grid grid-cols-8 gap-1",children:o[s].emojis.map((i,a)=>n.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),e(i),t()},className:"w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-lg",title:i,children:i},a))})})]})},zp=({suggestions:e,onSuggestionSelect:t,isVisible:r})=>!r||e.length===0?null:n.jsx("div",{className:"absolute bottom-full left-0 right-0 mb-1 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-lg z-40 max-h-32 overflow-y-auto",children:e.map((s,l)=>n.jsx("button",{onClick:()=>t(s),className:"w-full text-left px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0",children:s},l))}),Op=({user:e,wsService:t})=>{var sn,gt,Sr;const[r,s]=y.useState(null),[l,o]=y.useState([]),[i,a]=y.useState(!0),[c,d]=y.useState(null),[g,v]=y.useState(!1),[b,N]=y.useState(null),[j,k]=y.useState(!1),[S,m]=y.useState(!1),[u,p]=y.useState(!1),[E,L]=y.useState(""),[O,z]=y.useState(!1),[$,G]=y.useState(null),[x,U]=y.useState(null),[Y,ae]=y.useState("30"),[Te,Le]=y.useState(!1),[Ue,we]=y.useState([]),[f,C]=y.useState(!1),[_,T]=y.useState(null),[F,J]=y.useState(!1),[te,Ne]=y.useState("prompt"),[ne,ge]=y.useState(!1),ce=y.useRef(null),Lt=y.useRef(new Map),tr=y.useRef(null),lt=y.useRef(null),ns=async()=>{try{a(!0);const[w,P]=await Promise.all([K.getVoiceRoom(),K.getVoiceRoomMessages()]);s(w);const W=P.map(se=>({...se,sender:{...se.sender,role:se.sender.role||(se.sender.isAdmin?"admin":"member"),isAdmin:se.sender.isAdmin||!1,gender:se.sender.gender||"male"}}));o(W);const H=w.seats.find(se=>se.user&&se.user._id===e.id);H?(console.log("✅ User is in seat:",H.seatNumber),v(!0),N(H.seatNumber),m(H.isMuted)):(console.log("❌ User is not in any seat"),v(!1),N(null));const R=w.waitingQueue.some(se=>se.user._id===e.id);k(R),d(null)}catch(w){if(console.error("Error loading voice room:",w),w.message&&w.message.includes("مطرود من الغرفة الصوتية")){d(w.message),s({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,maxUsers:100,seats:[],waitingQueue:[],listeners:[],settings:{},isActive:!1}),o([]);return}d(w.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1)}};y.useEffect(()=>(ce.current=new I0(t),ce.current.onRemoteStreamAdded=(w,P)=>{const W=new Audio;W.srcObject=P,W.autoplay=!0,W.muted=ne,Lt.current.set(w,W)},ce.current.onRemoteStreamRemoved=w=>{const P=Lt.current.get(w);P&&(P.pause(),P.srcObject=null,Lt.current.delete(w))},ce.current.onVoiceActivity=w=>{s(P=>({...P,seats:P.seats.map(W=>{var H;return((H=W.user)==null?void 0:H._id)===e.id?{...W,isSpeaking:w.isSpeaking}:W})})),g&&t.send({type:"voice_activity",data:{userId:e.id,level:w.level,isSpeaking:w.isSpeaking}})},()=>{var w;(w=ce.current)==null||w.cleanup()}),[t,e.id]),y.useEffect(()=>{const w=R=>{const se={...R,sender:{...R.sender,role:R.sender.role||(R.sender.isAdmin?"admin":"member"),isAdmin:R.sender.isAdmin||!1,gender:R.sender.gender||"male"}};R.sender._id!==e.id&&(o(Ee=>[...Ee,se]),setTimeout(()=>{var Ee;(Ee=tr.current)==null||Ee.scrollIntoView({behavior:"smooth"})},100))},P=R=>{ns().then(()=>{R.action==="seat_joined"&&g&&R.userId!==e.id&&setTimeout(()=>{var se;(se=ce.current)==null||se.sendOffer(R.userId)},1e3)})},W=R=>{const{action:se,targetUserId:Ee,adminId:Pt,message:$e}=R;de(se,Ee),Ee===e.id&&(d($e||`تم تطبيق إجراء إداري: ${se}`),se==="kick"&&setTimeout(()=>{window.location.reload()},2e3))},H=R=>{const{userId:se,isSpeaking:Ee}=R;s(Pt=>({...Pt,seats:Pt.seats.map($e=>{var Cr;return((Cr=$e.user)==null?void 0:Cr._id)===se?{...$e,isSpeaking:Ee}:$e})}))};return t.onMessage("voice_room_message",w),t.onMessage("voice_room_update",P),t.onMessage("admin_action_update",W),t.onMessage("voice_activity",H),()=>{t.offMessage("voice_room_message",w),t.offMessage("voice_room_update",P),t.offMessage("admin_action_update",W)}},[t,g,e.id]),y.useEffect(()=>{var w;(w=tr.current)==null||w.scrollIntoView({behavior:"smooth"})},[l]),y.useEffect(()=>{ns(),console.log("User role:",e.role),console.log("User isAdmin:",e.isAdmin),console.log("User object:",e)},[]),y.useEffect(()=>{const w=()=>{const H=window.innerHeight<window.screen.height*.75;z(H)},P=()=>z(!0),W=()=>z(!1);return window.addEventListener("resize",w),lt.current&&(lt.current.addEventListener("focus",P),lt.current.addEventListener("blur",W)),()=>{window.removeEventListener("resize",w),lt.current&&(lt.current.removeEventListener("focus",P),lt.current.removeEventListener("blur",W))}},[]),y.useEffect(()=>{const w=P=>{$&&G(null),Te&&Le(!1)};return document.addEventListener("click",w),()=>document.removeEventListener("click",w)},[$,Te]),y.useEffect(()=>{const w=W=>{if(g)return W.preventDefault(),W.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},P=()=>{g&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",w),window.addEventListener("unload",P),()=>{window.removeEventListener("beforeunload",w),window.removeEventListener("unload",P)}},[g,e.id]);const Tl=async w=>{try{const P=await K.sendVoiceRoomMessage(w),W={_id:P.messageData._id,sender:{_id:e.id,username:e.username,role:e.role,isAdmin:e.isAdmin,gender:e.gender},content:w,timestamp:new Date().toISOString(),messageType:"text"};o(H=>[...H,W]),t.send({type:"voice_room_message",data:P.messageData})}catch(P){console.error("Error sending message:",P),d(P.message||"خطأ في إرسال الرسالة")}},rr=async()=>{try{p(!0),await K.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}})}catch(w){console.error("Error requesting mic:",w),d(w.message||"خطأ في طلب المايك")}finally{p(!1)}},ss=async w=>{try{if(p(!0),await K.joinVoiceSeat(w),v(!0),N(w),m(!1),ce.current&&(e!=null&&e.id)){console.log("🎤 Starting WebRTC voice chat for user:",e.username);const P=`voice-room-${(r==null?void 0:r.id)||"default"}`;await ce.current.joinRoom(P,e.id),console.log("✅ WebRTC voice chat started successfully")}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",w.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:w}})}catch(P){console.error("Error joining seat:",P),d(P.message||"خطأ في الانضمام للمقعد")}finally{p(!1)}},ht=async()=>{try{await K.leaveVoiceSeat(),v(!1),N(null),m(!1),ce.current&&ce.current.leaveRoom(),localStorage.removeItem("isInVoiceRoom"),localStorage.removeItem("voiceRoomSeat"),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:b}})}catch(w){console.error("Error leaving seat:",w),d(w.message||"خطأ في مغادرة المقعد")}},ls=async()=>{try{if(!g){d("يجب أن تكون في مقعد لاستخدام المايك");return}if(!ce.current){d("خدمة الصوت غير متاحة - جاري إعادة الاتصال..."),await initializeWebRTC();return}const w=!S;ce.current.setMute(w),m(w);try{await K.toggleMute(w)}catch(P){console.warn("Failed to update server mute state:",P)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:w}})}catch(w){console.error("Error toggling mute:",w),d("خطأ في تبديل كتم المايك"),m(!S)}},os=()=>{try{const w=!ne;ge(w),Lt.current.forEach(P=>{P.muted=w}),ce.current&&ce.current.setRemoteAudioMuted(w),localStorage.setItem("soundMuted",w.toString())}catch(w){console.error("Error toggling sound:",w),d("خطأ في تبديل كتم الصوت")}},is=w=>{const P=w.target.value;if(L(P),P.length>=2){const W=Hc.getSuggestions(P);we(W),C(W.length>0)}else C(!1)},ft=w=>{var P;L(w),C(!1),(P=lt.current)==null||P.focus()},Ll=w=>{var P;L(W=>W+w),Le(!1),(P=lt.current)==null||P.focus()},kr=async w=>{if(w.preventDefault(),!E.trim())return;const P=E.trim();L(""),C(!1),Hc.addMessage(P);try{await Tl(P)}catch{L(P)}},Ke=async(w,P,W)=>{try{let H;switch(w){case"kick":H=await K.kickUserFromVoiceRoom(P,W);break;case"mute":H=await K.muteUserInVoiceRoom(P);break;case"unmute":H=await K.unmuteUserInVoiceRoom(P);break;case"removeSeat":H=await K.removeUserFromSeat(P);break;case"removeQueue":H=await K.removeUserFromQueue(P);break;case"banChat":H=await K.banUserFromChat(P);break;case"unbanChat":H=await K.unbanUserFromChat(P);break}G(null),U(null),de(w,P),t.send({type:"admin_action_update",data:{action:w,targetUserId:P,adminId:e.id,duration:W,message:H==null?void 0:H.message}})}catch(H){console.error("Error performing admin action:",H),d(H.message||"خطأ في تنفيذ الإجراء الإداري")}},de=(w,P)=>{s(W=>{if(!W)return W;const H={...W};switch(w){case"kick":case"removeSeat":H.seats=H.seats.map(R=>R.user&&R.user._id===P?{...R,user:null,isMuted:!1}:R),H.waitingQueue=H.waitingQueue.filter(R=>R.user._id!==P);break;case"removeQueue":H.waitingQueue=H.waitingQueue.filter(R=>R.user._id!==P);break;case"mute":H.seats=H.seats.map(R=>R.user&&R.user._id===P?{...R,isMuted:!0}:R);break;case"unmute":H.seats=H.seats.map(R=>R.user&&R.user._id===P?{...R,isMuted:!1}:R);break;case"banChat":H.seats=H.seats.map(R=>R.user&&R.user._id===P?{...R,user:{...R.user,isChatBanned:!0}}:R),H.waitingQueue=H.waitingQueue.map(R=>R.user._id===P?{...R,user:{...R.user,isChatBanned:!0}}:R);break;case"unbanChat":H.seats=H.seats.map(R=>R.user&&R.user._id===P?{...R,user:{...R.user,isChatBanned:!1}}:R),H.waitingQueue=H.waitingQueue.map(R=>R.user._id===P?{...R,user:{...R.user,isChatBanned:!1}}:R);break}return H})},pt=async()=>{const w=parseInt(Y);await Ke("kick",x,w)},Ye=w=>{T(w),J(!0)},nn=()=>{T(null),J(!1)};y.useEffect(()=>{const w=P=>{P.key==="Escape"&&F&&nn()};return document.addEventListener("keydown",w),()=>{document.removeEventListener("keydown",w)}},[F]),y.useEffect(()=>{(async()=>{try{const W=await navigator.permissions.query({name:"microphone"});Ne(W.state),W.addEventListener("change",()=>{Ne(W.state)})}catch{console.log("Permission API not supported")}})(),localStorage.getItem("soundMuted")==="true"&&ge(!0)},[]);const as=w=>new Date(w).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1});if(i)return n.jsx("div",{className:"flex items-center justify-center h-full",children:n.jsxs("div",{className:"text-center",children:[n.jsx(fr,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),n.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})});if(c){const w=c.includes("مطرود من الغرفة الصوتية");return n.jsx("div",{className:"p-4",children:n.jsxs("div",{className:`border rounded-lg p-6 text-center max-w-md mx-auto ${w?"bg-orange-900/20 border-orange-500/30":"bg-red-900/20 border-red-500/30"}`,children:[n.jsx(a0,{className:`w-12 h-12 mx-auto mb-4 ${w?"text-orange-400":"text-red-400"}`}),w?n.jsxs(n.Fragment,{children:[n.jsx("h3",{className:"text-orange-400 font-bold text-lg mb-2",children:"تم طردك من الغرفة الصوتية"}),n.jsx("p",{className:"text-orange-300 mb-4 text-sm leading-relaxed",children:c}),n.jsx("p",{className:"text-gray-400 text-xs",children:"سيتم السماح لك بالدخول مرة أخرى بعد انتهاء المدة المحددة"})]}):n.jsxs(n.Fragment,{children:[n.jsx("p",{className:"text-red-400 mb-4",children:c}),n.jsx("button",{onClick:ns,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white",children:"إعادة المحاولة"})]})]})})}return r?n.jsxs("div",{className:"h-full flex flex-col bg-gradient-to-br from-gray-900/50 to-purple-900/30",children:[n.jsxs("div",{className:"p-3 border-b border-purple-500/20",children:[n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsxs("h1",{className:"text-sm font-bold text-white flex items-center gap-2",children:[n.jsx(Kn,{className:"w-4 h-4 text-purple-400"}),"INFINITY ROOM"]}),n.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-300",children:[n.jsx(br,{className:"w-3 h-3"}),n.jsxs("span",{children:[(((sn=r.seats)==null?void 0:sn.filter(w=>w.user).length)||0)+(((gt=r.listeners)==null?void 0:gt.length)||0),"/",r.maxUsers||100]}),n.jsxs("span",{className:"text-gray-500",children:["(🎤 ",((Sr=r.seats)==null?void 0:Sr.filter(w=>w.user).length)||0,"/5)"]}),(e.role==="admin"||e.isAdmin)&&n.jsx("span",{className:"bg-red-600 text-white px-2 py-1 rounded text-xs",children:"ADMIN"})]})]}),g&&n.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 mb-3",children:[n.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[n.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),n.jsxs("span",{className:"text-xs text-green-400 font-medium",children:["متصل - مقعد ",b]})]}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsxs("button",{onClick:ls,className:`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2 font-medium ${S?"bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/25":"bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/25"}`,children:[S?n.jsx(Jn,{className:"w-4 h-4"}):n.jsx(wt,{className:"w-4 h-4"}),n.jsx("span",{className:"text-sm",children:S?"إلغاء كتم":"كتم مايك"})]}),n.jsxs("button",{onClick:os,className:`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2 font-medium ${ne?"bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/25":"bg-blue-600 hover:bg-blue-700 text-white shadow-lg shadow-blue-600/25"}`,children:[ne?n.jsx(co,{className:"w-4 h-4"}):n.jsx(Kn,{className:"w-4 h-4"}),n.jsx("span",{className:"text-sm",children:ne?"إلغاء كتم":"كتم صوت"})]}),n.jsx("button",{onClick:ht,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors text-sm font-medium shadow-lg shadow-red-600/25",children:"مغادرة"})]})]}),n.jsxs("div",{className:"flex items-center gap-3 p-2 bg-gray-800/30 rounded-lg mb-3",children:[n.jsx("div",{className:"flex items-center gap-2",children:n.jsx("div",{className:`w-2 h-2 rounded-full ${te==="granted"?"bg-green-500":te==="denied"?"bg-red-500":"bg-yellow-500"}`})}),te==="denied"&&n.jsx("span",{className:"text-xs text-red-400",children:"مرفوض"})]})]}),n.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[n.jsxs("div",{className:"p-3 border-b border-gray-700/50",children:[n.jsx("div",{className:"flex justify-center gap-2 mb-2 overflow-x-auto px-1",children:r.seats.map(w=>n.jsx("div",{className:"flex flex-col items-center flex-shrink-0",children:w.user?n.jsxs("div",{className:"relative",children:[n.jsxs("div",{className:`relative w-14 h-14 rounded-full p-1 ${w.isSpeaking&&!w.isMuted?"bg-gradient-to-r from-green-400 to-green-500 shadow-lg shadow-green-500/50 animate-pulse":w.user._id===e.id?"bg-gradient-to-r from-green-500 to-green-600":w.user.role==="admin"||w.user.isAdmin?"bg-gradient-to-r from-red-500 to-red-600 shadow-lg shadow-red-500/50":"bg-gradient-to-r from-blue-500 to-purple-600"} shadow-lg`,children:[n.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:w.user.profileImage?n.jsx("img",{src:w.user.profileImage,alt:w.user.username,className:"w-full h-full object-cover"}):n.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:n.jsx("span",{className:"text-white font-bold text-lg",children:w.user.username.charAt(0).toUpperCase()})})}),n.jsx("div",{className:`absolute -bottom-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center shadow-lg ${w.isMuted?"bg-red-600":w.isSpeaking?"bg-green-500 animate-pulse shadow-green-500/50":"bg-green-600"}`,children:w.isMuted?n.jsx(Jn,{className:"w-3 h-3 text-white"}):n.jsx(wt,{className:`w-3 h-3 text-white ${w.isSpeaking?"animate-pulse":""}`})}),w.isSpeaking&&!w.isMuted&&n.jsxs("div",{className:"absolute inset-0 rounded-full",children:[n.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),n.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),n.jsxs("div",{className:"text-center mt-1 relative",children:[n.jsxs("div",{className:"flex items-center justify-center gap-1",children:[n.jsxs("div",{className:"flex flex-col items-center",children:[n.jsx("h3",{className:"font-medium text-white text-xs mb-1 truncate max-w-12",children:w.user.username}),n.jsxs("div",{className:"flex gap-1",children:[w.isMuted&&n.jsx("div",{className:"bg-red-600 rounded-full p-1",title:"مكتوم",children:n.jsx(co,{className:"w-2 h-2 text-white"})}),w.user.isChatBanned&&n.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:n.jsx(Ms,{className:"w-2 h-2 text-white"})})]})]}),(e.role==="admin"||e.isAdmin)&&w.user._id!==e.id&&n.jsx("button",{onClick:P=>{P.stopPropagation(),console.log("Admin menu clicked for user:",w.user.username),G($===w.user._id?null:w.user._id)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:n.jsx(Pc,{className:"w-4 h-4"})})]}),$===w.user._id&&(e.role==="admin"||e.isAdmin)&&n.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:n.jsxs("div",{className:"flex flex-col gap-1",children:[n.jsxs("button",{onClick:()=>Ke("removeSeat",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[n.jsx(Tf,{className:"w-3 h-3"}),"إنزال"]}),w.isMuted?n.jsxs("button",{onClick:()=>Ke("unmute",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[n.jsx(Df,{className:"w-3 h-3"}),"إلغاء كتم"]}):n.jsxs("button",{onClick:()=>Ke("mute",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-yellow-400 hover:bg-yellow-900/30 rounded transition-colors",children:[n.jsx(co,{className:"w-3 h-3"}),"كتم"]}),w.user.isChatBanned?n.jsxs("button",{onClick:()=>Ke("unbanChat",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[n.jsx(Rc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):n.jsxs("button",{onClick:()=>Ke("banChat",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[n.jsx(Ms,{className:"w-3 h-3"}),"منع الكتابة"]}),n.jsxs("button",{onClick:()=>{G(null),U(w.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[n.jsx(hi,{className:"w-3 h-3"}),"طرد"]})]})})]})]}):n.jsx("div",{className:"flex flex-col items-center",children:n.jsx("button",{onClick:()=>!g&&!j?ss(w.seatNumber):null,disabled:u||g||j,className:"relative w-14 h-14 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:n.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center",children:n.jsx(_l,{className:"w-5 h-5 text-gray-400"})})})})},w.seatNumber))}),!g&&!j&&r.seats.every(w=>w.user)&&n.jsxs("button",{onClick:rr,disabled:u,className:"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded-lg text-white font-medium transition-colors flex items-center justify-center gap-2 mb-4",children:[n.jsx(wt,{className:"w-4 h-4"}),u?"جاري الطلب...":"طلب المايك"]}),r.waitingQueue.length>0&&n.jsxs("div",{className:"bg-yellow-900/20 rounded-lg p-2 border border-yellow-500/20",children:[n.jsxs("h3",{className:"text-sm font-bold text-white mb-2 flex items-center gap-2",children:[n.jsx(mr,{className:"w-4 h-4 text-yellow-400"}),"قائمة الانتظار (",r.waitingQueue.length,")"]}),n.jsx("div",{className:"space-y-2",children:r.waitingQueue.map((w,P)=>n.jsxs("div",{className:"flex items-center gap-2 p-2 bg-gray-800/50 rounded text-sm relative",children:[n.jsx("div",{className:"w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-xs",children:P+1}),n.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[n.jsx("span",{className:"text-white",children:w.user.username}),n.jsx("div",{className:"flex gap-1",children:w.user.isChatBanned&&n.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:n.jsx(Ms,{className:"w-2 h-2 text-white"})})})]}),w.user._id===e.id&&n.jsx("span",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"}),(e.role==="admin"||e.isAdmin)&&w.user._id!==e.id&&n.jsx("button",{onClick:W=>{W.stopPropagation(),console.log("Admin menu clicked for queue user:",w.user.username),G($===`queue_${w.user._id}`?null:`queue_${w.user._id}`)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:n.jsx(Pc,{className:"w-4 h-4"})}),$===`queue_${w.user._id}`&&(e.role==="admin"||e.isAdmin)&&n.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:n.jsxs("div",{className:"flex flex-col gap-1",children:[n.jsxs("button",{onClick:()=>Ke("removeQueue",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[n.jsx(x0,{className:"w-3 h-3"}),"إزالة"]}),w.user.isChatBanned?n.jsxs("button",{onClick:()=>Ke("unbanChat",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[n.jsx(Rc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):n.jsxs("button",{onClick:()=>Ke("banChat",w.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[n.jsx(Ms,{className:"w-3 h-3"}),"منع الكتابة"]}),n.jsxs("button",{onClick:()=>{G(null),U(w.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[n.jsx(hi,{className:"w-3 h-3"}),"طرد"]})]})})]},w.user._id))})]})]}),n.jsxs("div",{className:"h-96 flex flex-col",children:[n.jsx("div",{className:"flex-1 overflow-y-auto p-2",children:n.jsxs("div",{className:"flex flex-col space-y-1",children:[l.length===0?n.jsxs("div",{className:"text-center text-gray-400 py-4",children:[n.jsx(hr,{className:"w-6 h-6 mx-auto mb-2 opacity-50"}),n.jsx("p",{className:"text-xs",children:"لا توجد رسائل بعد"})]}):l.map(w=>{const P=H=>H.gender==="female"?"text-pink-400":H.gender==="male"?"text-blue-400":["فاطمة","عائشة","خديجة","زينب","مريم","سارة","نور","هند","ليلى","أمل","رنا","دانا","لينا","ريم","نادية","سلمى","ياسمين","روان","جنى","تالا"].some(Ee=>H.username.includes(Ee))?"text-pink-400":"text-blue-400",W=()=>w.messageType==="system"?"bg-blue-900/30 border border-blue-500/30 text-blue-200 ml-auto max-w-[85%]":w.sender.role==="admin"||w.sender.isAdmin?"bg-red-900/60 border-2 border-red-400/50 text-white ml-auto max-w-[75%] shadow-xl shadow-red-500/30 ring-1 ring-red-400/20":w.sender._id===e.id?"bg-purple-900/50 border border-purple-500/30 text-white ml-auto max-w-[75%]":"bg-gray-800/50 border border-gray-600/30 text-gray-200 ml-auto max-w-[75%]";return n.jsxs("div",{className:`px-3 py-1.5 rounded-xl text-sm ${W()}`,children:[n.jsxs("div",{className:"flex items-center justify-between mb-0.5 gap-2",children:[n.jsxs("span",{className:`font-medium text-xs flex-shrink-0 ${w.sender.role==="admin"||w.sender.isAdmin?"text-red-300 font-bold":P(w.sender)}`,children:[w.sender.role==="admin"||w.sender.isAdmin?"👑 ":"",w.sender.username]}),n.jsx("span",{className:"text-xs opacity-60 flex-shrink-0",children:as(w.timestamp)})]}),n.jsx("div",{className:"text-sm leading-snug",children:w.content})]},w._id)}),n.jsx("div",{ref:tr})]})}),n.jsx("div",{className:"px-4 py-2 border-t border-gray-700/50 bg-gray-900/30",children:n.jsxs("div",{className:"flex justify-center gap-4",children:[n.jsxs("button",{onClick:()=>Ye("/speed-challenge.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"تحدي السرعة",children:[n.jsx(v0,{className:"w-6 h-6 text-yellow-400 group-hover:text-yellow-300"}),n.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"سرعة"})]}),n.jsxs("button",{onClick:()=>Ye("/game8.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"صناديق الحظ",children:[n.jsx(g0,{className:"w-6 h-6 text-green-400 group-hover:text-green-300"}),n.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"صناديق"})]}),n.jsxs("button",{onClick:()=>Ye("/mind-puzzles.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"ألغاز العقل",children:[n.jsx(f0,{className:"w-6 h-6 text-blue-400 group-hover:text-blue-300"}),n.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ألغاز"})]}),n.jsxs("button",{onClick:()=>Ye("/fruit-catching.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"قطف الفواكه",children:[n.jsx(l0,{className:"w-6 h-6 text-red-400 group-hover:text-red-300"}),n.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"فواكه"})]}),n.jsxs("button",{onClick:()=>Ye("/memory-match.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الذاكرة",children:[n.jsx(o0,{className:"w-6 h-6 text-purple-400 group-hover:text-purple-300"}),n.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ذاكرة"})]}),n.jsxs("button",{onClick:()=>Ye("/forest-game.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الغابة",children:[n.jsx(y0,{className:"w-6 h-6 text-green-600 group-hover:text-green-500"}),n.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"غابة"})]})]})}),n.jsx("div",{className:"p-4 mb-4 border-t border-gray-700/50 bg-gray-900/50",children:n.jsxs("div",{className:"relative",children:[n.jsx(zp,{suggestions:Ue,onSuggestionSelect:ft,isVisible:f}),Te&&n.jsx(Rp,{onEmojiSelect:Ll,onClose:()=>Le(!1)}),n.jsxs("form",{onSubmit:kr,className:"flex gap-2",children:[n.jsxs("div",{className:"flex-1 relative",children:[n.jsx("input",{ref:lt,type:"text",value:E,onChange:is,placeholder:"اكتب رسالتك...",maxLength:500,className:"w-full px-3 py-2 pr-10 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-purple-500/50 text-sm",onFocus:()=>C(Ue.length>0),onBlur:()=>setTimeout(()=>C(!1),200)}),n.jsx("button",{type:"button",onClick:w=>{w.preventDefault(),w.stopPropagation(),Le(!Te)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-yellow-400 transition-colors",children:n.jsx(p0,{className:"w-4 h-4"})})]}),n.jsx("button",{type:"submit",disabled:!E.trim(),className:"px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-lg text-white transition-colors",children:n.jsx(An,{className:"w-4 h-4"})}),!j&&n.jsx("button",{type:"button",onClick:rr,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors",children:n.jsx(wt,{className:"w-4 h-4"})})]})]})})]})]}),x&&n.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:n.jsxs("div",{className:"bg-gray-800 rounded-lg border border-red-500 p-6 w-full max-w-sm",children:[n.jsx("h3",{className:"text-white font-bold text-lg mb-4 text-center",children:"اختر مدة الطرد"}),n.jsxs("div",{className:"space-y-4",children:[n.jsxs("div",{children:[n.jsx("label",{className:"block text-gray-300 text-sm mb-2",children:"المدة:"}),n.jsxs("select",{value:Y,onChange:w=>ae(w.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500",children:[n.jsx("option",{value:"30",children:"30 دقيقة"}),n.jsx("option",{value:"60",children:"ساعة واحدة"}),n.jsx("option",{value:"180",children:"3 ساعات"}),n.jsx("option",{value:"360",children:"6 ساعات"}),n.jsx("option",{value:"720",children:"12 ساعة"}),n.jsx("option",{value:"1440",children:"يوم واحد"}),n.jsx("option",{value:"4320",children:"3 أيام"}),n.jsx("option",{value:"10080",children:"أسبوع واحد"}),n.jsx("option",{value:"43200",children:"شهر واحد"}),n.jsx("option",{value:"525600",children:"سنة واحدة"})]})]}),n.jsxs("div",{className:"flex gap-3",children:[n.jsx("button",{onClick:()=>U(null),className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"إلغاء"}),n.jsx("button",{onClick:pt,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors",children:"طرد"})]})]})]})}),F&&_&&n.jsxs("div",{className:"absolute inset-0 bg-gray-900/95 backdrop-blur-sm z-50 flex flex-col",children:[n.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-800/90 border-b border-gray-700",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),n.jsx("span",{className:"text-white font-medium",children:"اللعبة نشطة"})]}),n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("button",{onClick:()=>{_&&window.open(_,"_blank")},className:"px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors",children:"فتح في نافذة جديدة"}),n.jsx("button",{onClick:nn,className:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",title:"إغلاق اللعبة",children:n.jsx(da,{className:"w-5 h-5"})})]})]}),n.jsxs("div",{className:"flex-1 relative",children:[n.jsx("iframe",{src:_,className:"w-full h-full border-0",title:"لعبة مدمجة",allow:"fullscreen; autoplay; microphone; camera",sandbox:"allow-scripts allow-same-origin allow-forms allow-popups allow-modals"}),n.jsx("div",{className:"absolute inset-0 bg-gray-900 flex items-center justify-center pointer-events-none opacity-0 transition-opacity duration-300",id:"game-loading",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),n.jsx("p",{className:"text-white",children:"جاري تحميل اللعبة..."})]})})]}),n.jsx("div",{className:"p-3 bg-gray-800/90 border-t border-gray-700",children:n.jsxs("div",{className:"flex items-center justify-between text-sm",children:[n.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[n.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),n.jsx("span",{children:"يمكنك الاستمرار في الدردشة أثناء اللعب"})]}),n.jsx("div",{className:"text-gray-500",children:"اضغط ESC للخروج السريع"})]})})]})]}):n.jsx("div",{className:"p-4 text-center text-gray-400",children:n.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},Up=({userData:e,onLogout:t,onUpdateProfile:r,wsService:s})=>{const[l,o]=y.useState("games"),[i,a]=y.useState(e),[c,d]=y.useState(["games"]);y.useEffect(()=>{e&&(console.log("🔄 MobileDashboard: userData updated from parent:",e),a(e))},[e]);const g=S=>{console.log("🔄 MobileDashboard: Updating profile data:",S);const m={...i,...S};a(m),r&&r(m)};y.useEffect(()=>{a(e)},[e]);const v=S=>{console.log("🔄 Navigating to tab:",S),S!==l&&requestAnimationFrame(()=>{d(m=>[...m,S]),o(S),console.log("✅ Tab changed to:",S)})},b=()=>{if(c.length>1){const S=[...c];S.pop();const m=S[S.length-1];d(S),o(m)}},N=c.length>1,j=()=>{switch(l){case"games":return"مركز الألعاب";case"leaderboard":return"لوحة المتصدرين";case"voice":return"الغرفة الصوتية";case"profile":return"الملف الشخصي";case"admin":return"لوحة الإدارة";default:return"INFINITY BOX"}},k=()=>{switch(l){case"games":return n.jsx(Vs,{setActiveTab:v});case"leaderboard":return n.jsx($p,{});case"voice":return s?n.jsx(Op,{user:i,wsService:s}):n.jsx("div",{className:"p-4 text-center text-red-400",children:"خدمة WebSocket غير متاحة"});case"profile":return n.jsx(Lp,{userData:i,onUpdateProfile:g,onLogout:t,isOwner:!0});case"admin":return e!=null&&e.isAdmin?n.jsx(fi,{userData:e,onLogout:t}):n.jsx(Vs,{setActiveTab:v});default:return n.jsx(Vs,{setActiveTab:v})}};return l==="admin"&&(e!=null&&e.isAdmin)?n.jsx(fi,{userData:e,onLogout:t}):n.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-950 via-purple-950 to-slate-900 text-white flex flex-col relative overflow-hidden",children:[n.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[n.jsx("div",{className:"absolute -top-24 -left-24 w-72 h-72 bg-green-800 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"}),n.jsx("div",{className:"absolute -bottom-24 -right-24 w-72 h-72 bg-red-900 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"}),n.jsx("div",{className:"absolute inset-0 opacity-20",children:n.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((S,m)=>n.jsx("div",{className:`${m%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${m*150}ms`,animationDuration:`${2+m%3}s`}},m))})}),n.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"}),n.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-20",children:n.jsx("div",{className:"text-[18rem] md:text-[24rem] font-black text-cyan-500 animate-spin",style:{animationDuration:"40s"},children:"∞"})})]}),n.jsxs("div",{className:"relative z-10 flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-b border-purple-400/30 sticky top-0 z-40",children:[n.jsxs("div",{className:"flex items-center gap-3",children:[N&&n.jsx("button",{onClick:b,className:"p-2 rounded-lg hover:bg-slate-700/50 transition-colors",children:n.jsx(i0,{className:"w-5 h-5 text-white"})}),n.jsxs("div",{className:"relative",children:[n.jsx("img",{src:(i==null?void 0:i.profileImage)||"/images/default-avatar.png",alt:i==null?void 0:i.username,className:"w-9 h-9 rounded-full border-2 border-white/30 object-cover",onError:S=>{S.currentTarget.src="/images/default-avatar.png"}}),n.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),n.jsx("div",{children:n.jsx("h1",{className:"text-lg font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:l==="games"?(i==null?void 0:i.username)||"اللاعب":j()})})]}),n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[n.jsx(Il,{className:"w-5 h-5 text-yellow-300"}),n.jsx("span",{className:"text-yellow-300 text-sm font-bold",children:(i==null?void 0:i.goldCoins)||0})]}),n.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[n.jsx(cr,{className:"w-5 h-5 text-emerald-300"}),n.jsx("span",{className:"text-emerald-300 text-sm font-bold",children:(i==null?void 0:i.pearls)||0})]}),n.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-red-600/20 transition-colors",children:n.jsx(m0,{className:"w-5 h-5 text-red-400"})})]})]}),n.jsx("div",{className:"relative z-10 flex-1 overflow-hidden",children:k()}),n.jsx("div",{className:"relative z-10 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-t border-purple-400/30 px-2 py-2 sticky bottom-0 z-40",children:n.jsxs("div",{className:"flex items-center justify-around",children:[n.jsxs("button",{onClick:()=>v("games"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="games"?"bg-cyan-700/40 text-cyan-300":"text-gray-400 hover:text-cyan-200 hover:bg-slate-700/50"}`,children:[n.jsx(ca,{className:"w-6 h-6"}),n.jsx("span",{className:"text-[11px] font-medium",children:"الألعاب"})]}),n.jsxs("button",{onClick:()=>v("voice"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="voice"?"bg-purple-700/40 text-purple-300":"text-gray-400 hover:text-purple-200 hover:bg-slate-700/50"}`,children:[n.jsx(Kn,{className:"w-6 h-6"}),n.jsx("span",{className:"text-[11px] font-medium",children:"الغرفة الصوتية"})]}),n.jsxs("button",{onClick:()=>v("leaderboard"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="leaderboard"?"bg-amber-700/40 text-amber-300":"text-gray-400 hover:text-amber-200 hover:bg-slate-700/50"}`,children:[n.jsx(Zn,{className:"w-6 h-6"}),n.jsx("span",{className:"text-[11px] font-medium",children:"المتصدرين"})]}),n.jsxs("button",{onClick:()=>v("profile"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="profile"?"bg-emerald-700/40 text-emerald-300":"text-gray-400 hover:text-emerald-200 hover:bg-slate-700/50"}`,children:[n.jsx(pr,{className:"w-6 h-6"}),n.jsx("span",{className:"text-[11px] font-medium",children:"الملف الشخصي"})]}),(e==null?void 0:e.isAdmin)&&n.jsxs("button",{onClick:()=>v("admin"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="admin"?"bg-rose-700/40 text-rose-300":"text-gray-400 hover:text-rose-200 hover:bg-slate-700/50"}`,children:[n.jsx(Mn,{className:"w-6 h-6"}),n.jsx("span",{className:"text-[11px] font-medium",children:"الإدارة"})]})]})})]})},$p=()=>n.jsx("div",{className:"p-4 h-full overflow-y-auto",children:n.jsxs("div",{className:"text-center py-20",children:[n.jsx(Zn,{className:"w-16 h-16 text-yellow-400 mx-auto mb-4"}),n.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"لوحة المتصدرين"}),n.jsx("p",{className:"text-gray-400",children:"قريباً..."})]})}),Fp=({seats:e,waitingQueue:t,currentUser:r,isInSeat:s,currentSeatNumber:l,isInWaitingQueue:o,isConnecting:i,onJoinSeat:a,onRequestMic:c,onCancelMicRequest:d})=>{const g=j=>{const k=new Date,S=new Date(j),m=k.getTime()-S.getTime(),u=Math.floor(m/(1e3*60));return u<1?"الآن":u<60?`${u} دقيقة`:`${Math.floor(u/60)} ساعة`},v=()=>{const j=t.findIndex(k=>k.user._id===r.id);return j>=0?j+1:0},b=e.filter(j=>!j.user),N=b.length>0&&!s&&!o;return n.jsxs("div",{className:"space-y-6",children:[n.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-purple-900/30 rounded-xl p-6 border border-purple-500/20",children:[n.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[n.jsx(Kn,{className:"w-6 h-6 text-purple-400"}),"المقاعد الصوتية"]}),n.jsx("div",{className:"flex flex-wrap justify-center gap-6",children:e.map(j=>n.jsx("div",{className:"flex flex-col items-center",children:j.user?n.jsxs("div",{className:"relative",children:[n.jsxs("div",{className:`relative w-20 h-20 rounded-full p-1 transition-all duration-300 shadow-lg ${j.user._id===r.id?"bg-gradient-to-r from-green-500 to-green-600 shadow-green-500/30":"bg-gradient-to-r from-blue-500 to-purple-600 shadow-blue-500/30"}`,children:[n.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:j.user.profileImage?n.jsx("img",{src:j.user.profileImage,alt:j.user.username,className:"w-full h-full object-cover"}):n.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:n.jsx("span",{className:"text-white font-bold text-xl",children:j.user.username.charAt(0).toUpperCase()})})}),n.jsx("div",{className:`absolute -bottom-1 -right-1 w-7 h-7 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${j.isMuted?"bg-red-600":j.isSpeaking?"bg-green-600 animate-pulse shadow-green-500/50":"bg-gray-600"}`,children:j.isMuted?n.jsx(Jn,{className:"w-4 h-4 text-white"}):n.jsx(wt,{className:"w-4 h-4 text-white"})}),j.isSpeaking&&!j.isMuted&&n.jsxs("div",{className:"absolute inset-0 rounded-full",children:[n.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),n.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),n.jsxs("div",{className:"text-center mt-3 max-w-20",children:[n.jsx("h3",{className:"font-semibold text-white text-sm mb-1 truncate",children:j.user.username}),n.jsxs("p",{className:"text-xs text-gray-400 mb-2",children:["#",j.user.playerId]}),j.joinedAt&&n.jsxs("div",{className:"flex items-center justify-center gap-1 text-xs text-gray-400",children:[n.jsx(mr,{className:"w-3 h-3"}),n.jsx("span",{children:g(j.joinedAt)})]})]})]}):n.jsxs("div",{className:"flex flex-col items-center",children:[n.jsx("button",{onClick:()=>N?a(j.seatNumber):null,disabled:i||!N,className:"relative w-20 h-20 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:n.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center hover:border-purple-400 transition-colors",children:n.jsx(_l,{className:"w-8 h-8 text-gray-400"})})}),n.jsx("div",{className:"text-center mt-3",children:n.jsx("p",{className:"text-gray-400 text-sm",children:"مقعد فارغ"})})]})},j.seatNumber))}),n.jsxs("div",{className:"mt-6 flex flex-wrap gap-3 justify-center",children:[!s&&!o&&b.length===0&&n.jsxs("button",{onClick:c,disabled:i,className:"px-6 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white font-medium transition-colors flex items-center gap-2",children:[n.jsx(wt,{className:"w-4 h-4"}),i?"جاري الطلب...":"طلب المايك"]}),o&&n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsxs("div",{className:"px-4 py-2 bg-yellow-900/50 border border-yellow-500/50 rounded-lg text-yellow-300 flex items-center gap-2",children:[n.jsx(mr,{className:"w-4 h-4"}),n.jsxs("span",{children:["في قائمة الانتظار (المركز ",v(),")"]})]}),n.jsx("button",{onClick:d,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors",children:"إلغاء الطلب"})]})]})]}),t.length>0&&n.jsxs("div",{className:"bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-xl p-6 border border-yellow-500/20",children:[n.jsxs("h3",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[n.jsx(mr,{className:"w-5 h-5 text-yellow-400"}),"قائمة انتظار المايك (",t.length,")"]}),n.jsx("div",{className:"space-y-3",children:t.map((j,k)=>n.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",children:[n.jsx("div",{className:"w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:k+1}),n.jsxs("div",{className:"flex-1",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"font-medium text-white",children:j.user.username}),n.jsxs("span",{className:"text-xs text-gray-400",children:["#",j.user.playerId]})]}),n.jsxs("div",{className:"text-xs text-gray-400",children:["طلب منذ ",g(j.requestedAt)]})]}),j.user._id===r.id&&n.jsx("div",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"})]},j.user._id))})]})]})},Bp=({messages:e,currentUser:t,isInWaitingQueue:r,onSendMessage:s,onRequestMic:l})=>{const[o,i]=y.useState(""),[a,c]=y.useState(!1),d=y.useRef(null),g=y.useRef(null);y.useEffect(()=>{var S;(S=d.current)==null||S.scrollIntoView({behavior:"smooth"})},[e]),y.useEffect(()=>{var S;(S=g.current)==null||S.focus()},[]);const v=async S=>{var u;if(S.preventDefault(),!o.trim()||a)return;const m=o.trim();i(""),c(!0);try{await s(m)}catch(p){console.error("Error sending message:",p),i(m)}finally{c(!1),(u=g.current)==null||u.focus()}},b=S=>{S.key==="Enter"&&!S.shiftKey&&(S.preventDefault(),v(S))},N=S=>new Date(S).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1}),j=S=>{switch(S){case"system":return n.jsx(a0,{className:"w-4 h-4 text-blue-400"});case"mic_request":return n.jsx(wt,{className:"w-4 h-4 text-yellow-400"});default:return n.jsx(hr,{className:"w-4 h-4 text-gray-400"})}},k=(S,m)=>{const u=m===t.id;switch(S){case"system":return"bg-blue-900/30 border-blue-500/30 text-blue-200";case"mic_request":return"bg-yellow-900/30 border-yellow-500/30 text-yellow-200";default:return u?"bg-purple-900/50 border-purple-500/30 text-white":"bg-gray-800/50 border-gray-600/30 text-gray-200"}};return n.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-blue-900/30 rounded-xl border border-blue-500/20 flex flex-col h-[600px]",children:[n.jsxs("div",{className:"p-4 border-b border-gray-700/50",children:[n.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[n.jsx(hr,{className:"w-5 h-5 text-blue-400"}),"المحادثة النصية"]}),n.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:[e.length," رسالة"]})]}),n.jsx("div",{className:"flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800",children:n.jsxs("div",{className:"flex flex-col space-y-3",children:[e.length===0?n.jsxs("div",{className:"text-center text-gray-400 py-8",children:[n.jsx(hr,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),n.jsx("p",{children:"لا توجد رسائل بعد"}),n.jsx("p",{className:"text-sm mt-1",children:"ابدأ المحادثة!"})]}):e.map(S=>n.jsxs("div",{className:`p-3 rounded-lg border ${k(S.messageType,S.sender._id)} ${S.messageType!=="system"&&S.sender._id===t.id?"max-w-[60%] self-end ml-auto":""}`,children:[n.jsxs("div",{className:"flex items-center justify-between mb-2",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[j(S.messageType),n.jsx("span",{className:"font-medium text-sm",children:S.sender.username}),n.jsxs("span",{className:"text-xs opacity-60",children:["#",S.sender.playerId]})]}),n.jsxs("div",{className:"flex items-center gap-1 text-xs opacity-60",children:[n.jsx(mr,{className:"w-3 h-3"}),n.jsx("span",{children:N(S.timestamp)})]})]}),n.jsx("div",{className:"text-sm leading-relaxed",children:S.content})]},S._id)),n.jsx("div",{ref:d})]})}),n.jsxs("div",{className:"p-4 border-t border-gray-700/50",children:[n.jsxs("form",{onSubmit:v,className:"flex gap-2",children:[n.jsxs("div",{className:"flex-1 relative",children:[n.jsx("input",{ref:g,type:"text",value:o,onChange:S=>i(S.target.value),onKeyPress:b,placeholder:"اكتب رسالتك هنا...",maxLength:500,disabled:a,className:"w-full px-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-gray-800/70 transition-all disabled:opacity-50 disabled:cursor-not-allowed"}),n.jsxs("div",{className:"absolute bottom-1 left-2 text-xs text-gray-500",children:[o.length,"/500"]})]}),n.jsxs("button",{type:"submit",disabled:!o.trim()||a,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white transition-colors flex items-center gap-2",title:"إرسال الرسالة",children:[n.jsx(An,{className:"w-4 h-4"}),a?"جاري الإرسال...":"إرسال"]}),!r&&n.jsxs("button",{type:"button",onClick:l,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors flex items-center gap-1",title:"طلب المايك",children:[n.jsx(wt,{className:"w-4 h-4"}),n.jsx("span",{className:"hidden sm:inline",children:"طلب المايك"})]})]}),r&&n.jsxs("div",{className:"mt-2 p-2 bg-yellow-900/30 border border-yellow-500/30 rounded-lg text-yellow-200 text-sm flex items-center gap-2",children:[n.jsx(mr,{className:"w-4 h-4"}),n.jsx("span",{children:"أنت في قائمة انتظار المايك"})]}),n.jsxs("div",{className:"mt-2 text-xs text-gray-500 flex items-center gap-4",children:[n.jsx("span",{children:"اضغط Enter للإرسال"}),n.jsx("span",{children:"الحد الأقصى: 500 حرف"})]})]})]})},Vp=({user:e,wsService:t})=>{const[r,s]=y.useState(null),[l,o]=y.useState([]),[i,a]=y.useState(!0),[c,d]=y.useState(null),[g,v]=y.useState(!1),[b,N]=y.useState(null),[j,k]=y.useState(!1),[S,m]=y.useState(!1),[u,p]=y.useState(!1),[E,L]=y.useState(!1),[O,z]=y.useState([]),[$,G]=y.useState(new Map),x=y.useRef(null),U=async()=>{try{a(!0);const[f,C]=await Promise.all([K.getVoiceRoom(),K.getVoiceRoomMessages()]);s(f),o(C);const _=f.seats.find(F=>F.user&&F.user._id===e.id);_?(v(!0),N(_.seatNumber),m(_.isMuted)):(v(!1),N(null));const T=f.waitingQueue.some(F=>F.user._id===e.id);k(T),d(null)}catch(f){console.error("Error loading voice room:",f),d(f.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1)}};y.useEffect(()=>{if(!(e!=null&&e.id)){console.warn("⚠️ No user ID available, skipping WebRTC service setup");return}console.log("🔧 Setting up WebRTC Voice Service with user ID:",e.id);try{x.current=new I0(t)}catch(f){console.error("❌ Error creating WebRTC service:",f);return}return x.current&&(x.current.onUserJoined=f=>{console.log(`👤 User joined voice chat: ${f.id}`),z(C=>[...C.filter(_=>_.id!==f.id),f])},x.current.onUserLeft=f=>{console.log(`👋 User left voice chat: ${f}`),z(C=>C.filter(_=>_.id!==f)),G(C=>{const _=new Map(C);return _.delete(f),_})},x.current.onVoiceActivity=f=>{console.log("🎤 Voice activity changed:",f.isSpeaking?"speaking":"silent",`(level: ${f.level})`),G(C=>{const _=new Map(C);return _.set(f.userId,f),_}),e!=null&&e.id&&g?(console.log("📤 Voice activity sent:",f.isSpeaking?"speaking":"silent",`(userId: ${e.id})`),t.send({type:"voice_activity",data:{userId:e.id,level:f.level,isSpeaking:f.isSpeaking,timestamp:Date.now()}})):(e!=null&&e.id||console.warn("⚠️ No currentUserId available for voice activity"),g||console.log("🔍 User not in seat, voice activity not sent"))},x.current.onError=f=>{console.error("❌ WebRTC error:",f),d(`خطأ في الصوت: ${f.message}`)}),()=>{x.current&&(console.log("🧹 Cleaning up WebRTC service"),x.current.leaveRoom().catch(console.error))}},[t,e==null?void 0:e.id]),y.useEffect(()=>{const f=T=>{o(F=>[...F,T])},C=T=>{T.action&&T.userId&&(T.action==="seat_joined"||T.action==="seat_left"||T.action==="mute_toggled")||U(),T.action==="seat_joined"&&g&&T.userId!==e.id&&setTimeout(()=>{var F;(F=x.current)==null||F.sendOffer(T.userId)},1e3)},_=T=>{T.userId&&T.userId!==e.id&&(G(F=>{const J=new Map(F);return J.set(T.userId.toString(),{userId:T.userId.toString(),level:T.level,isSpeaking:T.isSpeaking}),J}),s(F=>({...F,seats:F.seats.map(J=>{var te;return((te=J.user)==null?void 0:te._id)===T.userId?{...J,isSpeaking:T.isSpeaking}:J})})))};return t.onMessage("voice_room_message",f),t.onMessage("voice_room_update",C),t.onMessage("voice_activity",_),()=>{t.offMessage("voice_room_message",f),t.offMessage("voice_room_update",C),t.offMessage("voice_activity",_)}},[t,g,e.id]),y.useEffect(()=>{U()},[]),y.useEffect(()=>{const f=_=>{if(g)return _.preventDefault(),_.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},C=()=>{g&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",f),window.addEventListener("unload",C),()=>{window.removeEventListener("beforeunload",f),window.removeEventListener("unload",C)}},[g,e.id]);const Y=async f=>{try{const C=await K.sendVoiceRoomMessage(f);t.send({type:"voice_room_message",data:C.messageData})}catch(C){console.error("Error sending message:",C),d(C.message||"خطأ في إرسال الرسالة")}},ae=async()=>{try{p(!0),await K.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}}),await U()}catch(f){console.error("Error requesting mic:",f),d(f.message||"خطأ في طلب المايك")}finally{p(!1)}},Te=async()=>{try{await K.cancelMicRequest(),t.send({type:"voice_room_update",data:{action:"mic_request_cancelled",userId:e.id}}),await U()}catch(f){console.error("Error cancelling mic request:",f),d(f.message||"خطأ في إلغاء طلب المايك")}},Le=async f=>{try{if(p(!0),d(null),await K.joinVoiceSeat(f),x.current&&(e!=null&&e.id)){console.log("🎤 Starting WebRTC voice chat for seat",f);try{const C=`voice-room-${(r==null?void 0:r.id)||"default"}`;await x.current.joinRoom(C,e.id),L(!0),console.log("✅ WebRTC voice chat started successfully")}catch(C){console.error("❌ WebRTC initialization failed:",C),d(`فشل في بدء المحادثة الصوتية: ${C.message}`)}}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",f.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:f}}),await U()}catch(C){console.error("Error joining seat:",C),d(C.message||"خطأ في الانضمام للمقعد"),L(!1)}finally{p(!1)}},Ue=async()=>{try{await K.leaveVoiceSeat(),x.current&&(await x.current.leaveRoom(),L(!1),z([]),G(new Map),console.log("🔇 WebRTC voice chat stopped")),localStorage.removeItem("isInVoiceRoom"),localStorage.removeItem("voiceRoomSeat"),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:b}}),await U()}catch(f){console.error("Error leaving seat:",f),d(f.message||"خطأ في مغادرة المقعد")}},we=async()=>{try{if(!x.current){d("خدمة الصوت غير متاحة");return}const f=!S;m(f),x.current.setMute(f),t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:f}}),K.toggleMute(f).catch(C=>{console.warn("Failed to update server mute state:",C)})}catch(f){console.error("Error toggling mute:",f),d("خطأ في تبديل كتم المايك")}};return i?n.jsx("div",{className:"flex items-center justify-center h-96",children:n.jsxs("div",{className:"text-center",children:[n.jsx(fr,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),n.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})}):c?n.jsxs("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center",children:[n.jsx("p",{className:"text-red-400 mb-4",children:c}),n.jsx("button",{onClick:U,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors",children:"إعادة المحاولة"})]}):r?n.jsxs("div",{className:`max-w-7xl mx-auto p-4 sm:p-6 space-y-6 ${g?"pb-24 sm:pb-6":""}`,children:[n.jsx("div",{className:"bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-xl p-4 sm:p-6 border border-purple-500/20",children:n.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[n.jsxs("div",{children:[n.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-white mb-2 flex items-center gap-3",children:[n.jsx(Kn,{className:"w-6 sm:w-8 h-6 sm:h-8 text-purple-400"}),"INFINITY ROOM"]}),n.jsx("p",{className:"text-gray-300 text-sm sm:text-base",children:"غرفة صوتية للمحادثة مع الأصدقاء"})]}),n.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[n.jsxs("div",{className:"flex items-center gap-4 text-gray-300",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx(br,{className:"w-4 sm:w-5 h-4 sm:h-5"}),n.jsxs("span",{className:"text-sm",children:[r.seats.filter(f=>f.user).length,"/",r.maxSeats]})]}),n.jsxs("div",{className:"flex items-center gap-2 bg-black/20 px-2 sm:px-3 py-1 rounded-lg",children:[E?n.jsx(Zf,{className:"w-3 sm:w-4 h-3 sm:h-4 text-green-400"}):n.jsx(Jf,{className:"w-3 sm:w-4 h-3 sm:h-4 text-red-400"}),n.jsx("span",{className:"text-xs sm:text-sm",children:E?"متصل":"غير متصل"})]})]}),g&&n.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto",children:[n.jsx("button",{onClick:we,className:`w-full sm:w-auto p-3 sm:p-2 rounded-lg transition-colors text-sm font-medium ${S?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:n.jsxs("div",{className:"flex items-center justify-center gap-2",children:[S?n.jsx(Jn,{className:"w-5 h-5"}):n.jsx(wt,{className:"w-5 h-5"}),n.jsx("span",{className:"sm:hidden",children:S?"إلغاء الكتم":"كتم المايك"})]})}),n.jsx("button",{onClick:Ue,className:"w-full sm:w-auto px-4 py-3 sm:py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors text-sm font-medium",children:"مغادرة المقعد"})]})]})]})}),g&&n.jsx("div",{className:"hidden sm:block mb-6",children:n.jsx("div",{className:"bg-gradient-to-r from-gray-800/50 to-purple-900/30 rounded-xl p-4 border border-purple-500/20",children:n.jsxs("div",{className:"flex items-center justify-between",children:[n.jsx("div",{className:"flex items-center gap-3",children:n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),n.jsxs("span",{className:"text-green-400 font-medium",children:["متصل - مقعد ",b]})]})}),n.jsxs("div",{className:"flex items-center gap-3",children:[n.jsxs("button",{onClick:we,className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 font-medium ${S?"bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/25":"bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/25"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:[S?n.jsx(Jn,{className:"w-4 h-4"}):n.jsx(wt,{className:"w-4 h-4"}),n.jsx("span",{children:S?"إلغاء كتم المايك":"كتم المايك"})]}),n.jsx("button",{onClick:Ue,className:"flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-all duration-200 font-medium shadow-lg shadow-red-600/25",title:"مغادرة المقعد",children:n.jsx("span",{children:"مغادرة المقعد"})})]})]})})}),n.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[n.jsx("div",{className:"lg:col-span-2",children:n.jsx(Fp,{seats:r.seats,waitingQueue:r.waitingQueue,currentUser:e,isInSeat:g,currentSeatNumber:b,isInWaitingQueue:j,isConnecting:u,onJoinSeat:Le,onRequestMic:ae,onCancelMicRequest:Te})}),n.jsx("div",{className:"lg:col-span-1",children:n.jsx(Bp,{messages:l,currentUser:e,isInWaitingQueue:j,onSendMessage:Y,onRequestMic:ae})})]})]}):n.jsx("div",{className:"text-center text-gray-400",children:n.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},qp=({user:e,onLogout:t,wsService:r})=>{var b;const[,s]=E0(),[l,o]=y.useState(()=>{const N=localStorage.getItem("isInVoiceRoom")==="true",j=localStorage.getItem("activeTab");return N?"voice":j||"games"}),[i,a]=y.useState(!1),[c,d]=y.useState(!1);y.useEffect(()=>{const N=()=>{a(window.innerWidth<=768)};return N(),window.addEventListener("resize",N),()=>window.removeEventListener("resize",N)},[]);const g=N=>{o(N),localStorage.setItem("activeTab",N)},v=()=>{localStorage.removeItem("token"),s("/login"),t()};return console.log("USER DATA IN MAIN DASHBOARD:",e),!e||!e.username?n.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-red-900 via-yellow-900 to-black text-white",children:[n.jsx("h1",{className:"text-3xl font-bold mb-4",children:"⚠️ لا توجد بيانات لاعب!"}),n.jsx("pre",{className:"bg-black/60 rounded-lg p-4 text-left text-xs max-w-xl overflow-x-auto mb-4",children:JSON.stringify(e,null,2)}),n.jsx("p",{className:"mb-4",children:"يرجى التأكد من أن حسابك يحتوي على اسم مستخدم وصورة ورصيد."}),n.jsx("button",{onClick:t,className:"px-6 py-2 bg-red-600 rounded-lg text-white font-bold",children:"تسجيل الخروج"})]}):i?n.jsx(Up,{userData:e,onLogout:v,wsService:r}):n.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white relative overflow-hidden",children:[n.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[n.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"}),n.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"}),n.jsx("div",{className:"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"})]}),n.jsx("header",{className:"relative z-10 bg-black/30 backdrop-blur-xl border-b border-white/10 shadow-2xl",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:n.jsxs("div",{className:"flex justify-between items-center h-20",children:[n.jsx("div",{className:"flex items-center space-x-4",children:n.jsxs("div",{className:"flex items-center space-x-3",children:[n.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg",children:n.jsx(Mn,{className:"w-7 h-7 text-white"})}),n.jsxs("div",{children:[n.jsxs("h1",{className:"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:[e.username," - Infinity Box"]}),n.jsx("p",{className:"text-xs text-gray-400",children:"عالم الألعاب المثير"})]})]})}),n.jsx("div",{className:"flex items-center space-x-6",children:n.jsxs("div",{className:"flex items-center space-x-4",children:[n.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-yellow-500/50 shadow-lg hover:from-yellow-500/40 hover:to-orange-500/40 transition-all duration-300",children:[n.jsx(Il,{className:"w-6 h-6 text-yellow-300"}),n.jsx("span",{className:"text-yellow-300 font-bold text-lg",children:((b=e.goldCoins)==null?void 0:b.toLocaleString())||0})]}),n.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500/30 to-purple-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-blue-500/50 shadow-lg hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-300",children:[n.jsx(c0,{className:"w-6 h-6 text-blue-300"}),n.jsx("span",{className:"text-blue-300 font-bold text-lg",children:e.pearls||0})]})]})}),n.jsxs("div",{className:"flex items-center space-x-4",children:[n.jsxs("button",{onClick:()=>d(!c),className:"relative p-2 bg-white/10 backdrop-blur-sm rounded-lg hover:bg-white/20 transition-all duration-300 border border-white/20",children:[n.jsx(Pf,{className:"w-5 h-5 text-white"}),n.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),n.jsxs("div",{className:"flex items-center space-x-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-sm rounded-xl px-4 py-2 border border-purple-500/30 hover:from-purple-500/30 hover:to-blue-500/30 transition-all duration-300 shadow-lg",children:[n.jsxs("div",{className:"relative",children:[n.jsx("img",{src:e.profileImage||"/images/default-avatar.png",alt:e.username,className:"w-12 h-12 rounded-full border-2 border-white/30 shadow-lg object-cover",onError:N=>{N.currentTarget.src="/images/default-avatar.png"}}),n.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),n.jsxs("div",{className:"text-right",children:[n.jsx("p",{className:"font-bold text-white text-lg",children:e.username}),n.jsxs("p",{className:"text-xs text-yellow-300",children:["مستوى ",e.level||1," - ",e.experience||0," XP"]})]})]}),n.jsxs("button",{onClick:v,className:"flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 backdrop-blur-sm rounded-lg transition-all duration-300 border border-red-500/30 text-red-400 hover:text-red-300",children:[n.jsx(m0,{className:"w-4 h-4"}),n.jsx("span",{className:"text-sm font-medium",children:"خروج"})]})]})]})})}),n.jsx("nav",{className:"relative z-10 bg-black/20 backdrop-blur-xl border-b border-white/10 shadow-lg",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:n.jsxs("div",{className:"flex space-x-8",children:[n.jsx("button",{onClick:()=>g("games"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="games"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎮 الألعاب"}),n.jsx("button",{onClick:()=>g("leaderboard"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="leaderboard"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🏆 المتصدرين"}),n.jsx("button",{onClick:()=>g("voice"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="voice"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎤 الغرفة الصوتية"}),n.jsx("button",{onClick:()=>g("profile"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="profile"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"👤 الملف الشخصي"}),e.isAdmin&&n.jsx("button",{onClick:()=>g("admin"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="admin"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"⚙️ الإدارة"})]})})}),n.jsxs("main",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[l==="games"&&n.jsx(Vs,{setActiveTab:g}),l==="leaderboard"&&n.jsxs("div",{className:"text-center",children:[n.jsxs("div",{className:"flex items-center justify-center mb-8",children:[n.jsx(Zn,{className:"w-12 h-12 text-yellow-400 mr-4"}),n.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"🏆 المتصدرين"})]}),n.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:n.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة قائمة المتصدرين..."})})]}),l==="voice"&&r&&n.jsx(Vp,{user:e,wsService:r}),l==="profile"&&n.jsxs("div",{className:"text-center",children:[n.jsxs("div",{className:"flex items-center justify-center mb-8",children:[n.jsx(pr,{className:"w-12 h-12 text-blue-400 mr-4"}),n.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"👤 الملف الشخصي"})]}),n.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:n.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة الملف الشخصي..."})})]}),l==="admin"&&e.isAdmin&&n.jsx(fi,{userData:e,onLogout:v})]}),c&&n.jsxs("div",{className:"fixed top-20 right-4 z-50 w-80 bg-black/90 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl",children:[n.jsx("div",{className:"p-4 border-b border-white/10",children:n.jsx("h3",{className:"text-lg font-semibold text-white",children:"الإشعارات"})}),n.jsx("div",{className:"p-4",children:n.jsx("p",{className:"text-gray-400 text-center",children:"لا توجد إشعارات جديدة"})})]})]})};function Hp(){const[e,t]=y.useState(!1),[r,s]=y.useState(null),[l,o]=y.useState(!0),[i]=y.useState(()=>new M0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`));y.useEffect(()=>{localStorage.removeItem("activeTab");const d=localStorage.getItem("token");console.log("🔍 App: Checking token:",d?"Token exists":"No token found"),d?(console.log("🔄 App: Attempting to get current user..."),K.getCurrentUser().then(g=>{if(console.log("✅ App: User data received:",g),g&&typeof g=="object")s(g),localStorage.setItem("isAdmin",g.isAdmin?"true":"false");else{const b=localStorage.getItem("isAdmin")==="true";s({id:"",username:"",isAdmin:b})}console.log("🔓 App: Setting authenticated to true"),t(!0);const v=localStorage.getItem("token");v&&i.connect(v).then(()=>{console.log("✅ WebSocket connected on app load")}).catch(b=>{console.error("❌ Failed to connect to WebSocket on app load:",b)})}).catch(g=>{console.log("❌ App: Error getting user:",g),g.message.includes("MULTIPLE_LOGIN")&&alert("تم تسجيل الدخول من جهاز آخر. سيتم تسجيل خروجك من هذا الجهاز."),console.log("🔒 App: Setting authenticated to false"),t(!1),s(null),localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}).finally(()=>{console.log("⏹️ App: Loading finished"),o(!1)})):(console.log("🔒 App: No token found, setting authenticated to false"),o(!1))},[]);const a=async d=>{s(d),t(!0);try{const g=localStorage.getItem("token");g&&await i.connect(g)}catch(g){console.error("Failed to connect to WebSocket:",g)}},c=()=>{localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),i.disconnect(),t(!1),s(null)};return l?(console.log("⏳ App: Showing loading screen"),n.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 flex items-center justify-center",children:n.jsxs("div",{className:"text-center",children:[n.jsx("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),n.jsx("p",{className:"text-white text-lg",children:"جاري التحميل..."})]})})):e?(console.log("🏠 App: Showing MainDashboard (authenticated)"),r?n.jsx(qp,{user:r,onLogout:c,wsService:i}):null):(console.log("🔐 App: Showing AuthPage (not authenticated)"),n.jsx(tp,{onAuthSuccess:a}))}n0(document.getElementById("root")).render(n.jsx(y.StrictMode,{children:n.jsx(Hp,{})}));
