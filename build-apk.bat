@echo off
echo 🚀 بناء تطبيق Infinity Box APK...
echo.

echo 📦 بناء التطبيق...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo 📱 نسخ الملفات إلى Android...
call npx cap copy android
if %errorlevel% neq 0 (
    echo ❌ فشل في نسخ الملفات
    pause
    exit /b 1
)

echo 🔄 تحديث الإضافات...
call npx cap update android
if %errorlevel% neq 0 (
    echo ❌ فشل في تحديث الإضافات
    pause
    exit /b 1
)

echo ✅ تم الانتهاء من التحضير!
echo.
echo 📱 لبناء APK، قم بتشغيل:
echo    npx cap open android
echo.
echo أو استخدم Android Studio لبناء APK
pause
