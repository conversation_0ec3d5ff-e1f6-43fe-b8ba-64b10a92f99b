function em(e,t){for(var n=0;n<t.length;n++){const s=t[n];if(typeof s!="string"&&!Array.isArray(s)){for(const l in s)if(l!=="default"&&!(l in e)){const o=Object.getOwnPropertyDescriptor(s,l);o&&Object.defineProperty(e,l,o.get?o:{enumerable:!0,get:()=>s[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))s(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function tm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Dc={exports:{}},xl={},Jc={exports:{}},Z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ns=Symbol.for("react.element"),nm=Symbol.for("react.portal"),rm=Symbol.for("react.fragment"),sm=Symbol.for("react.strict_mode"),lm=Symbol.for("react.profiler"),om=Symbol.for("react.provider"),im=Symbol.for("react.context"),am=Symbol.for("react.forward_ref"),cm=Symbol.for("react.suspense"),dm=Symbol.for("react.memo"),um=Symbol.for("react.lazy"),Ea=Symbol.iterator;function mm(e){return e===null||typeof e!="object"?null:(e=Ea&&e[Ea]||e["@@iterator"],typeof e=="function"?e:null)}var Zc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Kc=Object.assign,Yc={};function or(e,t,n){this.props=e,this.context=t,this.refs=Yc,this.updater=n||Zc}or.prototype.isReactComponent={};or.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};or.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Xc(){}Xc.prototype=or.prototype;function yi(e,t,n){this.props=e,this.context=t,this.refs=Yc,this.updater=n||Zc}var vi=yi.prototype=new Xc;vi.constructor=yi;Kc(vi,or.prototype);vi.isPureReactComponent=!0;var Ia=Array.isArray,ed=Object.prototype.hasOwnProperty,wi={current:null},td={key:!0,ref:!0,__self:!0,__source:!0};function nd(e,t,n){var s,l={},o=null,i=null;if(t!=null)for(s in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)ed.call(t,s)&&!td.hasOwnProperty(s)&&(l[s]=t[s]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var c=Array(a),u=0;u<a;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(s in a=e.defaultProps,a)l[s]===void 0&&(l[s]=a[s]);return{$$typeof:ns,type:e,key:o,ref:i,props:l,_owner:wi.current}}function fm(e,t){return{$$typeof:ns,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function bi(e){return typeof e=="object"&&e!==null&&e.$$typeof===ns}function hm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Aa=/\/+/g;function Fl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?hm(""+e.key):t.toString(36)}function Ls(e,t,n,s,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case ns:case nm:i=!0}}if(i)return i=e,l=l(i),e=s===""?"."+Fl(i,0):s,Ia(l)?(n="",e!=null&&(n=e.replace(Aa,"$&/")+"/"),Ls(l,t,n,"",function(u){return u})):l!=null&&(bi(l)&&(l=fm(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Aa,"$&/")+"/")+e)),t.push(l)),1;if(i=0,s=s===""?".":s+":",Ia(e))for(var a=0;a<e.length;a++){o=e[a];var c=s+Fl(o,a);i+=Ls(o,t,n,c,l)}else if(c=mm(e),typeof c=="function")for(e=c.call(e),a=0;!(o=e.next()).done;)o=o.value,c=s+Fl(o,a++),i+=Ls(o,t,n,c,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function ps(e,t,n){if(e==null)return e;var s=[],l=0;return Ls(e,s,"","",function(o){return t.call(n,o,l++)}),s}function pm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Oe={current:null},Ps={transition:null},gm={ReactCurrentDispatcher:Oe,ReactCurrentBatchConfig:Ps,ReactCurrentOwner:wi};function rd(){throw Error("act(...) is not supported in production builds of React.")}Z.Children={map:ps,forEach:function(e,t,n){ps(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ps(e,function(){t++}),t},toArray:function(e){return ps(e,function(t){return t})||[]},only:function(e){if(!bi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Z.Component=or;Z.Fragment=rm;Z.Profiler=lm;Z.PureComponent=yi;Z.StrictMode=sm;Z.Suspense=cm;Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=gm;Z.act=rd;Z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var s=Kc({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=wi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)ed.call(t,c)&&!td.hasOwnProperty(c)&&(s[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)s.children=n;else if(1<c){a=Array(c);for(var u=0;u<c;u++)a[u]=arguments[u+2];s.children=a}return{$$typeof:ns,type:e.type,key:l,ref:o,props:s,_owner:i}};Z.createContext=function(e){return e={$$typeof:im,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:om,_context:e},e.Consumer=e};Z.createElement=nd;Z.createFactory=function(e){var t=nd.bind(null,e);return t.type=e,t};Z.createRef=function(){return{current:null}};Z.forwardRef=function(e){return{$$typeof:am,render:e}};Z.isValidElement=bi;Z.lazy=function(e){return{$$typeof:um,_payload:{_status:-1,_result:e},_init:pm}};Z.memo=function(e,t){return{$$typeof:dm,type:e,compare:t===void 0?null:t}};Z.startTransition=function(e){var t=Ps.transition;Ps.transition={};try{e()}finally{Ps.transition=t}};Z.unstable_act=rd;Z.useCallback=function(e,t){return Oe.current.useCallback(e,t)};Z.useContext=function(e){return Oe.current.useContext(e)};Z.useDebugValue=function(){};Z.useDeferredValue=function(e){return Oe.current.useDeferredValue(e)};Z.useEffect=function(e,t){return Oe.current.useEffect(e,t)};Z.useId=function(){return Oe.current.useId()};Z.useImperativeHandle=function(e,t,n){return Oe.current.useImperativeHandle(e,t,n)};Z.useInsertionEffect=function(e,t){return Oe.current.useInsertionEffect(e,t)};Z.useLayoutEffect=function(e,t){return Oe.current.useLayoutEffect(e,t)};Z.useMemo=function(e,t){return Oe.current.useMemo(e,t)};Z.useReducer=function(e,t,n){return Oe.current.useReducer(e,t,n)};Z.useRef=function(e){return Oe.current.useRef(e)};Z.useState=function(e){return Oe.current.useState(e)};Z.useSyncExternalStore=function(e,t,n){return Oe.current.useSyncExternalStore(e,t,n)};Z.useTransition=function(){return Oe.current.useTransition()};Z.version="18.3.1";Jc.exports=Z;var v=Jc.exports;const xm=tm(v),ym=em({__proto__:null,default:xm},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vm=v,wm=Symbol.for("react.element"),bm=Symbol.for("react.fragment"),jm=Object.prototype.hasOwnProperty,Nm=vm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,km={key:!0,ref:!0,__self:!0,__source:!0};function sd(e,t,n){var s,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(s in t)jm.call(t,s)&&!km.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)l[s]===void 0&&(l[s]=t[s]);return{$$typeof:wm,type:e,key:o,ref:i,props:l,_owner:Nm.current}}xl.Fragment=bm;xl.jsx=sd;xl.jsxs=sd;Dc.exports=xl;var r=Dc.exports,ld={exports:{}},Ze={},od={exports:{}},id={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(h,C){var _=h.length;h.push(C);e:for(;0<_;){var $=_-1>>>1,R=h[$];if(0<l(R,C))h[$]=C,h[_]=R,_=$;else break e}}function n(h){return h.length===0?null:h[0]}function s(h){if(h.length===0)return null;var C=h[0],_=h.pop();if(_!==C){h[0]=_;e:for(var $=0,R=h.length,G=R>>>1;$<G;){var Y=2*($+1)-1,ne=h[Y],xe=Y+1,he=h[xe];if(0>l(ne,_))xe<R&&0>l(he,ne)?(h[$]=he,h[xe]=_,$=xe):(h[$]=ne,h[Y]=_,$=Y);else if(xe<R&&0>l(he,_))h[$]=he,h[xe]=_,$=xe;else break e}}return C}function l(h,C){var _=h.sortIndex-C.sortIndex;return _!==0?_:h.id-C.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var c=[],u=[],g=1,x=null,w=3,N=!1,j=!1,k=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(h){for(var C=n(u);C!==null;){if(C.callback===null)s(u);else if(C.startTime<=h)s(u),C.sortIndex=C.expirationTime,t(c,C);else break;C=n(u)}}function M(h){if(k=!1,p(h),!j)if(n(c)!==null)j=!0,Ie(L);else{var C=n(u);C!==null&&fe(M,C.startTime-h)}}function L(h,C){j=!1,k&&(k=!1,m(F),F=-1),N=!0;var _=w;try{for(p(C),x=n(c);x!==null&&(!(x.expirationTime>C)||h&&!O());){var $=x.callback;if(typeof $=="function"){x.callback=null,w=x.priorityLevel;var R=$(x.expirationTime<=C);C=e.unstable_now(),typeof R=="function"?x.callback=R:x===n(c)&&s(c),p(C)}else s(c);x=n(c)}if(x!==null)var G=!0;else{var Y=n(u);Y!==null&&fe(M,Y.startTime-C),G=!1}return G}finally{x=null,w=_,N=!1}}var P=!1,z=null,F=-1,D=5,y=-1;function O(){return!(e.unstable_now()-y<D)}function X(){if(z!==null){var h=e.unstable_now();y=h;var C=!0;try{C=z(!0,h)}finally{C?re():(P=!1,z=null)}}else P=!1}var re;if(typeof d=="function")re=function(){d(X)};else if(typeof MessageChannel<"u"){var He=new MessageChannel,Ye=He.port2;He.port1.onmessage=X,re=function(){Ye.postMessage(null)}}else re=function(){S(X,0)};function Ie(h){z=h,P||(P=!0,re())}function fe(h,C){F=S(function(){h(e.unstable_now())},C)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(h){h.callback=null},e.unstable_continueExecution=function(){j||N||(j=!0,Ie(L))},e.unstable_forceFrameRate=function(h){0>h||125<h?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<h?Math.floor(1e3/h):5},e.unstable_getCurrentPriorityLevel=function(){return w},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(h){switch(w){case 1:case 2:case 3:var C=3;break;default:C=w}var _=w;w=C;try{return h()}finally{w=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(h,C){switch(h){case 1:case 2:case 3:case 4:case 5:break;default:h=3}var _=w;w=h;try{return C()}finally{w=_}},e.unstable_scheduleCallback=function(h,C,_){var $=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?$+_:$):_=$,h){case 1:var R=-1;break;case 2:R=250;break;case 5:R=**********;break;case 4:R=1e4;break;default:R=5e3}return R=_+R,h={id:g++,callback:C,priorityLevel:h,startTime:_,expirationTime:R,sortIndex:-1},_>$?(h.sortIndex=_,t(u,h),n(c)===null&&h===n(u)&&(k?(m(F),F=-1):k=!0,fe(M,_-$))):(h.sortIndex=R,t(c,h),j||N||(j=!0,Ie(L))),h},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(h){var C=w;return function(){var _=w;w=C;try{return h.apply(this,arguments)}finally{w=_}}}})(id);od.exports=id;var Sm=od.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cm=v,Je=Sm;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ad=new Set,Rr={};function Sn(e,t){Yn(e,t),Yn(e+"Capture",t)}function Yn(e,t){for(Rr[e]=t,e=0;e<t.length;e++)ad.add(t[e])}var It=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),go=Object.prototype.hasOwnProperty,Mm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_a={},Ta={};function Em(e){return go.call(Ta,e)?!0:go.call(_a,e)?!1:Mm.test(e)?Ta[e]=!0:(_a[e]=!0,!1)}function Im(e,t,n,s){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Am(e,t,n,s){if(t===null||typeof t>"u"||Im(e,t,n,s))return!0;if(s)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ue(e,t,n,s,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Ee={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ee[e]=new Ue(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ee[t]=new Ue(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ee[e]=new Ue(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ee[e]=new Ue(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ee[e]=new Ue(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ee[e]=new Ue(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ee[e]=new Ue(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ee[e]=new Ue(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ee[e]=new Ue(e,5,!1,e.toLowerCase(),null,!1,!1)});var ji=/[\-:]([a-z])/g;function Ni(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ji,Ni);Ee[t]=new Ue(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ji,Ni);Ee[t]=new Ue(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ji,Ni);Ee[t]=new Ue(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ee[e]=new Ue(e,1,!1,e.toLowerCase(),null,!1,!1)});Ee.xlinkHref=new Ue("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ee[e]=new Ue(e,1,!1,e.toLowerCase(),null,!0,!0)});function ki(e,t,n,s){var l=Ee.hasOwnProperty(t)?Ee[t]:null;(l!==null?l.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Am(t,n,l,s)&&(n=null),s||l===null?Em(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,s=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,s?e.setAttributeNS(s,t,n):e.setAttribute(t,n))))}var Lt=Cm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,gs=Symbol.for("react.element"),Pn=Symbol.for("react.portal"),Rn=Symbol.for("react.fragment"),Si=Symbol.for("react.strict_mode"),xo=Symbol.for("react.profiler"),cd=Symbol.for("react.provider"),dd=Symbol.for("react.context"),Ci=Symbol.for("react.forward_ref"),yo=Symbol.for("react.suspense"),vo=Symbol.for("react.suspense_list"),Mi=Symbol.for("react.memo"),Rt=Symbol.for("react.lazy"),ud=Symbol.for("react.offscreen"),La=Symbol.iterator;function ur(e){return e===null||typeof e!="object"?null:(e=La&&e[La]||e["@@iterator"],typeof e=="function"?e:null)}var me=Object.assign,Bl;function vr(e){if(Bl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Bl=t&&t[1]||""}return`
`+Bl+e}var Vl=!1;function ql(e,t){if(!e||Vl)return"";Vl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var s=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){s=u}e.call(t.prototype)}else{try{throw Error()}catch(u){s=u}e()}}catch(u){if(u&&s&&typeof u.stack=="string"){for(var l=u.stack.split(`
`),o=s.stack.split(`
`),i=l.length-1,a=o.length-1;1<=i&&0<=a&&l[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(l[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||l[i]!==o[a]){var c=`
`+l[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{Vl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vr(e):""}function _m(e){switch(e.tag){case 5:return vr(e.type);case 16:return vr("Lazy");case 13:return vr("Suspense");case 19:return vr("SuspenseList");case 0:case 2:case 15:return e=ql(e.type,!1),e;case 11:return e=ql(e.type.render,!1),e;case 1:return e=ql(e.type,!0),e;default:return""}}function wo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Rn:return"Fragment";case Pn:return"Portal";case xo:return"Profiler";case Si:return"StrictMode";case yo:return"Suspense";case vo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case dd:return(e.displayName||"Context")+".Consumer";case cd:return(e._context.displayName||"Context")+".Provider";case Ci:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Mi:return t=e.displayName||null,t!==null?t:wo(e.type)||"Memo";case Rt:t=e._payload,e=e._init;try{return wo(e(t))}catch{}}return null}function Tm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wo(t);case 8:return t===Si?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Jt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function md(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Lm(e){var t=md(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){s=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(i){s=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function xs(e){e._valueTracker||(e._valueTracker=Lm(e))}function fd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),s="";return e&&(s=md(e)?e.checked?"true":"false":e.value),e=s,e!==n?(t.setValue(e),!0):!1}function Gs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function bo(e,t){var n=t.checked;return me({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Pa(e,t){var n=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;n=Jt(t.value!=null?t.value:n),e._wrapperState={initialChecked:s,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function hd(e,t){t=t.checked,t!=null&&ki(e,"checked",t,!1)}function jo(e,t){hd(e,t);var n=Jt(t.value),s=t.type;if(n!=null)s==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?No(e,t.type,n):t.hasOwnProperty("defaultValue")&&No(e,t.type,Jt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ra(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function No(e,t,n){(t!=="number"||Gs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var wr=Array.isArray;function Qn(e,t,n,s){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&s&&(e[n].defaultSelected=!0)}else{for(n=""+Jt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,s&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ko(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return me({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function za(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(wr(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Jt(n)}}function pd(e,t){var n=Jt(t.value),s=Jt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),s!=null&&(e.defaultValue=""+s)}function Oa(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function So(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ys,xd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,s,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,s,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ys=ys||document.createElement("div"),ys.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ys.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function zr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Nr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Pm=["Webkit","ms","Moz","O"];Object.keys(Nr).forEach(function(e){Pm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Nr[t]=Nr[e]})});function yd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Nr.hasOwnProperty(e)&&Nr[e]?(""+t).trim():t+"px"}function vd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var s=n.indexOf("--")===0,l=yd(n,t[n],s);n==="float"&&(n="cssFloat"),s?e.setProperty(n,l):e[n]=l}}var Rm=me({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Co(e,t){if(t){if(Rm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Mo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Eo=null;function Ei(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Io=null,Gn=null,Dn=null;function Ua(e){if(e=ls(e)){if(typeof Io!="function")throw Error(T(280));var t=e.stateNode;t&&(t=jl(t),Io(e.stateNode,e.type,t))}}function wd(e){Gn?Dn?Dn.push(e):Dn=[e]:Gn=e}function bd(){if(Gn){var e=Gn,t=Dn;if(Dn=Gn=null,Ua(e),t)for(e=0;e<t.length;e++)Ua(t[e])}}function jd(e,t){return e(t)}function Nd(){}var Hl=!1;function kd(e,t,n){if(Hl)return e(t,n);Hl=!0;try{return jd(e,t,n)}finally{Hl=!1,(Gn!==null||Dn!==null)&&(Nd(),bd())}}function Or(e,t){var n=e.stateNode;if(n===null)return null;var s=jl(n);if(s===null)return null;n=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Ao=!1;if(It)try{var mr={};Object.defineProperty(mr,"passive",{get:function(){Ao=!0}}),window.addEventListener("test",mr,mr),window.removeEventListener("test",mr,mr)}catch{Ao=!1}function zm(e,t,n,s,l,o,i,a,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(g){this.onError(g)}}var kr=!1,Ds=null,Js=!1,_o=null,Om={onError:function(e){kr=!0,Ds=e}};function Um(e,t,n,s,l,o,i,a,c){kr=!1,Ds=null,zm.apply(Om,arguments)}function $m(e,t,n,s,l,o,i,a,c){if(Um.apply(this,arguments),kr){if(kr){var u=Ds;kr=!1,Ds=null}else throw Error(T(198));Js||(Js=!0,_o=u)}}function Cn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Sd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function $a(e){if(Cn(e)!==e)throw Error(T(188))}function Fm(e){var t=e.alternate;if(!t){if(t=Cn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,s=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(s=l.return,s!==null){n=s;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return $a(l),e;if(o===s)return $a(l),t;o=o.sibling}throw Error(T(188))}if(n.return!==s.return)n=l,s=o;else{for(var i=!1,a=l.child;a;){if(a===n){i=!0,n=l,s=o;break}if(a===s){i=!0,s=l,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,s=l;break}if(a===s){i=!0,s=o,n=l;break}a=a.sibling}if(!i)throw Error(T(189))}}if(n.alternate!==s)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Cd(e){return e=Fm(e),e!==null?Md(e):null}function Md(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Md(e);if(t!==null)return t;e=e.sibling}return null}var Ed=Je.unstable_scheduleCallback,Fa=Je.unstable_cancelCallback,Bm=Je.unstable_shouldYield,Vm=Je.unstable_requestPaint,ye=Je.unstable_now,qm=Je.unstable_getCurrentPriorityLevel,Ii=Je.unstable_ImmediatePriority,Id=Je.unstable_UserBlockingPriority,Zs=Je.unstable_NormalPriority,Hm=Je.unstable_LowPriority,Ad=Je.unstable_IdlePriority,yl=null,wt=null;function Wm(e){if(wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot(yl,e,void 0,(e.current.flags&128)===128)}catch{}}var ut=Math.clz32?Math.clz32:Dm,Qm=Math.log,Gm=Math.LN2;function Dm(e){return e>>>=0,e===0?32:31-(Qm(e)/Gm|0)|0}var vs=64,ws=4194304;function br(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ks(e,t){var n=e.pendingLanes;if(n===0)return 0;var s=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~l;a!==0?s=br(a):(o&=i,o!==0&&(s=br(o)))}else i=n&~l,i!==0?s=br(i):o!==0&&(s=br(o));if(s===0)return 0;if(t!==0&&t!==s&&!(t&l)&&(l=s&-s,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(s&4&&(s|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)n=31-ut(t),l=1<<n,s|=e[n],t&=~l;return s}function Jm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zm(e,t){for(var n=e.suspendedLanes,s=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-ut(o),a=1<<i,c=l[i];c===-1?(!(a&n)||a&s)&&(l[i]=Jm(a,t)):c<=t&&(e.expiredLanes|=a),o&=~a}}function To(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function _d(){var e=vs;return vs<<=1,!(vs&4194240)&&(vs=64),e}function Wl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function rs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ut(t),e[t]=n}function Km(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-ut(n),o=1<<l;t[l]=0,s[l]=-1,e[l]=-1,n&=~o}}function Ai(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var s=31-ut(n),l=1<<s;l&t|e[s]&t&&(e[s]|=t),n&=~l}}var te=0;function Td(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ld,_i,Pd,Rd,zd,Lo=!1,bs=[],Bt=null,Vt=null,qt=null,Ur=new Map,$r=new Map,Ot=[],Ym="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ba(e,t){switch(e){case"focusin":case"focusout":Bt=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":qt=null;break;case"pointerover":case"pointerout":Ur.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":$r.delete(t.pointerId)}}function fr(e,t,n,s,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:s,nativeEvent:o,targetContainers:[l]},t!==null&&(t=ls(t),t!==null&&_i(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Xm(e,t,n,s,l){switch(t){case"focusin":return Bt=fr(Bt,e,t,n,s,l),!0;case"dragenter":return Vt=fr(Vt,e,t,n,s,l),!0;case"mouseover":return qt=fr(qt,e,t,n,s,l),!0;case"pointerover":var o=l.pointerId;return Ur.set(o,fr(Ur.get(o)||null,e,t,n,s,l)),!0;case"gotpointercapture":return o=l.pointerId,$r.set(o,fr($r.get(o)||null,e,t,n,s,l)),!0}return!1}function Od(e){var t=cn(e.target);if(t!==null){var n=Cn(t);if(n!==null){if(t=n.tag,t===13){if(t=Sd(n),t!==null){e.blockedOn=t,zd(e.priority,function(){Pd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Rs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Po(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var s=new n.constructor(n.type,n);Eo=s,n.target.dispatchEvent(s),Eo=null}else return t=ls(n),t!==null&&_i(t),e.blockedOn=n,!1;t.shift()}return!0}function Va(e,t,n){Rs(e)&&n.delete(t)}function ef(){Lo=!1,Bt!==null&&Rs(Bt)&&(Bt=null),Vt!==null&&Rs(Vt)&&(Vt=null),qt!==null&&Rs(qt)&&(qt=null),Ur.forEach(Va),$r.forEach(Va)}function hr(e,t){e.blockedOn===t&&(e.blockedOn=null,Lo||(Lo=!0,Je.unstable_scheduleCallback(Je.unstable_NormalPriority,ef)))}function Fr(e){function t(l){return hr(l,e)}if(0<bs.length){hr(bs[0],e);for(var n=1;n<bs.length;n++){var s=bs[n];s.blockedOn===e&&(s.blockedOn=null)}}for(Bt!==null&&hr(Bt,e),Vt!==null&&hr(Vt,e),qt!==null&&hr(qt,e),Ur.forEach(t),$r.forEach(t),n=0;n<Ot.length;n++)s=Ot[n],s.blockedOn===e&&(s.blockedOn=null);for(;0<Ot.length&&(n=Ot[0],n.blockedOn===null);)Od(n),n.blockedOn===null&&Ot.shift()}var Jn=Lt.ReactCurrentBatchConfig,Ys=!0;function tf(e,t,n,s){var l=te,o=Jn.transition;Jn.transition=null;try{te=1,Ti(e,t,n,s)}finally{te=l,Jn.transition=o}}function nf(e,t,n,s){var l=te,o=Jn.transition;Jn.transition=null;try{te=4,Ti(e,t,n,s)}finally{te=l,Jn.transition=o}}function Ti(e,t,n,s){if(Ys){var l=Po(e,t,n,s);if(l===null)to(e,t,s,Xs,n),Ba(e,s);else if(Xm(l,e,t,n,s))s.stopPropagation();else if(Ba(e,s),t&4&&-1<Ym.indexOf(e)){for(;l!==null;){var o=ls(l);if(o!==null&&Ld(o),o=Po(e,t,n,s),o===null&&to(e,t,s,Xs,n),o===l)break;l=o}l!==null&&s.stopPropagation()}else to(e,t,s,null,n)}}var Xs=null;function Po(e,t,n,s){if(Xs=null,e=Ei(s),e=cn(e),e!==null)if(t=Cn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Sd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xs=e,null}function Ud(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(qm()){case Ii:return 1;case Id:return 4;case Zs:case Hm:return 16;case Ad:return 536870912;default:return 16}default:return 16}}var $t=null,Li=null,zs=null;function $d(){if(zs)return zs;var e,t=Li,n=t.length,s,l="value"in $t?$t.value:$t.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(s=1;s<=i&&t[n-s]===l[o-s];s++);return zs=l.slice(e,1<s?1-s:void 0)}function Os(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function js(){return!0}function qa(){return!1}function Ke(e){function t(n,s,l,o,i){this._reactName=n,this._targetInst=l,this.type=s,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?js:qa,this.isPropagationStopped=qa,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=js)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=js)},persist:function(){},isPersistent:js}),t}var ir={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pi=Ke(ir),ss=me({},ir,{view:0,detail:0}),rf=Ke(ss),Ql,Gl,pr,vl=me({},ss,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ri,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pr&&(pr&&e.type==="mousemove"?(Ql=e.screenX-pr.screenX,Gl=e.screenY-pr.screenY):Gl=Ql=0,pr=e),Ql)},movementY:function(e){return"movementY"in e?e.movementY:Gl}}),Ha=Ke(vl),sf=me({},vl,{dataTransfer:0}),lf=Ke(sf),of=me({},ss,{relatedTarget:0}),Dl=Ke(of),af=me({},ir,{animationName:0,elapsedTime:0,pseudoElement:0}),cf=Ke(af),df=me({},ir,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),uf=Ke(df),mf=me({},ir,{data:0}),Wa=Ke(mf),ff={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},pf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function gf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=pf[e])?!!t[e]:!1}function Ri(){return gf}var xf=me({},ss,{key:function(e){if(e.key){var t=ff[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Os(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?hf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ri,charCode:function(e){return e.type==="keypress"?Os(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Os(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),yf=Ke(xf),vf=me({},vl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Qa=Ke(vf),wf=me({},ss,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ri}),bf=Ke(wf),jf=me({},ir,{propertyName:0,elapsedTime:0,pseudoElement:0}),Nf=Ke(jf),kf=me({},vl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Sf=Ke(kf),Cf=[9,13,27,32],zi=It&&"CompositionEvent"in window,Sr=null;It&&"documentMode"in document&&(Sr=document.documentMode);var Mf=It&&"TextEvent"in window&&!Sr,Fd=It&&(!zi||Sr&&8<Sr&&11>=Sr),Ga=" ",Da=!1;function Bd(e,t){switch(e){case"keyup":return Cf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Vd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var zn=!1;function Ef(e,t){switch(e){case"compositionend":return Vd(t);case"keypress":return t.which!==32?null:(Da=!0,Ga);case"textInput":return e=t.data,e===Ga&&Da?null:e;default:return null}}function If(e,t){if(zn)return e==="compositionend"||!zi&&Bd(e,t)?(e=$d(),zs=Li=$t=null,zn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fd&&t.locale!=="ko"?null:t.data;default:return null}}var Af={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ja(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Af[e.type]:t==="textarea"}function qd(e,t,n,s){wd(s),t=el(t,"onChange"),0<t.length&&(n=new Pi("onChange","change",null,n,s),e.push({event:n,listeners:t}))}var Cr=null,Br=null;function _f(e){eu(e,0)}function wl(e){var t=$n(e);if(fd(t))return e}function Tf(e,t){if(e==="change")return t}var Hd=!1;if(It){var Jl;if(It){var Zl="oninput"in document;if(!Zl){var Za=document.createElement("div");Za.setAttribute("oninput","return;"),Zl=typeof Za.oninput=="function"}Jl=Zl}else Jl=!1;Hd=Jl&&(!document.documentMode||9<document.documentMode)}function Ka(){Cr&&(Cr.detachEvent("onpropertychange",Wd),Br=Cr=null)}function Wd(e){if(e.propertyName==="value"&&wl(Br)){var t=[];qd(t,Br,e,Ei(e)),kd(_f,t)}}function Lf(e,t,n){e==="focusin"?(Ka(),Cr=t,Br=n,Cr.attachEvent("onpropertychange",Wd)):e==="focusout"&&Ka()}function Pf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return wl(Br)}function Rf(e,t){if(e==="click")return wl(t)}function zf(e,t){if(e==="input"||e==="change")return wl(t)}function Of(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:Of;function Vr(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var l=n[s];if(!go.call(t,l)||!ft(e[l],t[l]))return!1}return!0}function Ya(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xa(e,t){var n=Ya(e);e=0;for(var s;n;){if(n.nodeType===3){if(s=e+n.textContent.length,e<=t&&s>=t)return{node:n,offset:t-e};e=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ya(n)}}function Qd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Qd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Gd(){for(var e=window,t=Gs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Gs(e.document)}return t}function Oi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Uf(e){var t=Gd(),n=e.focusedElem,s=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Qd(n.ownerDocument.documentElement,n)){if(s!==null&&Oi(n)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(s.start,l);s=s.end===void 0?o:Math.min(s.end,l),!e.extend&&o>s&&(l=s,s=o,o=l),l=Xa(n,o);var i=Xa(n,s);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>s?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var $f=It&&"documentMode"in document&&11>=document.documentMode,On=null,Ro=null,Mr=null,zo=!1;function ec(e,t,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zo||On==null||On!==Gs(s)||(s=On,"selectionStart"in s&&Oi(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Mr&&Vr(Mr,s)||(Mr=s,s=el(Ro,"onSelect"),0<s.length&&(t=new Pi("onSelect","select",null,t,n),e.push({event:t,listeners:s}),t.target=On)))}function Ns(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Un={animationend:Ns("Animation","AnimationEnd"),animationiteration:Ns("Animation","AnimationIteration"),animationstart:Ns("Animation","AnimationStart"),transitionend:Ns("Transition","TransitionEnd")},Kl={},Dd={};It&&(Dd=document.createElement("div").style,"AnimationEvent"in window||(delete Un.animationend.animation,delete Un.animationiteration.animation,delete Un.animationstart.animation),"TransitionEvent"in window||delete Un.transitionend.transition);function bl(e){if(Kl[e])return Kl[e];if(!Un[e])return e;var t=Un[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Dd)return Kl[e]=t[n];return e}var Jd=bl("animationend"),Zd=bl("animationiteration"),Kd=bl("animationstart"),Yd=bl("transitionend"),Xd=new Map,tc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Kt(e,t){Xd.set(e,t),Sn(t,[e])}for(var Yl=0;Yl<tc.length;Yl++){var Xl=tc[Yl],Ff=Xl.toLowerCase(),Bf=Xl[0].toUpperCase()+Xl.slice(1);Kt(Ff,"on"+Bf)}Kt(Jd,"onAnimationEnd");Kt(Zd,"onAnimationIteration");Kt(Kd,"onAnimationStart");Kt("dblclick","onDoubleClick");Kt("focusin","onFocus");Kt("focusout","onBlur");Kt(Yd,"onTransitionEnd");Yn("onMouseEnter",["mouseout","mouseover"]);Yn("onMouseLeave",["mouseout","mouseover"]);Yn("onPointerEnter",["pointerout","pointerover"]);Yn("onPointerLeave",["pointerout","pointerover"]);Sn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Sn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Sn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Sn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Sn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Sn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vf=new Set("cancel close invalid load scroll toggle".split(" ").concat(jr));function nc(e,t,n){var s=e.type||"unknown-event";e.currentTarget=n,$m(s,t,void 0,e),e.currentTarget=null}function eu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var s=e[n],l=s.event;s=s.listeners;e:{var o=void 0;if(t)for(var i=s.length-1;0<=i;i--){var a=s[i],c=a.instance,u=a.currentTarget;if(a=a.listener,c!==o&&l.isPropagationStopped())break e;nc(l,a,u),o=c}else for(i=0;i<s.length;i++){if(a=s[i],c=a.instance,u=a.currentTarget,a=a.listener,c!==o&&l.isPropagationStopped())break e;nc(l,a,u),o=c}}}if(Js)throw e=_o,Js=!1,_o=null,e}function le(e,t){var n=t[Bo];n===void 0&&(n=t[Bo]=new Set);var s=e+"__bubble";n.has(s)||(tu(t,e,2,!1),n.add(s))}function eo(e,t,n){var s=0;t&&(s|=4),tu(n,e,s,t)}var ks="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[ks]){e[ks]=!0,ad.forEach(function(n){n!=="selectionchange"&&(Vf.has(n)||eo(n,!1,e),eo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ks]||(t[ks]=!0,eo("selectionchange",!1,t))}}function tu(e,t,n,s){switch(Ud(t)){case 1:var l=tf;break;case 4:l=nf;break;default:l=Ti}n=l.bind(null,t,n,e),l=void 0,!Ao||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),s?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function to(e,t,n,s,l){var o=s;if(!(t&1)&&!(t&2)&&s!==null)e:for(;;){if(s===null)return;var i=s.tag;if(i===3||i===4){var a=s.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(i===4)for(i=s.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;i=i.return}for(;a!==null;){if(i=cn(a),i===null)return;if(c=i.tag,c===5||c===6){s=o=i;continue e}a=a.parentNode}}s=s.return}kd(function(){var u=o,g=Ei(n),x=[];e:{var w=Xd.get(e);if(w!==void 0){var N=Pi,j=e;switch(e){case"keypress":if(Os(n)===0)break e;case"keydown":case"keyup":N=yf;break;case"focusin":j="focus",N=Dl;break;case"focusout":j="blur",N=Dl;break;case"beforeblur":case"afterblur":N=Dl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=Ha;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=lf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=bf;break;case Jd:case Zd:case Kd:N=cf;break;case Yd:N=Nf;break;case"scroll":N=rf;break;case"wheel":N=Sf;break;case"copy":case"cut":case"paste":N=uf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=Qa}var k=(t&4)!==0,S=!k&&e==="scroll",m=k?w!==null?w+"Capture":null:w;k=[];for(var d=u,p;d!==null;){p=d;var M=p.stateNode;if(p.tag===5&&M!==null&&(p=M,m!==null&&(M=Or(d,m),M!=null&&k.push(Hr(d,M,p)))),S)break;d=d.return}0<k.length&&(w=new N(w,j,null,n,g),x.push({event:w,listeners:k}))}}if(!(t&7)){e:{if(w=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",w&&n!==Eo&&(j=n.relatedTarget||n.fromElement)&&(cn(j)||j[At]))break e;if((N||w)&&(w=g.window===g?g:(w=g.ownerDocument)?w.defaultView||w.parentWindow:window,N?(j=n.relatedTarget||n.toElement,N=u,j=j?cn(j):null,j!==null&&(S=Cn(j),j!==S||j.tag!==5&&j.tag!==6)&&(j=null)):(N=null,j=u),N!==j)){if(k=Ha,M="onMouseLeave",m="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(k=Qa,M="onPointerLeave",m="onPointerEnter",d="pointer"),S=N==null?w:$n(N),p=j==null?w:$n(j),w=new k(M,d+"leave",N,n,g),w.target=S,w.relatedTarget=p,M=null,cn(g)===u&&(k=new k(m,d+"enter",j,n,g),k.target=p,k.relatedTarget=S,M=k),S=M,N&&j)t:{for(k=N,m=j,d=0,p=k;p;p=Ln(p))d++;for(p=0,M=m;M;M=Ln(M))p++;for(;0<d-p;)k=Ln(k),d--;for(;0<p-d;)m=Ln(m),p--;for(;d--;){if(k===m||m!==null&&k===m.alternate)break t;k=Ln(k),m=Ln(m)}k=null}else k=null;N!==null&&rc(x,w,N,k,!1),j!==null&&S!==null&&rc(x,S,j,k,!0)}}e:{if(w=u?$n(u):window,N=w.nodeName&&w.nodeName.toLowerCase(),N==="select"||N==="input"&&w.type==="file")var L=Tf;else if(Ja(w))if(Hd)L=zf;else{L=Pf;var P=Lf}else(N=w.nodeName)&&N.toLowerCase()==="input"&&(w.type==="checkbox"||w.type==="radio")&&(L=Rf);if(L&&(L=L(e,u))){qd(x,L,n,g);break e}P&&P(e,w,u),e==="focusout"&&(P=w._wrapperState)&&P.controlled&&w.type==="number"&&No(w,"number",w.value)}switch(P=u?$n(u):window,e){case"focusin":(Ja(P)||P.contentEditable==="true")&&(On=P,Ro=u,Mr=null);break;case"focusout":Mr=Ro=On=null;break;case"mousedown":zo=!0;break;case"contextmenu":case"mouseup":case"dragend":zo=!1,ec(x,n,g);break;case"selectionchange":if($f)break;case"keydown":case"keyup":ec(x,n,g)}var z;if(zi)e:{switch(e){case"compositionstart":var F="onCompositionStart";break e;case"compositionend":F="onCompositionEnd";break e;case"compositionupdate":F="onCompositionUpdate";break e}F=void 0}else zn?Bd(e,n)&&(F="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(F="onCompositionStart");F&&(Fd&&n.locale!=="ko"&&(zn||F!=="onCompositionStart"?F==="onCompositionEnd"&&zn&&(z=$d()):($t=g,Li="value"in $t?$t.value:$t.textContent,zn=!0)),P=el(u,F),0<P.length&&(F=new Wa(F,e,null,n,g),x.push({event:F,listeners:P}),z?F.data=z:(z=Vd(n),z!==null&&(F.data=z)))),(z=Mf?Ef(e,n):If(e,n))&&(u=el(u,"onBeforeInput"),0<u.length&&(g=new Wa("onBeforeInput","beforeinput",null,n,g),x.push({event:g,listeners:u}),g.data=z))}eu(x,t)})}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function el(e,t){for(var n=t+"Capture",s=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Or(e,n),o!=null&&s.unshift(Hr(e,o,l)),o=Or(e,t),o!=null&&s.push(Hr(e,o,l))),e=e.return}return s}function Ln(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function rc(e,t,n,s,l){for(var o=t._reactName,i=[];n!==null&&n!==s;){var a=n,c=a.alternate,u=a.stateNode;if(c!==null&&c===s)break;a.tag===5&&u!==null&&(a=u,l?(c=Or(n,o),c!=null&&i.unshift(Hr(n,c,a))):l||(c=Or(n,o),c!=null&&i.push(Hr(n,c,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var qf=/\r\n?/g,Hf=/\u0000|\uFFFD/g;function sc(e){return(typeof e=="string"?e:""+e).replace(qf,`
`).replace(Hf,"")}function Ss(e,t,n){if(t=sc(t),sc(e)!==t&&n)throw Error(T(425))}function tl(){}var Oo=null,Uo=null;function $o(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Fo=typeof setTimeout=="function"?setTimeout:void 0,Wf=typeof clearTimeout=="function"?clearTimeout:void 0,lc=typeof Promise=="function"?Promise:void 0,Qf=typeof queueMicrotask=="function"?queueMicrotask:typeof lc<"u"?function(e){return lc.resolve(null).then(e).catch(Gf)}:Fo;function Gf(e){setTimeout(function(){throw e})}function no(e,t){var n=t,s=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(s===0){e.removeChild(l),Fr(t);return}s--}else n!=="$"&&n!=="$?"&&n!=="$!"||s++;n=l}while(n);Fr(t)}function Ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function oc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ar=Math.random().toString(36).slice(2),yt="__reactFiber$"+ar,Wr="__reactProps$"+ar,At="__reactContainer$"+ar,Bo="__reactEvents$"+ar,Df="__reactListeners$"+ar,Jf="__reactHandles$"+ar;function cn(e){var t=e[yt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[At]||n[yt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=oc(e);e!==null;){if(n=e[yt])return n;e=oc(e)}return t}e=n,n=e.parentNode}return null}function ls(e){return e=e[yt]||e[At],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function $n(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function jl(e){return e[Wr]||null}var Vo=[],Fn=-1;function Yt(e){return{current:e}}function oe(e){0>Fn||(e.current=Vo[Fn],Vo[Fn]=null,Fn--)}function se(e,t){Fn++,Vo[Fn]=e.current,e.current=t}var Zt={},Le=Yt(Zt),Be=Yt(!1),vn=Zt;function Xn(e,t){var n=e.type.contextTypes;if(!n)return Zt;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ve(e){return e=e.childContextTypes,e!=null}function nl(){oe(Be),oe(Le)}function ic(e,t,n){if(Le.current!==Zt)throw Error(T(168));se(Le,t),se(Be,n)}function nu(e,t,n){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return n;s=s.getChildContext();for(var l in s)if(!(l in t))throw Error(T(108,Tm(e)||"Unknown",l));return me({},n,s)}function rl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Zt,vn=Le.current,se(Le,e),se(Be,Be.current),!0}function ac(e,t,n){var s=e.stateNode;if(!s)throw Error(T(169));n?(e=nu(e,t,vn),s.__reactInternalMemoizedMergedChildContext=e,oe(Be),oe(Le),se(Le,e)):oe(Be),se(Be,n)}var St=null,Nl=!1,ro=!1;function ru(e){St===null?St=[e]:St.push(e)}function Zf(e){Nl=!0,ru(e)}function Xt(){if(!ro&&St!==null){ro=!0;var e=0,t=te;try{var n=St;for(te=1;e<n.length;e++){var s=n[e];do s=s(!0);while(s!==null)}St=null,Nl=!1}catch(l){throw St!==null&&(St=St.slice(e+1)),Ed(Ii,Xt),l}finally{te=t,ro=!1}}return null}var Bn=[],Vn=0,sl=null,ll=0,et=[],tt=0,wn=null,Ct=1,Mt="";function on(e,t){Bn[Vn++]=ll,Bn[Vn++]=sl,sl=e,ll=t}function su(e,t,n){et[tt++]=Ct,et[tt++]=Mt,et[tt++]=wn,wn=e;var s=Ct;e=Mt;var l=32-ut(s)-1;s&=~(1<<l),n+=1;var o=32-ut(t)+l;if(30<o){var i=l-l%5;o=(s&(1<<i)-1).toString(32),s>>=i,l-=i,Ct=1<<32-ut(t)+l|n<<l|s,Mt=o+e}else Ct=1<<o|n<<l|s,Mt=e}function Ui(e){e.return!==null&&(on(e,1),su(e,1,0))}function $i(e){for(;e===sl;)sl=Bn[--Vn],Bn[Vn]=null,ll=Bn[--Vn],Bn[Vn]=null;for(;e===wn;)wn=et[--tt],et[tt]=null,Mt=et[--tt],et[tt]=null,Ct=et[--tt],et[tt]=null}var De=null,Ge=null,ae=!1,dt=null;function lu(e,t){var n=nt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function cc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Ge=Ht(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Ge=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=wn!==null?{id:Ct,overflow:Mt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=nt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Ge=null,!0):!1;default:return!1}}function qo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ho(e){if(ae){var t=Ge;if(t){var n=t;if(!cc(e,t)){if(qo(e))throw Error(T(418));t=Ht(n.nextSibling);var s=De;t&&cc(e,t)?lu(s,n):(e.flags=e.flags&-4097|2,ae=!1,De=e)}}else{if(qo(e))throw Error(T(418));e.flags=e.flags&-4097|2,ae=!1,De=e}}}function dc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function Cs(e){if(e!==De)return!1;if(!ae)return dc(e),ae=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!$o(e.type,e.memoizedProps)),t&&(t=Ge)){if(qo(e))throw ou(),Error(T(418));for(;t;)lu(e,t),t=Ht(t.nextSibling)}if(dc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ge=Ht(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ge=null}}else Ge=De?Ht(e.stateNode.nextSibling):null;return!0}function ou(){for(var e=Ge;e;)e=Ht(e.nextSibling)}function er(){Ge=De=null,ae=!1}function Fi(e){dt===null?dt=[e]:dt.push(e)}var Kf=Lt.ReactCurrentBatchConfig;function gr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var s=n.stateNode}if(!s)throw Error(T(147,e));var l=s,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=l.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Ms(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uc(e){var t=e._init;return t(e._payload)}function iu(e){function t(m,d){if(e){var p=m.deletions;p===null?(m.deletions=[d],m.flags|=16):p.push(d)}}function n(m,d){if(!e)return null;for(;d!==null;)t(m,d),d=d.sibling;return null}function s(m,d){for(m=new Map;d!==null;)d.key!==null?m.set(d.key,d):m.set(d.index,d),d=d.sibling;return m}function l(m,d){return m=Dt(m,d),m.index=0,m.sibling=null,m}function o(m,d,p){return m.index=p,e?(p=m.alternate,p!==null?(p=p.index,p<d?(m.flags|=2,d):p):(m.flags|=2,d)):(m.flags|=1048576,d)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,d,p,M){return d===null||d.tag!==6?(d=uo(p,m.mode,M),d.return=m,d):(d=l(d,p),d.return=m,d)}function c(m,d,p,M){var L=p.type;return L===Rn?g(m,d,p.props.children,M,p.key):d!==null&&(d.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Rt&&uc(L)===d.type)?(M=l(d,p.props),M.ref=gr(m,d,p),M.return=m,M):(M=Hs(p.type,p.key,p.props,null,m.mode,M),M.ref=gr(m,d,p),M.return=m,M)}function u(m,d,p,M){return d===null||d.tag!==4||d.stateNode.containerInfo!==p.containerInfo||d.stateNode.implementation!==p.implementation?(d=mo(p,m.mode,M),d.return=m,d):(d=l(d,p.children||[]),d.return=m,d)}function g(m,d,p,M,L){return d===null||d.tag!==7?(d=hn(p,m.mode,M,L),d.return=m,d):(d=l(d,p),d.return=m,d)}function x(m,d,p){if(typeof d=="string"&&d!==""||typeof d=="number")return d=uo(""+d,m.mode,p),d.return=m,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case gs:return p=Hs(d.type,d.key,d.props,null,m.mode,p),p.ref=gr(m,null,d),p.return=m,p;case Pn:return d=mo(d,m.mode,p),d.return=m,d;case Rt:var M=d._init;return x(m,M(d._payload),p)}if(wr(d)||ur(d))return d=hn(d,m.mode,p,null),d.return=m,d;Ms(m,d)}return null}function w(m,d,p,M){var L=d!==null?d.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return L!==null?null:a(m,d,""+p,M);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case gs:return p.key===L?c(m,d,p,M):null;case Pn:return p.key===L?u(m,d,p,M):null;case Rt:return L=p._init,w(m,d,L(p._payload),M)}if(wr(p)||ur(p))return L!==null?null:g(m,d,p,M,null);Ms(m,p)}return null}function N(m,d,p,M,L){if(typeof M=="string"&&M!==""||typeof M=="number")return m=m.get(p)||null,a(d,m,""+M,L);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case gs:return m=m.get(M.key===null?p:M.key)||null,c(d,m,M,L);case Pn:return m=m.get(M.key===null?p:M.key)||null,u(d,m,M,L);case Rt:var P=M._init;return N(m,d,p,P(M._payload),L)}if(wr(M)||ur(M))return m=m.get(p)||null,g(d,m,M,L,null);Ms(d,M)}return null}function j(m,d,p,M){for(var L=null,P=null,z=d,F=d=0,D=null;z!==null&&F<p.length;F++){z.index>F?(D=z,z=null):D=z.sibling;var y=w(m,z,p[F],M);if(y===null){z===null&&(z=D);break}e&&z&&y.alternate===null&&t(m,z),d=o(y,d,F),P===null?L=y:P.sibling=y,P=y,z=D}if(F===p.length)return n(m,z),ae&&on(m,F),L;if(z===null){for(;F<p.length;F++)z=x(m,p[F],M),z!==null&&(d=o(z,d,F),P===null?L=z:P.sibling=z,P=z);return ae&&on(m,F),L}for(z=s(m,z);F<p.length;F++)D=N(z,m,F,p[F],M),D!==null&&(e&&D.alternate!==null&&z.delete(D.key===null?F:D.key),d=o(D,d,F),P===null?L=D:P.sibling=D,P=D);return e&&z.forEach(function(O){return t(m,O)}),ae&&on(m,F),L}function k(m,d,p,M){var L=ur(p);if(typeof L!="function")throw Error(T(150));if(p=L.call(p),p==null)throw Error(T(151));for(var P=L=null,z=d,F=d=0,D=null,y=p.next();z!==null&&!y.done;F++,y=p.next()){z.index>F?(D=z,z=null):D=z.sibling;var O=w(m,z,y.value,M);if(O===null){z===null&&(z=D);break}e&&z&&O.alternate===null&&t(m,z),d=o(O,d,F),P===null?L=O:P.sibling=O,P=O,z=D}if(y.done)return n(m,z),ae&&on(m,F),L;if(z===null){for(;!y.done;F++,y=p.next())y=x(m,y.value,M),y!==null&&(d=o(y,d,F),P===null?L=y:P.sibling=y,P=y);return ae&&on(m,F),L}for(z=s(m,z);!y.done;F++,y=p.next())y=N(z,m,F,y.value,M),y!==null&&(e&&y.alternate!==null&&z.delete(y.key===null?F:y.key),d=o(y,d,F),P===null?L=y:P.sibling=y,P=y);return e&&z.forEach(function(X){return t(m,X)}),ae&&on(m,F),L}function S(m,d,p,M){if(typeof p=="object"&&p!==null&&p.type===Rn&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case gs:e:{for(var L=p.key,P=d;P!==null;){if(P.key===L){if(L=p.type,L===Rn){if(P.tag===7){n(m,P.sibling),d=l(P,p.props.children),d.return=m,m=d;break e}}else if(P.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Rt&&uc(L)===P.type){n(m,P.sibling),d=l(P,p.props),d.ref=gr(m,P,p),d.return=m,m=d;break e}n(m,P);break}else t(m,P);P=P.sibling}p.type===Rn?(d=hn(p.props.children,m.mode,M,p.key),d.return=m,m=d):(M=Hs(p.type,p.key,p.props,null,m.mode,M),M.ref=gr(m,d,p),M.return=m,m=M)}return i(m);case Pn:e:{for(P=p.key;d!==null;){if(d.key===P)if(d.tag===4&&d.stateNode.containerInfo===p.containerInfo&&d.stateNode.implementation===p.implementation){n(m,d.sibling),d=l(d,p.children||[]),d.return=m,m=d;break e}else{n(m,d);break}else t(m,d);d=d.sibling}d=mo(p,m.mode,M),d.return=m,m=d}return i(m);case Rt:return P=p._init,S(m,d,P(p._payload),M)}if(wr(p))return j(m,d,p,M);if(ur(p))return k(m,d,p,M);Ms(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,d!==null&&d.tag===6?(n(m,d.sibling),d=l(d,p),d.return=m,m=d):(n(m,d),d=uo(p,m.mode,M),d.return=m,m=d),i(m)):n(m,d)}return S}var tr=iu(!0),au=iu(!1),ol=Yt(null),il=null,qn=null,Bi=null;function Vi(){Bi=qn=il=null}function qi(e){var t=ol.current;oe(ol),e._currentValue=t}function Wo(e,t,n){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===n)break;e=e.return}}function Zn(e,t){il=e,Bi=qn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Fe=!0),e.firstContext=null)}function st(e){var t=e._currentValue;if(Bi!==e)if(e={context:e,memoizedValue:t,next:null},qn===null){if(il===null)throw Error(T(308));qn=e,il.dependencies={lanes:0,firstContext:e}}else qn=qn.next=e;return t}var dn=null;function Hi(e){dn===null?dn=[e]:dn.push(e)}function cu(e,t,n,s){var l=t.interleaved;return l===null?(n.next=n,Hi(t)):(n.next=l.next,l.next=n),t.interleaved=n,_t(e,s)}function _t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var zt=!1;function Wi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function du(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Et(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Wt(e,t,n){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,ee&2){var l=s.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),s.pending=t,_t(e,n)}return l=s.interleaved,l===null?(t.next=t,Hi(s)):(t.next=l.next,l.next=t),s.interleaved=t,_t(e,n)}function Us(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,Ai(e,n)}}function mc(e,t){var n=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:s.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:s.shared,effects:s.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function al(e,t,n,s){var l=e.updateQueue;zt=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,u=c.next;c.next=null,i===null?o=u:i.next=u,i=c;var g=e.alternate;g!==null&&(g=g.updateQueue,a=g.lastBaseUpdate,a!==i&&(a===null?g.firstBaseUpdate=u:a.next=u,g.lastBaseUpdate=c))}if(o!==null){var x=l.baseState;i=0,g=u=c=null,a=o;do{var w=a.lane,N=a.eventTime;if((s&w)===w){g!==null&&(g=g.next={eventTime:N,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var j=e,k=a;switch(w=t,N=n,k.tag){case 1:if(j=k.payload,typeof j=="function"){x=j.call(N,x,w);break e}x=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=k.payload,w=typeof j=="function"?j.call(N,x,w):j,w==null)break e;x=me({},x,w);break e;case 2:zt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,w=l.effects,w===null?l.effects=[a]:w.push(a))}else N={eventTime:N,lane:w,tag:a.tag,payload:a.payload,callback:a.callback,next:null},g===null?(u=g=N,c=x):g=g.next=N,i|=w;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;w=a,a=w.next,w.next=null,l.lastBaseUpdate=w,l.shared.pending=null}}while(!0);if(g===null&&(c=x),l.baseState=c,l.firstBaseUpdate=u,l.lastBaseUpdate=g,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);jn|=i,e.lanes=i,e.memoizedState=x}}function fc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],l=s.callback;if(l!==null){if(s.callback=null,s=n,typeof l!="function")throw Error(T(191,l));l.call(s)}}}var os={},bt=Yt(os),Qr=Yt(os),Gr=Yt(os);function un(e){if(e===os)throw Error(T(174));return e}function Qi(e,t){switch(se(Gr,t),se(Qr,e),se(bt,os),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:So(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=So(t,e)}oe(bt),se(bt,t)}function nr(){oe(bt),oe(Qr),oe(Gr)}function uu(e){un(Gr.current);var t=un(bt.current),n=So(t,e.type);t!==n&&(se(Qr,e),se(bt,n))}function Gi(e){Qr.current===e&&(oe(bt),oe(Qr))}var de=Yt(0);function cl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var so=[];function Di(){for(var e=0;e<so.length;e++)so[e]._workInProgressVersionPrimary=null;so.length=0}var $s=Lt.ReactCurrentDispatcher,lo=Lt.ReactCurrentBatchConfig,bn=0,ue=null,we=null,je=null,dl=!1,Er=!1,Dr=0,Yf=0;function Ae(){throw Error(T(321))}function Ji(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ft(e[n],t[n]))return!1;return!0}function Zi(e,t,n,s,l,o){if(bn=o,ue=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,$s.current=e===null||e.memoizedState===null?nh:rh,e=n(s,l),Er){o=0;do{if(Er=!1,Dr=0,25<=o)throw Error(T(301));o+=1,je=we=null,t.updateQueue=null,$s.current=sh,e=n(s,l)}while(Er)}if($s.current=ul,t=we!==null&&we.next!==null,bn=0,je=we=ue=null,dl=!1,t)throw Error(T(300));return e}function Ki(){var e=Dr!==0;return Dr=0,e}function xt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return je===null?ue.memoizedState=je=e:je=je.next=e,je}function lt(){if(we===null){var e=ue.alternate;e=e!==null?e.memoizedState:null}else e=we.next;var t=je===null?ue.memoizedState:je.next;if(t!==null)je=t,we=e;else{if(e===null)throw Error(T(310));we=e,e={memoizedState:we.memoizedState,baseState:we.baseState,baseQueue:we.baseQueue,queue:we.queue,next:null},je===null?ue.memoizedState=je=e:je=je.next=e}return je}function Jr(e,t){return typeof t=="function"?t(e):t}function oo(e){var t=lt(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var s=we,l=s.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}s.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,s=s.baseState;var a=i=null,c=null,u=o;do{var g=u.lane;if((bn&g)===g)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),s=u.hasEagerState?u.eagerState:e(s,u.action);else{var x={lane:g,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(a=c=x,i=s):c=c.next=x,ue.lanes|=g,jn|=g}u=u.next}while(u!==null&&u!==o);c===null?i=s:c.next=a,ft(s,t.memoizedState)||(Fe=!0),t.memoizedState=s,t.baseState=i,t.baseQueue=c,n.lastRenderedState=s}if(e=n.interleaved,e!==null){l=e;do o=l.lane,ue.lanes|=o,jn|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function io(e){var t=lt(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var s=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);ft(o,t.memoizedState)||(Fe=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,s]}function mu(){}function fu(e,t){var n=ue,s=lt(),l=t(),o=!ft(s.memoizedState,l);if(o&&(s.memoizedState=l,Fe=!0),s=s.queue,Yi(gu.bind(null,n,s,e),[e]),s.getSnapshot!==t||o||je!==null&&je.memoizedState.tag&1){if(n.flags|=2048,Zr(9,pu.bind(null,n,s,l,t),void 0,null),Ne===null)throw Error(T(349));bn&30||hu(n,t,l)}return l}function hu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function pu(e,t,n,s){t.value=n,t.getSnapshot=s,xu(t)&&yu(e)}function gu(e,t,n){return n(function(){xu(t)&&yu(e)})}function xu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ft(e,n)}catch{return!0}}function yu(e){var t=_t(e,1);t!==null&&mt(t,e,1,-1)}function hc(e){var t=xt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Jr,lastRenderedState:e},t.queue=e,e=e.dispatch=th.bind(null,ue,e),[t.memoizedState,e]}function Zr(e,t,n,s){return e={tag:e,create:t,destroy:n,deps:s,next:null},t=ue.updateQueue,t===null?(t={lastEffect:null,stores:null},ue.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(s=n.next,n.next=e,e.next=s,t.lastEffect=e)),e}function vu(){return lt().memoizedState}function Fs(e,t,n,s){var l=xt();ue.flags|=e,l.memoizedState=Zr(1|t,n,void 0,s===void 0?null:s)}function kl(e,t,n,s){var l=lt();s=s===void 0?null:s;var o=void 0;if(we!==null){var i=we.memoizedState;if(o=i.destroy,s!==null&&Ji(s,i.deps)){l.memoizedState=Zr(t,n,o,s);return}}ue.flags|=e,l.memoizedState=Zr(1|t,n,o,s)}function pc(e,t){return Fs(8390656,8,e,t)}function Yi(e,t){return kl(2048,8,e,t)}function wu(e,t){return kl(4,2,e,t)}function bu(e,t){return kl(4,4,e,t)}function ju(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Nu(e,t,n){return n=n!=null?n.concat([e]):null,kl(4,4,ju.bind(null,t,e),n)}function Xi(){}function ku(e,t){var n=lt();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&Ji(t,s[1])?s[0]:(n.memoizedState=[e,t],e)}function Su(e,t){var n=lt();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&Ji(t,s[1])?s[0]:(e=e(),n.memoizedState=[e,t],e)}function Cu(e,t,n){return bn&21?(ft(n,t)||(n=_d(),ue.lanes|=n,jn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Fe=!0),e.memoizedState=n)}function Xf(e,t){var n=te;te=n!==0&&4>n?n:4,e(!0);var s=lo.transition;lo.transition={};try{e(!1),t()}finally{te=n,lo.transition=s}}function Mu(){return lt().memoizedState}function eh(e,t,n){var s=Gt(e);if(n={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null},Eu(e))Iu(t,n);else if(n=cu(e,t,n,s),n!==null){var l=ze();mt(n,e,s,l),Au(n,t,s)}}function th(e,t,n){var s=Gt(e),l={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null};if(Eu(e))Iu(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(l.hasEagerState=!0,l.eagerState=a,ft(a,i)){var c=t.interleaved;c===null?(l.next=l,Hi(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=cu(e,t,l,s),n!==null&&(l=ze(),mt(n,e,s,l),Au(n,t,s))}}function Eu(e){var t=e.alternate;return e===ue||t!==null&&t===ue}function Iu(e,t){Er=dl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Au(e,t,n){if(n&4194240){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,Ai(e,n)}}var ul={readContext:st,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useInsertionEffect:Ae,useLayoutEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useMutableSource:Ae,useSyncExternalStore:Ae,useId:Ae,unstable_isNewReconciler:!1},nh={readContext:st,useCallback:function(e,t){return xt().memoizedState=[e,t===void 0?null:t],e},useContext:st,useEffect:pc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Fs(4194308,4,ju.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Fs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Fs(4,2,e,t)},useMemo:function(e,t){var n=xt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var s=xt();return t=n!==void 0?n(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=eh.bind(null,ue,e),[s.memoizedState,e]},useRef:function(e){var t=xt();return e={current:e},t.memoizedState=e},useState:hc,useDebugValue:Xi,useDeferredValue:function(e){return xt().memoizedState=e},useTransition:function(){var e=hc(!1),t=e[0];return e=Xf.bind(null,e[1]),xt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var s=ue,l=xt();if(ae){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),Ne===null)throw Error(T(349));bn&30||hu(s,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,pc(gu.bind(null,s,o,e),[e]),s.flags|=2048,Zr(9,pu.bind(null,s,o,n,t),void 0,null),n},useId:function(){var e=xt(),t=Ne.identifierPrefix;if(ae){var n=Mt,s=Ct;n=(s&~(1<<32-ut(s)-1)).toString(32)+n,t=":"+t+"R"+n,n=Dr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Yf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},rh={readContext:st,useCallback:ku,useContext:st,useEffect:Yi,useImperativeHandle:Nu,useInsertionEffect:wu,useLayoutEffect:bu,useMemo:Su,useReducer:oo,useRef:vu,useState:function(){return oo(Jr)},useDebugValue:Xi,useDeferredValue:function(e){var t=lt();return Cu(t,we.memoizedState,e)},useTransition:function(){var e=oo(Jr)[0],t=lt().memoizedState;return[e,t]},useMutableSource:mu,useSyncExternalStore:fu,useId:Mu,unstable_isNewReconciler:!1},sh={readContext:st,useCallback:ku,useContext:st,useEffect:Yi,useImperativeHandle:Nu,useInsertionEffect:wu,useLayoutEffect:bu,useMemo:Su,useReducer:io,useRef:vu,useState:function(){return io(Jr)},useDebugValue:Xi,useDeferredValue:function(e){var t=lt();return we===null?t.memoizedState=e:Cu(t,we.memoizedState,e)},useTransition:function(){var e=io(Jr)[0],t=lt().memoizedState;return[e,t]},useMutableSource:mu,useSyncExternalStore:fu,useId:Mu,unstable_isNewReconciler:!1};function at(e,t){if(e&&e.defaultProps){t=me({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Qo(e,t,n,s){t=e.memoizedState,n=n(s,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Sl={isMounted:function(e){return(e=e._reactInternals)?Cn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var s=ze(),l=Gt(e),o=Et(s,l);o.payload=t,n!=null&&(o.callback=n),t=Wt(e,o,l),t!==null&&(mt(t,e,l,s),Us(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var s=ze(),l=Gt(e),o=Et(s,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Wt(e,o,l),t!==null&&(mt(t,e,l,s),Us(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ze(),s=Gt(e),l=Et(n,s);l.tag=2,t!=null&&(l.callback=t),t=Wt(e,l,s),t!==null&&(mt(t,e,s,n),Us(t,e,s))}};function gc(e,t,n,s,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,o,i):t.prototype&&t.prototype.isPureReactComponent?!Vr(n,s)||!Vr(l,o):!0}function _u(e,t,n){var s=!1,l=Zt,o=t.contextType;return typeof o=="object"&&o!==null?o=st(o):(l=Ve(t)?vn:Le.current,s=t.contextTypes,o=(s=s!=null)?Xn(e,l):Zt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Sl,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function xc(e,t,n,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,s),t.state!==e&&Sl.enqueueReplaceState(t,t.state,null)}function Go(e,t,n,s){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Wi(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=st(o):(o=Ve(t)?vn:Le.current,l.context=Xn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Qo(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Sl.enqueueReplaceState(l,l.state,null),al(e,n,l,s),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function rr(e,t){try{var n="",s=t;do n+=_m(s),s=s.return;while(s);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function ao(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Do(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var lh=typeof WeakMap=="function"?WeakMap:Map;function Tu(e,t,n){n=Et(-1,n),n.tag=3,n.payload={element:null};var s=t.value;return n.callback=function(){fl||(fl=!0,si=s),Do(e,t)},n}function Lu(e,t,n){n=Et(-1,n),n.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var l=t.value;n.payload=function(){return s(l)},n.callback=function(){Do(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Do(e,t),typeof s!="function"&&(Qt===null?Qt=new Set([this]):Qt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function yc(e,t,n){var s=e.pingCache;if(s===null){s=e.pingCache=new lh;var l=new Set;s.set(t,l)}else l=s.get(t),l===void 0&&(l=new Set,s.set(t,l));l.has(n)||(l.add(n),e=vh.bind(null,e,t,n),t.then(e,e))}function vc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function wc(e,t,n,s,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Et(-1,1),t.tag=2,Wt(n,t,1))),n.lanes|=1),e)}var oh=Lt.ReactCurrentOwner,Fe=!1;function Re(e,t,n,s){t.child=e===null?au(t,null,n,s):tr(t,e.child,n,s)}function bc(e,t,n,s,l){n=n.render;var o=t.ref;return Zn(t,l),s=Zi(e,t,n,s,o,l),n=Ki(),e!==null&&!Fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Tt(e,t,l)):(ae&&n&&Ui(t),t.flags|=1,Re(e,t,s,l),t.child)}function jc(e,t,n,s,l){if(e===null){var o=n.type;return typeof o=="function"&&!ia(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Pu(e,t,o,s,l)):(e=Hs(n.type,null,s,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:Vr,n(i,s)&&e.ref===t.ref)return Tt(e,t,l)}return t.flags|=1,e=Dt(o,s),e.ref=t.ref,e.return=t,t.child=e}function Pu(e,t,n,s,l){if(e!==null){var o=e.memoizedProps;if(Vr(o,s)&&e.ref===t.ref)if(Fe=!1,t.pendingProps=s=o,(e.lanes&l)!==0)e.flags&131072&&(Fe=!0);else return t.lanes=e.lanes,Tt(e,t,l)}return Jo(e,t,n,s,l)}function Ru(e,t,n){var s=t.pendingProps,l=s.children,o=e!==null?e.memoizedState:null;if(s.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},se(Wn,Qe),Qe|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,se(Wn,Qe),Qe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=o!==null?o.baseLanes:n,se(Wn,Qe),Qe|=s}else o!==null?(s=o.baseLanes|n,t.memoizedState=null):s=n,se(Wn,Qe),Qe|=s;return Re(e,t,l,n),t.child}function zu(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Jo(e,t,n,s,l){var o=Ve(n)?vn:Le.current;return o=Xn(t,o),Zn(t,l),n=Zi(e,t,n,s,o,l),s=Ki(),e!==null&&!Fe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Tt(e,t,l)):(ae&&s&&Ui(t),t.flags|=1,Re(e,t,n,l),t.child)}function Nc(e,t,n,s,l){if(Ve(n)){var o=!0;rl(t)}else o=!1;if(Zn(t,l),t.stateNode===null)Bs(e,t),_u(t,n,s),Go(t,n,s,l),s=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=st(u):(u=Ve(n)?vn:Le.current,u=Xn(t,u));var g=n.getDerivedStateFromProps,x=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function";x||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==s||c!==u)&&xc(t,i,s,u),zt=!1;var w=t.memoizedState;i.state=w,al(t,s,i,l),c=t.memoizedState,a!==s||w!==c||Be.current||zt?(typeof g=="function"&&(Qo(t,n,g,s),c=t.memoizedState),(a=zt||gc(t,n,a,s,w,c,u))?(x||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=c),i.props=s,i.state=c,i.context=u,s=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{i=t.stateNode,du(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:at(t.type,a),i.props=u,x=t.pendingProps,w=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=st(c):(c=Ve(n)?vn:Le.current,c=Xn(t,c));var N=n.getDerivedStateFromProps;(g=typeof N=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==x||w!==c)&&xc(t,i,s,c),zt=!1,w=t.memoizedState,i.state=w,al(t,s,i,l);var j=t.memoizedState;a!==x||w!==j||Be.current||zt?(typeof N=="function"&&(Qo(t,n,N,s),j=t.memoizedState),(u=zt||gc(t,n,u,s,w,j,c)||!1)?(g||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(s,j,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(s,j,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=j),i.props=s,i.state=j,i.context=c,s=u):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),s=!1)}return Zo(e,t,n,s,o,l)}function Zo(e,t,n,s,l,o){zu(e,t);var i=(t.flags&128)!==0;if(!s&&!i)return l&&ac(t,n,!1),Tt(e,t,o);s=t.stateNode,oh.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&i?(t.child=tr(t,e.child,null,o),t.child=tr(t,null,a,o)):Re(e,t,a,o),t.memoizedState=s.state,l&&ac(t,n,!0),t.child}function Ou(e){var t=e.stateNode;t.pendingContext?ic(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ic(e,t.context,!1),Qi(e,t.containerInfo)}function kc(e,t,n,s,l){return er(),Fi(l),t.flags|=256,Re(e,t,n,s),t.child}var Ko={dehydrated:null,treeContext:null,retryLane:0};function Yo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Uu(e,t,n){var s=t.pendingProps,l=de.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),se(de,l&1),e===null)return Ho(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=s.children,e=s.fallback,o?(s=t.mode,o=t.child,i={mode:"hidden",children:i},!(s&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=El(i,s,0,null),e=hn(e,s,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Yo(n),t.memoizedState=Ko,e):ea(t,i));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return ih(e,t,i,s,a,l,n);if(o){o=s.fallback,i=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:s.children};return!(i&1)&&t.child!==l?(s=t.child,s.childLanes=0,s.pendingProps=c,t.deletions=null):(s=Dt(l,c),s.subtreeFlags=l.subtreeFlags&14680064),a!==null?o=Dt(a,o):(o=hn(o,i,n,null),o.flags|=2),o.return=t,s.return=t,s.sibling=o,t.child=s,s=o,o=t.child,i=e.child.memoizedState,i=i===null?Yo(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=Ko,s}return o=e.child,e=o.sibling,s=Dt(o,{mode:"visible",children:s.children}),!(t.mode&1)&&(s.lanes=n),s.return=t,s.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=s,t.memoizedState=null,s}function ea(e,t){return t=El({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Es(e,t,n,s){return s!==null&&Fi(s),tr(t,e.child,null,n),e=ea(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ih(e,t,n,s,l,o,i){if(n)return t.flags&256?(t.flags&=-257,s=ao(Error(T(422))),Es(e,t,i,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=s.fallback,l=t.mode,s=El({mode:"visible",children:s.children},l,0,null),o=hn(o,l,i,null),o.flags|=2,s.return=t,o.return=t,s.sibling=o,t.child=s,t.mode&1&&tr(t,e.child,null,i),t.child.memoizedState=Yo(i),t.memoizedState=Ko,o);if(!(t.mode&1))return Es(e,t,i,null);if(l.data==="$!"){if(s=l.nextSibling&&l.nextSibling.dataset,s)var a=s.dgst;return s=a,o=Error(T(419)),s=ao(o,s,void 0),Es(e,t,i,s)}if(a=(i&e.childLanes)!==0,Fe||a){if(s=Ne,s!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(s.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,_t(e,l),mt(s,e,l,-1))}return oa(),s=ao(Error(T(421))),Es(e,t,i,s)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=wh.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,Ge=Ht(l.nextSibling),De=t,ae=!0,dt=null,e!==null&&(et[tt++]=Ct,et[tt++]=Mt,et[tt++]=wn,Ct=e.id,Mt=e.overflow,wn=t),t=ea(t,s.children),t.flags|=4096,t)}function Sc(e,t,n){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Wo(e.return,t,n)}function co(e,t,n,s,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=s,o.tail=n,o.tailMode=l)}function $u(e,t,n){var s=t.pendingProps,l=s.revealOrder,o=s.tail;if(Re(e,t,s.children,n),s=de.current,s&2)s=s&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Sc(e,n,t);else if(e.tag===19)Sc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(se(de,s),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&cl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),co(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&cl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}co(t,!0,n,null,o);break;case"together":co(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Bs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Tt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),jn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Dt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Dt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ah(e,t,n){switch(t.tag){case 3:Ou(t),er();break;case 5:uu(t);break;case 1:Ve(t.type)&&rl(t);break;case 4:Qi(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,l=t.memoizedProps.value;se(ol,s._currentValue),s._currentValue=l;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(se(de,de.current&1),t.flags|=128,null):n&t.child.childLanes?Uu(e,t,n):(se(de,de.current&1),e=Tt(e,t,n),e!==null?e.sibling:null);se(de,de.current&1);break;case 19:if(s=(n&t.childLanes)!==0,e.flags&128){if(s)return $u(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),se(de,de.current),s)break;return null;case 22:case 23:return t.lanes=0,Ru(e,t,n)}return Tt(e,t,n)}var Fu,Xo,Bu,Vu;Fu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Xo=function(){};Bu=function(e,t,n,s){var l=e.memoizedProps;if(l!==s){e=t.stateNode,un(bt.current);var o=null;switch(n){case"input":l=bo(e,l),s=bo(e,s),o=[];break;case"select":l=me({},l,{value:void 0}),s=me({},s,{value:void 0}),o=[];break;case"textarea":l=ko(e,l),s=ko(e,s),o=[];break;default:typeof l.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=tl)}Co(n,s);var i;n=null;for(u in l)if(!s.hasOwnProperty(u)&&l.hasOwnProperty(u)&&l[u]!=null)if(u==="style"){var a=l[u];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Rr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in s){var c=s[u];if(a=l!=null?l[u]:void 0,s.hasOwnProperty(u)&&c!==a&&(c!=null||a!=null))if(u==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(o||(o=[]),o.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(o=o||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(o=o||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Rr.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&le("scroll",e),o||a===c||(o=[])):(o=o||[]).push(u,c))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};Vu=function(e,t,n,s){n!==s&&(t.flags|=4)};function xr(e,t){if(!ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function _e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,s=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags&14680064,s|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags,s|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=s,e.childLanes=n,t}function ch(e,t,n){var s=t.pendingProps;switch($i(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _e(t),null;case 1:return Ve(t.type)&&nl(),_e(t),null;case 3:return s=t.stateNode,nr(),oe(Be),oe(Le),Di(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(Cs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,dt!==null&&(ii(dt),dt=null))),Xo(e,t),_e(t),null;case 5:Gi(t);var l=un(Gr.current);if(n=t.type,e!==null&&t.stateNode!=null)Bu(e,t,n,s,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(T(166));return _e(t),null}if(e=un(bt.current),Cs(t)){s=t.stateNode,n=t.type;var o=t.memoizedProps;switch(s[yt]=t,s[Wr]=o,e=(t.mode&1)!==0,n){case"dialog":le("cancel",s),le("close",s);break;case"iframe":case"object":case"embed":le("load",s);break;case"video":case"audio":for(l=0;l<jr.length;l++)le(jr[l],s);break;case"source":le("error",s);break;case"img":case"image":case"link":le("error",s),le("load",s);break;case"details":le("toggle",s);break;case"input":Pa(s,o),le("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!o.multiple},le("invalid",s);break;case"textarea":za(s,o),le("invalid",s)}Co(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?s.textContent!==a&&(o.suppressHydrationWarning!==!0&&Ss(s.textContent,a,e),l=["children",a]):typeof a=="number"&&s.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&Ss(s.textContent,a,e),l=["children",""+a]):Rr.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&le("scroll",s)}switch(n){case"input":xs(s),Ra(s,o,!0);break;case"textarea":xs(s),Oa(s);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(s.onclick=tl)}s=l,t.updateQueue=s,s!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=i.createElement(n,{is:s.is}):(e=i.createElement(n),n==="select"&&(i=e,s.multiple?i.multiple=!0:s.size&&(i.size=s.size))):e=i.createElementNS(e,n),e[yt]=t,e[Wr]=s,Fu(e,t,!1,!1),t.stateNode=e;e:{switch(i=Mo(n,s),n){case"dialog":le("cancel",e),le("close",e),l=s;break;case"iframe":case"object":case"embed":le("load",e),l=s;break;case"video":case"audio":for(l=0;l<jr.length;l++)le(jr[l],e);l=s;break;case"source":le("error",e),l=s;break;case"img":case"image":case"link":le("error",e),le("load",e),l=s;break;case"details":le("toggle",e),l=s;break;case"input":Pa(e,s),l=bo(e,s),le("invalid",e);break;case"option":l=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},l=me({},s,{value:void 0}),le("invalid",e);break;case"textarea":za(e,s),l=ko(e,s),le("invalid",e);break;default:l=s}Co(n,l),a=l;for(o in a)if(a.hasOwnProperty(o)){var c=a[o];o==="style"?vd(e,c):o==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&xd(e,c)):o==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&zr(e,c):typeof c=="number"&&zr(e,""+c):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Rr.hasOwnProperty(o)?c!=null&&o==="onScroll"&&le("scroll",e):c!=null&&ki(e,o,c,i))}switch(n){case"input":xs(e),Ra(e,s,!1);break;case"textarea":xs(e),Oa(e);break;case"option":s.value!=null&&e.setAttribute("value",""+Jt(s.value));break;case"select":e.multiple=!!s.multiple,o=s.value,o!=null?Qn(e,!!s.multiple,o,!1):s.defaultValue!=null&&Qn(e,!!s.multiple,s.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=tl)}switch(n){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return _e(t),null;case 6:if(e&&t.stateNode!=null)Vu(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(T(166));if(n=un(Gr.current),un(bt.current),Cs(t)){if(s=t.stateNode,n=t.memoizedProps,s[yt]=t,(o=s.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:Ss(s.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ss(s.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else s=(n.nodeType===9?n:n.ownerDocument).createTextNode(s),s[yt]=t,t.stateNode=s}return _e(t),null;case 13:if(oe(de),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ae&&Ge!==null&&t.mode&1&&!(t.flags&128))ou(),er(),t.flags|=98560,o=!1;else if(o=Cs(t),s!==null&&s.dehydrated!==null){if(e===null){if(!o)throw Error(T(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(T(317));o[yt]=t}else er(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;_e(t),o=!1}else dt!==null&&(ii(dt),dt=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,t.mode&1&&(e===null||de.current&1?be===0&&(be=3):oa())),t.updateQueue!==null&&(t.flags|=4),_e(t),null);case 4:return nr(),Xo(e,t),e===null&&qr(t.stateNode.containerInfo),_e(t),null;case 10:return qi(t.type._context),_e(t),null;case 17:return Ve(t.type)&&nl(),_e(t),null;case 19:if(oe(de),o=t.memoizedState,o===null)return _e(t),null;if(s=(t.flags&128)!==0,i=o.rendering,i===null)if(s)xr(o,!1);else{if(be!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=cl(e),i!==null){for(t.flags|=128,xr(o,!1),s=i.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=n,n=t.child;n!==null;)o=n,e=s,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return se(de,de.current&1|2),t.child}e=e.sibling}o.tail!==null&&ye()>sr&&(t.flags|=128,s=!0,xr(o,!1),t.lanes=4194304)}else{if(!s)if(e=cl(i),e!==null){if(t.flags|=128,s=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),xr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!ae)return _e(t),null}else 2*ye()-o.renderingStartTime>sr&&n!==1073741824&&(t.flags|=128,s=!0,xr(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=ye(),t.sibling=null,n=de.current,se(de,s?n&1|2:n&1),t):(_e(t),null);case 22:case 23:return la(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&t.mode&1?Qe&1073741824&&(_e(t),t.subtreeFlags&6&&(t.flags|=8192)):_e(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function dh(e,t){switch($i(t),t.tag){case 1:return Ve(t.type)&&nl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return nr(),oe(Be),oe(Le),Di(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Gi(t),null;case 13:if(oe(de),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));er()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(de),null;case 4:return nr(),null;case 10:return qi(t.type._context),null;case 22:case 23:return la(),null;case 24:return null;default:return null}}var Is=!1,Te=!1,uh=typeof WeakSet=="function"?WeakSet:Set,V=null;function Hn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(s){ge(e,t,s)}else n.current=null}function ei(e,t,n){try{n()}catch(s){ge(e,t,s)}}var Cc=!1;function mh(e,t){if(Oo=Ys,e=Gd(),Oi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var l=s.anchorOffset,o=s.focusNode;s=s.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,c=-1,u=0,g=0,x=e,w=null;t:for(;;){for(var N;x!==n||l!==0&&x.nodeType!==3||(a=i+l),x!==o||s!==0&&x.nodeType!==3||(c=i+s),x.nodeType===3&&(i+=x.nodeValue.length),(N=x.firstChild)!==null;)w=x,x=N;for(;;){if(x===e)break t;if(w===n&&++u===l&&(a=i),w===o&&++g===s&&(c=i),(N=x.nextSibling)!==null)break;x=w,w=x.parentNode}x=N}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Uo={focusedElem:e,selectionRange:n},Ys=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var k=j.memoizedProps,S=j.memoizedState,m=t.stateNode,d=m.getSnapshotBeforeUpdate(t.elementType===t.type?k:at(t.type,k),S);m.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(M){ge(t,t.return,M)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return j=Cc,Cc=!1,j}function Ir(e,t,n){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var l=s=s.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&ei(t,n,o)}l=l.next}while(l!==s)}}function Cl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var s=n.create;n.destroy=s()}n=n.next}while(n!==t)}}function ti(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function qu(e){var t=e.alternate;t!==null&&(e.alternate=null,qu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[yt],delete t[Wr],delete t[Bo],delete t[Df],delete t[Jf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Hu(e){return e.tag===5||e.tag===3||e.tag===4}function Mc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Hu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ni(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=tl));else if(s!==4&&(e=e.child,e!==null))for(ni(e,t,n),e=e.sibling;e!==null;)ni(e,t,n),e=e.sibling}function ri(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(ri(e,t,n),e=e.sibling;e!==null;)ri(e,t,n),e=e.sibling}var Ce=null,ct=!1;function Pt(e,t,n){for(n=n.child;n!==null;)Wu(e,t,n),n=n.sibling}function Wu(e,t,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount(yl,n)}catch{}switch(n.tag){case 5:Te||Hn(n,t);case 6:var s=Ce,l=ct;Ce=null,Pt(e,t,n),Ce=s,ct=l,Ce!==null&&(ct?(e=Ce,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ce.removeChild(n.stateNode));break;case 18:Ce!==null&&(ct?(e=Ce,n=n.stateNode,e.nodeType===8?no(e.parentNode,n):e.nodeType===1&&no(e,n),Fr(e)):no(Ce,n.stateNode));break;case 4:s=Ce,l=ct,Ce=n.stateNode.containerInfo,ct=!0,Pt(e,t,n),Ce=s,ct=l;break;case 0:case 11:case 14:case 15:if(!Te&&(s=n.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){l=s=s.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&ei(n,t,i),l=l.next}while(l!==s)}Pt(e,t,n);break;case 1:if(!Te&&(Hn(n,t),s=n.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=n.memoizedProps,s.state=n.memoizedState,s.componentWillUnmount()}catch(a){ge(n,t,a)}Pt(e,t,n);break;case 21:Pt(e,t,n);break;case 22:n.mode&1?(Te=(s=Te)||n.memoizedState!==null,Pt(e,t,n),Te=s):Pt(e,t,n);break;default:Pt(e,t,n)}}function Ec(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new uh),t.forEach(function(s){var l=bh.bind(null,e,s);n.has(s)||(n.add(s),s.then(l,l))})}}function it(e,t){var n=t.deletions;if(n!==null)for(var s=0;s<n.length;s++){var l=n[s];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Ce=a.stateNode,ct=!1;break e;case 3:Ce=a.stateNode.containerInfo,ct=!0;break e;case 4:Ce=a.stateNode.containerInfo,ct=!0;break e}a=a.return}if(Ce===null)throw Error(T(160));Wu(o,i,l),Ce=null,ct=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(u){ge(l,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Qu(t,e),t=t.sibling}function Qu(e,t){var n=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(it(t,e),gt(e),s&4){try{Ir(3,e,e.return),Cl(3,e)}catch(k){ge(e,e.return,k)}try{Ir(5,e,e.return)}catch(k){ge(e,e.return,k)}}break;case 1:it(t,e),gt(e),s&512&&n!==null&&Hn(n,n.return);break;case 5:if(it(t,e),gt(e),s&512&&n!==null&&Hn(n,n.return),e.flags&32){var l=e.stateNode;try{zr(l,"")}catch(k){ge(e,e.return,k)}}if(s&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&hd(l,o),Mo(a,i);var u=Mo(a,o);for(i=0;i<c.length;i+=2){var g=c[i],x=c[i+1];g==="style"?vd(l,x):g==="dangerouslySetInnerHTML"?xd(l,x):g==="children"?zr(l,x):ki(l,g,x,u)}switch(a){case"input":jo(l,o);break;case"textarea":pd(l,o);break;case"select":var w=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var N=o.value;N!=null?Qn(l,!!o.multiple,N,!1):w!==!!o.multiple&&(o.defaultValue!=null?Qn(l,!!o.multiple,o.defaultValue,!0):Qn(l,!!o.multiple,o.multiple?[]:"",!1))}l[Wr]=o}catch(k){ge(e,e.return,k)}}break;case 6:if(it(t,e),gt(e),s&4){if(e.stateNode===null)throw Error(T(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(k){ge(e,e.return,k)}}break;case 3:if(it(t,e),gt(e),s&4&&n!==null&&n.memoizedState.isDehydrated)try{Fr(t.containerInfo)}catch(k){ge(e,e.return,k)}break;case 4:it(t,e),gt(e);break;case 13:it(t,e),gt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(ra=ye())),s&4&&Ec(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(Te=(u=Te)||g,it(t,e),Te=u):it(t,e),gt(e),s&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!g&&e.mode&1)for(V=e,g=e.child;g!==null;){for(x=V=g;V!==null;){switch(w=V,N=w.child,w.tag){case 0:case 11:case 14:case 15:Ir(4,w,w.return);break;case 1:Hn(w,w.return);var j=w.stateNode;if(typeof j.componentWillUnmount=="function"){s=w,n=w.return;try{t=s,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(k){ge(s,n,k)}}break;case 5:Hn(w,w.return);break;case 22:if(w.memoizedState!==null){Ac(x);continue}}N!==null?(N.return=w,V=N):Ac(x)}g=g.sibling}e:for(g=null,x=e;;){if(x.tag===5){if(g===null){g=x;try{l=x.stateNode,u?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=x.stateNode,c=x.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=yd("display",i))}catch(k){ge(e,e.return,k)}}}else if(x.tag===6){if(g===null)try{x.stateNode.nodeValue=u?"":x.memoizedProps}catch(k){ge(e,e.return,k)}}else if((x.tag!==22&&x.tag!==23||x.memoizedState===null||x===e)&&x.child!==null){x.child.return=x,x=x.child;continue}if(x===e)break e;for(;x.sibling===null;){if(x.return===null||x.return===e)break e;g===x&&(g=null),x=x.return}g===x&&(g=null),x.sibling.return=x.return,x=x.sibling}}break;case 19:it(t,e),gt(e),s&4&&Ec(e);break;case 21:break;default:it(t,e),gt(e)}}function gt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Hu(n)){var s=n;break e}n=n.return}throw Error(T(160))}switch(s.tag){case 5:var l=s.stateNode;s.flags&32&&(zr(l,""),s.flags&=-33);var o=Mc(e);ri(e,o,l);break;case 3:case 4:var i=s.stateNode.containerInfo,a=Mc(e);ni(e,a,i);break;default:throw Error(T(161))}}catch(c){ge(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function fh(e,t,n){V=e,Gu(e)}function Gu(e,t,n){for(var s=(e.mode&1)!==0;V!==null;){var l=V,o=l.child;if(l.tag===22&&s){var i=l.memoizedState!==null||Is;if(!i){var a=l.alternate,c=a!==null&&a.memoizedState!==null||Te;a=Is;var u=Te;if(Is=i,(Te=c)&&!u)for(V=l;V!==null;)i=V,c=i.child,i.tag===22&&i.memoizedState!==null?_c(l):c!==null?(c.return=i,V=c):_c(l);for(;o!==null;)V=o,Gu(o),o=o.sibling;V=l,Is=a,Te=u}Ic(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,V=o):Ic(e)}}function Ic(e){for(;V!==null;){var t=V;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Te||Cl(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!Te)if(n===null)s.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:at(t.type,n.memoizedProps);s.componentDidUpdate(l,n.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&fc(t,o,s);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}fc(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var g=u.memoizedState;if(g!==null){var x=g.dehydrated;x!==null&&Fr(x)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}Te||t.flags&512&&ti(t)}catch(w){ge(t,t.return,w)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function Ac(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function _c(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Cl(4,t)}catch(c){ge(t,n,c)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var l=t.return;try{s.componentDidMount()}catch(c){ge(t,l,c)}}var o=t.return;try{ti(t)}catch(c){ge(t,o,c)}break;case 5:var i=t.return;try{ti(t)}catch(c){ge(t,i,c)}}}catch(c){ge(t,t.return,c)}if(t===e){V=null;break}var a=t.sibling;if(a!==null){a.return=t.return,V=a;break}V=t.return}}var hh=Math.ceil,ml=Lt.ReactCurrentDispatcher,ta=Lt.ReactCurrentOwner,rt=Lt.ReactCurrentBatchConfig,ee=0,Ne=null,ve=null,Me=0,Qe=0,Wn=Yt(0),be=0,Kr=null,jn=0,Ml=0,na=0,Ar=null,$e=null,ra=0,sr=1/0,kt=null,fl=!1,si=null,Qt=null,As=!1,Ft=null,hl=0,_r=0,li=null,Vs=-1,qs=0;function ze(){return ee&6?ye():Vs!==-1?Vs:Vs=ye()}function Gt(e){return e.mode&1?ee&2&&Me!==0?Me&-Me:Kf.transition!==null?(qs===0&&(qs=_d()),qs):(e=te,e!==0||(e=window.event,e=e===void 0?16:Ud(e.type)),e):1}function mt(e,t,n,s){if(50<_r)throw _r=0,li=null,Error(T(185));rs(e,n,s),(!(ee&2)||e!==Ne)&&(e===Ne&&(!(ee&2)&&(Ml|=n),be===4&&Ut(e,Me)),qe(e,s),n===1&&ee===0&&!(t.mode&1)&&(sr=ye()+500,Nl&&Xt()))}function qe(e,t){var n=e.callbackNode;Zm(e,t);var s=Ks(e,e===Ne?Me:0);if(s===0)n!==null&&Fa(n),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(n!=null&&Fa(n),t===1)e.tag===0?Zf(Tc.bind(null,e)):ru(Tc.bind(null,e)),Qf(function(){!(ee&6)&&Xt()}),n=null;else{switch(Td(s)){case 1:n=Ii;break;case 4:n=Id;break;case 16:n=Zs;break;case 536870912:n=Ad;break;default:n=Zs}n=t0(n,Du.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Du(e,t){if(Vs=-1,qs=0,ee&6)throw Error(T(327));var n=e.callbackNode;if(Kn()&&e.callbackNode!==n)return null;var s=Ks(e,e===Ne?Me:0);if(s===0)return null;if(s&30||s&e.expiredLanes||t)t=pl(e,s);else{t=s;var l=ee;ee|=2;var o=Zu();(Ne!==e||Me!==t)&&(kt=null,sr=ye()+500,fn(e,t));do try{xh();break}catch(a){Ju(e,a)}while(!0);Vi(),ml.current=o,ee=l,ve!==null?t=0:(Ne=null,Me=0,t=be)}if(t!==0){if(t===2&&(l=To(e),l!==0&&(s=l,t=oi(e,l))),t===1)throw n=Kr,fn(e,0),Ut(e,s),qe(e,ye()),n;if(t===6)Ut(e,s);else{if(l=e.current.alternate,!(s&30)&&!ph(l)&&(t=pl(e,s),t===2&&(o=To(e),o!==0&&(s=o,t=oi(e,o))),t===1))throw n=Kr,fn(e,0),Ut(e,s),qe(e,ye()),n;switch(e.finishedWork=l,e.finishedLanes=s,t){case 0:case 1:throw Error(T(345));case 2:an(e,$e,kt);break;case 3:if(Ut(e,s),(s&130023424)===s&&(t=ra+500-ye(),10<t)){if(Ks(e,0)!==0)break;if(l=e.suspendedLanes,(l&s)!==s){ze(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Fo(an.bind(null,e,$e,kt),t);break}an(e,$e,kt);break;case 4:if(Ut(e,s),(s&4194240)===s)break;for(t=e.eventTimes,l=-1;0<s;){var i=31-ut(s);o=1<<i,i=t[i],i>l&&(l=i),s&=~o}if(s=l,s=ye()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*hh(s/1960))-s,10<s){e.timeoutHandle=Fo(an.bind(null,e,$e,kt),s);break}an(e,$e,kt);break;case 5:an(e,$e,kt);break;default:throw Error(T(329))}}}return qe(e,ye()),e.callbackNode===n?Du.bind(null,e):null}function oi(e,t){var n=Ar;return e.current.memoizedState.isDehydrated&&(fn(e,t).flags|=256),e=pl(e,t),e!==2&&(t=$e,$e=n,t!==null&&ii(t)),e}function ii(e){$e===null?$e=e:$e.push.apply($e,e)}function ph(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var s=0;s<n.length;s++){var l=n[s],o=l.getSnapshot;l=l.value;try{if(!ft(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ut(e,t){for(t&=~na,t&=~Ml,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ut(t),s=1<<n;e[n]=-1,t&=~s}}function Tc(e){if(ee&6)throw Error(T(327));Kn();var t=Ks(e,0);if(!(t&1))return qe(e,ye()),null;var n=pl(e,t);if(e.tag!==0&&n===2){var s=To(e);s!==0&&(t=s,n=oi(e,s))}if(n===1)throw n=Kr,fn(e,0),Ut(e,t),qe(e,ye()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,an(e,$e,kt),qe(e,ye()),null}function sa(e,t){var n=ee;ee|=1;try{return e(t)}finally{ee=n,ee===0&&(sr=ye()+500,Nl&&Xt())}}function Nn(e){Ft!==null&&Ft.tag===0&&!(ee&6)&&Kn();var t=ee;ee|=1;var n=rt.transition,s=te;try{if(rt.transition=null,te=1,e)return e()}finally{te=s,rt.transition=n,ee=t,!(ee&6)&&Xt()}}function la(){Qe=Wn.current,oe(Wn)}function fn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Wf(n)),ve!==null)for(n=ve.return;n!==null;){var s=n;switch($i(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&nl();break;case 3:nr(),oe(Be),oe(Le),Di();break;case 5:Gi(s);break;case 4:nr();break;case 13:oe(de);break;case 19:oe(de);break;case 10:qi(s.type._context);break;case 22:case 23:la()}n=n.return}if(Ne=e,ve=e=Dt(e.current,null),Me=Qe=t,be=0,Kr=null,na=Ml=jn=0,$e=Ar=null,dn!==null){for(t=0;t<dn.length;t++)if(n=dn[t],s=n.interleaved,s!==null){n.interleaved=null;var l=s.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,s.next=i}n.pending=s}dn=null}return e}function Ju(e,t){do{var n=ve;try{if(Vi(),$s.current=ul,dl){for(var s=ue.memoizedState;s!==null;){var l=s.queue;l!==null&&(l.pending=null),s=s.next}dl=!1}if(bn=0,je=we=ue=null,Er=!1,Dr=0,ta.current=null,n===null||n.return===null){be=1,Kr=t,ve=null;break}e:{var o=e,i=n.return,a=n,c=t;if(t=Me,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,g=a,x=g.tag;if(!(g.mode&1)&&(x===0||x===11||x===15)){var w=g.alternate;w?(g.updateQueue=w.updateQueue,g.memoizedState=w.memoizedState,g.lanes=w.lanes):(g.updateQueue=null,g.memoizedState=null)}var N=vc(i);if(N!==null){N.flags&=-257,wc(N,i,a,o,t),N.mode&1&&yc(o,u,t),t=N,c=u;var j=t.updateQueue;if(j===null){var k=new Set;k.add(c),t.updateQueue=k}else j.add(c);break e}else{if(!(t&1)){yc(o,u,t),oa();break e}c=Error(T(426))}}else if(ae&&a.mode&1){var S=vc(i);if(S!==null){!(S.flags&65536)&&(S.flags|=256),wc(S,i,a,o,t),Fi(rr(c,a));break e}}o=c=rr(c,a),be!==4&&(be=2),Ar===null?Ar=[o]:Ar.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Tu(o,c,t);mc(o,m);break e;case 1:a=c;var d=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof d.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Qt===null||!Qt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var M=Lu(o,a,t);mc(o,M);break e}}o=o.return}while(o!==null)}Yu(n)}catch(L){t=L,ve===n&&n!==null&&(ve=n=n.return);continue}break}while(!0)}function Zu(){var e=ml.current;return ml.current=ul,e===null?ul:e}function oa(){(be===0||be===3||be===2)&&(be=4),Ne===null||!(jn&268435455)&&!(Ml&268435455)||Ut(Ne,Me)}function pl(e,t){var n=ee;ee|=2;var s=Zu();(Ne!==e||Me!==t)&&(kt=null,fn(e,t));do try{gh();break}catch(l){Ju(e,l)}while(!0);if(Vi(),ee=n,ml.current=s,ve!==null)throw Error(T(261));return Ne=null,Me=0,be}function gh(){for(;ve!==null;)Ku(ve)}function xh(){for(;ve!==null&&!Bm();)Ku(ve)}function Ku(e){var t=e0(e.alternate,e,Qe);e.memoizedProps=e.pendingProps,t===null?Yu(e):ve=t,ta.current=null}function Yu(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=dh(n,t),n!==null){n.flags&=32767,ve=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{be=6,ve=null;return}}else if(n=ch(n,t,Qe),n!==null){ve=n;return}if(t=t.sibling,t!==null){ve=t;return}ve=t=e}while(t!==null);be===0&&(be=5)}function an(e,t,n){var s=te,l=rt.transition;try{rt.transition=null,te=1,yh(e,t,n,s)}finally{rt.transition=l,te=s}return null}function yh(e,t,n,s){do Kn();while(Ft!==null);if(ee&6)throw Error(T(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Km(e,o),e===Ne&&(ve=Ne=null,Me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||As||(As=!0,t0(Zs,function(){return Kn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=rt.transition,rt.transition=null;var i=te;te=1;var a=ee;ee|=4,ta.current=null,mh(e,n),Qu(n,e),Uf(Uo),Ys=!!Oo,Uo=Oo=null,e.current=n,fh(n),Vm(),ee=a,te=i,rt.transition=o}else e.current=n;if(As&&(As=!1,Ft=e,hl=l),o=e.pendingLanes,o===0&&(Qt=null),Wm(n.stateNode),qe(e,ye()),t!==null)for(s=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],s(l.value,{componentStack:l.stack,digest:l.digest});if(fl)throw fl=!1,e=si,si=null,e;return hl&1&&e.tag!==0&&Kn(),o=e.pendingLanes,o&1?e===li?_r++:(_r=0,li=e):_r=0,Xt(),null}function Kn(){if(Ft!==null){var e=Td(hl),t=rt.transition,n=te;try{if(rt.transition=null,te=16>e?16:e,Ft===null)var s=!1;else{if(e=Ft,Ft=null,hl=0,ee&6)throw Error(T(331));var l=ee;for(ee|=4,V=e.current;V!==null;){var o=V,i=o.child;if(V.flags&16){var a=o.deletions;if(a!==null){for(var c=0;c<a.length;c++){var u=a[c];for(V=u;V!==null;){var g=V;switch(g.tag){case 0:case 11:case 15:Ir(8,g,o)}var x=g.child;if(x!==null)x.return=g,V=x;else for(;V!==null;){g=V;var w=g.sibling,N=g.return;if(qu(g),g===u){V=null;break}if(w!==null){w.return=N,V=w;break}V=N}}}var j=o.alternate;if(j!==null){var k=j.child;if(k!==null){j.child=null;do{var S=k.sibling;k.sibling=null,k=S}while(k!==null)}}V=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,V=i;else e:for(;V!==null;){if(o=V,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Ir(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,V=m;break e}V=o.return}}var d=e.current;for(V=d;V!==null;){i=V;var p=i.child;if(i.subtreeFlags&2064&&p!==null)p.return=i,V=p;else e:for(i=d;V!==null;){if(a=V,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Cl(9,a)}}catch(L){ge(a,a.return,L)}if(a===i){V=null;break e}var M=a.sibling;if(M!==null){M.return=a.return,V=M;break e}V=a.return}}if(ee=l,Xt(),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot(yl,e)}catch{}s=!0}return s}finally{te=n,rt.transition=t}}return!1}function Lc(e,t,n){t=rr(n,t),t=Tu(e,t,1),e=Wt(e,t,1),t=ze(),e!==null&&(rs(e,1,t),qe(e,t))}function ge(e,t,n){if(e.tag===3)Lc(e,e,n);else for(;t!==null;){if(t.tag===3){Lc(t,e,n);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(Qt===null||!Qt.has(s))){e=rr(n,e),e=Lu(t,e,1),t=Wt(t,e,1),e=ze(),t!==null&&(rs(t,1,e),qe(t,e));break}}t=t.return}}function vh(e,t,n){var s=e.pingCache;s!==null&&s.delete(t),t=ze(),e.pingedLanes|=e.suspendedLanes&n,Ne===e&&(Me&n)===n&&(be===4||be===3&&(Me&130023424)===Me&&500>ye()-ra?fn(e,0):na|=n),qe(e,t)}function Xu(e,t){t===0&&(e.mode&1?(t=ws,ws<<=1,!(ws&130023424)&&(ws=4194304)):t=1);var n=ze();e=_t(e,t),e!==null&&(rs(e,t,n),qe(e,n))}function wh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Xu(e,n)}function bh(e,t){var n=0;switch(e.tag){case 13:var s=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(T(314))}s!==null&&s.delete(t),Xu(e,n)}var e0;e0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Be.current)Fe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Fe=!1,ah(e,t,n);Fe=!!(e.flags&131072)}else Fe=!1,ae&&t.flags&1048576&&su(t,ll,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;Bs(e,t),e=t.pendingProps;var l=Xn(t,Le.current);Zn(t,n),l=Zi(null,t,s,e,l,n);var o=Ki();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ve(s)?(o=!0,rl(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Wi(t),l.updater=Sl,t.stateNode=l,l._reactInternals=t,Go(t,s,e,n),t=Zo(null,t,s,!0,o,n)):(t.tag=0,ae&&o&&Ui(t),Re(null,t,l,n),t=t.child),t;case 16:s=t.elementType;e:{switch(Bs(e,t),e=t.pendingProps,l=s._init,s=l(s._payload),t.type=s,l=t.tag=Nh(s),e=at(s,e),l){case 0:t=Jo(null,t,s,e,n);break e;case 1:t=Nc(null,t,s,e,n);break e;case 11:t=bc(null,t,s,e,n);break e;case 14:t=jc(null,t,s,at(s.type,e),n);break e}throw Error(T(306,s,""))}return t;case 0:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:at(s,l),Jo(e,t,s,l,n);case 1:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:at(s,l),Nc(e,t,s,l,n);case 3:e:{if(Ou(t),e===null)throw Error(T(387));s=t.pendingProps,o=t.memoizedState,l=o.element,du(e,t),al(t,s,null,n);var i=t.memoizedState;if(s=i.element,o.isDehydrated)if(o={element:s,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=rr(Error(T(423)),t),t=kc(e,t,s,n,l);break e}else if(s!==l){l=rr(Error(T(424)),t),t=kc(e,t,s,n,l);break e}else for(Ge=Ht(t.stateNode.containerInfo.firstChild),De=t,ae=!0,dt=null,n=au(t,null,s,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(er(),s===l){t=Tt(e,t,n);break e}Re(e,t,s,n)}t=t.child}return t;case 5:return uu(t),e===null&&Ho(t),s=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,$o(s,l)?i=null:o!==null&&$o(s,o)&&(t.flags|=32),zu(e,t),Re(e,t,i,n),t.child;case 6:return e===null&&Ho(t),null;case 13:return Uu(e,t,n);case 4:return Qi(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=tr(t,null,s,n):Re(e,t,s,n),t.child;case 11:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:at(s,l),bc(e,t,s,l,n);case 7:return Re(e,t,t.pendingProps,n),t.child;case 8:return Re(e,t,t.pendingProps.children,n),t.child;case 12:return Re(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(s=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,se(ol,s._currentValue),s._currentValue=i,o!==null)if(ft(o.value,i)){if(o.children===l.children&&!Be.current){t=Tt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var c=a.firstContext;c!==null;){if(c.context===s){if(o.tag===1){c=Et(-1,n&-n),c.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var g=u.pending;g===null?c.next=c:(c.next=g.next,g.next=c),u.pending=c}}o.lanes|=n,c=o.alternate,c!==null&&(c.lanes|=n),Wo(o.return,n,t),a.lanes|=n;break}c=c.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(T(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Wo(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}Re(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,s=t.pendingProps.children,Zn(t,n),l=st(l),s=s(l),t.flags|=1,Re(e,t,s,n),t.child;case 14:return s=t.type,l=at(s,t.pendingProps),l=at(s.type,l),jc(e,t,s,l,n);case 15:return Pu(e,t,t.type,t.pendingProps,n);case 17:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:at(s,l),Bs(e,t),t.tag=1,Ve(s)?(e=!0,rl(t)):e=!1,Zn(t,n),_u(t,s,l),Go(t,s,l,n),Zo(null,t,s,!0,e,n);case 19:return $u(e,t,n);case 22:return Ru(e,t,n)}throw Error(T(156,t.tag))};function t0(e,t){return Ed(e,t)}function jh(e,t,n,s){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nt(e,t,n,s){return new jh(e,t,n,s)}function ia(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Nh(e){if(typeof e=="function")return ia(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ci)return 11;if(e===Mi)return 14}return 2}function Dt(e,t){var n=e.alternate;return n===null?(n=nt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Hs(e,t,n,s,l,o){var i=2;if(s=e,typeof e=="function")ia(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Rn:return hn(n.children,l,o,t);case Si:i=8,l|=8;break;case xo:return e=nt(12,n,t,l|2),e.elementType=xo,e.lanes=o,e;case yo:return e=nt(13,n,t,l),e.elementType=yo,e.lanes=o,e;case vo:return e=nt(19,n,t,l),e.elementType=vo,e.lanes=o,e;case ud:return El(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case cd:i=10;break e;case dd:i=9;break e;case Ci:i=11;break e;case Mi:i=14;break e;case Rt:i=16,s=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=nt(i,n,t,l),t.elementType=e,t.type=s,t.lanes=o,t}function hn(e,t,n,s){return e=nt(7,e,s,t),e.lanes=n,e}function El(e,t,n,s){return e=nt(22,e,s,t),e.elementType=ud,e.lanes=n,e.stateNode={isHidden:!1},e}function uo(e,t,n){return e=nt(6,e,null,t),e.lanes=n,e}function mo(e,t,n){return t=nt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function kh(e,t,n,s,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Wl(0),this.expirationTimes=Wl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Wl(0),this.identifierPrefix=s,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function aa(e,t,n,s,l,o,i,a,c){return e=new kh(e,t,n,a,c),t===1?(t=1,o===!0&&(t|=8)):t=0,o=nt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:s,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Wi(o),e}function Sh(e,t,n){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Pn,key:s==null?null:""+s,children:e,containerInfo:t,implementation:n}}function n0(e){if(!e)return Zt;e=e._reactInternals;e:{if(Cn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ve(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Ve(n))return nu(e,n,t)}return t}function r0(e,t,n,s,l,o,i,a,c){return e=aa(n,s,!0,e,l,o,i,a,c),e.context=n0(null),n=e.current,s=ze(),l=Gt(n),o=Et(s,l),o.callback=t??null,Wt(n,o,l),e.current.lanes=l,rs(e,l,s),qe(e,s),e}function Il(e,t,n,s){var l=t.current,o=ze(),i=Gt(l);return n=n0(n),t.context===null?t.context=n:t.pendingContext=n,t=Et(o,i),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=Wt(l,t,i),e!==null&&(mt(e,l,i,o),Us(e,l,i)),i}function gl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Pc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ca(e,t){Pc(e,t),(e=e.alternate)&&Pc(e,t)}function Ch(){return null}var s0=typeof reportError=="function"?reportError:function(e){console.error(e)};function da(e){this._internalRoot=e}Al.prototype.render=da.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));Il(e,t,null,null)};Al.prototype.unmount=da.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Nn(function(){Il(null,e,null,null)}),t[At]=null}};function Al(e){this._internalRoot=e}Al.prototype.unstable_scheduleHydration=function(e){if(e){var t=Rd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ot.length&&t!==0&&t<Ot[n].priority;n++);Ot.splice(n,0,e),n===0&&Od(e)}};function ua(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function _l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Rc(){}function Mh(e,t,n,s,l){if(l){if(typeof s=="function"){var o=s;s=function(){var u=gl(i);o.call(u)}}var i=r0(t,s,e,0,null,!1,!1,"",Rc);return e._reactRootContainer=i,e[At]=i.current,qr(e.nodeType===8?e.parentNode:e),Nn(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof s=="function"){var a=s;s=function(){var u=gl(c);a.call(u)}}var c=aa(e,0,!1,null,null,!1,!1,"",Rc);return e._reactRootContainer=c,e[At]=c.current,qr(e.nodeType===8?e.parentNode:e),Nn(function(){Il(t,c,n,s)}),c}function Tl(e,t,n,s,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var a=l;l=function(){var c=gl(i);a.call(c)}}Il(t,i,e,l)}else i=Mh(n,t,e,l,s);return gl(i)}Ld=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=br(t.pendingLanes);n!==0&&(Ai(t,n|1),qe(t,ye()),!(ee&6)&&(sr=ye()+500,Xt()))}break;case 13:Nn(function(){var s=_t(e,1);if(s!==null){var l=ze();mt(s,e,1,l)}}),ca(e,1)}};_i=function(e){if(e.tag===13){var t=_t(e,134217728);if(t!==null){var n=ze();mt(t,e,134217728,n)}ca(e,134217728)}};Pd=function(e){if(e.tag===13){var t=Gt(e),n=_t(e,t);if(n!==null){var s=ze();mt(n,e,t,s)}ca(e,t)}};Rd=function(){return te};zd=function(e,t){var n=te;try{return te=e,t()}finally{te=n}};Io=function(e,t,n){switch(t){case"input":if(jo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var s=n[t];if(s!==e&&s.form===e.form){var l=jl(s);if(!l)throw Error(T(90));fd(s),jo(s,l)}}}break;case"textarea":pd(e,n);break;case"select":t=n.value,t!=null&&Qn(e,!!n.multiple,t,!1)}};jd=sa;Nd=Nn;var Eh={usingClientEntryPoint:!1,Events:[ls,$n,jl,wd,bd,sa]},yr={findFiberByHostInstance:cn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ih={bundleType:yr.bundleType,version:yr.version,rendererPackageName:yr.rendererPackageName,rendererConfig:yr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Lt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Cd(e),e===null?null:e.stateNode},findFiberByHostInstance:yr.findFiberByHostInstance||Ch,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var _s=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!_s.isDisabled&&_s.supportsFiber)try{yl=_s.inject(Ih),wt=_s}catch{}}Ze.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Eh;Ze.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ua(t))throw Error(T(200));return Sh(e,t,null,n)};Ze.createRoot=function(e,t){if(!ua(e))throw Error(T(299));var n=!1,s="",l=s0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=aa(e,1,!1,null,null,n,!1,s,l),e[At]=t.current,qr(e.nodeType===8?e.parentNode:e),new da(t)};Ze.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Cd(t),e=e===null?null:e.stateNode,e};Ze.flushSync=function(e){return Nn(e)};Ze.hydrate=function(e,t,n){if(!_l(t))throw Error(T(200));return Tl(null,e,t,!0,n)};Ze.hydrateRoot=function(e,t,n){if(!ua(e))throw Error(T(405));var s=n!=null&&n.hydratedSources||null,l=!1,o="",i=s0;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=r0(t,null,e,1,n??null,l,!1,o,i),e[At]=t.current,qr(e),s)for(e=0;e<s.length;e++)n=s[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Al(t)};Ze.render=function(e,t,n){if(!_l(t))throw Error(T(200));return Tl(null,e,t,!1,n)};Ze.unmountComponentAtNode=function(e){if(!_l(e))throw Error(T(40));return e._reactRootContainer?(Nn(function(){Tl(null,null,e,!1,function(){e._reactRootContainer=null,e[At]=null})}),!0):!1};Ze.unstable_batchedUpdates=sa;Ze.unstable_renderSubtreeIntoContainer=function(e,t,n,s){if(!_l(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return Tl(e,t,n,!1,s)};Ze.version="18.3.1-next-f1338f8080-20240426";function l0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(l0)}catch(e){console.error(e)}}l0(),ld.exports=Ze;var Ah=ld.exports,o0,zc=Ah;o0=zc.createRoot,zc.hydrateRoot;/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _h=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i0=(...e)=>e.filter((t,n,s)=>!!t&&s.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Th={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lh=v.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:l="",children:o,iconNode:i,...a},c)=>v.createElement("svg",{ref:c,...Th,width:t,height:t,stroke:e,strokeWidth:s?Number(n)*24/Number(t):n,className:i0("lucide",l),...a},[...i.map(([u,g])=>v.createElement(u,g)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=(e,t)=>{const n=v.forwardRef(({className:s,...l},o)=>v.createElement(Lh,{ref:o,iconNode:t,className:i0(`lucide-${_h(e)}`,s),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ai=U("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a0=U("Apple",[["path",{d:"M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z",key:"3s7exb"}],["path",{d:"M10 2c1 .5 2 2 2 5",key:"fcco2y"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph=U("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rh=U("ArrowLeftRight",[["path",{d:"M8 3 4 7l4 4",key:"9rb6wj"}],["path",{d:"M4 7h16",key:"6tx8e3"}],["path",{d:"m16 21 4-4-4-4",key:"siv7j2"}],["path",{d:"M20 17H4",key:"h6l3hr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zh=U("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=U("Box",[["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c0=U("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oh=U("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uh=U("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $h=U("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fh=U("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d0=U("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u0=U("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=U("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ws=U("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pn=U("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bh=U("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=U("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vh=U("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=U("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oc=U("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ui=U("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=U("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ma=U("Gamepad2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m0=U("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qh=U("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hh=U("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f0=U("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=U("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=U("Infinity",[["path",{d:"M12 12c-2-2.67-4-4-6-4a4 4 0 1 0 0 8c2 0 4-1.33 6-4Zm0 0c2 2.67 4 4 6 4a4 4 0 0 0 0-8c-2 0-4 1.33-6 4Z",key:"1z0uae"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=U("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mi=U("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=U("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=U("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gn=U("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ts=U("MessageSquareOff",[["path",{d:"M21 15V5a2 2 0 0 0-2-2H9",key:"43el77"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10",key:"pwpm4a"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=U("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=U("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vt=U("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g0=U("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wh=U("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=U("Puzzle",[["path",{d:"M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z",key:"i0oyt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=U("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qh=U("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=U("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pr=U("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $c=U("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=U("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mn=U("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=U("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=U("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w0=U("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b0=U("Trees",[["path",{d:"M10 10v.2A3 3 0 0 1 8.9 16H5a3 3 0 0 1-1-5.8V10a3 3 0 0 1 6 0Z",key:"1l6gj6"}],["path",{d:"M7 16v6",key:"1a82de"}],["path",{d:"M13 19v3",key:"13sx9i"}],["path",{d:"M12 19h8.3a1 1 0 0 0 .7-1.7L18 14h.3a1 1 0 0 0 .7-1.7L16 9h.2a1 1 0 0 0 .8-1.7L13 3l-1.4 1.5",key:"1sj9kv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=U("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=U("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dh=U("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jh=U("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=U("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gi=U("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=U("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kn=U("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zh=U("Volume1",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=U("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fo=U("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kh=U("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yh=U("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fa=U("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const j0=U("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),ho={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},Xh=(ho==null?void 0:ho.VITE_API_URL)||"";class ep{constructor(){this.token=null,this.token=localStorage.getItem("token")}async request(t,n={}){const s=`${Xh}${t}`,l={headers:{"Content-Type":"application/json",...this.token&&{Authorization:`Bearer ${this.token}`},...n.headers},...n};try{const o=await fetch(s,l);if(!o.ok){const i=await o.json().catch(()=>({}));throw o.status===401&&i.code==="MULTIPLE_LOGIN"&&(this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),window.location.reload()),new Error(i.message||`HTTP error! status: ${o.status}`)}return await o.json()}catch(o){throw console.error("API request failed:",o),o}}async login(t,n){const s=await this.request("/api/auth/login",{method:"POST",body:JSON.stringify({username:t,password:n})});return this.token=s.token,localStorage.setItem("token",s.token),localStorage.setItem("username",s.user.username),localStorage.setItem("isAdmin",s.user.isAdmin?"true":"false"),{...s,username:s.user.username,isAdmin:s.user.isAdmin}}async register(t,n,s){return this.request("/api/auth/register",{method:"POST",body:JSON.stringify({username:t,email:n,password:s})})}async logout(){try{await this.request("/api/auth/logout",{method:"POST"})}catch{}this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}async getCurrentUser(){return this.request("/api/user")}async updateProfile(t){return this.request("/api/users/profile",{method:"PUT",body:JSON.stringify(t)})}async uploadAvatar(t){const n=new FormData;return n.append("avatar",t),this.request("/api/users/upload-avatar",{method:"POST",headers:{},body:n})}async getAllUsersAdmin(){return this.request("/api/admin/users")}async searchUsersAdmin(t){return this.request(`/api/users/admin/search?q=${encodeURIComponent(t)}`)}async updateUserAdmin(t,n){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(n)})}async updateCurrency(t,n){return this.request("/api/user/currency",{method:"PUT",body:JSON.stringify({goldCoins:t,pearls:n})})}async getNotifications(){return this.request("/api/notifications")}async markNotificationAsRead(t){return this.request(`/api/notifications/${t}/read`,{method:"PUT"})}async markAllNotificationsAsRead(){return this.request("/api/notifications/mark-all-read",{method:"PUT"})}async getMessages(t){return this.request(`/api/messages/${t}`)}async sendMessage(t,n,s="text"){return this.request("/api/messages",{method:"POST",body:JSON.stringify({recipientId:t,content:n,messageType:s})})}async getFriends(){return this.request("/api/profile/friends")}async getFriendRequests(){return this.request("/api/profile/friend-requests")}async sendFriendRequest(t){return this.request("/api/profile/friend-request",{method:"POST",body:JSON.stringify({friendId:t})})}async acceptFriendRequest(t){return this.request("/api/profile/accept-friend",{method:"POST",body:JSON.stringify({friendshipId:t})})}async checkFriendship(t){return this.request(`/api/friends/check/${t}`)}async searchUserById(t){return this.request(`/api/users/search-by-id/${t}`)}async exchangeGoldToPearls(t){return this.request("/api/profile/exchange-gold-to-pearls",{method:"POST",body:JSON.stringify({goldAmount:t})})}async sendItem(t,n,s){return this.request("/api/profile/send-item",{method:"POST",body:JSON.stringify({toUserId:t,itemType:n,message:s})})}async getGifts(){return this.request("/api/profile/gifts")}async sendGift(t,n,s,l){return this.request("/api/profile/send-gift",{method:"POST",body:JSON.stringify({toUserId:t,giftType:n,amount:s,message:l})})}async claimGift(t){return this.request("/api/profile/claim-gift",{method:"POST",body:JSON.stringify({giftId:t})})}async getItems(){return this.request("/api/profile/items")}async getVoiceRoom(){return this.request("/api/voice-room")}async joinVoiceSeat(t){return this.request("/api/voice-room/join-seat",{method:"POST",body:JSON.stringify({seatNumber:t})})}async leaveVoiceSeat(){return this.request("/api/voice-room/leave-seat",{method:"POST"})}async leaveSeat(){return this.leaveVoiceSeat()}async requestMic(){return this.request("/api/voice-room/request-mic",{method:"POST"})}async cancelMicRequest(){return this.request("/api/voice-room/cancel-mic-request",{method:"POST"})}async sendVoiceRoomMessage(t){return this.request("/api/voice-room/send-message",{method:"POST",body:JSON.stringify({content:t})})}async getVoiceRoomMessages(){return this.request("/api/voice-room/messages")}async toggleMute(t){return this.request("/api/voice-room/toggle-mute",{method:"POST",body:JSON.stringify({isMuted:t})})}async kickUserFromVoiceRoom(t,n){return this.request("/api/voice-room/admin/kick",{method:"POST",body:JSON.stringify({userId:t,durationInMinutes:n})})}async muteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/mute",{method:"POST",body:JSON.stringify({userId:t})})}async unmuteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/unmute",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromSeat(t){return this.request("/api/voice-room/admin/remove-seat",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromQueue(t){return this.request("/api/voice-room/admin/remove-queue",{method:"POST",body:JSON.stringify({userId:t})})}async banUserFromChat(t){return this.request("/api/voice-room/admin/ban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async unbanUserFromChat(t){return this.request("/api/voice-room/admin/unban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async getUserItems(t){return this.request(`/api/user-items/${t}`)}async getShield(t){return this.request(`/api/profile/shield/${t}`)}async activateShield(t){return this.request("/api/profile/activate-shield",{method:"POST",body:JSON.stringify({shieldType:t})})}async getTransactions(t=1,n=20){return this.request(`/api/profile/transactions?page=${t}&limit=${n}`)}async chargeBalance(t){return this.request("/api/profile/charge-balance",{method:"POST",body:JSON.stringify({amount:t})})}async activateItem(t){return this.request("/api/profile/activate-item",{method:"POST",body:JSON.stringify({itemId:t})})}async getGameSettings(){return this.request("/api/game/settings")}async updateGameSettings(t){return this.request("/api/game/settings",{method:"POST",body:t})}async getSuspiciousActivities(){return this.request("/api/admin/suspicious-activities")}async getPlayerId(t){return this.request(`/api/admin/users/${t}/player-id`)}async updatePlayerId(t,n){return this.request(`/api/admin/users/${t}/player-id`,{method:"PUT",body:JSON.stringify({playerId:n})})}async getUsersWithIds(t=1,n=12,s=""){return this.request(`/api/users/admin/users-with-ids?page=${t}&limit=${n}&search=${s}`)}async updateUser(t,n){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(n)})}async deleteUser(t){return this.request(`/api/users/admin/delete/${t}`,{method:"DELETE"})}async deleteUserImage(t){return this.request(`/api/users/admin/delete-image/${t}`,{method:"DELETE"})}async manageUserImage(t,n,s,l){return this.request("/api/users/admin/manage-user-image",{method:"PUT",body:JSON.stringify({targetUserId:t,action:n,imageData:s,imageType:l})})}async debugAllUsers(){return this.request("/api/admin/debug/all-users")}async updateBalance(t,n,s,l){return this.request("/api/users/update-balance",{method:"POST",body:JSON.stringify({balanceChange:t,gameType:n,sessionId:s,gameResult:l})})}async getGameProfile(){return this.request("/api/users/profile")}async endGameSession(t){return this.request("/api/games/session-end",{method:"POST",body:t})}async getPlayerStats(){return this.request("/api/games/player-stats")}clearLocalData(){localStorage.removeItem("token"),localStorage.removeItem("userData"),localStorage.removeItem("adminToken"),localStorage.removeItem("selectedUser"),localStorage.removeItem("userCache"),console.log("🧹 Cleared all local storage data")}}const K=new ep,Fc={ar:{login:"تسجيل الدخول",username:"اسم المستخدم",password:"كلمة المرور",loginButton:"دخول",switchToRegister:"ليس لديك حساب؟ إنشاء حساب",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",requiredFields:"الرجاء إدخال اسم المستخدم وكلمة المرور",loginFailed:"فشل في تسجيل الدخول"},en:{login:"Login",username:"Username",password:"Password",loginButton:"Login",switchToRegister:"Don't have an account? Register",loginDesc:"Profitable activities that blend with fun and friendships",requiredFields:"Please enter username and password",loginFailed:"Login failed"},ur:{login:"لاگ ان",username:"صارف نام",password:"پاس ورڈ",loginButton:"داخل ہوں",switchToRegister:"اکاؤنٹ نہیں ہے؟ رجسٹر کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",requiredFields:"براہ کرم صارف نام اور پاس ورڈ درج کریں",loginFailed:"لاگ ان ناکام"},es:{login:"Iniciar Sesión",username:"Nombre de Usuario",password:"Contraseña",loginButton:"Entrar",switchToRegister:"¿No tienes cuenta? Regístrate",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",requiredFields:"Por favor ingresa nombre de usuario y contraseña",loginFailed:"Error al iniciar sesión"}},tp=({onLoginSuccess:e,onSwitchToRegister:t})=>{const[n,s]=v.useState({username:"",password:""}),[l,o]=v.useState(!1),[i,a]=v.useState(!1),[c,u]=v.useState(""),g=localStorage.getItem("selectedLanguage")||"ar",x=j=>{var k;return((k=Fc[g])==null?void 0:k[j])||Fc.ar[j]||j},w=async j=>{if(j.preventDefault(),u(""),!n.username.trim()||!n.password.trim()){u(x("requiredFields"));return}a(!0);try{const k=await K.login(n.username,n.password);e(k)}catch(k){u(k.message||x("loginFailed"))}finally{a(!1)}},N=j=>{const{name:k,value:S}=j.target;s(m=>({...m,[k]:S})),c&&u("")};return r.jsx("div",{className:"w-full max-w-sm mx-auto",children:r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-800/20 via-blue-900/25 to-slate-800/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-500 animate-pulse"}),r.jsx("div",{className:"relative bg-gradient-to-br from-blue-900/40 via-blue-800/30 to-slate-800/40 backdrop-blur-2xl rounded-2xl p-5 border border-blue-400/30 shadow-xl hover:shadow-blue-500/30 transition-all duration-500 hover:border-blue-300/50",children:r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsxs("div",{className:"relative mx-auto mb-4",children:[r.jsxs("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full flex items-center justify-center mx-auto relative shadow-xl shadow-blue-500/40 hover:shadow-blue-500/60 transition-all duration-500 hover:scale-105 group",children:[r.jsx(ci,{className:"w-8 h-8 text-white drop-shadow-lg"}),r.jsx(Pl,{className:"w-4 h-4 text-white absolute -top-0.5 -right-0.5 animate-spin",style:{animationDuration:"8s"}})]}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full blur-lg opacity-30 animate-pulse"})]}),r.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-2 drop-shadow-lg",children:x("login")}),r.jsxs("p",{className:"text-purple-200 font-medium text-sm",children:["🌟 ",x("loginDesc")," 🌟"]})]}),c&&r.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:r.jsx("p",{className:"text-red-300 text-sm text-center",children:c})}),r.jsxs("form",{onSubmit:w,className:"space-y-4",children:[r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:r.jsx(fi,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),r.jsx("input",{type:"text",name:"username",value:n.username,onChange:N,placeholder:x("username"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"username",required:!0}),r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]}),r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:r.jsx(mi,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),r.jsx("input",{type:l?"text":"password",name:"password",value:n.password,onChange:N,placeholder:x("password"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 pl-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"current-password",required:!0}),r.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center z-10 group/eye",children:l?r.jsx(ui,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"}):r.jsx(Yr,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"})}),r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]})]}),r.jsxs("button",{type:"submit",disabled:i,className:"relative w-full group overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl transition-all duration-500 group-hover:from-blue-600 group-hover:via-blue-700 group-hover:to-blue-800"}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl blur-md opacity-40 group-hover:opacity-60 transition-opacity duration-500"}),r.jsx("div",{className:"relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-500 hover:via-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 active:scale-95 flex items-center justify-center gap-2 text-sm shadow-xl",children:i?r.jsxs(r.Fragment,{children:[r.jsx(h0,{className:"w-4 h-4 animate-spin"}),r.jsx("span",{className:"animate-pulse",children:x("loggingIn")||"جاري تسجيل الدخول..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(ci,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),r.jsxs("span",{className:"group-hover:tracking-wider transition-all duration-300",children:["🚀 ",x("loginButton")]})]})}),r.jsx("div",{className:"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"})})]})]}),r.jsxs("div",{className:"mt-5 space-y-4",children:[r.jsx("div",{className:"text-center",children:r.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10",children:[r.jsx("p",{className:"text-blue-200 mb-2 text-sm",children:x("switchToRegister")}),r.jsxs("button",{onClick:t,className:"group relative inline-flex items-center gap-2 bg-gradient-to-r from-blue-800/20 to-blue-900/20 hover:from-blue-700/30 hover:to-blue-800/30 text-blue-300 hover:text-white font-bold py-2 px-4 rounded-lg border border-blue-400/30 hover:border-blue-300/60 transition-all duration-300 hover:scale-105",children:[r.jsx(yn,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),r.jsx("span",{className:"group-hover:tracking-wider transition-all duration-300 text-sm",children:"✨ إنشاء حساب جديد"})]})]})}),r.jsx("div",{className:"bg-gradient-to-br from-blue-800/20 via-blue-900/15 to-slate-800/20 backdrop-blur-sm rounded-xl p-4 border border-blue-400/20",children:r.jsxs("div",{className:"text-center space-y-3",children:[r.jsx("h3",{className:"text-blue-200 font-bold text-sm mb-3",children:"📞 للاستفسار وشحن العملات في INFINITY BOX"}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[r.jsx(fi,{className:"w-3 h-3"}),r.jsx("span",{className:"font-medium",children:"<EMAIL>"})]}),r.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[r.jsx(Wh,{className:"w-3 h-3"}),r.jsx("span",{className:"font-medium",children:"00966554593007"})]})]}),r.jsx("div",{className:"pt-2 border-t border-blue-400/20",children:r.jsx("p",{className:"text-blue-400/70 text-xs",children:"© 2024 INFINITY BOX - جميع الحقوق محفوظة"})})]})})]})]})})]})})},Bc={ar:{register:"إنشاء حساب",username:"اسم المستخدم",email:"البريد الإلكتروني",password:"كلمة المرور",confirmPassword:"تأكيد كلمة المرور",registerButton:"إنشاء حساب",switchToLogin:"لديك حساب؟ تسجيل الدخول",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",usernameRequired:"اسم المستخدم مطلوب",usernameMinLength:"اسم المستخدم يجب أن يكون 3 أحرف على الأقل",emailRequired:"البريد الإلكتروني مطلوب",emailInvalid:"البريد الإلكتروني غير صالح",passwordRequired:"كلمة المرور مطلوبة",passwordMinLength:"كلمة المرور يجب أن تكون 6 أحرف على الأقل",passwordMismatch:"كلمات المرور غير متطابقة",registering:"جاري إنشاء الحساب...",registerFailed:"فشل في إنشاء الحساب"},en:{register:"Register",username:"Username",email:"Email",password:"Password",confirmPassword:"Confirm Password",registerButton:"Create Account",switchToLogin:"Have an account? Login",registerDesc:"Join us and enjoy a unique experience",usernameRequired:"Username is required",usernameMinLength:"Username must be at least 3 characters",emailRequired:"Email is required",emailInvalid:"Invalid email address",passwordRequired:"Password is required",passwordMinLength:"Password must be at least 6 characters",passwordMismatch:"Passwords do not match",registering:"Creating account...",registerFailed:"Failed to create account"},ur:{register:"رجسٹر",username:"صارف نام",email:"ای میل",password:"پاس ورڈ",confirmPassword:"پاس ورڈ کی تصدیق",registerButton:"اکاؤنٹ بنائیں",switchToLogin:"اکاؤنٹ ہے؟ لاگ ان کریں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",usernameRequired:"صارف نام ضروری ہے",usernameMinLength:"صارف نام کم از کم 3 حروف کا ہونا چاہیے",emailRequired:"ای میل ضروری ہے",emailInvalid:"غلط ای میل ایڈریس",passwordRequired:"پاس ورڈ ضروری ہے",passwordMinLength:"پاس ورڈ کم از کم 6 حروف کا ہونا چاہیے",passwordMismatch:"پاس ورڈ میل نہیں کھاتے",registering:"اکاؤنٹ بنایا جا رہا ہے...",registerFailed:"اکاؤنٹ بنانے میں ناکامی"},es:{register:"Registrarse",username:"Nombre de Usuario",email:"Correo Electrónico",password:"Contraseña",confirmPassword:"Confirmar Contraseña",registerButton:"Crear Cuenta",switchToLogin:"¿Tienes cuenta? Inicia sesión",registerDesc:"Únete a nosotros y disfruta de una experiencia única",usernameRequired:"El nombre de usuario es requerido",usernameMinLength:"El nombre de usuario debe tener al menos 3 caracteres",emailRequired:"El correo electrónico es requerido",emailInvalid:"Dirección de correo electrónico inválida",passwordRequired:"La contraseña es requerida",passwordMinLength:"La contraseña debe tener al menos 6 caracteres",passwordMismatch:"Las contraseñas no coinciden",registering:"Creando cuenta...",registerFailed:"Error al crear la cuenta"}},np=({onRegisterSuccess:e,onSwitchToLogin:t})=>{const[n,s]=v.useState({username:"",email:"",password:"",confirmPassword:""}),[l,o]=v.useState(!1),[i,a]=v.useState(!1),[c,u]=v.useState(!1),[g,x]=v.useState(""),w=localStorage.getItem("selectedLanguage")||"ar",N=m=>{var d;return((d=Bc[w])==null?void 0:d[m])||Bc.ar[m]||m},j=()=>n.username.trim()?n.username.length<3?N("usernameMinLength"):n.email.trim()?/\S+@\S+\.\S+/.test(n.email)?n.password?n.password.length<6?N("passwordMinLength"):n.password!==n.confirmPassword?N("passwordMismatch"):null:N("passwordRequired"):N("emailInvalid"):N("emailRequired"):N("usernameRequired"),k=async m=>{m.preventDefault(),x("");const d=j();if(d){x(d);return}u(!0);try{const p=await K.register(n.username,n.email,n.password);p.token&&p.user&&(localStorage.setItem("token",p.token),localStorage.setItem("userData",JSON.stringify(p.user))),p.isNewUser&&p.welcomeMessage?alert(p.welcomeMessage):p.rewards&&alert(`🎉 مرحباً بك في المنصة!

هدية الترحيب:
🪙 ${p.rewards.goldCoins.toLocaleString()} عملة ذهبية
🦪 ${p.rewards.pearls} لآلئ

استمتع باللعب واربح المزيد!`),e(p.user)}catch(p){x(p.message||N("registerFailed"))}finally{u(!1)}},S=m=>{const{name:d,value:p}=m.target;s(M=>({...M,[d]:p})),g&&x("")};return r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl",children:[r.jsxs("div",{className:"text-center mb-8",children:[r.jsxs("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 relative",children:[r.jsx(Rl,{className:"w-10 h-10 text-white"}),r.jsx(Pl,{className:"w-5 h-5 text-white absolute -top-1 -right-1"})]}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:N("register")}),r.jsx("p",{className:"text-gray-300",children:N("registerDesc")})]}),g&&r.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:r.jsx("p",{className:"text-red-300 text-sm text-center",children:g})}),r.jsxs("form",{onSubmit:k,className:"space-y-6",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(yn,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"text",name:"username",value:n.username,onChange:S,placeholder:N("username"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"username",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(fi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"email",name:"email",value:n.email,onChange:S,placeholder:N("email"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"email",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(mi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:l?"text":"password",name:"password",value:n.password,onChange:S,placeholder:N("password"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"new-password",required:!0}),r.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:l?r.jsx(ui,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):r.jsx(Yr,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(mi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:i?"text":"password",name:"confirmPassword",value:n.confirmPassword,onChange:S,placeholder:N("confirmPassword"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",required:!0}),r.jsx("button",{type:"button",onClick:()=>a(!i),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:i?r.jsx(ui,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):r.jsx(Yr,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]})]}),r.jsx("button",{type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx(h0,{className:"w-5 h-5 animate-spin"}),N("registering")]}):r.jsxs(r.Fragment,{children:[r.jsx(ci,{className:"w-5 h-5"}),N("registerButton")]})})]}),r.jsxs("div",{className:"mt-6 text-center",children:[r.jsx("p",{className:"text-gray-300",children:N("switchToLogin")}),r.jsx("button",{onClick:t,className:"text-blue-400 hover:text-blue-300 font-semibold transition-colors mt-2",children:N("login")})]})]})})},Vc={ar:{welcome:"مرحباً بك",login:"تسجيل الدخول",register:"إنشاء حساب",selectLanguage:"اختر اللغة",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",registerSuccess:"تم إنشاء الحساب بنجاح!",welcomeMessage:"مرحباً بك في INFINITY BOX - يمكنك الآن تسجيل الدخول"},en:{welcome:"Welcome",login:"Login",register:"Register",selectLanguage:"Select Language",loginDesc:"Profitable activities that blend with fun and friendships",registerDesc:"Join us and enjoy a unique experience",registerSuccess:"Account created successfully!",welcomeMessage:"Welcome to INFINITY BOX - you can now login"},ur:{welcome:"خوش آمدید",login:"لاگ ان",register:"رجسٹر",selectLanguage:"زبان منتخب کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",registerSuccess:"اکاؤنٹ کامیابی سے بن گیا!",welcomeMessage:"INFINITY BOX میں خوش آمدید - اب آپ لاگ ان کر سکتے ہیں"},es:{welcome:"Bienvenido",login:"Iniciar Sesión",register:"Registrarse",selectLanguage:"Seleccionar Idioma",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",registerDesc:"Únete a nosotros y disfruta de una experiencia única",registerSuccess:"¡Cuenta creada exitosamente!",welcomeMessage:"Bienvenido a INFINITY BOX - ahora puedes iniciar sesión"}},rp=({onAuthSuccess:e})=>{const[t,n]=v.useState("login"),[s,l]=v.useState(!1),[o,i]=v.useState(()=>localStorage.getItem("selectedLanguage")||"ar"),[a,c]=v.useState(!1),u=k=>({ar:"🇸🇦",en:"🇺🇸",ur:"🇵🇰",es:"🇪🇸"})[k]||"🌍",g=k=>({ar:"العربية",en:"English",ur:"اردو",es:"Español"})[k]||k,x=k=>{var S;return((S=Vc[o])==null?void 0:S[k])||Vc.ar[k]||k},w=k=>{i(k),localStorage.setItem("selectedLanguage",k),document.documentElement.dir=["ar","ur"].includes(k)?"rtl":"ltr",document.documentElement.lang=k,c(!1)};v.useEffect(()=>{document.documentElement.dir=["ar","ur"].includes(o)?"rtl":"ltr",document.documentElement.lang=o},[o]);const N=k=>{if(k.isAdmin&&window.confirm("مرحباً أيها المشرف! هل تريد الذهاب إلى لوحة التحكم؟ (إلغاء للذهاب إلى اللعبة)")){window.location.href="/admin.html";return}e(k)},j=k=>{k?N(k):(l(!0),setTimeout(()=>{l(!1),n("login")},3e3))};return r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white relative overflow-hidden",children:[r.jsx("div",{className:"absolute top-4 right-4 z-50",children:r.jsxs("button",{onClick:()=>c(!0),className:"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full hover:bg-white/20 transition-all duration-300",children:[r.jsx(Hh,{className:"w-5 h-5"}),r.jsx("span",{className:"text-2xl",children:u(o)}),r.jsx("span",{className:"hidden sm:inline",children:g(o)})]})}),a&&r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:r.jsxs("div",{className:"bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 max-w-md w-full",children:[r.jsx("h3",{className:"text-xl font-bold text-center mb-6",children:x("selectLanguage")}),r.jsx("div",{className:"grid grid-cols-2 gap-4",children:["ar","en","ur","es"].map(k=>r.jsxs("button",{onClick:()=>w(k),className:`flex items-center gap-3 p-4 rounded-xl transition-all duration-300 ${o===k?"bg-blue-500/30 border-2 border-blue-400":"bg-white/5 border border-white/10 hover:bg-white/10"}`,children:[r.jsx("span",{className:"text-3xl",children:u(k)}),r.jsx("span",{className:"font-medium",children:g(k)})]},k))}),r.jsx("button",{onClick:()=>c(!1),className:"w-full mt-6 py-3 bg-gray-500/20 hover:bg-gray-500/30 rounded-xl transition-all duration-300",children:"إغلاق / Close"})]})}),r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/30 to-indigo-900/50"}),r.jsxs("div",{className:"absolute -inset-10 opacity-30",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),r.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-72 h-72 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"}),r.jsx("div",{className:"absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-3000"})]}),r.jsxs("div",{className:"absolute inset-0 opacity-20",children:[r.jsx("div",{className:"absolute top-10 left-10 w-32 h-32 bg-white rounded-full filter blur-xl animate-bounce delay-500"}),r.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 bg-yellow-300 rounded-full filter blur-lg animate-bounce delay-1500"}),r.jsx("div",{className:"absolute top-1/3 right-10 w-20 h-20 bg-green-400 rounded-full filter blur-lg animate-bounce delay-2500"})]}),r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((k,S)=>r.jsx("div",{className:`${S%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${S*150}ms`,animationDuration:`${2+S%3}s`}},S))})}),r.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"})]}),r.jsxs("div",{className:"relative z-10 min-h-screen flex flex-col",children:[r.jsxs("header",{className:"text-center py-12 relative",children:[r.jsx("div",{className:"relative z-10",children:r.jsx("div",{className:"inline-flex items-center justify-center mb-8",children:r.jsxs("div",{className:"relative group",children:[r.jsxs("div",{className:"w-32 h-32 sphere-3d rounded-full flex items-center justify-center relative group-hover:scale-110 transition-all duration-500 rotate-y",children:[r.jsx("div",{className:"color-layer"}),r.jsxs("div",{className:"text-center relative z-10",children:[r.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight mb-1 drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"INFINITY"}),r.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"BOX"})]}),r.jsx(Pl,{className:"w-6 h-6 text-white absolute -top-2 -right-2 animate-spin bg-gradient-to-r from-yellow-600 to-blue-700 rounded-full p-1 z-20",style:{animationDuration:"12s"}})]}),r.jsx("div",{className:"absolute inset-0 sphere-glow rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 rotate-y-reverse"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-white/10 rotate-y-fast"}),r.jsx("div",{className:"absolute -inset-2 rounded-full border border-white/5 rotate-y-slow"})]})})}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-5",children:r.jsx("div",{className:"text-8xl font-black text-white animate-spin",style:{animationDuration:"25s"},children:"∞"})})]}),r.jsx("div",{className:"flex-1 flex items-start justify-center px-4 pt-8 pb-8",children:r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx("div",{className:"w-full",children:s?r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsxs("div",{className:"bg-green-500/20 border border-green-500/50 rounded-3xl p-8 text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:x("registerSuccess")}),r.jsx("p",{className:"text-green-300",children:x("welcomeMessage")})]})}):t==="login"?r.jsx(tp,{onLoginSuccess:N,onSwitchToRegister:()=>n("register")}):r.jsx(np,{onRegisterSuccess:j,onSwitchToLogin:()=>n("login")})})})})})]})]})};function sp(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,s,l,o,i=[],a="",c=e.split("/");for(c[0]||c.shift();l=c.shift();)n=l[0],n==="*"?(i.push(n),a+=l[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(s=l.indexOf("?",1),o=l.indexOf(".",1),i.push(l.substring(1,~s?s:~o?o:l.length)),a+=~s&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~s?"?":"")+"\\"+l.substring(o))):a+="/"+l;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var N0={exports:{}},k0={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lr=v;function lp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var op=typeof Object.is=="function"?Object.is:lp,ip=lr.useState,ap=lr.useEffect,cp=lr.useLayoutEffect,dp=lr.useDebugValue;function up(e,t){var n=t(),s=ip({inst:{value:n,getSnapshot:t}}),l=s[0].inst,o=s[1];return cp(function(){l.value=n,l.getSnapshot=t,po(l)&&o({inst:l})},[e,n,t]),ap(function(){return po(l)&&o({inst:l}),e(function(){po(l)&&o({inst:l})})},[e]),dp(n),n}function po(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!op(e,n)}catch{return!0}}function mp(e,t){return t()}var fp=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?mp:up;k0.useSyncExternalStore=lr.useSyncExternalStore!==void 0?lr.useSyncExternalStore:fp;N0.exports=k0;var hp=N0.exports;const pp=ym.useInsertionEffect,gp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",xp=gp?v.useLayoutEffect:v.useEffect,yp=pp||xp,S0=e=>{const t=v.useRef([e,(...n)=>t[0](...n)]).current;return yp(()=>{t[0]=e}),t[1]},vp="popstate",ha="pushState",pa="replaceState",wp="hashchange",qc=[vp,ha,pa,wp],bp=e=>{for(const t of qc)addEventListener(t,e);return()=>{for(const t of qc)removeEventListener(t,e)}},C0=(e,t)=>hp.useSyncExternalStore(bp,e,t),jp=()=>location.search,Np=({ssrSearch:e=""}={})=>C0(jp,()=>e),Hc=()=>location.pathname,kp=({ssrPath:e}={})=>C0(Hc,e?()=>e:Hc),Sp=(e,{replace:t=!1,state:n=null}={})=>history[t?pa:ha](n,"",e),Cp=(e={})=>[kp(e),Sp],Wc=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Wc]>"u"){for(const e of[ha,pa]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),s=new Event(e);return s.arguments=arguments,dispatchEvent(s),n}}Object.defineProperty(window,Wc,{value:!0})}const Mp=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",M0=(e="")=>e==="/"?"":e,Ep=(e,t)=>e[0]==="~"?e.slice(1):M0(t)+e,Ip=(e="",t)=>Mp(Qc(M0(e)),Qc(t)),Qc=e=>{try{return decodeURI(e)}catch{return e}},Ap={hook:Cp,searchHook:Np,parser:sp,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},_p=v.createContext(Ap),E0=()=>v.useContext(_p),Tp={};v.createContext(Tp);const I0=e=>{const[t,n]=e.hook(e);return[Ip(e.base,t),S0((s,l)=>n(Ep(s,e.base),l))]},A0=()=>I0(E0());v.forwardRef((e,t)=>{const n=E0(),[s,l]=I0(n),{to:o="",href:i=o,onClick:a,asChild:c,children:u,className:g,replace:x,state:w,...N}=e,j=S0(S=>{S.ctrlKey||S.metaKey||S.altKey||S.shiftKey||S.button!==0||(a==null||a(S),S.defaultPrevented||(S.preventDefault(),l(i,e)))}),k=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return c&&v.isValidElement(u)?v.cloneElement(u,{onClick:j,href:k}):v.createElement("a",{...N,onClick:j,href:k,className:g!=null&&g.call?g(s===i):g,children:u,ref:t})});const Qs=({setActiveTab:e})=>{A0();const t=[{icon:r.jsx(j0,{className:"w-8 h-8"}),title:"تحدي السرعة",description:"اختبر سرعة ردود أفعالك في تحدي مثير",color:"from-yellow-500 to-orange-500",onClick:()=>window.open("/speed-challenge.html","_blank")},{icon:r.jsx(v0,{className:"w-8 h-8"}),title:"صناديق الحظ",description:"اكسر الصناديق واجمع الكنوز والجواهر",color:"from-green-500 to-emerald-500",onClick:()=>window.open("/game8.html","_blank")},{icon:r.jsx(x0,{className:"w-8 h-8"}),title:"ألغاز العقل",description:"حل الألغاز المعقدة واختبر ذكاءك",color:"from-blue-500 to-cyan-500",onClick:()=>window.open("/mind-puzzles.html","_blank")},{icon:r.jsx(a0,{className:"w-8 h-8"}),title:"قطف الفواكه",description:"اقطف الفواكه الساقطة واجمع النقاط",color:"from-red-500 to-yellow-500",onClick:()=>window.open("/fruit-catching.html","_blank")},{icon:r.jsx(c0,{className:"w-8 h-8"}),title:"لعبة الذاكرة",description:"اختبر ذاكرتك وطابق البطاقات",color:"from-purple-500 to-pink-500",onClick:()=>window.open("/memory-match.html","_blank")},{icon:r.jsx(b0,{className:"w-8 h-8"}),title:"لعبة الغابة",description:"اكتشف الحيوانات وتعلم أسماءها",color:"from-green-600 to-emerald-600",onClick:()=>window.open("/forest-game.html","_blank")}];return r.jsx("div",{className:"space-y-8",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8",children:r.jsxs("section",{children:[r.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(ma,{className:"w-5 h-5 text-white"})}),r.jsx("h2",{className:"text-xl lg:text-2xl font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:"قاعة الألعاب"})]}),r.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:t.map((n,s)=>r.jsxs("button",{onClick:n.onClick,className:"group p-2 sm:p-3 crystal-game-card rounded-xl text-center",children:[r.jsx("div",{className:`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-r ${n.color} rounded-full flex items-center justify-center mb-2 sm:mb-3 group-hover:scale-110 transition-transform duration-300 mx-auto shadow-lg ring-4 ring-white/20`,children:n.icon}),r.jsx("h3",{className:"text-xs sm:text-sm font-semibold text-white mb-1",children:n.title}),r.jsx("p",{className:"text-gray-300 text-xs leading-tight",children:n.description})]},s))})]})})})},Lp=()=>{const[e,t]=v.useState([]),[n,s]=v.useState(1),[l,o]=v.useState(""),[i,a]=v.useState(null),[c,u]=v.useState(!1),[g,x]=v.useState(null),[w,N]=v.useState(""),[j,k]=v.useState(null),[S,m]=v.useState(null);v.useState(!1),v.useState(null);const[d,p]=v.useState(!1),[M,L]=v.useState(null);v.useEffect(()=>{z()},[]);const P=(h,C)=>{L({type:h,text:C}),setTimeout(()=>L(null),5e3)},z=async(h=1,C="")=>{var _;u(!0);try{const $=await K.getUsersWithImages(h,12,C);console.log("📥 Loaded users data:",$.users),(_=$.users)==null||_.forEach(R=>{R.profileImage&&console.log(`👤 User ${R.username} has profileImage:`,R.profileImage.substring(0,50)+"...")}),t($.users||[]),a($.pagination),s(h),o(C)}catch($){console.error("❌ Error loading users:",$),P("error","خطأ في تحميل المستخدمين: "+$.message)}finally{u(!1)}},F=()=>{const h=document.getElementById("searchInput"),C=(h==null?void 0:h.value.trim())||"";z(1,C)},D=(h,C)=>{console.log("🖼️ Image action for user:",h),console.log("🔍 User ID fields:",{id:h.id,userId:h.userId,_id:h._id});const _=h.id||h.userId||h._id;if(!_){P("error","خطأ: لا يمكن العثور على معرف المستخدم");return}const $={...h,id:_,userId:_};x($),N(C),k(null),m(null),p(!0)},y=async h=>{var _;const C=(_=h.target.files)==null?void 0:_[0];if(C){if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(C.type)){P("error","نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP");return}const R=5*1024*1024;if(C.size>R){P("error","حجم الصورة كبير جداً. الحد الأقصى 5MB");return}k(C);try{const G=await re(C,400,.9);m(G),P("info","تم تحسين الصورة وضغطها للحصول على أفضل جودة")}catch(G){console.error("Error compressing preview:",G);const Y=new FileReader;Y.onload=ne=>{var xe;m((xe=ne.target)==null?void 0:xe.result)},Y.readAsDataURL(C),P("info","تم تحميل الصورة بنجاح")}}},O=h=>new Promise((C,_)=>{const $=new FileReader;$.readAsDataURL(h),$.onload=()=>C($.result),$.onerror=R=>_(R)}),X=(h,C=500)=>new Promise((_,$)=>{try{if(console.log("📊 Original file size:",Math.round(h.size/1024),"KB"),h.size<=C*1024){console.log("✅ File size acceptable, using original"),O(h).then(_).catch($);return}const R=new FileReader;R.onload=()=>{try{const G=R.result;console.log("✅ File converted to base64, size:",Math.round(G.length*.75/1024),"KB (estimated)"),_(G)}catch(G){console.error("❌ Error processing file:",G),$(G)}},R.onerror=G=>{console.error("❌ FileReader error:",G),$(G)},R.readAsDataURL(h)}catch(R){console.error("❌ Error in compressImageSafe:",R),$(R)}}),re=(h,C=800,_=.8)=>new Promise(($,R)=>{try{console.log("🔄 Attempting safe compression..."),X(h,500).then($).catch(G=>{console.warn("⚠️ Safe compression failed, trying Canvas method:",G);try{const Y=(()=>{try{return document.createElement("canvas")}catch(he){return console.warn("❌ Canvas creation failed:",he),null}})();if(!Y){console.warn("❌ Canvas not available, using fallback"),O(h).then($).catch(R);return}const ne=Y.getContext("2d");if(!ne){console.warn("❌ Canvas context not available, using fallback"),O(h).then($).catch(R);return}const xe=new window.Image;xe.onload=()=>{try{let{width:he,height:ot}=xe;he>C&&(ot=ot*C/he,he=C),Y.width=he,Y.height=ot,ne.imageSmoothingEnabled=!0,ne.imageSmoothingQuality="high",ne.drawImage(xe,0,0,he,ot);const en=Y.toDataURL("image/jpeg",_);console.log("✅ Canvas compression successful"),$(en)}catch(he){console.error("❌ Canvas processing error:",he),O(h).then($).catch(R)}},xe.onerror=()=>{console.error("❌ Image load error, using fallback"),O(h).then($).catch(R)},xe.src=URL.createObjectURL(h)}catch(Y){console.error("❌ Canvas method failed:",Y),O(h).then($).catch(R)}})}catch(G){console.error("❌ Error in compressImage:",G),O(h).then($).catch(R)}}),He=async h=>{if(h.preventDefault(),!(!g||!w))try{const C=g.userId||g.id;if(!C){P("error","خطأ: معرف المستخدم غير صحيح");return}console.log("🔄 Managing image for user:",C,"action:",w);let _=null,$=null;if(w.startsWith("change_")&&j)try{P("info","جاري ضغط الصورة..."),_=await re(j,800,.85),$="image/jpeg",console.log("✅ Image compressed successfully for upload"),P("info","جاري رفع الصورة المحسنة...")}catch(R){console.error("❌ Error compressing for upload:",R),P("info","فشل ضغط الصورة، جاري استخدام الصورة الأصلية...");try{_=await O(j),$=j.type,console.log("✅ Using original file as fallback")}catch(G){throw console.error("❌ Error with fallback method:",G),new Error("فشل في معالجة الصورة: "+G.message)}}console.log("📤 Sending image management request:",{userId:C,action:w,hasImageData:!!_,imageType:$}),await K.manageUserImage(C,w,_||void 0,$||void 0),P("success","تم تحديث الصورة بنجاح"),console.log("🔄 Reloading users data after image update..."),await z(n,l),Ye()}catch(C){console.error("❌ Image management error:",C),P("error","خطأ في إدارة الصورة: "+C.message)}},Ye=()=>{p(!1),x(null),N(""),k(null),m(null)},Ie=h=>h.profileImage||h.avatar?"موجودة":"غير موجودة",fe=h=>{var _;const C=h.profileImage||h.avatar;return console.log(`🖼️ Getting image for user ${h.username}:`,{hasProfileImage:!!h.profileImage,hasAvatar:!!h.avatar,profileImageLength:((_=h.profileImage)==null?void 0:_.length)||0,finalImageUrl:C?"HAS_IMAGE":"NO_IMAGE"}),C||"/images/default-avatar.png"};return r.jsxs("div",{className:"space-y-8",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center",children:r.jsx(Lr,{className:"w-5 h-5 text-white"})}),r.jsx("h2",{className:"text-2xl font-bold text-white",children:"إدارة صور المستخدمين"})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(kn,{className:"w-8 h-8 text-blue-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.length}),r.jsx("p",{className:"text-sm text-gray-400",children:"إجمالي المستخدمين"})]})]})}),r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Lr,{className:"w-8 h-8 text-green-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(h=>h.profileImage||h.avatar).length}),r.jsx("p",{className:"text-sm text-gray-400",children:"لديهم صور"})]})]})}),r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Yr,{className:"w-8 h-8 text-purple-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(h=>!h.profileImage&&!h.avatar).length}),r.jsx("p",{className:"text-sm text-gray-400",children:"بدون صور"})]})]})})]}),M&&r.jsx("div",{className:`p-4 rounded-2xl border ${M.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":M.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:r.jsxs("div",{className:"flex items-center gap-2",children:[M.type==="success"&&r.jsx(di,{className:"w-5 h-5"}),M.type==="error"&&r.jsx(Ws,{className:"w-5 h-5"}),M.type==="info"&&r.jsx(pi,{className:"w-5 h-5"}),r.jsx("span",{children:M.text})]})}),r.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-3xl border border-white/10 shadow-2xl overflow-hidden",children:[r.jsx("div",{className:"p-6 border-b border-white/10",children:r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsxs("div",{className:"relative flex-1",children:[r.jsx(hi,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{id:"searchInput",type:"text",placeholder:"البحث عن مستخدم...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",onKeyPress:h=>h.key==="Enter"&&F()})]}),r.jsxs("button",{onClick:F,className:"bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-6 py-3 text-purple-300 transition-colors flex items-center gap-2",children:[r.jsx(hi,{className:"w-4 h-4"}),"بحث"]}),r.jsxs("button",{onClick:()=>z(),className:"bg-green-500/20 hover:bg-green-500/30 border border-green-500/50 rounded-xl px-6 py-3 text-green-300 transition-colors flex items-center gap-2",children:[r.jsx(xn,{className:"w-4 h-4"}),"تحديث"]})]})}),r.jsxs("div",{className:"p-6",children:[c?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(xn,{className:"w-8 h-8 text-purple-400 animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-gray-300",children:"جاري التحميل..."})]}):e.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(kn,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد مستخدمين"}),r.jsx("p",{className:"text-gray-300",children:"لم يتم العثور على أي مستخدمين"})]}):r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(h=>r.jsxs("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-200",children:[r.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[r.jsx("img",{src:fe(h),alt:"صورة المستخدم",className:"w-16 h-16 rounded-full object-cover border-2 border-purple-500"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-semibold text-white",children:h.displayName||h.username}),r.jsxs("p",{className:"text-sm text-gray-400",children:["المعرف: ",h.playerId||"غير محدد"]})]})]}),r.jsx("div",{className:"space-y-3 mb-4",children:r.jsxs("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("img",{src:fe(h),alt:"الصورة الشخصية",className:"w-8 h-8 rounded-lg object-cover"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-sm font-medium text-white",children:"الصورة الشخصية"}),r.jsx("p",{className:"text-xs text-gray-400",children:Ie(h)})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>D(h,"remove_avatar"),className:"p-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg text-red-300 transition-colors",title:"حذف",children:r.jsx(w0,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>D(h,"change_avatar"),className:"p-2 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/50 rounded-lg text-yellow-300 transition-colors",title:"تغيير",children:r.jsx(g0,{className:"w-4 h-4"})})]})]})})]},h.userId))}),i&&i.totalPages>1&&r.jsxs("div",{className:"flex justify-center items-center gap-2 mt-8",children:[i.hasPrevPage&&r.jsx("button",{onClick:()=>z(n-1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"السابق"}),Array.from({length:i.totalPages},(h,C)=>C+1).map(h=>r.jsx("button",{onClick:()=>z(h,l),className:`px-4 py-2 rounded-lg transition-colors ${h===n?"bg-purple-500 text-white":"bg-white/10 hover:bg-white/20 border border-white/20 text-white"}`,children:h},h)),i.hasNextPage&&r.jsx("button",{onClick:()=>z(n+1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"التالي"})]})]})]}),d&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h3",{className:"text-xl font-bold text-white",children:["إدارة صور المستخدم ",g==null?void 0:g.userId]}),r.jsx("button",{onClick:Ye,className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:r.jsx(fa,{className:"w-5 h-5 text-gray-400"})})]}),r.jsxs("form",{onSubmit:He,className:"space-y-6",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"الإجراء"}),r.jsxs("select",{value:w,onChange:h=>N(h.target.value),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0,children:[r.jsx("option",{value:"",children:"اختر الإجراء"}),r.jsx("option",{value:"remove_avatar",children:"حذف الصورة الشخصية"}),r.jsx("option",{value:"change_avatar",children:"تغيير الصورة الشخصية"})]})]}),w.startsWith("change_")&&r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اختر الصورة"}),r.jsxs("div",{className:"relative",children:[r.jsx("input",{type:"file",accept:"image/*",onChange:y,className:"hidden",id:"imageFile"}),r.jsxs("label",{htmlFor:"imageFile",className:"w-full bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-4 py-3 text-purple-300 cursor-pointer transition-colors flex items-center justify-center gap-2",children:[r.jsx(Dh,{className:"w-5 h-5"}),"اختيار ملف"]})]}),S&&r.jsx("div",{className:"mt-4",children:r.jsx("img",{src:S,alt:"معاينة الصورة",className:"w-full max-h-40 object-cover rounded-xl"})})]}),r.jsxs("div",{className:"flex gap-4",children:[r.jsx("button",{type:"submit",className:"flex-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),r.jsx("button",{type:"button",onClick:Ye,className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})]})})]})},xi=({userData:e,onLogout:t})=>{const[n,s]=v.useState("users"),[l,o]=v.useState([]),[i,a]=v.useState(null),[c,u]=v.useState(""),[g,x]=v.useState(!1),[w,N]=v.useState(null),[j,k]=v.useState({numBoxes:10,winRatio:.3}),[S,m]=v.useState([]);v.useEffect(()=>{e!=null&&e.isAdmin&&(p(),M(),L())},[e]);const d=(y,O)=>{N({type:y,text:O}),setTimeout(()=>N(null),5e3)},p=async()=>{x(!0);try{const y=await K.getAllUsersAdmin();console.log("📥 Loaded users:",y.users);const O=(y.users||[]).map(X=>({...X,id:X.id||X.userId||X._id,userId:X.userId||X.id||X._id}));console.log("✅ Processed users:",O),o(O)}catch(y){console.error("❌ Error loading users:",y),d("error","خطأ في تحميل المستخدمين")}finally{x(!1)}},M=async()=>{try{const y=await K.getGameSettings();k(y)}catch(y){console.error("خطأ في تحميل إعدادات اللعبة:",y)}},L=async()=>{try{const y=await K.getSuspiciousActivities();m(y||[])}catch(y){console.error("خطأ في تحميل النشاطات المشبوهة:",y),m([])}},P=async y=>{if(!y.trim()){p();return}x(!0);try{const O=await K.searchUsersAdmin(y);o(O.users||[])}catch{d("error","خطأ في البحث")}finally{x(!1)}},z=async(y,O)=>{try{if(!y||y==="undefined"||y===""){console.error("❌ Invalid userId:",y),console.error("❌ Updates object:",O),d("error","خطأ: معرف المستخدم غير صحيح. يرجى إعادة تحميل الصفحة.");return}if(console.log("🔄 Updating user:",y,"with updates:",O),O.playerId){if(!/^\d{6}$/.test(O.playerId)){d("error","معرف اللاعب يجب أن يكون 6 أرقام فقط");return}await K.updatePlayerId(y,O.playerId),d("success","تم تحديث معرف اللاعب بنجاح")}const{playerId:X,...re}=O;Object.keys(re).length>0&&(await K.updateUserAdmin(y,re),d("success","تم تحديث المستخدم بنجاح")),p(),a(null)}catch{d("error","خطأ في تحديث المستخدم")}},F=async()=>{try{await K.updateGameSettings(j),d("success","تم حفظ إعدادات اللعبة بنجاح")}catch{d("error","خطأ في حفظ الإعدادات")}},D=l.filter(y=>y.username.toLowerCase().includes(c.toLowerCase())||y.email.toLowerCase().includes(c.toLowerCase()));return e!=null&&e.isAdmin?r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white",children:[r.jsx("div",{className:"absolute inset-0 overflow-hidden",children:r.jsxs("div",{className:"absolute -inset-10 opacity-20",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-3/4 right-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),r.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"})]})}),r.jsxs("div",{className:"relative z-10 px-4 py-4",children:[r.jsxs("div",{className:"mb-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center relative",children:[r.jsx(Tr,{className:"w-6 h-6 text-white"}),r.jsx(Pl,{className:"w-3 h-3 text-white absolute -top-1 -right-1"})]}),r.jsxs("div",{children:[r.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"لوحة تحكم المشرف"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"INFINITY BOX"})]})]}),r.jsx("button",{onClick:t,className:"bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 hover:text-red-200 transition-all duration-200 text-sm",children:"خروج"})]}),r.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl px-3 py-2 border border-white/20 flex items-center gap-2",children:[r.jsx(Tr,{className:"w-4 h-4 text-yellow-400"}),r.jsxs("span",{className:"text-sm text-gray-300",children:["مرحباً، ",e.username]})]})]}),w&&r.jsx("div",{className:`mb-6 p-4 rounded-2xl border ${w.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":w.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:r.jsxs("div",{className:"flex items-center gap-2",children:[w.type==="success"&&r.jsx(di,{className:"w-5 h-5"}),w.type==="error"&&r.jsx(Ws,{className:"w-5 h-5"}),w.type==="info"&&r.jsx(pi,{className:"w-5 h-5"}),r.jsx("span",{children:w.text})]})}),r.jsx("div",{className:"grid grid-cols-2 gap-2 mb-6",children:[{id:"users",label:"إدارة المستخدمين",shortLabel:"المستخدمين",icon:kn,color:"from-blue-500 to-cyan-500"},{id:"game",label:"إعدادات اللعبة",shortLabel:"الألعاب",icon:$c,color:"from-green-500 to-emerald-500"},{id:"suspicious",label:"النشاطات المشبوهة",shortLabel:"المشبوهة",icon:ai,color:"from-red-500 to-pink-500"},{id:"images",label:"إدارة الصور",shortLabel:"الصور",icon:Lr,color:"from-purple-500 to-indigo-500"}].map(y=>r.jsxs("button",{onClick:()=>s(y.id),className:`flex flex-col items-center gap-2 px-3 py-3 rounded-xl transition-all duration-200 ${n===y.id?`bg-gradient-to-r ${y.color} text-white shadow-lg`:"bg-white/10 hover:bg-white/20 text-gray-300"}`,children:[r.jsx(y.icon,{className:"w-6 h-6"}),r.jsx("span",{className:"text-sm font-medium",children:y.shortLabel})]},y.id))}),r.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-2xl border border-white/10 shadow-2xl overflow-hidden",children:[n==="users"&&r.jsxs("div",{className:"p-4",children:[r.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[r.jsx("div",{className:"flex items-center justify-between",children:r.jsxs("h2",{className:"text-lg font-bold text-white flex items-center gap-2",children:[r.jsx(kn,{className:"w-5 h-5 text-blue-400"}),"المستخدمين (",D.length,")"]})}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("button",{onClick:()=>{console.log("🧹 Clearing local data..."),K.clearLocalData(),d("info","تم تنظيف البيانات المحلية. سيتم إعادة تحميل الصفحة..."),setTimeout(()=>window.location.reload(),1e3)},className:"flex-1 flex items-center justify-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 transition-colors text-sm",children:[r.jsx(Ws,{className:"w-4 h-4"}),"تنظيف البيانات"]}),r.jsxs("button",{onClick:p,className:"flex-1 flex items-center justify-center gap-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-3 py-2 text-blue-300 transition-colors text-sm",children:[r.jsx(xn,{className:"w-4 h-4"}),"تحديث"]})]})]}),r.jsxs("div",{className:"relative mb-4",children:[r.jsx(hi,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),r.jsx("input",{type:"text",value:c,onChange:y=>{u(y.target.value),P(y.target.value)},placeholder:"البحث عن المستخدمين...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",autoComplete:"off"})]}),g?r.jsxs("div",{className:"text-center py-8",children:[r.jsx(xn,{className:"w-6 h-6 text-blue-400 animate-spin mx-auto mb-3"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"جاري التحميل..."})]}):r.jsx("div",{className:"space-y-3",children:D.map(y=>r.jsxs("div",{className:"bg-white/10 rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-200",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-sm font-bold",children:y.username.charAt(0).toUpperCase()}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("h3",{className:"font-semibold text-white flex items-center gap-2 text-sm",children:[r.jsx("span",{className:"truncate",children:y.username}),y.isAdmin&&r.jsx(Tr,{className:"w-3 h-3 text-yellow-400 flex-shrink-0"})]}),r.jsx("p",{className:"text-xs text-gray-400 truncate",children:y.email}),r.jsxs("p",{className:"text-xs text-blue-400",children:["المعرف: ",y.playerId]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[r.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[r.jsx(Ll,{className:"w-3 h-3 text-yellow-400"}),r.jsx("span",{className:"text-yellow-400 font-semibold text-sm",children:y.goldCoins||y.coins||0})]}),r.jsx("p",{className:"text-xs text-gray-400",children:"العملات"})]}),r.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[r.jsx(mn,{className:"w-3 h-3 text-purple-400"}),r.jsx("span",{className:"text-purple-400 font-semibold text-sm",children:y.pearls||0})]}),r.jsx("p",{className:"text-xs text-gray-400",children:"اللآلئ"})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("button",{onClick:()=>a(y),className:"flex-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-2 py-2 text-blue-300 text-xs transition-colors flex items-center justify-center gap-1",children:[r.jsx(g0,{className:"w-3 h-3"}),"تعديل"]}),r.jsxs("button",{onClick:()=>z(y.id||y._id,{isAdmin:!y.isAdmin}),className:`flex-1 border rounded-lg px-2 py-2 text-xs transition-colors flex items-center justify-center gap-1 ${y.isAdmin?"bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300":"bg-green-500/20 hover:bg-green-500/30 border-green-500/50 text-green-300"}`,children:[y.isAdmin?r.jsx(gi,{className:"w-3 h-3"}):r.jsx(Jh,{className:"w-3 h-3"}),y.isAdmin?"إلغاء":"ترقية"]})]})]},y.id))})]}),n==="game"&&r.jsxs("div",{className:"p-4",children:[r.jsxs("h2",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx($c,{className:"w-5 h-5 text-green-400"}),"إعدادات اللعبة"]}),r.jsx("div",{className:"space-y-4",children:r.jsxs("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:[r.jsx("h3",{className:"text-base font-semibold text-white mb-4",children:"إعدادات صناديق الحظ"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"عدد الصناديق في الجولة"}),r.jsx("input",{type:"number",min:"5",max:"100",value:j.numBoxes,onChange:y=>k(O=>({...O,numBoxes:parseInt(y.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"نسبة الفوز (من 0.1 إلى 0.9)"}),r.jsx("input",{type:"number",min:"0.1",max:"0.9",step:"0.1",value:j.winRatio,onChange:y=>k(O=>({...O,winRatio:parseFloat(y.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),r.jsxs("button",{onClick:F,className:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm",children:[r.jsx(Qh,{className:"w-4 h-4"}),"حفظ الإعدادات"]})]})]})})]}),n==="suspicious"&&r.jsxs("div",{className:"p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h2",{className:"text-2xl font-bold text-white flex items-center gap-2",children:[r.jsx(ai,{className:"w-6 h-6 text-red-400"}),"النشاطات المشبوهة"]}),r.jsxs("button",{onClick:L,className:"flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-xl px-4 py-2 text-red-300 transition-colors",children:[r.jsx(xn,{className:"w-4 h-4"}),"تحديث"]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[r.jsx("div",{className:"bg-red-500/20 rounded-2xl p-6 border border-red-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(pi,{className:"w-8 h-8 text-red-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-red-300",children:S.length}),r.jsx("p",{className:"text-sm text-red-400",children:"مستخدمين مشبوهين"})]})]})}),r.jsx("div",{className:"bg-orange-500/20 rounded-2xl p-6 border border-orange-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx($h,{className:"w-8 h-8 text-orange-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-orange-300",children:S.filter(y=>y.riskLevel==="high").length}),r.jsx("p",{className:"text-sm text-orange-400",children:"مخاطر عالية"})]})]})}),r.jsx("div",{className:"bg-yellow-500/20 rounded-2xl p-6 border border-yellow-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Yr,{className:"w-8 h-8 text-yellow-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-yellow-300",children:S.filter(y=>y.riskLevel==="medium").length}),r.jsx("p",{className:"text-sm text-yellow-400",children:"تحت المراقبة"})]})]})})]}),S.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(di,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد نشاطات مشبوهة"}),r.jsx("p",{className:"text-gray-300",children:"جميع المستخدمين يتصرفون بشكل طبيعي"})]}):r.jsx("div",{className:"space-y-4",children:S.map((y,O)=>{var X,re;return r.jsx("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-lg font-bold",children:((re=(X=y.username)==null?void 0:X.charAt(0))==null?void 0:re.toUpperCase())||"?"}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-semibold text-white",children:y.username}),r.jsxs("p",{className:"text-sm text-gray-400",children:["آخر نشاط: ",y.lastActivity]})]})]}),r.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${y.riskLevel==="high"?"bg-red-500/20 text-red-300":y.riskLevel==="medium"?"bg-yellow-500/20 text-yellow-300":"bg-green-500/20 text-green-300"}`,children:y.riskLevel==="high"?"خطر عالي":y.riskLevel==="medium"?"خطر متوسط":"خطر منخفض"})]})},O)})})]}),n==="images"&&r.jsxs("div",{className:"p-8",children:[r.jsxs("div",{className:"mb-6",children:[r.jsxs("h2",{className:"text-2xl font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(Lr,{className:"w-6 h-6 text-purple-400"}),"إدارة الصور"]}),r.jsxs("div",{className:"bg-purple-500/10 border border-purple-500/30 rounded-xl p-4 mb-6",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[r.jsx(Lr,{className:"w-5 h-5 text-purple-400"}),r.jsx("h3",{className:"font-semibold text-purple-300",children:"إدارة صور المستخدمين"})]}),r.jsx("p",{className:"text-gray-300 text-sm",children:"عرض وإدارة جميع صور المستخدمين، حذف الصور غير المناسبة، والبحث في المستخدمين."})]})]}),r.jsx(Lp,{})]})]})]}),i&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[r.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"تعديل المستخدم"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"معرف اللاعب"}),r.jsx("input",{type:"text",value:i.playerId,onChange:y=>a(O=>O?{...O,playerId:y.target.value}:null),maxLength:6,pattern:"\\d{6}",className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"6 أرقام فقط"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اسم المستخدم"}),r.jsx("input",{type:"text",value:i.username,onChange:y=>a(O=>O?{...O,username:y.target.value}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"username"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"العملات الذهبية"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"number",value:i.goldCoins||i.coins||0,onChange:y=>a(O=>O?{...O,goldCoins:parseInt(y.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),r.jsxs("div",{className:"flex gap-2 flex-wrap",children:[r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,goldCoins:(y.goldCoins||y.coins||0)+1e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+1,000"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,goldCoins:(y.goldCoins||y.coins||0)+5e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+5,000"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,goldCoins:(y.goldCoins||y.coins||0)+1e4}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+10,000"})]})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اللآلئ"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"number",value:i.pearls||0,onChange:y=>a(O=>O?{...O,pearls:parseInt(y.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),r.jsxs("div",{className:"flex gap-2 flex-wrap",children:[r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,pearls:(y.pearls||0)+1}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+1"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,pearls:(y.pearls||0)+5}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+5"}),r.jsx("button",{type:"button",onClick:()=>a(y=>y?{...y,pearls:(y.pearls||0)+10}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+10"})]})]})]}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("input",{type:"checkbox",id:"isAdmin",checked:i.isAdmin,onChange:y=>a(O=>O?{...O,isAdmin:y.target.checked}:null),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),r.jsx("label",{htmlFor:"isAdmin",className:"text-sm font-medium text-gray-300",children:"صلاحيات المشرف"})]})]}),r.jsxs("div",{className:"flex gap-4 mt-8",children:[r.jsx("button",{onClick:()=>{const y=i.id||i.userId;if(console.log("🔍 Selected user data:",i),console.log("🔍 Extracted userId:",y),!y){d("error","خطأ: لا يمكن العثور على معرف المستخدم. يرجى إعادة تحميل الصفحة.");return}z(y,i)},className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),r.jsx("button",{onClick:()=>a(null),className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})})]}):r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:r.jsxs("div",{className:"bg-red-500/20 border border-red-500/50 rounded-3xl p-8 text-center max-w-md",children:[r.jsx(Ws,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"غير مصرح"}),r.jsx("p",{className:"text-red-300 mb-6",children:"ليس لديك صلاحيات للوصول إلى لوحة تحكم المشرف"}),r.jsx("button",{onClick:t,className:"bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-xl transition-colors",children:"العودة لتسجيل الدخول"})]})})};class _0{constructor(t){this.url=t,this.ws=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.messageHandlers=new Map}connect(t){return new Promise((n,s)=>{try{this.ws=new WebSocket(`${this.url}?token=${t}`),this.ws.onopen=()=>{console.log("🔌 WebSocket connected"),this.reconnectAttempts=0,n()},this.ws.onmessage=l=>{try{const o=JSON.parse(l.data);this.handleMessage(o)}catch(o){console.error("Error parsing WebSocket message:",o)}},this.ws.onclose=()=>{console.log("🛑 WebSocket disconnected"),this.handleReconnect()},this.ws.onerror=l=>{console.error("WebSocket error:",l),s(l)}}catch(l){s(l)}})}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.connect(this.getTokenFromUrl()).catch(t=>{console.error("Reconnection failed:",t)})},this.reconnectDelay*this.reconnectAttempts)):console.error("❌ Max reconnection attempts reached")}getTokenFromUrl(){return new URLSearchParams(window.location.search).get("token")||""}handleMessage(t){const n=this.messageHandlers.get(t.type);n&&n.forEach(s=>s(t.data))}sendMessage(t,n){if(this.ws&&this.ws.readyState===WebSocket.OPEN){const s={type:t,data:n,timestamp:Date.now()};this.ws.send(JSON.stringify(s))}else console.warn("WebSocket is not connected")}sendPrivateMessage(t,n){this.sendMessage("private_message",{messageData:t,recipientId:n})}onMessage(t,n){this.messageHandlers.has(t)||this.messageHandlers.set(t,[]),this.messageHandlers.get(t).push(n)}offMessage(t,n){const s=this.messageHandlers.get(t);if(s){const l=s.indexOf(n);l>-1&&s.splice(l,1)}}addMessageListener(t){["voice_room_message","voice_room_update","admin_action_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.onMessage(s,t)})}removeMessageListener(t){["voice_room_message","voice_room_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.offMessage(s,t)})}send(t){this.sendMessage(t.type,t.data)}disconnect(){this.ws&&(this.ws.close(),this.ws=null)}}const Pp=`
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
`;if(typeof document<"u"){const e=document.createElement("style");e.textContent=Pp,document.head.appendChild(e)}const Rp=({userData:e,isOwner:t,onUpdateProfile:n,onLogout:s})=>{var Na,ka,Sa,Ca,Ma;const[l,o]=v.useState("overview"),[i,a]=v.useState(!1),[c,u]=v.useState(""),[g,x]=v.useState((e==null?void 0:e.gender)||"male"),[w,N]=v.useState(!1),[j,k]=v.useState({gems:0,stars:0,coins:0,bombs:0,bats:0,snakes:0}),[S,m]=v.useState(!1),[d,p]=v.useState(!1),[M,L]=v.useState(""),[P,z]=v.useState(!1),[F,D]=v.useState(null),[y,O]=v.useState(""),[X,re]=v.useState(null),[He,Ye]=v.useState(!1),[Ie,fe]=v.useState(""),[h,C]=v.useState(null),[_,$]=v.useState(100),[R,G]=v.useState("gold"),[Y,ne]=v.useState(""),[xe,he]=v.useState(!1),[ot,en]=v.useState([]),[Mn,cr]=v.useState([]),[zl,tn]=v.useState([]),[nn,ke]=v.useState(!1),[We,En]=v.useState(1e4),[Xe,In]=v.useState(!1),[ht,is]=v.useState(250),[An,as]=v.useState({"1_dollar":!0,"5_dollar":!0}),[pe,pt]=v.useState(""),[rn,cs]=v.useState(""),[ds,us]=v.useState(!1),[Se,_n]=v.useState([]),[Ol,jt]=v.useState(!1),[Tn,ms]=v.useState(!1),[ce,b]=v.useState(null),[A,W]=v.useState([]),[q,B]=v.useState("");v.useState(!1);const[Pe,ie]=v.useState(!1),[sn,Nt]=v.useState(!1);(f=>f==="female"?{primary:"from-pink-500 to-red-400",secondary:"bg-pink-50",accent:"text-pink-600",button:"bg-pink-500 hover:bg-pink-600",border:"border-pink-200"}:{primary:"from-blue-500 to-yellow-400",secondary:"bg-blue-50",accent:"text-blue-600",button:"bg-blue-500 hover:bg-blue-600",border:"border-blue-200"})((e==null?void 0:e.gender)||"male"),v.useEffect(()=>{let f=!0;const I=setTimeout(async()=>{if(t&&(e!=null&&e.id)&&f)try{const H=localStorage.getItem("token");if(!H)return;const J=await fetch("/api/profile/me",{method:"GET",headers:{Authorization:`Bearer ${H}`,"Content-Type":"application/json"}});if(J.ok&&f){const Q=J.headers.get("content-type");if(Q&&Q.includes("application/json")){const ln=await J.json();n&&f&&n(ln)}}}catch(H){console.error("❌ Error fetching complete user data:",H)}},100);return()=>{f=!1,clearTimeout(I)}},[t,e==null?void 0:e.id]),v.useEffect(()=>{e!=null&&e.id&&(P0(),D0(),t&&ga())},[e==null?void 0:e.id,t]),v.useEffect(()=>{if(t&&(e!=null&&e.id))return L0()},[t,e==null?void 0:e.id]);const dr=new _0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`);v.useEffect(()=>{let f=!0;if(t&&(e!=null&&e.id)&&f){xa(),fs(),z0(),hs(),F0();const E=setInterval(()=>{f&&(fs(),hs())},3e5);return()=>{f=!1,clearInterval(E)}}},[t,e==null?void 0:e.id]);const L0=()=>{console.log("🔧 Setting up message listener...");const f=E=>{if(console.log("📨 WebSocket message received:",E),E.messageData){const I=E.messageData.sender._id,H=E.messageData.recipient._id,J=e==null?void 0:e.id;if(console.log("📋 Message details:",{senderId:I,recipientId:H,currentUserId:J,showChat:Tn,chatUserId:(ce==null?void 0:ce.id)||(ce==null?void 0:ce._id)}),H===J)if(console.log("✅ Message is for me, processing..."),Tn&&ce&&(I===ce.id||I===ce._id)){console.log("💬 Adding message to open chat"),W(Q=>Q.some(X0=>X0._id===E.messageData._id)?(console.log("⚠️ Message already exists, skipping"),Q):(console.log("✅ Adding new message to chat"),[...Q,E.messageData])),setTimeout($l,100);try{const Q=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");Q.volume=.5,Q.play().catch(()=>{})}catch{}}else{console.log("🔔 Chat not open, refreshing notifications"),hs();try{const Q=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");Q.volume=.3,Q.play().catch(()=>{})}catch{}}else console.log("ℹ️ Message not for me, ignoring")}else console.log("⚠️ No messageData in WebSocket message")};return dr.offMessage("new_message",f),dr.onMessage("new_message",f),console.log("✅ Message listener added"),()=>{console.log("🧹 Cleaning up message listener"),dr.offMessage("new_message",f)}},P0=async()=>{try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for fetching user items");return}const E=await fetch(`/api/user-items/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const I=await E.json();k(I.items),console.log("✅ User items fetched successfully:",I.items)}else console.error("❌ Failed to fetch user items:",E.status,E.statusText)}catch(f){console.error("Error fetching user items:",f)}},ga=async()=>{try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for fetching user shield");return}const E=await fetch(`/api/profile/shield/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const I=await E.json();D(I.shield),console.log("✅ User shield fetched successfully:",I.shield)}else console.error("❌ Failed to fetch user shield:",E.status,E.statusText)}catch(f){console.error("Error fetching user shield:",f)}},R0=async(f,E)=>{if(!P){z(!0);try{const I=localStorage.getItem("token"),H=await fetch("/api/profile/activate-shield",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify({shieldType:f})}),J=await H.json();H.ok?(D(J.shield),n&&J.newBalance!==void 0&&n({goldCoins:J.newBalance}),alert(J.message),await ga()):alert(J.message||"فشل في تفعيل الدرع الواقي")}catch(I){console.error("Error activating shield:",I),alert("حدث خطأ في تفعيل الدرع الواقي")}finally{z(!1)}}},xa=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/friends",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const I=await E.json();en(I),console.log("✅ Friends fetched:",I.length)}}catch(f){console.error("Error fetching friends:",f)}},fs=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/friend-requests",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const I=await E.json();cr(I),console.log("✅ Friend requests fetched:",I.length)}}catch(f){console.error("Error fetching friend requests:",f)}},z0=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/gifts",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const I=await E.json();tn(I),console.log("✅ Gifts fetched:",I.length)}}catch(f){console.error("Error fetching gifts:",f)}},ya=async()=>{if(!y.trim()){fe("يرجى إدخال رقم اللاعب");return}if(y.length!==6){fe("رقم اللاعب يجب أن يكون 6 أرقام");return}Ye(!0),fe(""),re(null);try{const f=localStorage.getItem("token"),E=await fetch(`/api/users/search-by-id/${y}`,{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const I=await E.json();re(I),console.log("✅ User found:",I.username)}else fe("لم يتم العثور على لاعب بهذا الرقم")}catch(f){console.error("Error searching for friend:",f),fe("حدث خطأ أثناء البحث")}finally{Ye(!1)}},O0=async f=>{try{const E=localStorage.getItem("token"),I=await fetch("/api/profile/friend-request",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({friendId:f})});if(I.ok){const H=await I.json();alert(H.message),re(null),O(""),await hs()}else{const H=await I.json();alert(H.message||"فشل في إرسال طلب الصداقة")}}catch(E){console.error("Error sending friend request:",E),alert("حدث خطأ في إرسال طلب الصداقة")}},U0=async()=>{if(h){he(!0);try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/send-gift",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({toUserId:h.id,giftType:R,amount:_,message:Y})});if(E.ok){const I=await E.json();alert(I.message),C(null),ne(""),$(100),n&&I.fromUserBalance&&n(I.fromUserBalance)}else{const I=await E.json();alert(I.message||"فشل في إرسال الهدية")}}catch(f){console.error("Error sending gift:",f),alert("حدث خطأ في إرسال الهدية")}finally{he(!1)}}},$0=async f=>{try{const E=localStorage.getItem("token"),I=await fetch("/api/profile/accept-friend",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({friendshipId:f})});if(I.ok){const H=await I.json();alert(H.message),await fs(),await xa()}else{const H=await I.json();alert(H.message||"فشل في قبول طلب الصداقة")}}catch(E){console.error("Error accepting friend request:",E),alert("حدث خطأ في قبول طلب الصداقة")}},F0=async()=>{try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/free-charges",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const I=await E.json();as(I.availableCharges)}}catch(f){console.error("Error fetching free charges:",f)}},va=async(f,E=!1,I)=>{ke(!0);try{const H=localStorage.getItem("token"),J=await fetch("/api/profile/charge-balance",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${H}`},body:JSON.stringify({amount:f,isFree:E,chargeType:I})});if(J.ok){const Q=await J.json();alert(Q.message),n&&Q.newBalance!==void 0&&n({goldCoins:Q.newBalance}),E&&I&&as(ln=>({...ln,[I]:!1}))}else{const Q=await J.json();alert(Q.message||"فشل في شحن الرصيد")}}catch(H){console.error("Error charging balance:",H),alert("حدث خطأ في شحن الرصيد")}finally{ke(!1)}},B0=async()=>{if(We<1e4){alert("الحد الأدنى للتحويل هو 10,000 عملة ذهبية");return}if(We%1e4!==0){alert("يجب أن تكون الكمية مضاعفات 10,000");return}if(((e==null?void 0:e.goldCoins)||0)<We){alert("رصيد العملات الذهبية غير كافي");return}In(!0);try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/exchange-gold-to-pearls",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({goldAmount:We})});if(E.ok){const I=await E.json();alert(I.message),n&&I.newBalance&&n(I.newBalance),En(1e4)}else{const I=await E.json();alert(I.message||"فشل في تحويل العملات")}}catch(f){console.error("Error exchanging gold to pearls:",f),alert("حدث خطأ في تحويل العملات")}finally{In(!1)}},V0=async()=>{if(ht<250){alert("الحد الأدنى للسحب هو 250 لؤلؤة ($25)");return}if(((e==null?void 0:e.pearls)||0)<ht){alert("رصيد اللآلئ غير كافي");return}const I=`https://wa.me/1234567890?text=${`طلب سحب دولارات%0Aالمبلغ: $${ht/10}%0Aاللآلئ المطلوبة: ${ht}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}`}`;window.open(I,"_blank")},q0=async()=>{if(!pe){alert("يرجى اختيار عنصر للإرسال");return}if(!rn||rn.length!==6){alert("يرجى إدخال رقم لاعب صحيح (6 أرقام)");return}us(!0);try{const f=localStorage.getItem("token"),E=await fetch(`/api/users/search-by-id/${rn}`,{headers:{Authorization:`Bearer ${f}`}});if(!E.ok){alert("لم يتم العثور على لاعب بهذا الرقم");return}const I=await E.json(),H=await fetch("/api/profile/send-item",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({toUserId:I.id,itemType:pe,message:`عنصر ${wa(pe)} من ${e==null?void 0:e.username}`})});if(H.ok){const J=await H.json();alert(J.message),pt(""),cs("")}else{const J=await H.json();alert(J.message||"فشل في إرسال العنصر")}}catch(f){console.error("Error sending item:",f),alert("حدث خطأ في إرسال العنصر")}finally{us(!1)}},wa=f=>({bomb:"قنبلة مدمرة",bat:"خفاش مؤذي",snake:"ثعبان سام",gem:"جوهرة نادرة",star:"نجمة ذهبية",coin:"عملة خاصة",gold:"عملات ذهبية"})[f]||f,ba=(f,E)=>{const H=`https://wa.me/1234567890?text=${`طلب شحن رصيد%0Aالمبلغ المطلوب: ${f} عملة ذهبية%0Aالسعر: ${E}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}%0Aالرصيد الحالي: ${(e==null?void 0:e.goldCoins)||0} عملة`}`;window.open(H,"_blank")},hs=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/notifications",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const I=await E.json();_n(I),console.log("✅ Notifications fetched:",I.length);const H=I.filter(Q=>{var ln;return!Q.isRead&&Q.type==="item_received"&&((ln=Q.data)==null?void 0:ln.newBalance)});if(H.length>0&&n){const Q=H[0];n(Q.data.newBalance),console.log("💰 Balance updated from item notification:",Q.data.newBalance)}I.filter(Q=>Q.type==="friend_request"&&!Q.isRead).length>0&&(console.log("🤝 New friend request notifications found, refreshing friend requests"),await fs())}}catch(f){console.error("Error fetching notifications:",f)}},H0=async f=>{try{const E=localStorage.getItem("token");(await fetch(`/api/notifications/${f}/read`,{method:"PUT",headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}})).ok&&(_n(H=>H.map(J=>J._id===f?{...J,isRead:!0}:J)),console.log("✅ Notification marked as read:",f))}catch(E){console.error("Error marking notification as read:",E)}},W0=async()=>{try{const f=localStorage.getItem("token");(await fetch("/api/notifications/mark-all-read",{method:"PUT",headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}})).ok&&(_n(I=>I.map(H=>({...H,isRead:!0}))),console.log("✅ All notifications marked as read"))}catch(f){console.error("Error marking all notifications as read:",f)}},Q0=async f=>{try{const E=localStorage.getItem("token");if(!E){console.log("❌ No token found");return}console.log("📥 Fetching messages for user:",f);const I=await fetch(`/api/messages/${f}`,{headers:{Authorization:`Bearer ${E}`}});if(I.ok){const H=await I.json();console.log("✅ Messages fetched:",H.length),W(H)}else{const H=await I.json();console.error("❌ Failed to fetch messages:",H)}}catch(E){console.error("Error fetching messages:",E)}},ja=async()=>{if(!q.trim()||!ce){console.log("❌ Missing message or chat user:",{newMessage:q,chatUser:ce});return}const f=ce.id||ce._id;if(!f){console.error("❌ No recipient ID found:",ce),alert("خطأ: لا يمكن تحديد المستقبل");return}console.log("📤 Sending message:",{recipientId:f,content:q});try{const E=localStorage.getItem("token"),I=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({recipientId:f,content:q})});if(I.ok){const H=await I.json();console.log("✅ Message sent successfully:",H),W([...A,H.messageData]),B(""),$l(),dr&&dr.sendPrivateMessage(H.messageData,f);try{const J=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");J.volume=.3,J.play().catch(()=>{})}catch{}}else{const H=await I.json();console.error("❌ Message send failed:",H),alert(H.message||"فشل في إرسال الرسالة")}}catch(E){console.error("Error sending message:",E),alert("حدث خطأ في إرسال الرسالة")}},$l=()=>{setTimeout(()=>{const f=document.getElementById("messages-container");f&&f.scrollTo({top:f.scrollHeight,behavior:"smooth"})},100)},G0=f=>{console.log("💬 Opening chat with user:",f);const E=f.id||f._id;console.log("📋 User ID for messages:",E),b(f),ms(!0),W([]),E?Q0(E).then(()=>{$l()}):console.error("❌ No user ID found for chat")},D0=async()=>{if(!t&&(e!=null&&e.id))try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for checking friendship");return}const E=await fetch(`/api/friends/check/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const I=await E.json();m(I.isFriend),console.log("✅ Friendship status checked:",I.isFriend)}else console.error("❌ Failed to check friendship:",E.status),m(!1)}catch(f){console.error("Error checking friendship:",f),m(!1)}},J0=async()=>{if(M.trim())try{const f=localStorage.getItem("token"),E=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({recipientId:e.id,content:M})});if(E.ok)L(""),p(!1),alert("تم إرسال الرسالة بنجاح!");else{const I=await E.json();alert(I.message||"فشل في إرسال الرسالة")}}catch(f){console.error("Error sending message:",f),alert("خطأ في إرسال الرسالة")}},Z0=f=>{var I;const E=(I=f.target.files)==null?void 0:I[0];if(E){if(E.size>5*1024*1024){alert("حجم الصورة كبير جداً. يجب أن يكون أقل من 5 ميجابايت");return}const H=new FileReader;H.onload=J=>{var Q;u((Q=J.target)==null?void 0:Q.result)},H.readAsDataURL(E)}},K0=async()=>{var f,E;N(!0);try{const I=localStorage.getItem("token"),H={gender:g};c&&c!==(e==null?void 0:e.profileImage)&&(H.profileImage=c),console.log("🔄 Updating profile with data:",{hasProfileImage:!!H.profileImage,gender:H.gender,selectedImageLength:(c==null?void 0:c.length)||0,currentImageLength:((f=e==null?void 0:e.profileImage)==null?void 0:f.length)||0});const J=await fetch("/api/profile/update",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${I}`},body:JSON.stringify(H)});if(J.ok){const Q=await J.json();console.log("✅ Profile updated successfully:",{hasProfileImage:!!Q.profileImage,profileImageLength:((E=Q.profileImage)==null?void 0:E.length)||0}),n==null||n(Q),u(""),a(!1),alert("تم تحديث الملف الشخصي بنجاح!")}else{const Q=await J.json();console.error("❌ Profile update failed:",Q),alert(Q.message||"فشل في تحديث الملف الشخصي")}}catch(I){console.error("Error updating profile:",I),alert("حدث خطأ في تحديث الملف الشخصي")}finally{N(!1)}},Y0=()=>c||(e!=null&&e.profileImage?e.profileImage:(e==null?void 0:e.gender)==="female"?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNGRjY5QjQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNFMxMy4xIDYgMTIgNiAxMCA1LjEgMTAgNFMxMC45IDIgMTIgMlpNMjEgOVYxMUgyMFYxMkMxOS4xIDEyIDE4LjQgMTIuNiAxOC4xIDEzLjNDMTcuMSAxMS45IDE1LjEgMTEgMTMuOCAxMC43QzE0IDEwLjUgMTQuMSAxMC4yIDE0LjEgMTBDMTQgOS4xIDEzLjYgOC40IDEzIDhDMTMuNCA3LjYgMTMuNyA3IDE0IDYuOUMxNS40IDcuNyAxNi4yIDkuMSAxNiAzMEMxOC40IDI5IDEwLjUgMzAgOFMxMS42IDI5IDEwIDI5LjdIMThDMTggMjguNSAxOC4zIDI3LjUgMTguOSAyNi43QzE5LjMgMjcuMSAxOS44IDI3LjMgMjAuNSAyNy4zSDE5QzE5IDI3IDEwLjMgMjcgMTAuNSAyNy4zSDE5LjQgMjEgOVoiLz4KPHN2Zz4KPHN2Zz4K":"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQTMgMyAwIDAgMSAxNSA1QTMgMyAwIDAgMSAxMiA4QTMgMyAwIDAgMSA5IDVBMyAzIDAgMCAxIDEyIDJNMjEgMjFWMjBDMjEgMTYuMTMgMTcuODcgMTMgMTQgMTNIMTBDNi4xMyAxMyAzIDE2LjEzIDMgMjBWMjFIMjFaIi8+Cjwvc3ZnPgo=");return r.jsxs("div",{className:"max-w-md mx-auto bg-gradient-to-br from-gray-900 via-gray-800 to-black min-h-screen shadow-2xl overflow-hidden flex flex-col",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-4 text-white relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 opacity-20",children:[r.jsx("div",{className:"absolute top-0 left-0 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-10 right-0 w-16 h-16 bg-indigo-400/5 rounded-full blur-lg animate-bounce"}),r.jsx("div",{className:"absolute bottom-0 left-1/2 w-24 h-24 bg-slate-400/5 rounded-full blur-2xl animate-pulse delay-1000"})]}),r.jsxs("div",{className:"absolute inset-0 opacity-15",children:[r.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-white/30 rounded-full -translate-x-20 -translate-y-20 blur-xl"}),r.jsx("div",{className:"absolute bottom-0 right-0 w-32 h-32 bg-white/20 rounded-full translate-x-12 translate-y-12 blur-lg"}),r.jsx("div",{className:"absolute top-1/2 left-1/2 w-24 h-24 bg-white/10 rounded-full -translate-x-12 -translate-y-12 blur-md"})]}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"flex flex-col items-center mb-3",children:[r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 via-indigo-500 to-slate-500 animate-spin-slow opacity-60 blur-sm"}),r.jsxs("div",{className:"relative w-16 h-16 rounded-full overflow-hidden border-2 border-white/70 shadow-lg bg-gradient-to-br from-blue-400 to-indigo-500 ring-1 ring-white/40 backdrop-blur-sm transform group-hover:scale-105 transition-all duration-300",children:[r.jsx("img",{src:Y0(),alt:"الصورة الشخصية",className:"w-full h-full object-cover"}),r.jsx("div",{className:"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border border-white shadow-sm animate-pulse"})]}),t&&r.jsx("button",{onClick:()=>{var f;return i?(f=document.getElementById("imageUpload"))==null?void 0:f.click():a(!0)},className:"absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center shadow-lg border border-white/60 hover:scale-110 transition-all duration-300",children:r.jsx(Uh,{className:"w-3 h-3 text-white"})}),i&&r.jsx("input",{id:"imageUpload",type:"file",accept:"image/*",onChange:Z0,className:"hidden"})]}),r.jsxs("div",{className:"text-center mt-2",children:[r.jsx("h2",{className:"text-lg font-bold text-white mb-1 drop-shadow-md",children:e==null?void 0:e.username}),r.jsxs("p",{className:"text-white/60 text-xs bg-white/10 px-2 py-0.5 rounded-full backdrop-blur-sm",children:["ID: ",e==null?void 0:e.playerId]}),r.jsxs("div",{className:"flex items-center justify-center mt-1 gap-1",children:[r.jsxs("div",{className:"flex items-center bg-blue-500/20 px-1.5 py-0.5 rounded-full",children:[r.jsx(mn,{className:"w-2.5 h-2.5 text-blue-300 mr-1"}),r.jsxs("span",{className:"text-blue-200 text-xs font-medium",children:["Lv.",(e==null?void 0:e.level)||1]})]}),r.jsxs("div",{className:"flex items-center bg-indigo-500/20 px-1.5 py-0.5 rounded-full",children:[r.jsx(es,{className:"w-2.5 h-2.5 text-indigo-300 mr-1"}),r.jsx("span",{className:"text-indigo-200 text-xs font-medium",children:(e==null?void 0:e.points)||0})]})]})]}),i&&r.jsxs("div",{className:"mt-3 flex space-x-2 rtl:space-x-reverse",children:[r.jsx("button",{onClick:()=>x("male"),className:`px-3 py-1 rounded-full text-xs ${g==="male"?"bg-white text-blue-600":"bg-white/20 text-white"}`,children:"ذكر"}),r.jsx("button",{onClick:()=>x("female"),className:`px-3 py-1 rounded-full text-xs ${g==="female"?"bg-white text-pink-600":"bg-white/20 text-white"}`,children:"أنثى"})]})]}),t?r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"grid grid-cols-3 gap-1.5 text-center mt-3",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(Ll,{className:"w-3 h-3 text-yellow-400"})}),r.jsx("div",{className:"text-sm font-bold text-yellow-200",children:(e==null?void 0:e.goldCoins)||0}),r.jsx("div",{className:"text-xs text-yellow-300/80",children:"ذهب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-indigo-700/40 rounded-lg p-2 backdrop-blur-sm border border-indigo-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(m0,{className:"w-3 h-3 text-purple-400"})}),r.jsx("div",{className:"text-sm font-bold text-purple-200",children:(e==null?void 0:e.pearls)||0}),r.jsx("div",{className:"text-xs text-purple-300/80",children:"لؤلؤ"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(mn,{className:"w-3 h-3 text-blue-400"})}),r.jsxs("div",{className:"text-sm font-bold text-blue-200",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-blue-300/80",children:"مستوى"})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-1.5 text-center mt-1.5",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-green-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-green-400/20",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(ai,{className:"w-3 h-3 text-green-400"})}),r.jsx("div",{className:"text-sm font-bold text-green-200",children:(e==null?void 0:e.gamesPlayed)||0}),r.jsx("div",{className:"text-xs text-green-300/80",children:"ألعاب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-red-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-red-400/20",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(f0,{className:"w-3 h-3 text-red-400"})}),r.jsx("div",{className:"text-sm font-bold text-red-200",children:((Na=e==null?void 0:e.friends)==null?void 0:Na.length)||0}),r.jsx("div",{className:"text-xs text-red-300/80",children:"أصدقاء"})]})]}),r.jsx("div",{className:"mt-3 flex justify-center",children:r.jsxs("button",{onClick:()=>jt(!0),className:"relative bg-white/20 hover:bg-white/30 rounded-lg p-3 backdrop-blur-sm transition-all duration-300 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"🔔"}),r.jsx("span",{className:"text-white text-sm font-medium",children:"الإشعارات"}),Se.filter(f=>!f.isRead).length>0&&r.jsx("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold",children:Se.filter(f=>!f.isRead).length})]})})]}):r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"bg-white/20 rounded-lg p-3 backdrop-blur-sm text-center",children:[r.jsxs("div",{className:"text-lg font-bold text-black",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-white/80",children:"مستوى"})]}),S&&r.jsxs("button",{onClick:()=>p(!0),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[r.jsx(gn,{className:"w-4 h-4"}),r.jsx("span",{children:"إرسال رسالة"})]})]})]}),i&&r.jsxs("div",{className:"absolute top-4 right-4 flex space-x-2 rtl:space-x-reverse",children:[r.jsx("button",{onClick:K0,disabled:w,className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:r.jsx(Fh,{className:"w-4 h-4 text-white"})}),r.jsx("button",{onClick:()=>{a(!1),u(""),x((e==null?void 0:e.gender)||"male")},className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:r.jsx(fa,{className:"w-4 h-4 text-white"})})]})]}),r.jsx("div",{className:"bg-gradient-to-r from-gray-900/95 via-purple-900/95 to-gray-900/95 backdrop-blur-md border-b border-purple-500/30 z-20 mx-4 rounded-xl mt-4 shadow-xl flex-shrink-0",children:r.jsx("div",{className:"flex overflow-x-auto scrollbar-hide p-2",children:[{id:"overview",label:"عام",icon:yn,color:"from-blue-500 to-cyan-500"},...t?[{id:"friends",label:"أصدقاء",icon:kn,color:"from-green-500 to-emerald-500"},{id:"gifts",label:"هدايا",icon:qh,color:"from-pink-500 to-rose-500"},{id:"items",label:"عناصر",icon:mn,color:"from-yellow-500 to-orange-500"},{id:"charge",label:"شحن",icon:Vh,color:"from-purple-500 to-violet-500"},{id:"exchange",label:"تبديل",icon:Rh,color:"from-indigo-500 to-blue-500"}]:[]].map(f=>r.jsxs("button",{onClick:()=>o(f.id),className:`flex-shrink-0 flex flex-col items-center px-4 py-3 min-w-[70px] transition-all duration-500 rounded-xl relative overflow-hidden group ${l===f.id?`bg-gradient-to-br ${f.color} text-white shadow-2xl transform scale-110 animate-glow`:"text-gray-300 hover:bg-gray-800/60 hover:text-white hover:scale-105"}`,children:[l===f.id&&r.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${f.color} opacity-20 blur-xl`}),r.jsxs("div",{className:"relative z-10 flex flex-col items-center",children:[r.jsx(f.icon,{className:`w-5 h-5 mb-1 transition-all duration-300 ${l===f.id?"animate-bounce":"group-hover:scale-110"}`}),r.jsx("span",{className:"text-xs font-medium",children:f.label})]}),l===f.id&&r.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"})]},f.id))})}),r.jsxs("div",{className:"flex-1 flex flex-col",children:[l==="overview"&&r.jsxs("div",{className:"flex-1 flex flex-col p-4 gap-4",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/90 via-blue-800/90 to-indigo-800/90 rounded-xl p-3 border border-blue-400/30 shadow-lg backdrop-blur-sm relative overflow-hidden flex-shrink-0",children:[r.jsx("div",{className:"absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-full blur-xl animate-pulse"}),r.jsx("div",{className:"absolute bottom-0 left-0 w-12 h-12 bg-indigo-500/10 rounded-full blur-lg animate-float"}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("h3",{className:"font-bold text-blue-300 mb-3 text-base flex items-center gap-2",children:[r.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center",children:r.jsx(yn,{className:"w-3 h-3 text-white"})}),"معلومات الحساب"]}),r.jsxs("div",{className:"grid gap-2",children:[r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-pink-500 to-purple-500 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"👤"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الجنس"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:(e==null?void 0:e.gender)==="female"?"👩 أنثى":"👨 ذكر"})]}),r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-blue-500 to-indigo-500 rounded flex items-center justify-center",children:r.jsx(Oh,{className:"w-3 h-3 text-white"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الانضمام"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.joinedAt).toLocaleDateString("ar-EG")})]}),r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-500 rounded flex items-center justify-center",children:r.jsx(pn,{className:"w-3 h-3 text-white"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"آخر نشاط"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.lastActive).toLocaleDateString("ar-EG")})]})]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-3 flex-shrink-0",children:[r.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-blue-800/80 rounded-xl p-3 shadow-lg border border-blue-400/30 backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:r.jsx(es,{className:"w-4 h-4 text-white"})}),r.jsx("div",{className:"text-lg font-bold text-blue-200",children:(e==null?void 0:e.experience)||0}),r.jsx("div",{className:"text-xs text-blue-300 font-medium",children:"خبرة"})]})}),r.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-indigo-800/80 rounded-xl p-3 shadow-lg border border-indigo-400/30 backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:r.jsx(mn,{className:"w-4 h-4 text-white"})}),r.jsxs("div",{className:"text-lg font-bold text-indigo-200",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-indigo-300 font-medium",children:"مستوى"})]})})]}),!t&&r.jsx("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 rounded-xl p-3 border border-blue-400/30 shadow-md backdrop-blur-sm flex-shrink-0",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-sm",children:r.jsx(yn,{className:"w-3 h-3 text-white"})}),r.jsxs("div",{children:[r.jsx("h4",{className:"font-bold text-blue-300 text-sm",children:"ملف عام"}),r.jsx("p",{className:"text-xs text-blue-200",children:"معلومات أساسية فقط"})]})]})})]}),t&&l==="friends"&&r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"👥"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إدارة الأصدقاء"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"أضف وتفاعل مع أصدقائك في المنصة"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx(Rl,{className:"w-6 h-6 text-blue-300"}),r.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إضافة صديق جديد"})]}),r.jsxs("div",{className:"flex gap-3 mb-4",children:[r.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:y,onChange:f=>{const E=f.target.value.replace(/\D/g,"");O(E),fe("")},className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:6,onKeyPress:f=>f.key==="Enter"&&ya()}),r.jsxs("button",{onClick:ya,disabled:He||y.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:[He?"⏳":"🔍"," ",He?"جاري البحث...":"بحث"]})]}),Ie&&r.jsx("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-3 mb-4",children:r.jsx("p",{className:"text-red-200 text-sm text-center",children:Ie})}),X&&r.jsx("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-4",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:X.profileImage?r.jsx("img",{src:X.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(Sa=(ka=X.username)==null?void 0:ka.charAt(0))==null?void 0:Sa.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-green-200 font-bold",children:X.username}),r.jsxs("p",{className:"text-green-300 text-xs",children:["رقم اللاعب: ",X.playerId]})]})]}),r.jsx("button",{onClick:()=>O0(X.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"➕ إضافة صديق"})]})})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"👫"}),"قائمة الأصدقاء (",ot.length,")"]}),ot.length===0?r.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"😔"}),"لا توجد أصدقاء حالياً"]}):r.jsx("div",{className:"space-y-3",children:ot.map(f=>{var E,I;return r.jsxs("div",{className:"flex items-center justify-between p-3 bg-slate-900/40 rounded-xl",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:f.profileImage?r.jsx("img",{src:f.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(I=(E=f.username)==null?void 0:E.charAt(0))==null?void 0:I.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-slate-200 font-medium",children:f.username}),r.jsxs("p",{className:"text-slate-400 text-xs",children:["رقم اللاعب: ",f.playerId]})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>G0(f),className:"bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all",children:"💬 محادثة"}),r.jsx("button",{onClick:()=>C(f),className:"bg-gradient-to-r from-purple-500 to-pink-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-purple-600 hover:to-pink-700 transition-all",children:"🎁 هدية"})]})]},f.id)})})]}),r.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/60 to-green-800/60 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-emerald-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📩"}),"طلبات الصداقة (",Mn.length,")"]}),Mn.length===0?r.jsxs("div",{className:"text-center py-6 text-emerald-300 text-sm bg-emerald-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"📭"}),"لا توجد طلبات صداقة جديدة"]}):r.jsx("div",{className:"space-y-3",children:Mn.map(f=>{var E,I;return r.jsxs("div",{className:"flex items-center justify-between p-3 bg-emerald-900/40 rounded-xl",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:f.requester.profileImage?r.jsx("img",{src:f.requester.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(I=(E=f.requester.username)==null?void 0:E.charAt(0))==null?void 0:I.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-emerald-200 font-medium",children:f.requester.username}),r.jsxs("p",{className:"text-emerald-400 text-xs",children:["رقم اللاعب: ",f.requester.playerId]}),r.jsx("p",{className:"text-emerald-500 text-xs",children:new Date(f.requestedAt).toLocaleDateString("ar")})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>$0(f.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"✅ قبول"}),r.jsx("button",{className:"bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-red-600 hover:to-red-700 transition-all",children:"❌ رفض"})]})]},f.id)})})]})]}),t&&l==="gifts"&&r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🎁"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام إدارة الهدايا"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"أرسل واستقبل الهدايا المتنوعة مع الأصدقاء"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-5",children:[r.jsx(Pr,{className:"w-6 h-6 text-blue-300"}),r.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إرسال هدية جديدة"})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-yellow-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"💰"}),"العملات الذهبية"]}),r.jsx("div",{className:"grid grid-cols-1 gap-3",children:r.jsxs("button",{onClick:()=>pt("gold"),className:`p-4 border rounded-xl hover:bg-yellow-700/50 transition-all duration-300 flex items-center gap-4 shadow-lg ${pe==="gold"?"bg-yellow-700/60 border-yellow-300":"bg-yellow-800/40 border-yellow-400/30"}`,children:[r.jsx("div",{className:"text-3xl drop-shadow-lg",children:"🪙"}),r.jsxs("div",{className:"text-right flex-1",children:[r.jsx("div",{className:"text-sm font-bold text-yellow-200",children:"عملات ذهبية"}),r.jsx("div",{className:"text-xs text-yellow-300",children:"للشراء والاستخدام في المنصة"})]}),pe==="gold"&&r.jsx("div",{className:"text-yellow-300",children:"✓"})]})})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-red-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"⚠️"}),"العناصر الضارة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{onClick:()=>pt("bomb"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${pe==="bomb"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💣"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"قنبلة مدمرة"}),pe==="bomb"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>pt("bat"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${pe==="bat"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🦇"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"خفاش مؤذي"}),pe==="bat"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>pt("snake"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${pe==="snake"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🐍"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"ثعبان سام"}),pe==="snake"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]})]})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-emerald-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"✨"}),"العناصر المفيدة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{onClick:()=>pt("gem"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${pe==="gem"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💎"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"جوهرة نادرة"}),pe==="gem"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>pt("star"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${pe==="star"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"⭐"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"نجمة ذهبية"}),pe==="star"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>pt("coin"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${pe==="coin"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🪙"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"عملة خاصة"}),pe==="coin"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]})]})]}),r.jsxs("div",{className:"space-y-4",children:[pe&&r.jsx("div",{className:"bg-blue-900/30 p-3 rounded-xl border border-blue-400/30",children:r.jsxs("p",{className:"text-blue-200 text-sm text-center",children:["العنصر المختار: ",r.jsx("span",{className:"font-bold text-blue-100",children:wa(pe)})]})}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:rn,onChange:f=>{const E=f.target.value.replace(/\D/g,"");cs(E)},maxLength:6,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("button",{onClick:q0,disabled:ds||!pe||rn.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:ds?"⏳ جاري الإرسال...":"🎁 إرسال"})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📦"}),"الهدايا المستلمة"]}),r.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"🎈"}),"لا توجد هدايا جديدة في الوقت الحالي"]})]})]}),t&&l==="items"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-4xl mb-3",children:"⚡"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"العناصر المجمعة"}),r.jsx("p",{className:"text-sm text-gray-500",children:"العناصر التي حصلت عليها من الألعاب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/80 to-green-800/80 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-emerald-300 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"⭐"}),"العناصر المفيدة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💎"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"جوهرة نادرة"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 500 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.gems})]}),r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"⭐"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"نجمة ذهبية"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 200 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.stars})]}),r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"عملة خاصة"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 100 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.coins})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-red-800/80 to-rose-800/80 p-6 rounded-2xl border border-red-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-red-300 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"💣"}),"العناصر الضارة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💣"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"قنبلة مدمرة"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 100 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bombs})]}),r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🦇"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"خفاش مؤذي"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 50 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bats})]}),r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🐍"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"ثعبان سام"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 75 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.snakes})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-amber-200 mb-3 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"💡"}),"نصائح مهمة"]}),r.jsx("p",{className:"text-amber-100 text-sm leading-relaxed",children:"اجمع العناصر المفيدة من الألعاب • أرسلها كهدايا للأصدقاء • بادلها بعملات ذهبية قيمة"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/80 to-indigo-800/80 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-3xl drop-shadow-lg",children:"🛡️"}),"نظام الحماية المتطور"]}),r.jsx("p",{className:"text-blue-100 text-sm mb-6 leading-relaxed",children:"احمِ نفسك من العناصر الضارة والهجمات الخطيرة في الألعاب والهدايا"}),r.jsxs("div",{className:"grid grid-cols-1 gap-5",children:[r.jsxs("div",{className:"bg-blue-800/40 p-5 rounded-xl border border-blue-400/30 backdrop-blur-sm shadow-lg",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-bold text-blue-200 text-base",children:"🥇 درع ذهبي أساسي"}),r.jsx("span",{className:"text-xs text-blue-100 bg-blue-600/40 px-3 py-1 rounded-full font-medium",children:"24 ساعة"})]}),r.jsx("p",{className:"text-sm text-blue-100 mb-4 leading-relaxed",children:"حماية قوية ضد القنابل المدمرة والخفافيش المؤذية والثعابين السامة"}),F!=null&&F.isActive?r.jsxs("div",{className:"bg-green-600/40 p-3 rounded-xl border border-green-400/30 text-center",children:[r.jsx("div",{className:"text-green-200 text-sm font-bold",children:"🛡️ الدرع نشط"}),r.jsxs("div",{className:"text-green-100 text-xs mt-1",children:["ينتهي في: ",F.expiresAt?new Date(F.expiresAt).toLocaleString("ar"):"غير محدد"]})]}):r.jsx("button",{onClick:()=>R0("gold"),disabled:P,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:P?"⏳ جاري التفعيل...":"🛡️ تفعيل الحماية (5,000 🪙)"})]}),r.jsxs("div",{className:"bg-purple-800/40 p-5 rounded-xl border border-purple-400/30 backdrop-blur-sm shadow-lg",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-bold text-purple-200 text-base",children:"👑 درع متقدم مميز"}),r.jsx("span",{className:"text-xs text-purple-100 bg-purple-600/40 px-3 py-1 rounded-full font-medium",children:"7 أيام"})]}),r.jsx("p",{className:"text-sm text-purple-100 mb-4 leading-relaxed",children:"حماية مميزة وشاملة لمدة أسبوع كامل ضد جميع العناصر الضارة والهجمات"}),r.jsx("button",{onClick:()=>alert("الدرع المميز غير متاح حالياً. استخدم الدرع الأساسي."),className:"w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 rounded-xl text-sm font-bold cursor-not-allowed opacity-60",disabled:!0,children:"👑 قريباً - الحماية المميزة"})]})]})]})]}),t&&l==="charge"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"💰"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"شحن الرصيد الذهبي"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"اشحن عملاتك الذهبية بأفضل الأسعار"})]}),r.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[r.jsx("div",{className:"bg-gradient-to-br from-yellow-800/60 to-amber-800/60 p-6 rounded-2xl border border-yellow-400/30 shadow-xl backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("h4",{className:"font-bold text-yellow-200 mb-2 text-lg",children:"5,000 عملة ذهبية"}),r.jsx("p",{className:"text-yellow-100 text-base mb-4 font-semibold",children:"💵 $1 USD فقط"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>va(5e3,!0,"1_dollar"),disabled:nn||!An["1_dollar"],className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:nn?"⏳":An["1_dollar"]?"🆓 مجاني":"✅ مستخدم"}),r.jsx("button",{onClick:()=>ba(5e3,"$1 USD"),className:"flex-1 bg-gradient-to-r from-yellow-500 to-amber-600 text-white py-3 rounded-xl text-xs font-bold hover:from-yellow-600 hover:to-amber-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})}),r.jsx("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("h4",{className:"font-bold text-green-200 mb-2 text-lg",children:"27,200 عملة ذهبية"}),r.jsx("p",{className:"text-green-100 text-base mb-1 font-semibold",children:"💵 $5 USD"}),r.jsx("p",{className:"text-sm text-green-300 bg-green-900/30 px-3 py-1 rounded-lg mb-4 font-medium",children:"🎉 وفر 8% أكثر!"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>va(27200,!0,"5_dollar"),disabled:nn||!An["5_dollar"],className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-3 rounded-xl text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:nn?"⏳":An["5_dollar"]?"🆓 مجاني":"✅ مستخدم"}),r.jsx("button",{onClick:()=>ba(27200,"$5 USD"),className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})})]})]}),t&&l==="exchange"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🔄"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام تبديل العملات"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"اللآلئ مخصصة حصرياً للتحويل إلى دولارات نقدية"})]}),r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🪙➡️🦪"}),"تحويل ذهب إلى لآلئ"]}),r.jsx("p",{className:"text-blue-100 text-sm mb-4 bg-blue-900/30 px-3 py-2 rounded-lg",children:"معدل التحويل: 10,000 🪙 = 1 🦪"}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("input",{type:"number",placeholder:"10000",value:We,onChange:f=>En(Math.max(1e4,parseInt(f.target.value)||1e4)),min:"10000",step:"10000",max:(e==null?void 0:e.goldCoins)||0,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("span",{className:"text-blue-200 font-medium",children:"🪙 ➡️ 🦪"})]}),r.jsxs("div",{className:"text-center mb-4",children:[r.jsxs("p",{className:"text-blue-200 text-sm",children:["ستحصل على: ",r.jsxs("span",{className:"font-bold text-blue-100",children:[Math.floor(We/1e4)," 🦪"]})]}),r.jsxs("p",{className:"text-blue-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.goldCoins)||0," 🪙"]})]}),r.jsx("button",{onClick:B0,disabled:Xe||We<1e4||((e==null?void 0:e.goldCoins)||0)<We,className:"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:Xe?"⏳ جاري التحويل...":"🔄 تحويل إلى لآلئ"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-green-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🦪➡️💵"}),"سحب دولارات نقدية"]}),r.jsx("div",{className:"bg-green-900/30 p-4 rounded-xl mb-4",children:r.jsxs("p",{className:"text-green-100 text-sm leading-relaxed",children:[r.jsx("strong",{className:"text-green-200",children:"💰 معدل التحويل:"})," 10 🦪 = $1 USD",r.jsx("br",{}),r.jsx("strong",{className:"text-green-200",children:"🎯 الحد الأدنى للسحب:"})," $25 USD (250 🦪)"]})}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("input",{type:"number",placeholder:"250",value:ht,onChange:f=>is(Math.max(250,parseInt(f.target.value)||250)),min:"250",max:(e==null?void 0:e.pearls)||0,className:"flex-1 px-4 py-3 bg-green-900/30 border border-green-400/30 rounded-xl text-white placeholder-green-300 focus:outline-none focus:ring-2 focus:ring-green-500"}),r.jsx("span",{className:"text-green-200 font-medium",children:"🦪 ➡️ $"})]}),r.jsxs("div",{className:"text-center mb-4",children:[r.jsxs("p",{className:"text-green-200 text-sm",children:["ستحصل على: ",r.jsxs("span",{className:"font-bold text-green-100",children:["$",ht/10," USD"]})]}),r.jsxs("p",{className:"text-green-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.pearls)||0," 🦪"]})]}),r.jsx("button",{onClick:V0,disabled:ht<250||((e==null?void 0:e.pearls)||0)<ht,className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:"📱 طلب سحب عبر واتساب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-amber-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📝"}),"معلومات مهمة"]}),r.jsxs("div",{className:"space-y-2 text-amber-100 text-sm leading-relaxed",children:[r.jsxs("p",{children:["• ",r.jsx("strong",{children:"اللآلئ 🦪"})," - مخصصة حصرياً للتحويل إلى دولارات نقدية"]}),r.jsxs("p",{children:["• ",r.jsx("strong",{children:"العملات الذهبية 🪙"})," - للشراء والتبادل داخل المنصة"]}),r.jsxs("p",{children:["• ",r.jsx("strong",{children:"العناصر الخاصة"})," - تُكسب من الألعاب والتحديات"]})]})]})]})]})]}),d&&r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-slate-800 to-blue-800 rounded-2xl p-6 w-full max-w-md shadow-2xl border border-blue-400/30",children:[r.jsxs("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:["إرسال رسالة إلى ",e==null?void 0:e.username]}),r.jsx("textarea",{value:M,onChange:f=>L(f.target.value),placeholder:"اكتب رسالتك هنا...",className:"w-full p-3 rounded-xl bg-slate-800/40 border border-blue-400/30 text-white placeholder-blue-300 resize-none h-32 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:500}),r.jsxs("div",{className:"text-right text-xs text-blue-300 mt-1 mb-4",children:[M.length,"/500"]}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>{p(!1),L("")},className:"flex-1 bg-slate-600 hover:bg-slate-700 text-white py-2 px-4 rounded-xl transition-colors",children:"إلغاء"}),r.jsxs("button",{onClick:J0,disabled:!M.trim(),className:"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-500 disabled:to-gray-600 text-white py-2 px-4 rounded-xl transition-all flex items-center justify-center gap-2",children:[r.jsx(Pr,{className:"w-4 h-4"}),"إرسال"]})]})]})}),h&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-purple-900 to-pink-900 rounded-2xl p-6 w-full max-w-md border border-purple-400/30 shadow-2xl",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-4xl mb-3",children:"🎁"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إرسال هدية"}),r.jsxs("p",{className:"text-purple-200 text-sm",children:["إلى: ",h.username]})]}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"نوع الهدية:"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>G("gold"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${R==="gold"?"bg-gradient-to-r from-yellow-500 to-orange-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"🪙 عملات ذهبية"}),r.jsx("button",{onClick:()=>G("pearls"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${R==="pearls"?"bg-gradient-to-r from-blue-500 to-cyan-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"💎 لآلئ"})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"الكمية:"}),r.jsx("input",{type:"number",value:_,onChange:f=>$(Math.max(1,parseInt(f.target.value)||1)),min:"1",max:R==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls,className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500"}),r.jsxs("p",{className:"text-purple-300 text-xs mt-1",children:["الحد الأقصى: ",R==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"رسالة (اختيارية):"}),r.jsx("textarea",{value:Y,onChange:f=>ne(f.target.value),placeholder:"اكتب رسالة مع الهدية...",className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none",rows:3,maxLength:200})]})]}),r.jsxs("div",{className:"flex gap-3 mt-6",children:[r.jsx("button",{onClick:()=>{C(null),ne(""),$(100)},className:"flex-1 bg-gray-600 text-white py-3 rounded-xl font-bold hover:bg-gray-700 transition-all",children:"إلغاء"}),r.jsx("button",{onClick:U0,disabled:xe||_<=0,className:"flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-xl font-bold hover:from-purple-600 hover:to-pink-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:xe?"⏳ جاري الإرسال...":"🎁 إرسال الهدية"})]})]})}),Ol&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-slate-900 to-purple-900 rounded-2xl p-6 w-full max-w-md max-h-[80vh] overflow-hidden border border-purple-400/30 shadow-2xl",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("h3",{className:"text-xl font-bold text-white flex items-center gap-2",children:["🔔 الإشعارات",Se.filter(f=>!f.isRead).length>0&&r.jsx("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:Se.filter(f=>!f.isRead).length})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[Se.filter(f=>!f.isRead).length>0&&r.jsx("button",{onClick:W0,className:"text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors",children:"تحديد الكل كمقروء"}),r.jsx("button",{onClick:()=>jt(!1),className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]})]}),r.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:Se.length===0?r.jsxs("div",{className:"text-center py-8 text-gray-400",children:[r.jsx("div",{className:"text-4xl mb-2",children:"📭"}),r.jsx("p",{children:"لا توجد إشعارات"})]}):Se.map(f=>r.jsx("div",{onClick:()=>{f.isRead||H0(f._id)},className:`p-3 rounded-xl border transition-all cursor-pointer hover:bg-opacity-80 ${f.isRead?"bg-slate-800/50 border-slate-600/30":"bg-blue-900/30 border-blue-400/30 shadow-lg"}`,children:r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:"text-2xl",children:[f.type==="gift_received"&&"🎁",f.type==="item_received"&&"📦",f.type==="friend_request"&&"👥",f.type==="message"&&"💬"]}),!f.isRead&&r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-slate-900 animate-pulse"})]}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h4",{className:`font-bold text-sm ${f.isRead?"text-gray-300":"text-white"}`,children:f.title}),r.jsx("p",{className:`text-xs mt-1 ${f.isRead?"text-gray-400":"text-gray-200"}`,children:f.message}),r.jsxs("div",{className:"flex items-center justify-between mt-2",children:[r.jsx("span",{className:"text-gray-400 text-xs",children:new Date(f.createdAt).toLocaleString("ar")}),!f.isRead&&r.jsx("span",{className:"text-blue-400 text-xs font-bold",children:"جديد"})]})]})]})},f._id))})]})}),Tn&&ce&&r.jsxs("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex flex-col",onClick:f=>{f.target===f.currentTarget&&Nt(!1)},children:[r.jsxs("div",{className:"bg-gradient-to-r from-green-600 to-green-700 px-4 py-3 flex items-center gap-3 shadow-lg",children:[r.jsx("button",{onClick:()=>ms(!1),className:"text-white hover:bg-white/20 rounded-full p-2 transition-colors",children:r.jsx(d0,{className:"w-5 h-5"})}),r.jsx("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-white/20 border-2 border-white/30",children:ce.profileImage?r.jsx("img",{src:ce.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold text-lg",children:(Ma=(Ca=ce.username)==null?void 0:Ca.charAt(0))==null?void 0:Ma.toUpperCase()})}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-bold text-white text-lg",children:ce.username}),r.jsxs("p",{className:"text-green-100 text-xs opacity-90",children:["رقم اللاعب: ",ce.playerId]}),r.jsx("p",{className:"text-green-100 text-xs opacity-75",children:"🕐 المحادثات تختفي بعد 3 أيام"})]}),r.jsx("div",{className:"text-green-100",children:r.jsx(gn,{className:"w-6 h-6"})})]}),r.jsxs("div",{className:"flex-1 overflow-y-auto px-4 py-6 space-y-4",id:"messages-container",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,backgroundColor:"#0f172a"},children:[A.length===0?r.jsxs("div",{className:"text-center py-20",children:[r.jsx("div",{className:"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(gn,{className:"w-10 h-10 text-white/50"})}),r.jsx("p",{className:"text-white/70 text-lg font-medium",children:"ابدأ محادثة جديدة"}),r.jsx("p",{className:"text-white/50 text-sm mt-2",children:"أرسل رسالة لبدء المحادثة"}),r.jsx("div",{className:"mt-4 p-3 bg-yellow-500/20 rounded-lg border border-yellow-500/30",children:r.jsx("p",{className:"text-yellow-200 text-xs",children:"🕐 تنبيه: المحادثات تُحذف تلقائياً بعد 3 أيام للحفاظ على الخصوصية"})})]}):A.map((f,E)=>{var J,Q;const I=((J=f.sender)==null?void 0:J._id)===(e==null?void 0:e.id),H=E===0||new Date(f.createdAt).getTime()-new Date((Q=A[E-1])==null?void 0:Q.createdAt).getTime()>3e5;return r.jsxs("div",{children:[H&&r.jsx("div",{className:"text-center my-4",children:r.jsx("span",{className:"bg-black/30 text-white/70 px-3 py-1 rounded-full text-xs",children:new Date(f.createdAt).toLocaleDateString("ar",{weekday:"short",hour:"2-digit",minute:"2-digit"})})}),r.jsx("div",{className:`flex ${I?"justify-end":"justify-start"} mb-2`,children:r.jsxs("div",{className:`max-w-[80%] px-4 py-3 rounded-2xl shadow-lg relative ${I?"bg-gradient-to-r from-green-500 to-green-600 text-white rounded-br-md":"bg-white text-gray-800 rounded-bl-md"}`,children:[r.jsx("p",{className:"text-sm leading-relaxed break-words whitespace-pre-wrap",children:f.content}),r.jsxs("div",{className:`flex items-center justify-end mt-1 text-xs ${I?"text-green-100":"text-gray-500"}`,children:[r.jsx("span",{children:new Date(f.createdAt).toLocaleTimeString("ar",{hour:"2-digit",minute:"2-digit"})}),I&&r.jsx("div",{className:"ml-1 text-green-200",children:"✓✓"})]}),r.jsx("div",{className:`absolute bottom-0 w-4 h-4 ${I?"-right-2 bg-gradient-to-r from-green-500 to-green-600":"-left-2 bg-white"}`,style:{clipPath:I?"polygon(0 0, 100% 0, 0 100%)":"polygon(100% 0, 0 0, 100% 100%)"}})]})})]},f._id)}),Pe&&r.jsx("div",{className:"flex justify-start mb-2",children:r.jsx("div",{className:"bg-white px-4 py-3 rounded-2xl rounded-bl-md shadow-lg",children:r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsxs("div",{className:"flex gap-1",children:[r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),r.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"يكتب..."})]})})})]}),r.jsxs("div",{className:"bg-gray-100 px-4 py-3 flex items-end gap-3 relative",children:[sn&&r.jsx("div",{className:"absolute bottom-full left-4 right-4 mb-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-h-48 overflow-y-auto",children:r.jsx("div",{className:"grid grid-cols-8 gap-2",children:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","👍","👎","👌","🤌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤝","🙏","✍️","💪","🦾","🦿","🦵"].map((f,E)=>r.jsx("button",{onClick:()=>{B(I=>I+f),Nt(!1)},className:"text-xl hover:bg-gray-100 rounded-lg p-1 transition-colors",children:f},E))})}),r.jsx("button",{onClick:()=>Nt(!sn),className:`text-gray-500 hover:text-gray-700 transition-colors p-2 ${sn?"bg-gray-200 rounded-full":""}`,children:r.jsx("span",{className:"text-xl",children:"😊"})}),r.jsx("div",{className:"flex-1 relative",children:r.jsx("textarea",{value:q,onChange:f=>{B(f.target.value),f.target.style.height="auto",f.target.style.height=Math.min(f.target.scrollHeight,120)+"px"},placeholder:"اكتب رسالة...",className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-3xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none max-h-[120px] min-h-[48px]",onKeyPress:f=>{f.key==="Enter"&&!f.shiftKey&&(f.preventDefault(),q.trim()&&ja())},rows:1,autoFocus:!0})}),r.jsx("button",{onClick:ja,disabled:!q.trim(),className:`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${q.trim()?"bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:r.jsx(Pr,{className:"w-5 h-5"})})]})]})]})};class T0{constructor(t){this.localStream=null,this.peerConnections=new Map,this.remoteUsers=new Map,this.isJoined=!1,this.isMuted=!1,this.roomId=null,this.userId=null,this.processingOffers=new Set,this.connectionAttempts=new Map,this.connectionMonitorInterval=null,this.audioContext=null,this.analyser=null,this.voiceActivityThreshold=25,this.isMonitoringVoice=!1,this.isSpeaking=!1,this.lastVoiceActivitySent=0,this.voiceActivityDebounce=500,this.iceServers=[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"},{urls:"turn:openrelay.metered.ca:80",username:"openrelayproject",credential:"openrelayproject"}],this.wsService=t,this.setupWebSocketHandlers()}setupWebSocketHandlers(){this.wsService.onMessage("webrtc_offer",this.handleOffer.bind(this)),this.wsService.onMessage("webrtc_answer",this.handleAnswer.bind(this)),this.wsService.onMessage("webrtc_ice_candidate",this.handleIceCandidate.bind(this)),this.wsService.onMessage("user_joined_voice",this.handleUserJoined.bind(this)),this.wsService.onMessage("user_left_voice",this.handleUserLeft.bind(this))}async joinRoom(t,n){var s;try{this.roomId=t,this.userId=n,this.localStream=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0},video:!1}),this.startVoiceActivityDetection(),this.wsService.send({type:"join_voice_room",data:{roomId:t,userId:n}}),this.isJoined=!0,this.startConnectionMonitoring()}catch(l){throw console.error("❌ Error joining voice room:",l),(s=this.onError)==null||s.call(this,l),l}}async leaveRoom(){var t;try{this.peerConnections.forEach((n,s)=>{n.close()}),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(n=>n.stop()),this.localStream=null),this.stopVoiceActivityDetection(),this.roomId&&this.userId&&this.wsService.send({type:"leave_voice_room",data:{roomId:this.roomId,userId:this.userId}}),this.isJoined=!1,this.remoteUsers.clear(),this.processingOffers.clear(),this.connectionAttempts.clear(),this.stopConnectionMonitoring()}catch(n){console.error("❌ Error leaving voice room:",n),(t=this.onError)==null||t.call(this,n)}}async toggleMute(){if(!this.localStream)return!1;const t=this.localStream.getAudioTracks()[0];return t?(t.enabled=!t.enabled,this.isMuted=!t.enabled,this.isMuted):!1}setMute(t){if(!this.localStream)return;const n=this.localStream.getAudioTracks()[0];n&&(n.enabled=!t,this.isMuted=t,console.log(`🎤 Local audio ${t?"muted":"unmuted"}`))}setRemoteAudioMuted(t){this.peerConnections.forEach((n,s)=>{const l=n.getRemoteStreams()[0];l&&l.getAudioTracks().forEach(i=>{i.enabled=!t})})}async createPeerConnection(t){const n=new RTCPeerConnection({iceServers:this.iceServers});return this.localStream&&this.localStream.getTracks().forEach(s=>{n.addTrack(s,this.localStream)}),n.ontrack=s=>{var a;const[l]=s.streams;console.log("🎵 Received remote stream from:",t);const o=new Audio;o.srcObject=l,o.volume=.8,o.autoplay=!0,o.play().then(()=>{console.log("✅ Remote audio playing from:",t)}).catch(c=>{console.warn("⚠️ Audio play failed, trying user interaction:",c),document.addEventListener("click",()=>{o.play().catch(()=>{})},{once:!0})});const i=this.remoteUsers.get(t)||{id:t,isSpeaking:!1,isMuted:!1,audioLevel:0};this.remoteUsers.set(t,i),(a=this.onUserJoined)==null||a.call(this,i)},n.onicecandidate=s=>{s.candidate&&this.wsService.send({type:"webrtc_ice_candidate",data:{candidate:s.candidate,targetUserId:t,fromUserId:this.userId}})},n.onconnectionstatechange=()=>{console.log(`🔗 Connection state with ${t}: ${n.connectionState}`),n.connectionState==="connected"?(console.log(`✅ Successfully connected to ${t}`),this.processingOffers.delete(t),this.connectionAttempts.delete(t)):(n.connectionState==="failed"||n.connectionState==="disconnected")&&(console.log(`❌ Connection failed/disconnected with ${t}`),this.peerConnections.delete(t),this.processingOffers.delete(t))},n.oniceconnectionstatechange=()=>{console.log(`🧊 ICE state with ${t}: ${n.iceConnectionState}`),(n.iceConnectionState==="connected"||n.iceConnectionState==="completed")&&console.log(`🎉 ICE connection established with ${t}`)},this.peerConnections.set(t,n),n}async handleOffer(t){try{const{offer:n,fromUserId:s}=t;if(this.processingOffers.has(s)){console.log("⏭️ Already processing offer from:",s);return}const l=this.connectionAttempts.get(s)||0;if(l>=3){console.log("🛑 Too many connection attempts with:",s);return}this.processingOffers.add(s),this.connectionAttempts.set(s,l+1),console.log("📥 Received offer from:",s);const o=this.peerConnections.get(s);if(o&&o.connectionState==="connected"){console.log("✅ Connection already established with:",s),this.processingOffers.delete(s);return}o&&o.signalingState!=="closed"&&(console.log("🔄 Closing existing connection before creating new one"),o.close(),this.peerConnections.delete(s)),console.log("🔄 Creating peer connection and answer for:",s);const i=await this.createPeerConnection(s);try{await i.setRemoteDescription(n),console.log("✅ Set remote description (offer)");const a=await i.createAnswer();await i.setLocalDescription(a),console.log("✅ Created and set local description (answer)"),console.log("📤 Sending WebRTC answer to:",s),this.wsService.send({type:"webrtc_answer",data:{answer:a,targetUserId:s,fromUserId:this.userId}}),setTimeout(()=>{this.processingOffers.delete(s)},2e3)}catch(a){console.error("❌ SDP error in offer handling:",a),i.close(),this.peerConnections.delete(s),this.processingOffers.delete(s)}}catch(n){console.error("❌ Error handling offer:",n),this.processingOffers.delete(t.fromUserId)}}async handleAnswer(t){try{const{answer:n,fromUserId:s}=t;console.log("📥 Received answer from:",s);const l=this.peerConnections.get(s);if(!l){console.warn("⚠️ No peer connection found for:",s);return}if(l.signalingState==="have-local-offer")try{await l.setRemoteDescription(n),console.log("✅ Set remote description (answer) for:",s),console.log("🔗 WebRTC connection should be established with:",s),setTimeout(()=>{l.connectionState==="connected"?console.log("🎉 Connection confirmed with:",s):console.log("⏳ Waiting for connection to stabilize with:",s)},1e3)}catch(o){console.warn("⚠️ SDP error, recreating connection:",o.message),this.peerConnections.delete(s),this.processingOffers.delete(s),setTimeout(()=>{this.userId<s&&this.handleUserJoined({userId:s})},2e3)}else l.signalingState==="stable"?console.log("ℹ️ Connection already stable with:",s):(console.warn("⚠️ Peer connection not in correct state for answer:",l.signalingState),console.log("🔄 Current state:",l.signalingState,"Connection state:",l.connectionState))}catch(n){console.error("❌ Error handling answer:",n)}}async handleIceCandidate(t){try{const{candidate:n,fromUserId:s}=t,l=this.peerConnections.get(s);l&&l.remoteDescription?await l.addIceCandidate(n):l&&setTimeout(()=>this.handleIceCandidate(t),100)}catch(n){n.message.includes("ICE candidate")||console.error("❌ Error handling ICE candidate:",n)}}async handleUserJoined(t){try{const{userId:n}=t;if(n===this.userId)return;const s=this.peerConnections.get(n);if(s&&s.connectionState==="connected"){console.log("✅ Already connected to:",n);return}if(this.processingOffers.has(n)){console.log("⏭️ Already processing connection with:",n);return}if(console.log("👤 User joined voice room:",n),this.userId<n){const o=this.connectionAttempts.get(n)||0;if(o>=3){console.log("🛑 Too many offer attempts to:",n);return}this.connectionAttempts.set(n,o+1),console.log("🔄 Creating peer connection and offer for:",n);const i=await this.createPeerConnection(n),a=await i.createOffer();await i.setLocalDescription(a),console.log("📤 Sending WebRTC offer to:",n),this.wsService.send({type:"webrtc_offer",data:{offer:a,targetUserId:n,fromUserId:this.userId}})}else console.log("⏳ Waiting for offer from:",n),await this.createPeerConnection(n)}catch(n){console.error("❌ Error handling user joined:",n)}}handleUserLeft(t){var l;const{userId:n}=t;console.log("👋 User left voice room:",n);const s=this.peerConnections.get(n);s&&(s.close(),this.peerConnections.delete(n)),this.processingOffers.delete(n),this.connectionAttempts.delete(n),this.remoteUsers.delete(n),(l=this.onUserLeft)==null||l.call(this,n)}startVoiceActivityDetection(){if(!(!this.localStream||this.isMonitoringVoice))try{this.audioContext=new AudioContext;const t=this.audioContext.createMediaStreamSource(this.localStream);this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=256,t.connect(this.analyser),this.isMonitoringVoice=!0,this.monitorVoiceActivity()}catch(t){console.error("❌ Error starting voice activity detection:",t)}}monitorVoiceActivity(){if(!this.analyser||!this.isMonitoringVoice)return;const t=new Uint8Array(this.analyser.frequencyBinCount),n=()=>{if(!this.isMonitoringVoice)return;this.analyser.getByteFrequencyData(t);const s=t.reduce((u,g)=>u+g,0)/t.length,l=Math.round(s*10)/10,o=l>this.voiceActivityThreshold,i=Date.now(),a=o!==this.isSpeaking,c=i-this.lastVoiceActivitySent>this.voiceActivityDebounce;(a||c)&&this.onVoiceActivity&&this.userId&&(this.onVoiceActivity({userId:this.userId,level:l,isSpeaking:o}),this.lastVoiceActivitySent=i,this.isSpeaking=o),requestAnimationFrame(n)};n()}stopVoiceActivityDetection(){this.isMonitoringVoice=!1,this.audioContext&&(this.audioContext.close(),this.audioContext=null),this.analyser=null}get isConnected(){return this.isJoined}get mutedState(){return this.isMuted}get connectedUsers(){return Array.from(this.remoteUsers.values())}async sendOffer(t){try{if(t===this.userId)return;console.log("🔄 Creating peer connection and offer for:",t);const n=await this.createPeerConnection(t),s=await n.createOffer();await n.setLocalDescription(s),console.log("📤 Sending WebRTC offer to:",t),this.wsService.send({type:"webrtc_offer",data:{offer:s,targetUserId:t,fromUserId:this.userId}})}catch(n){console.error("❌ Error sending offer:",n)}}checkConnectionsAndRetry(){this.peerConnections.forEach((t,n)=>{(t.connectionState==="failed"||t.connectionState==="disconnected")&&(console.log("🔄 Retrying connection with:",n),this.peerConnections.delete(n),this.processingOffers.delete(n),setTimeout(()=>{this.userId<n&&this.handleUserJoined({userId:n})},1e3))})}startConnectionMonitoring(){this.connectionMonitorInterval||(this.connectionMonitorInterval=setInterval(()=>{this.checkConnectionsAndRetry()},5e3))}stopConnectionMonitoring(){this.connectionMonitorInterval&&(clearInterval(this.connectionMonitorInterval),this.connectionMonitorInterval=null)}cleanup(){this.stopConnectionMonitoring(),this.leaveRoom().catch(console.error)}}class zp{constructor(){this.commonPhrases=["السلام عليكم","وعليكم السلام","أهلاً وسهلاً","مرحباً بكم","حياكم الله","أهلاً بك","مساء الخير","صباح الخير","تصبحون على خير","ليلة سعيدة","كيف حالكم؟","كيف الأحوال؟","إن شاء الله","ما شاء الله","بارك الله فيك","جزاك الله خيراً","الله يعطيك العافية","تسلم إيدك","الله يبارك فيك","ربي يحفظك","ممتاز!","رائع جداً","أحسنت","بالتوفيق","الله يوفقك","نعم صحيح","أوافقك الرأي","هذا صحيح","بالضبط","تماماً","من أين أنت؟","كم عمرك؟","ما اسمك؟","أين تسكن؟","ما هو عملك؟","هل أنت متزوج؟","كم طفل لديك؟","ما هوايتك؟","مع السلامة","إلى اللقاء","نراكم قريباً","الله معكم","في أمان الله","وداعاً","إلى اللقاء قريباً","هيا نلعب","من يريد اللعب؟","لعبة جميلة","أحب هذه اللعبة","فزت!","خسرت","لعبة أخرى؟","تحدي جديد","لا تحزن","كله خير","الله معك","ستتحسن الأمور","لا تيأس","كن قوياً","أنا معك","سأساعدك","شكراً لك","عفواً","لا شكر على واجب","أعتذر","آسف","لا بأس","لا مشكلة","بالعكس","أنا جائع","ما رأيكم في الطعام؟","هل تناولتم الطعام؟","طعام لذيذ","أحب هذا الطبق","وجبة شهية","الجو جميل اليوم","الطقس حار","الطقس بارد","يبدو أنه سيمطر","الشمس مشرقة","الجو معتدل"],this.recentMessages=[]}addMessage(t){this.recentMessages.unshift(t),this.recentMessages.length>50&&(this.recentMessages=this.recentMessages.slice(0,50))}getSuggestions(t){if(!t||t.length<2)return[];const n=t.toLowerCase().trim(),s=[];return this.commonPhrases.forEach(l=>{l.toLowerCase().includes(n)&&s.push(l)}),this.recentMessages.forEach(l=>{l.toLowerCase().includes(n)&&!s.includes(l)&&l.length>t.length&&s.push(l)}),s.sort((l,o)=>{const i=l.length-o.length;if(i!==0)return i;const a=this.commonPhrases.includes(l),c=this.commonPhrases.includes(o);return a&&!c?-1:!a&&c?1:0}).slice(0,5)}getQuickSuggestions(){return["السلام عليكم","كيف حالكم؟","أهلاً وسهلاً","شكراً لك","مع السلامة"]}addCustomPhrase(t){this.commonPhrases.includes(t)||this.commonPhrases.push(t)}}const Gc=new zp,Op=({onEmojiSelect:e,onClose:t})=>{const n=v.useRef(null),[s,l]=v.useState("faces"),o={faces:{name:"الوجوه",icon:r.jsx(y0,{className:"w-4 h-4"}),emojis:["😀","😃","😄","😁","😆","😅","🤣","😂","🙂","🙃","😉","😊","😇","🥰","😍","🤩","😘","😗","😚","😙","😋","😛","😜","🤪","😝","🤑","🤗","🤭","🤫","🤔","🤐","🤨","😐","😑","😶","😏","😒","🙄","😬","🤥","😔","😪","🤤","😴","😷","🤒","🤕","🤢","🤮","🤧","🥵","🥶","🥴","😵","🤯","🤠","🥳","😎","🤓","🧐"]},hearts:{name:"القلوب",icon:r.jsx(f0,{className:"w-4 h-4"}),emojis:["❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","♥️","💋","💌","💐","🌹","🌺","🌻","🌷","🌸","💒","💍"]},gestures:{name:"الإيماءات",icon:r.jsx(Gh,{className:"w-4 h-4"}),emojis:["👍","👎","👌","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤲","🤝","🙏","✍️","💪","🦾","🦿","🦵","🦶"]},symbols:{name:"الرموز",icon:r.jsx(mn,{className:"w-4 h-4"}),emojis:["💯","💢","💥","💫","💦","💨","🕳️","💣","💬","👁️‍🗨️","🗨️","🗯️","💭","💤","💮","♨️","💈","🛑","⭐","🌟","✨","⚡","🔥","💎","🏆","🥇","🥈","🥉","🎖️","🏅"]},food:{name:"الطعام",icon:r.jsx(Bh,{className:"w-4 h-4"}),emojis:["🍎","🍊","🍋","🍌","🍉","🍇","🍓","🫐","🍈","🍒","🍑","🥭","🍍","🥥","🥝","🍅","🍆","🥑","🥦","🥬","☕","🍵","🧃","🥤","🍺","🍻","🥂","🍷","🥃","🍸"]},games:{name:"الألعاب",icon:r.jsx(ma,{className:"w-4 h-4"}),emojis:["🎮","🕹️","🎯","🎲","🃏","🀄","🎰","🎳","🏓","🏸","⚽","🏀","🏈","⚾","🥎","🎾","🏐","🏉","🥏","🎱"]}};return v.useEffect(()=>{const i=a=>{n.current&&!n.current.contains(a.target)&&t()};return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[t]),r.jsxs("div",{ref:n,onClick:i=>i.stopPropagation(),className:"absolute bottom-12 left-0 right-0 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-2xl z-50 max-w-sm",children:[r.jsxs("div",{className:"flex border-b border-gray-600 p-2 overflow-x-auto",children:[Object.entries(o).map(([i,a])=>r.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),l(i)},className:`p-2 rounded-lg transition-colors flex-shrink-0 ${s===i?"bg-purple-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,title:a.name,children:a.icon},i)),r.jsx("button",{onClick:i=>{i.preventDefault(),i.stopPropagation(),t()},className:"ml-auto p-2 text-gray-400 hover:text-white flex-shrink-0",children:"✕"})]}),r.jsx("div",{className:"p-3 max-h-48 overflow-y-auto",children:r.jsx("div",{className:"grid grid-cols-8 gap-1",children:o[s].emojis.map((i,a)=>r.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),e(i),t()},className:"w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-lg",title:i,children:i},a))})})]})},Up=({suggestions:e,onSuggestionSelect:t,isVisible:n})=>!n||e.length===0?null:r.jsx("div",{className:"absolute bottom-full left-0 right-0 mb-1 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-lg z-40 max-h-32 overflow-y-auto",children:e.map((s,l)=>r.jsx("button",{onClick:()=>t(s),className:"w-full text-left px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0",children:s},l))}),$p=({user:e,wsService:t})=>{var ce;const[n,s]=v.useState(null),[l,o]=v.useState([]),[i,a]=v.useState(!0),[c,u]=v.useState(!1),[g,x]=v.useState(null),[w,N]=v.useState(!1),[j,k]=v.useState(null),[S,m]=v.useState(!1),[d,p]=v.useState(!1),[M,L]=v.useState(!1),[P,z]=v.useState(""),[F,D]=v.useState(!1),[y,O]=v.useState(null),[X,re]=v.useState(null),[He,Ye]=v.useState("30"),[Ie,fe]=v.useState(!1),[h,C]=v.useState(!1),[_,$]=v.useState("#ffffff"),[R,G]=v.useState([]),[Y,ne]=v.useState(!1),xe=[{name:"أبيض",value:"#ffffff"},{name:"أحمر",value:"#ef4444"},{name:"أزرق",value:"#3b82f6"},{name:"أخضر",value:"#10b981"},{name:"أصفر",value:"#f59e0b"},{name:"بنفسجي",value:"#8b5cf6"},{name:"وردي",value:"#ec4899"},{name:"برتقالي",value:"#f97316"},{name:"سماوي",value:"#06b6d4"},{name:"ذهبي",value:"#eab308"}],[he,ot]=v.useState(null),[en,Mn]=v.useState(!1),[cr,zl]=v.useState("prompt"),[tn,nn]=v.useState(!1),ke=v.useRef(null),We=v.useRef(new Map),En=v.useRef(null),Xe=v.useRef(null),In=async(b=!1)=>{try{b?a(!0):u(!0);const[A,W]=await Promise.all([K.getVoiceRoom(),K.getVoiceRoomMessages()]);s(A);const q=W.map(ie=>({...ie,sender:{...ie.sender,role:ie.sender.role||(ie.sender.isAdmin?"admin":"member"),isAdmin:ie.sender.isAdmin||!1,gender:ie.sender.gender||"male"}}));o(q);const B=A.seats.find(ie=>ie.user&&ie.user._id===e.id);B?(console.log("✅ User is in seat:",B.seatNumber),N(!0),k(B.seatNumber),p(B.isMuted)):(console.log("❌ User is not in any seat"),N(!1),k(null));const Pe=A.waitingQueue.some(ie=>ie.user._id===e.id);m(Pe),x(null)}catch(A){if(console.error("Error loading voice room:",A),A.message&&A.message.includes("مطرود من الغرفة الصوتية")){x(A.message),s({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,maxUsers:100,seats:[],waitingQueue:[],listeners:[],settings:{},isActive:!1}),o([]);return}x(A.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1),u(!1)}};v.useEffect(()=>(ke.current=new T0(t),ke.current.onRemoteStreamAdded=(b,A)=>{const W=new Audio;W.srcObject=A,W.autoplay=!0,W.muted=tn,We.current.set(b,W)},ke.current.onRemoteStreamRemoved=b=>{const A=We.current.get(b);A&&(A.pause(),A.srcObject=null,We.current.delete(b))},ke.current.onVoiceActivity=b=>{s(A=>({...A,seats:A.seats.map(W=>{var q;return((q=W.user)==null?void 0:q._id)===e.id?{...W,isSpeaking:b.isSpeaking}:W})})),w&&t.send({type:"voice_activity",data:{userId:e.id,level:b.level,isSpeaking:b.isSpeaking}})},()=>{var b;(b=ke.current)==null||b.cleanup()}),[t,e.id]),v.useEffect(()=>{const b=B=>{const Pe={...B,sender:{...B.sender,role:B.sender.role||(B.sender.isAdmin?"admin":"member"),isAdmin:B.sender.isAdmin||!1,gender:B.sender.gender||"male"}};B.sender._id!==e.id&&(o(ie=>[...ie,Pe]),setTimeout(()=>{var ie;(ie=En.current)==null||ie.scrollIntoView({behavior:"smooth"})},100))},A=B=>{In(!1).then(()=>{B.action==="seat_joined"&&w&&B.userId!==e.id&&setTimeout(()=>{var Pe;(Pe=ke.current)==null||Pe.sendOffer(B.userId)},1e3)})},W=B=>{const{action:Pe,targetUserId:ie,adminId:sn,message:Nt}=B;_n(Pe,ie),ie===e.id&&(x(Nt||`تم تطبيق إجراء إداري: ${Pe}`),Pe==="kick"&&setTimeout(()=>{window.location.reload()},2e3))},q=B=>{const{userId:Pe,isSpeaking:ie}=B;s(sn=>({...sn,seats:sn.seats.map(Nt=>{var Ul;return((Ul=Nt.user)==null?void 0:Ul._id)===Pe?{...Nt,isSpeaking:ie}:Nt})}))};return t.onMessage("voice_room_message",b),t.onMessage("voice_room_update",A),t.onMessage("admin_action_update",W),t.onMessage("voice_activity",q),()=>{t.offMessage("voice_room_message",b),t.offMessage("voice_room_update",A),t.offMessage("admin_action_update",W)}},[t,w,e.id]),v.useEffect(()=>{var b;(b=En.current)==null||b.scrollIntoView({behavior:"smooth"})},[l]),v.useEffect(()=>{In(!0),console.log("User role:",e.role),console.log("User isAdmin:",e.isAdmin),console.log("User object:",e)},[]),v.useEffect(()=>{const b=()=>{const q=window.innerHeight<window.screen.height*.75;D(q)},A=()=>D(!0),W=()=>D(!1);return window.addEventListener("resize",b),Xe.current&&(Xe.current.addEventListener("focus",A),Xe.current.addEventListener("blur",W)),()=>{window.removeEventListener("resize",b),Xe.current&&(Xe.current.removeEventListener("focus",A),Xe.current.removeEventListener("blur",W))}},[]),v.useEffect(()=>{const b=A=>{y&&O(null),Ie&&fe(!1),h&&C(!1)};return document.addEventListener("click",b),()=>document.removeEventListener("click",b)},[y,Ie,h]),v.useEffect(()=>{const b=W=>{if(w)return W.preventDefault(),W.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},A=()=>{w&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",b),window.addEventListener("unload",A),()=>{window.removeEventListener("beforeunload",b),window.removeEventListener("unload",A)}},[w,e.id]);const ht=async b=>{try{const A=await K.sendVoiceRoomMessage(b),W={_id:A.messageData._id,sender:{_id:e.id,username:e.username,role:e.role,isAdmin:e.isAdmin,gender:e.gender},content:b,timestamp:new Date().toISOString(),messageType:"text",textColor:_};o(q=>[...q,W]),t.send({type:"voice_room_message",data:{...A.messageData,textColor:_}})}catch(A){console.error("Error sending message:",A),x(A.message||"خطأ في إرسال الرسالة")}},is=async()=>{try{L(!0),await K.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}})}catch(b){console.error("Error requesting mic:",b),x(b.message||"خطأ في طلب المايك")}finally{L(!1)}},An=async b=>{try{if(L(!0),await K.joinVoiceSeat(b),N(!0),k(b),p(!1),ke.current&&(e!=null&&e.id)){console.log("🎤 Starting WebRTC voice chat for user:",e.username);const A=`voice-room-${(n==null?void 0:n.id)||"default"}`;await ke.current.joinRoom(A,e.id),console.log("✅ WebRTC voice chat started successfully")}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",b.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:b}})}catch(A){console.error("Error joining seat:",A),x(A.message||"خطأ في الانضمام للمقعد")}finally{L(!1)}},as=async()=>{var b;try{await K.leaveVoiceSeat(),N(!1),k(null),p(!1),ke.current&&ke.current.leaveRoom(),localStorage.removeItem("isInVoiceRoom"),localStorage.removeItem("voiceRoomSeat"),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:j}})}catch(A){console.error("Error leaving seat:",A),(b=A.message)!=null&&b.includes("لست في أي مقعد")||x(A.message||"خطأ في مغادرة المقعد")}},pe=async()=>{try{if(!w){x("يجب أن تكون في مقعد لاستخدام المايك");return}if(!ke.current){x("خدمة الصوت غير متاحة - جاري إعادة الاتصال..."),await initializeWebRTC();return}const b=!d;ke.current.setMute(b),p(b);try{await K.toggleMute(b)}catch(A){console.warn("Failed to update server mute state:",A)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:b}})}catch(b){console.error("Error toggling mute:",b),x("خطأ في تبديل كتم المايك"),p(!d)}},pt=()=>{try{const b=!tn;nn(b),We.current.forEach(A=>{A.muted=b}),ke.current&&ke.current.setRemoteAudioMuted(b),localStorage.setItem("soundMuted",b.toString())}catch(b){console.error("Error toggling sound:",b),x("خطأ في تبديل كتم الصوت")}},rn=b=>{const A=b.target.value;if(z(A),A.length>=2){const W=Gc.getSuggestions(A);G(W),ne(W.length>0)}else ne(!1)},cs=b=>{var A;z(b),ne(!1),(A=Xe.current)==null||A.focus()},ds=b=>{var A;z(W=>W+b),fe(!1),(A=Xe.current)==null||A.focus()},us=async b=>{if(b.preventDefault(),!P.trim())return;const A=P.trim();z(""),ne(!1),Gc.addMessage(A);try{await ht(A)}catch{z(A)}},Se=async(b,A,W)=>{try{let q;switch(b){case"kick":q=await K.kickUserFromVoiceRoom(A,W);break;case"mute":q=await K.muteUserInVoiceRoom(A);break;case"unmute":q=await K.unmuteUserInVoiceRoom(A);break;case"removeSeat":q=await K.removeUserFromSeat(A);break;case"removeQueue":q=await K.removeUserFromQueue(A);break;case"banChat":q=await K.banUserFromChat(A);break;case"unbanChat":q=await K.unbanUserFromChat(A);break}O(null),re(null),_n(b,A),t.send({type:"admin_action_update",data:{action:b,targetUserId:A,adminId:e.id,duration:W,message:q==null?void 0:q.message}})}catch(q){console.error("Error performing admin action:",q),x(q.message||"خطأ في تنفيذ الإجراء الإداري")}},_n=(b,A)=>{s(W=>{if(!W)return W;const q={...W};switch(b){case"kick":case"removeSeat":q.seats=q.seats.map(B=>B.user&&B.user._id===A?{...B,user:null,isMuted:!1}:B),q.waitingQueue=q.waitingQueue.filter(B=>B.user._id!==A);break;case"removeQueue":q.waitingQueue=q.waitingQueue.filter(B=>B.user._id!==A);break;case"mute":q.seats=q.seats.map(B=>B.user&&B.user._id===A?{...B,isMuted:!0}:B);break;case"unmute":q.seats=q.seats.map(B=>B.user&&B.user._id===A?{...B,isMuted:!1}:B);break;case"banChat":q.seats=q.seats.map(B=>B.user&&B.user._id===A?{...B,user:{...B.user,isChatBanned:!0}}:B),q.waitingQueue=q.waitingQueue.map(B=>B.user._id===A?{...B,user:{...B.user,isChatBanned:!0}}:B);break;case"unbanChat":q.seats=q.seats.map(B=>B.user&&B.user._id===A?{...B,user:{...B.user,isChatBanned:!1}}:B),q.waitingQueue=q.waitingQueue.map(B=>B.user._id===A?{...B,user:{...B.user,isChatBanned:!1}}:B);break}return q})},Ol=async()=>{const b=parseInt(He);await Se("kick",X,b)},jt=b=>{ot(b),Mn(!0)},Tn=()=>{ot(null),Mn(!1)};v.useEffect(()=>{const b=A=>{A.key==="Escape"&&en&&Tn()};return document.addEventListener("keydown",b),()=>{document.removeEventListener("keydown",b)}},[en]),v.useEffect(()=>{(async()=>{try{const W=await navigator.permissions.query({name:"microphone"});zl(W.state),W.addEventListener("change",()=>{zl(W.state)})}catch{console.log("Permission API not supported")}})(),localStorage.getItem("soundMuted")==="true"&&nn(!0)},[]);const ms=b=>new Date(b).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1});if(i)return r.jsx("div",{className:"flex items-center justify-center h-full",children:r.jsxs("div",{className:"text-center",children:[r.jsx(xn,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),r.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})});if(g){const b=g.includes("مطرود من الغرفة الصوتية");return r.jsx("div",{className:"p-4",children:r.jsxs("div",{className:`border rounded-lg p-6 text-center max-w-md mx-auto ${b?"bg-orange-900/20 border-orange-500/30":"bg-red-900/20 border-red-500/30"}`,children:[r.jsx(u0,{className:`w-12 h-12 mx-auto mb-4 ${b?"text-orange-400":"text-red-400"}`}),b?r.jsxs(r.Fragment,{children:[r.jsx("h3",{className:"text-orange-400 font-bold text-lg mb-2",children:"تم طردك من الغرفة الصوتية"}),r.jsx("p",{className:"text-orange-300 mb-4 text-sm leading-relaxed",children:g}),r.jsx("p",{className:"text-gray-400 text-xs",children:"سيتم السماح لك بالدخول مرة أخرى بعد انتهاء المدة المحددة"})]}):r.jsxs(r.Fragment,{children:[r.jsx("p",{className:"text-red-400 mb-4",children:g}),r.jsx("button",{onClick:In,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white",children:"إعادة المحاولة"})]})]})})}return n?r.jsxs("div",{className:"h-screen flex flex-col bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white overflow-hidden",children:[r.jsxs("div",{className:"bg-black/30 backdrop-blur-sm border-b border-white/10 p-2 flex-shrink-0",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:onBack,className:"p-1.5 hover:bg-white/10 rounded-lg transition-colors",children:r.jsx(ArrowLeft,{className:"w-4 h-4"})}),r.jsxs("div",{children:[r.jsxs("h1",{className:"text-sm font-bold flex items-center gap-1",children:[r.jsx(ts,{className:"w-3 h-3 text-purple-400"}),"INFINITY ROOM"]}),r.jsx("p",{className:"text-xs text-gray-300",children:"غرفة صوتية للمحادثة"})]})]}),r.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[c&&r.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-1"}),r.jsx(kn,{className:"w-3 h-3 text-gray-400"}),r.jsxs("span",{className:"text-gray-300",children:[((ce=n.seats)==null?void 0:ce.filter(b=>b.user).length)||0,"/5"]}),(e.role==="admin"||e.isAdmin)&&r.jsx("span",{className:"bg-red-600 text-white px-1.5 py-0.5 rounded text-xs ml-1",children:"ADMIN"})]})]}),w&&r.jsxs("div",{className:"bg-black/20 backdrop-blur-sm rounded-lg p-2 mt-2",children:[r.jsxs("div",{className:"flex items-center gap-1 mb-2",children:[r.jsx("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"}),r.jsxs("span",{className:"text-xs text-green-400 font-medium",children:["مقعد ",j]})]}),r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsxs("button",{onClick:pe,className:`flex-1 py-1.5 px-2 rounded-md transition-colors flex items-center justify-center gap-1 text-xs font-medium ${d?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,children:[d?r.jsx(Xr,{className:"w-3 h-3"}):r.jsx(vt,{className:"w-3 h-3"}),r.jsx("span",{children:d?"إلغاء كتم":"كتم"})]}),r.jsxs("button",{onClick:pt,className:`flex-1 py-1.5 px-2 rounded-md transition-colors flex items-center justify-center gap-1 text-xs font-medium ${tn?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:[tn?r.jsx(fo,{className:"w-3 h-3"}):r.jsx(ts,{className:"w-3 h-3"}),r.jsx("span",{children:tn?"تشغيل":"صامت"})]}),r.jsx("button",{onClick:as,className:"px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded-md text-white transition-colors text-xs font-medium",children:"مغادرة"})]})]}),r.jsxs("div",{className:"flex items-center gap-3 p-2 bg-gray-800/30 rounded-lg mb-3",children:[r.jsx("div",{className:"flex items-center gap-2",children:r.jsx("div",{className:`w-2 h-2 rounded-full ${cr==="granted"?"bg-green-500":cr==="denied"?"bg-red-500":"bg-yellow-500"}`})}),cr==="denied"&&r.jsx("span",{className:"text-xs text-red-400",children:"مرفوض"})]})]}),r.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[r.jsxs("div",{className:"p-2 border-b border-gray-700/50 flex-shrink-0",children:[r.jsx("div",{className:"flex justify-center gap-1.5 mb-1 overflow-x-auto px-1",children:n.seats.map(b=>r.jsx("div",{className:"flex flex-col items-center flex-shrink-0",children:b.user?r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:`relative w-12 h-12 rounded-full p-0.5 ${b.isSpeaking&&!b.isMuted?"bg-gradient-to-r from-green-400 to-green-500 shadow-md shadow-green-500/50 animate-pulse":b.user._id===e.id?"bg-gradient-to-r from-green-500 to-green-600":b.user.role==="admin"||b.user.isAdmin?"bg-gradient-to-r from-red-500 to-red-600 shadow-md shadow-red-500/50":"bg-gradient-to-r from-blue-500 to-purple-600"} shadow-md`,children:[r.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:b.user.profileImage?r.jsx("img",{src:b.user.profileImage,alt:b.user.username,className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-lg",children:b.user.username.charAt(0).toUpperCase()})})}),r.jsx("div",{className:`absolute -bottom-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center shadow-lg ${b.isMuted?"bg-red-600":b.isSpeaking?"bg-green-500 animate-pulse shadow-green-500/50":"bg-green-600"}`,children:b.isMuted?r.jsx(Xr,{className:"w-3 h-3 text-white"}):r.jsx(vt,{className:`w-3 h-3 text-white ${b.isSpeaking?"animate-pulse":""}`})}),b.isSpeaking&&!b.isMuted&&r.jsxs("div",{className:"absolute inset-0 rounded-full",children:[r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),r.jsxs("div",{className:"text-center mt-1 relative",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1",children:[r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("h3",{className:"font-medium text-white text-xs mb-1 truncate max-w-12",children:b.user.username}),r.jsxs("div",{className:"flex gap-1",children:[b.isMuted&&r.jsx("div",{className:"bg-red-600 rounded-full p-1",title:"مكتوم",children:r.jsx(fo,{className:"w-2 h-2 text-white"})}),b.user.isChatBanned&&r.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:r.jsx(Ts,{className:"w-2 h-2 text-white"})})]})]}),(e.role==="admin"||e.isAdmin)&&b.user._id!==e.id&&r.jsx("button",{onClick:A=>{A.stopPropagation(),console.log("Admin menu clicked for user:",b.user.username),O(y===b.user._id?null:b.user._id)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:r.jsx(Oc,{className:"w-4 h-4"})})]}),y===b.user._id&&(e.role==="admin"||e.isAdmin)&&r.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:r.jsxs("div",{className:"flex flex-col gap-1",children:[r.jsxs("button",{onClick:()=>Se("removeSeat",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[r.jsx(Ph,{className:"w-3 h-3"}),"إنزال"]}),b.isMuted?r.jsxs("button",{onClick:()=>Se("unmute",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Zh,{className:"w-3 h-3"}),"إلغاء كتم"]}):r.jsxs("button",{onClick:()=>Se("mute",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-yellow-400 hover:bg-yellow-900/30 rounded transition-colors",children:[r.jsx(fo,{className:"w-3 h-3"}),"كتم"]}),b.user.isChatBanned?r.jsxs("button",{onClick:()=>Se("unbanChat",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Uc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):r.jsxs("button",{onClick:()=>Se("banChat",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[r.jsx(Ts,{className:"w-3 h-3"}),"منع الكتابة"]}),r.jsxs("button",{onClick:()=>{O(null),re(b.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[r.jsx(gi,{className:"w-3 h-3"}),"طرد"]})]})})]})]}):r.jsx("div",{className:"flex flex-col items-center",children:r.jsx("button",{onClick:()=>!w&&!S?An(b.seatNumber):null,disabled:M||w||S,className:"relative w-14 h-14 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:r.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center",children:r.jsx(Rl,{className:"w-5 h-5 text-gray-400"})})})})},b.seatNumber))}),!w&&!S&&n.seats.every(b=>b.user)&&r.jsxs("button",{onClick:is,disabled:M,className:"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded-lg text-white font-medium transition-colors flex items-center justify-center gap-2 mb-4",children:[r.jsx(vt,{className:"w-4 h-4"}),M?"جاري الطلب...":"طلب المايك"]}),n.waitingQueue.length>0&&r.jsxs("div",{className:"bg-yellow-900/20 rounded-lg p-2 border border-yellow-500/20",children:[r.jsxs("h3",{className:"text-sm font-bold text-white mb-2 flex items-center gap-2",children:[r.jsx(pn,{className:"w-4 h-4 text-yellow-400"}),"قائمة الانتظار (",n.waitingQueue.length,")"]}),r.jsx("div",{className:"space-y-2",children:n.waitingQueue.map((b,A)=>r.jsxs("div",{className:"flex items-center gap-2 p-2 bg-gray-800/50 rounded text-sm relative",children:[r.jsx("div",{className:"w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-xs",children:A+1}),r.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[r.jsx("span",{className:"text-white",children:b.user.username}),r.jsx("div",{className:"flex gap-1",children:b.user.isChatBanned&&r.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:r.jsx(Ts,{className:"w-2 h-2 text-white"})})})]}),b.user._id===e.id&&r.jsx("span",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"}),(e.role==="admin"||e.isAdmin)&&b.user._id!==e.id&&r.jsx("button",{onClick:W=>{W.stopPropagation(),console.log("Admin menu clicked for queue user:",b.user.username),O(y===`queue_${b.user._id}`?null:`queue_${b.user._id}`)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:r.jsx(Oc,{className:"w-4 h-4"})}),y===`queue_${b.user._id}`&&(e.role==="admin"||e.isAdmin)&&r.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:r.jsxs("div",{className:"flex flex-col gap-1",children:[r.jsxs("button",{onClick:()=>Se("removeQueue",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[r.jsx(w0,{className:"w-3 h-3"}),"إزالة"]}),b.user.isChatBanned?r.jsxs("button",{onClick:()=>Se("unbanChat",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Uc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):r.jsxs("button",{onClick:()=>Se("banChat",b.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[r.jsx(Ts,{className:"w-3 h-3"}),"منع الكتابة"]}),r.jsxs("button",{onClick:()=>{O(null),re(b.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[r.jsx(gi,{className:"w-3 h-3"}),"طرد"]})]})})]},b.user._id))})]})]}),r.jsxs("div",{className:"h-96 flex flex-col",children:[r.jsx("div",{className:"flex-1 overflow-y-auto p-2",children:r.jsxs("div",{className:"flex flex-col space-y-1",children:[l.length===0?r.jsxs("div",{className:"text-center text-gray-400 py-4",children:[r.jsx(gn,{className:"w-6 h-6 mx-auto mb-2 opacity-50"}),r.jsx("p",{className:"text-xs",children:"لا توجد رسائل بعد"})]}):l.map(b=>{const A=q=>q.gender==="female"?"text-pink-400":q.gender==="male"?"text-blue-400":["فاطمة","عائشة","خديجة","زينب","مريم","سارة","نور","هند","ليلى","أمل","رنا","دانا","لينا","ريم","نادية","سلمى","ياسمين","روان","جنى","تالا"].some(ie=>q.username.includes(ie))?"text-pink-400":"text-blue-400",W=()=>b.messageType==="system"?"bg-blue-900/30 border border-blue-500/30 text-blue-200 ml-auto max-w-[85%]":b.sender.role==="admin"||b.sender.isAdmin?"bg-red-900/60 border-2 border-red-400/50 text-white ml-auto max-w-[75%] shadow-xl shadow-red-500/30 ring-1 ring-red-400/20":b.sender._id===e.id?"bg-purple-900/50 border border-purple-500/30 text-white ml-auto max-w-[75%]":"bg-gray-800/50 border border-gray-600/30 text-gray-200 ml-auto max-w-[75%]";return r.jsxs("div",{className:`px-3 py-1.5 rounded-xl text-sm ${W()}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-0.5 gap-2",children:[r.jsxs("span",{className:`font-medium text-xs flex-shrink-0 ${b.sender.role==="admin"||b.sender.isAdmin?"text-red-300 font-bold":A(b.sender)}`,children:[b.sender.role==="admin"||b.sender.isAdmin?"👑 ":"",b.sender.username]}),r.jsx("span",{className:"text-xs opacity-60 flex-shrink-0",children:ms(b.timestamp)})]}),r.jsx("div",{className:"text-sm leading-snug",style:{color:b.textColor||"#ffffff"},children:b.content})]},b._id)}),r.jsx("div",{ref:En})]})}),r.jsx("div",{className:"px-4 py-2 border-t border-gray-700/50 bg-gray-900/30",children:r.jsxs("div",{className:"flex justify-center gap-4",children:[r.jsxs("button",{onClick:()=>jt("/speed-challenge.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"تحدي السرعة",children:[r.jsx(j0,{className:"w-6 h-6 text-yellow-400 group-hover:text-yellow-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"سرعة"})]}),r.jsxs("button",{onClick:()=>jt("/game8.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"صناديق الحظ",children:[r.jsx(v0,{className:"w-6 h-6 text-green-400 group-hover:text-green-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"صناديق"})]}),r.jsxs("button",{onClick:()=>jt("/mind-puzzles.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"ألغاز العقل",children:[r.jsx(x0,{className:"w-6 h-6 text-blue-400 group-hover:text-blue-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ألغاز"})]}),r.jsxs("button",{onClick:()=>jt("/fruit-catching.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"قطف الفواكه",children:[r.jsx(a0,{className:"w-6 h-6 text-red-400 group-hover:text-red-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"فواكه"})]}),r.jsxs("button",{onClick:()=>jt("/memory-match.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الذاكرة",children:[r.jsx(c0,{className:"w-6 h-6 text-purple-400 group-hover:text-purple-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ذاكرة"})]}),r.jsxs("button",{onClick:()=>jt("/forest-game.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الغابة",children:[r.jsx(b0,{className:"w-6 h-6 text-green-600 group-hover:text-green-500"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"غابة"})]})]})}),r.jsx("div",{className:"p-4 mb-4 border-t border-gray-700/50 bg-gray-900/50",children:r.jsxs("div",{className:"relative",children:[r.jsx(Up,{suggestions:R,onSuggestionSelect:cs,isVisible:Y}),Ie&&r.jsx(Op,{onEmojiSelect:ds,onClose:()=>fe(!1)}),h&&r.jsxs("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-gray-800/95 backdrop-blur-sm border border-gray-600/50 rounded-lg p-3 shadow-xl",children:[r.jsx("div",{className:"text-xs text-gray-300 mb-2 font-medium",children:"اختر لون النص:"}),r.jsx("div",{className:"grid grid-cols-5 gap-2",children:xe.map(b=>r.jsx("button",{type:"button",onClick:A=>{A.preventDefault(),A.stopPropagation(),$(b.value),C(!1)},className:`w-8 h-8 rounded-full border-2 transition-all hover:scale-110 ${_===b.value?"border-white shadow-lg":"border-gray-500 hover:border-gray-300"}`,style:{backgroundColor:b.value},title:b.name,children:_===b.value&&r.jsx("div",{className:"w-full h-full rounded-full flex items-center justify-center",children:r.jsx("div",{className:"w-2 h-2 bg-black rounded-full opacity-50"})})},b.value))})]}),r.jsxs("form",{onSubmit:us,className:"flex gap-2",children:[r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("input",{ref:Xe,type:"text",value:P,onChange:rn,placeholder:"اكتب رسالتك...",maxLength:500,style:{color:_},className:"w-full px-3 py-2 pr-16 bg-gray-800/50 border border-gray-600/50 rounded-lg placeholder-gray-400 focus:outline-none focus:border-purple-500/50 text-sm",onFocus:()=>ne(R.length>0),onBlur:()=>setTimeout(()=>ne(!1),200)}),r.jsx("button",{type:"button",onClick:b=>{b.preventDefault(),b.stopPropagation(),C(!h),fe(!1)},className:"absolute left-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-400 transition-colors",children:r.jsx("div",{className:"w-4 h-4 rounded-full border-2 border-gray-400",style:{backgroundColor:_}})}),r.jsx("button",{type:"button",onClick:b=>{b.preventDefault(),b.stopPropagation(),fe(!Ie),C(!1)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-yellow-400 transition-colors",children:r.jsx(y0,{className:"w-4 h-4"})})]}),r.jsx("button",{type:"submit",disabled:!P.trim(),className:"px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-lg text-white transition-colors",children:r.jsx(Pr,{className:"w-4 h-4"})}),!S&&r.jsx("button",{type:"button",onClick:is,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors",children:r.jsx(vt,{className:"w-4 h-4"})})]})]})})]})]}),X&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg border border-red-500 p-6 w-full max-w-sm",children:[r.jsx("h3",{className:"text-white font-bold text-lg mb-4 text-center",children:"اختر مدة الطرد"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-gray-300 text-sm mb-2",children:"المدة:"}),r.jsxs("select",{value:He,onChange:b=>Ye(b.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500",children:[r.jsx("option",{value:"30",children:"30 دقيقة"}),r.jsx("option",{value:"60",children:"ساعة واحدة"}),r.jsx("option",{value:"180",children:"3 ساعات"}),r.jsx("option",{value:"360",children:"6 ساعات"}),r.jsx("option",{value:"720",children:"12 ساعة"}),r.jsx("option",{value:"1440",children:"يوم واحد"}),r.jsx("option",{value:"4320",children:"3 أيام"}),r.jsx("option",{value:"10080",children:"أسبوع واحد"}),r.jsx("option",{value:"43200",children:"شهر واحد"}),r.jsx("option",{value:"525600",children:"سنة واحدة"})]})]}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>re(null),className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"إلغاء"}),r.jsx("button",{onClick:Ol,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors",children:"طرد"})]})]})]})}),en&&he&&r.jsxs("div",{className:"absolute inset-0 bg-gray-900/95 backdrop-blur-sm z-50 flex flex-col",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-800/90 border-b border-gray-700",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),r.jsx("span",{className:"text-white font-medium",children:"اللعبة نشطة"})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:()=>{he&&window.open(he,"_blank")},className:"px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors",children:"فتح في نافذة جديدة"}),r.jsx("button",{onClick:Tn,className:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",title:"إغلاق اللعبة",children:r.jsx(fa,{className:"w-5 h-5"})})]})]}),r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("iframe",{src:he,className:"w-full h-full border-0",title:"لعبة مدمجة",allow:"fullscreen; autoplay; microphone; camera",sandbox:"allow-scripts allow-same-origin allow-forms allow-popups allow-modals"}),r.jsx("div",{className:"absolute inset-0 bg-gray-900 flex items-center justify-center pointer-events-none opacity-0 transition-opacity duration-300",id:"game-loading",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-white",children:"جاري تحميل اللعبة..."})]})})]}),r.jsx("div",{className:"p-3 bg-gray-800/90 border-t border-gray-700",children:r.jsxs("div",{className:"flex items-center justify-between text-sm",children:[r.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[r.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),r.jsx("span",{children:"يمكنك الاستمرار في الدردشة أثناء اللعب"})]}),r.jsx("div",{className:"text-gray-500",children:"اضغط ESC للخروج السريع"})]})})]})]}):r.jsx("div",{className:"p-4 text-center text-gray-400",children:r.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},Fp=({userData:e,onLogout:t,onUpdateProfile:n,wsService:s})=>{const[l,o]=v.useState("games"),[i,a]=v.useState(e),[c,u]=v.useState(["games"]);v.useEffect(()=>{e&&(console.log("🔄 MobileDashboard: userData updated from parent:",e),a(e))},[e]);const g=S=>{console.log("🔄 MobileDashboard: Updating profile data:",S);const m={...i,...S};a(m),n&&n(m)};v.useEffect(()=>{a(e)},[e]);const x=S=>{console.log("🔄 Navigating to tab:",S),S!==l&&requestAnimationFrame(()=>{u(m=>[...m,S]),o(S),console.log("✅ Tab changed to:",S)})},w=()=>{if(c.length>1){const S=[...c];S.pop();const m=S[S.length-1];u(S),o(m)}},N=c.length>1,j=()=>{switch(l){case"games":return"مركز الألعاب";case"leaderboard":return"لوحة المتصدرين";case"voice":return"الغرفة الصوتية";case"profile":return"الملف الشخصي";case"admin":return"لوحة الإدارة";default:return"INFINITY BOX"}},k=()=>{switch(l){case"games":return r.jsx(Qs,{setActiveTab:x});case"leaderboard":return r.jsx(Bp,{});case"voice":return s?r.jsx($p,{user:i,wsService:s}):r.jsx("div",{className:"p-4 text-center text-red-400",children:"خدمة WebSocket غير متاحة"});case"profile":return r.jsx(Rp,{userData:i,onUpdateProfile:g,onLogout:t,isOwner:!0});case"admin":return e!=null&&e.isAdmin?r.jsx(xi,{userData:e,onLogout:t}):r.jsx(Qs,{setActiveTab:x});default:return r.jsx(Qs,{setActiveTab:x})}};return l==="admin"&&(e!=null&&e.isAdmin)?r.jsx(xi,{userData:e,onLogout:t}):r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-950 via-purple-950 to-slate-900 text-white flex flex-col relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute -top-24 -left-24 w-72 h-72 bg-green-800 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"}),r.jsx("div",{className:"absolute -bottom-24 -right-24 w-72 h-72 bg-red-900 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"}),r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((S,m)=>r.jsx("div",{className:`${m%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${m*150}ms`,animationDuration:`${2+m%3}s`}},m))})}),r.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-20",children:r.jsx("div",{className:"text-[18rem] md:text-[24rem] font-black text-cyan-500 animate-spin",style:{animationDuration:"40s"},children:"∞"})})]}),r.jsxs("div",{className:"relative z-10 flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-b border-purple-400/30 sticky top-0 z-40",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[N&&r.jsx("button",{onClick:w,className:"p-2 rounded-lg hover:bg-slate-700/50 transition-colors",children:r.jsx(d0,{className:"w-5 h-5 text-white"})}),r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:(i==null?void 0:i.profileImage)||"/images/default-avatar.png",alt:i==null?void 0:i.username,className:"w-9 h-9 rounded-full border-2 border-white/30 object-cover",onError:S=>{S.currentTarget.src="/images/default-avatar.png"}}),r.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),r.jsx("div",{children:r.jsx("h1",{className:"text-lg font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:l==="games"?(i==null?void 0:i.username)||"اللاعب":j()})})]}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[r.jsx(Ll,{className:"w-5 h-5 text-yellow-300"}),r.jsx("span",{className:"text-yellow-300 text-sm font-bold",children:(i==null?void 0:i.goldCoins)||0})]}),r.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[r.jsx(mn,{className:"w-5 h-5 text-emerald-300"}),r.jsx("span",{className:"text-emerald-300 text-sm font-bold",children:(i==null?void 0:i.pearls)||0})]}),r.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-red-600/20 transition-colors",children:r.jsx(p0,{className:"w-5 h-5 text-red-400"})})]})]}),r.jsx("div",{className:"relative z-10 flex-1 overflow-hidden",children:k()}),r.jsx("div",{className:"relative z-10 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-t border-purple-400/30 px-2 py-2 sticky bottom-0 z-40",children:r.jsxs("div",{className:"flex items-center justify-around",children:[r.jsxs("button",{onClick:()=>x("games"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="games"?"bg-cyan-700/40 text-cyan-300":"text-gray-400 hover:text-cyan-200 hover:bg-slate-700/50"}`,children:[r.jsx(ma,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الألعاب"})]}),r.jsxs("button",{onClick:()=>x("voice"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="voice"?"bg-purple-700/40 text-purple-300":"text-gray-400 hover:text-purple-200 hover:bg-slate-700/50"}`,children:[r.jsx(ts,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الغرفة الصوتية"})]}),r.jsxs("button",{onClick:()=>x("leaderboard"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="leaderboard"?"bg-amber-700/40 text-amber-300":"text-gray-400 hover:text-amber-200 hover:bg-slate-700/50"}`,children:[r.jsx(es,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"المتصدرين"})]}),r.jsxs("button",{onClick:()=>x("profile"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="profile"?"bg-emerald-700/40 text-emerald-300":"text-gray-400 hover:text-emerald-200 hover:bg-slate-700/50"}`,children:[r.jsx(yn,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الملف الشخصي"})]}),(e==null?void 0:e.isAdmin)&&r.jsxs("button",{onClick:()=>x("admin"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="admin"?"bg-rose-700/40 text-rose-300":"text-gray-400 hover:text-rose-200 hover:bg-slate-700/50"}`,children:[r.jsx(Tr,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الإدارة"})]})]})})]})},Bp=()=>r.jsx("div",{className:"p-4 h-full overflow-y-auto",children:r.jsxs("div",{className:"text-center py-20",children:[r.jsx(es,{className:"w-16 h-16 text-yellow-400 mx-auto mb-4"}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"لوحة المتصدرين"}),r.jsx("p",{className:"text-gray-400",children:"قريباً..."})]})}),Vp=({seats:e,waitingQueue:t,currentUser:n,isInSeat:s,currentSeatNumber:l,isInWaitingQueue:o,isConnecting:i,onJoinSeat:a,onRequestMic:c,onCancelMicRequest:u})=>{const g=j=>{const k=new Date,S=new Date(j),m=k.getTime()-S.getTime(),d=Math.floor(m/(1e3*60));return d<1?"الآن":d<60?`${d} دقيقة`:`${Math.floor(d/60)} ساعة`},x=()=>{const j=t.findIndex(k=>k.user._id===n.id);return j>=0?j+1:0},w=e.filter(j=>!j.user),N=w.length>0&&!s&&!o;return r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-purple-900/30 rounded-xl p-6 border border-purple-500/20",children:[r.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(ts,{className:"w-6 h-6 text-purple-400"}),"المقاعد الصوتية"]}),r.jsx("div",{className:"flex flex-wrap justify-center gap-6",children:e.map(j=>r.jsx("div",{className:"flex flex-col items-center",children:j.user?r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:`relative w-20 h-20 rounded-full p-1 transition-all duration-300 shadow-lg ${j.user._id===n.id?"bg-gradient-to-r from-green-500 to-green-600 shadow-green-500/30":"bg-gradient-to-r from-blue-500 to-purple-600 shadow-blue-500/30"}`,children:[r.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:j.user.profileImage?r.jsx("img",{src:j.user.profileImage,alt:j.user.username,className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-xl",children:j.user.username.charAt(0).toUpperCase()})})}),r.jsx("div",{className:`absolute -bottom-1 -right-1 w-7 h-7 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${j.isMuted?"bg-red-600":j.isSpeaking?"bg-green-600 animate-pulse shadow-green-500/50":"bg-gray-600"}`,children:j.isMuted?r.jsx(Xr,{className:"w-4 h-4 text-white"}):r.jsx(vt,{className:"w-4 h-4 text-white"})}),j.isSpeaking&&!j.isMuted&&r.jsxs("div",{className:"absolute inset-0 rounded-full",children:[r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),r.jsxs("div",{className:"text-center mt-3 max-w-20",children:[r.jsx("h3",{className:"font-semibold text-white text-sm mb-1 truncate",children:j.user.username}),r.jsxs("p",{className:"text-xs text-gray-400 mb-2",children:["#",j.user.playerId]}),j.joinedAt&&r.jsxs("div",{className:"flex items-center justify-center gap-1 text-xs text-gray-400",children:[r.jsx(pn,{className:"w-3 h-3"}),r.jsx("span",{children:g(j.joinedAt)})]})]})]}):r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("button",{onClick:()=>N?a(j.seatNumber):null,disabled:i||!N,className:"relative w-20 h-20 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:r.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center hover:border-purple-400 transition-colors",children:r.jsx(Rl,{className:"w-8 h-8 text-gray-400"})})}),r.jsx("div",{className:"text-center mt-3",children:r.jsx("p",{className:"text-gray-400 text-sm",children:"مقعد فارغ"})})]})},j.seatNumber))}),r.jsxs("div",{className:"mt-6 flex flex-wrap gap-3 justify-center",children:[!s&&!o&&w.length===0&&r.jsxs("button",{onClick:c,disabled:i,className:"px-6 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white font-medium transition-colors flex items-center gap-2",children:[r.jsx(vt,{className:"w-4 h-4"}),i?"جاري الطلب...":"طلب المايك"]}),o&&r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"px-4 py-2 bg-yellow-900/50 border border-yellow-500/50 rounded-lg text-yellow-300 flex items-center gap-2",children:[r.jsx(pn,{className:"w-4 h-4"}),r.jsxs("span",{children:["في قائمة الانتظار (المركز ",x(),")"]})]}),r.jsx("button",{onClick:u,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors",children:"إلغاء الطلب"})]})]})]}),t.length>0&&r.jsxs("div",{className:"bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-xl p-6 border border-yellow-500/20",children:[r.jsxs("h3",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(pn,{className:"w-5 h-5 text-yellow-400"}),"قائمة انتظار المايك (",t.length,")"]}),r.jsx("div",{className:"space-y-3",children:t.map((j,k)=>r.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",children:[r.jsx("div",{className:"w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:k+1}),r.jsxs("div",{className:"flex-1",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"font-medium text-white",children:j.user.username}),r.jsxs("span",{className:"text-xs text-gray-400",children:["#",j.user.playerId]})]}),r.jsxs("div",{className:"text-xs text-gray-400",children:["طلب منذ ",g(j.requestedAt)]})]}),j.user._id===n.id&&r.jsx("div",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"})]},j.user._id))})]})]})},qp=({messages:e,currentUser:t,isInWaitingQueue:n,onSendMessage:s,onRequestMic:l})=>{const[o,i]=v.useState(""),[a,c]=v.useState(!1),u=v.useRef(null),g=v.useRef(null);v.useEffect(()=>{var S;(S=u.current)==null||S.scrollIntoView({behavior:"smooth"})},[e]),v.useEffect(()=>{var S;(S=g.current)==null||S.focus()},[]);const x=async S=>{var d;if(S.preventDefault(),!o.trim()||a)return;const m=o.trim();i(""),c(!0);try{await s(m)}catch(p){console.error("Error sending message:",p),i(m)}finally{c(!1),(d=g.current)==null||d.focus()}},w=S=>{S.key==="Enter"&&!S.shiftKey&&(S.preventDefault(),x(S))},N=S=>new Date(S).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1}),j=S=>{switch(S){case"system":return r.jsx(u0,{className:"w-4 h-4 text-blue-400"});case"mic_request":return r.jsx(vt,{className:"w-4 h-4 text-yellow-400"});default:return r.jsx(gn,{className:"w-4 h-4 text-gray-400"})}},k=(S,m)=>{const d=m===t.id;switch(S){case"system":return"bg-blue-900/30 border-blue-500/30 text-blue-200";case"mic_request":return"bg-yellow-900/30 border-yellow-500/30 text-yellow-200";default:return d?"bg-purple-900/50 border-purple-500/30 text-white":"bg-gray-800/50 border-gray-600/30 text-gray-200"}};return r.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-blue-900/30 rounded-xl border border-blue-500/20 flex flex-col h-[600px]",children:[r.jsxs("div",{className:"p-4 border-b border-gray-700/50",children:[r.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[r.jsx(gn,{className:"w-5 h-5 text-blue-400"}),"المحادثة النصية"]}),r.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:[e.length," رسالة"]})]}),r.jsx("div",{className:"flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800",children:r.jsxs("div",{className:"flex flex-col space-y-3",children:[e.length===0?r.jsxs("div",{className:"text-center text-gray-400 py-8",children:[r.jsx(gn,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),r.jsx("p",{children:"لا توجد رسائل بعد"}),r.jsx("p",{className:"text-sm mt-1",children:"ابدأ المحادثة!"})]}):e.map(S=>r.jsxs("div",{className:`p-3 rounded-lg border ${k(S.messageType,S.sender._id)} ${S.messageType!=="system"&&S.sender._id===t.id?"max-w-[60%] self-end ml-auto":""}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[j(S.messageType),r.jsx("span",{className:"font-medium text-sm",children:S.sender.username}),r.jsxs("span",{className:"text-xs opacity-60",children:["#",S.sender.playerId]})]}),r.jsxs("div",{className:"flex items-center gap-1 text-xs opacity-60",children:[r.jsx(pn,{className:"w-3 h-3"}),r.jsx("span",{children:N(S.timestamp)})]})]}),r.jsx("div",{className:"text-sm leading-relaxed",children:S.content})]},S._id)),r.jsx("div",{ref:u})]})}),r.jsxs("div",{className:"p-4 border-t border-gray-700/50",children:[r.jsxs("form",{onSubmit:x,className:"flex gap-2",children:[r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("input",{ref:g,type:"text",value:o,onChange:S=>i(S.target.value),onKeyPress:w,placeholder:"اكتب رسالتك هنا...",maxLength:500,disabled:a,className:"w-full px-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-gray-800/70 transition-all disabled:opacity-50 disabled:cursor-not-allowed"}),r.jsxs("div",{className:"absolute bottom-1 left-2 text-xs text-gray-500",children:[o.length,"/500"]})]}),r.jsxs("button",{type:"submit",disabled:!o.trim()||a,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white transition-colors flex items-center gap-2",title:"إرسال الرسالة",children:[r.jsx(Pr,{className:"w-4 h-4"}),a?"جاري الإرسال...":"إرسال"]}),!n&&r.jsxs("button",{type:"button",onClick:l,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors flex items-center gap-1",title:"طلب المايك",children:[r.jsx(vt,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden sm:inline",children:"طلب المايك"})]})]}),n&&r.jsxs("div",{className:"mt-2 p-2 bg-yellow-900/30 border border-yellow-500/30 rounded-lg text-yellow-200 text-sm flex items-center gap-2",children:[r.jsx(pn,{className:"w-4 h-4"}),r.jsx("span",{children:"أنت في قائمة انتظار المايك"})]}),r.jsxs("div",{className:"mt-2 text-xs text-gray-500 flex items-center gap-4",children:[r.jsx("span",{children:"اضغط Enter للإرسال"}),r.jsx("span",{children:"الحد الأقصى: 500 حرف"})]})]})]})},Hp=({user:e,wsService:t})=>{const[n,s]=v.useState(null),[l,o]=v.useState([]),[i,a]=v.useState(!0),[c,u]=v.useState(null),[g,x]=v.useState(!1),[w,N]=v.useState(null),[j,k]=v.useState(!1),[S,m]=v.useState(!1),[d,p]=v.useState(!1),[M,L]=v.useState(!1),[P,z]=v.useState([]),[F,D]=v.useState(new Map),y=v.useRef(null),O=async()=>{try{a(!0);const[h,C]=await Promise.all([K.getVoiceRoom(),K.getVoiceRoomMessages()]);s(h),o(C);const _=h.seats.find(R=>R.user&&R.user._id===e.id);_?(x(!0),N(_.seatNumber),m(_.isMuted)):(x(!1),N(null));const $=h.waitingQueue.some(R=>R.user._id===e.id);k($),u(null)}catch(h){if(console.error("Error loading voice room:",h),h.message&&h.message.includes("مطرود من الغرفة الصوتية")){u(h.message),s({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,seats:[],waitingQueue:[],settings:{allowTextChat:!0,autoKickInactive:!1,inactiveTimeoutMinutes:30},isActive:!1});return}u(h.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1)}};v.useEffect(()=>{if(!(e!=null&&e.id)){console.warn("⚠️ No user ID available, skipping WebRTC service setup");return}console.log("🔧 Setting up WebRTC Voice Service with user ID:",e.id);try{y.current=new T0(t)}catch(h){console.error("❌ Error creating WebRTC service:",h);return}return y.current&&(y.current.onUserJoined=h=>{console.log(`👤 User joined voice chat: ${h.id}`),z(C=>[...C.filter(_=>_.id!==h.id),h])},y.current.onUserLeft=h=>{console.log(`👋 User left voice chat: ${h}`),z(C=>C.filter(_=>_.id!==h)),D(C=>{const _=new Map(C);return _.delete(h),_})},y.current.onVoiceActivity=h=>{console.log("🎤 Voice activity changed:",h.isSpeaking?"speaking":"silent",`(level: ${h.level})`),D(C=>{const _=new Map(C);return _.set(h.userId,h),_}),e!=null&&e.id&&g?(console.log("📤 Voice activity sent:",h.isSpeaking?"speaking":"silent",`(userId: ${e.id})`),t.send({type:"voice_activity",data:{userId:e.id,level:h.level,isSpeaking:h.isSpeaking,timestamp:Date.now()}})):(e!=null&&e.id||console.warn("⚠️ No currentUserId available for voice activity"),g||console.log("🔍 User not in seat, voice activity not sent"))},y.current.onError=h=>{console.error("❌ WebRTC error:",h),u(`خطأ في الصوت: ${h.message}`)}),()=>{y.current&&(console.log("🧹 Cleaning up WebRTC service"),y.current.leaveRoom().catch(console.error))}},[t,e==null?void 0:e.id]),v.useEffect(()=>{const h=R=>{o(G=>[...G,R])},C=R=>{R.action&&R.userId&&(R.action==="seat_joined"||R.action==="seat_left"||R.action==="mute_toggled")||O(),R.action==="seat_joined"&&g&&R.userId!==e.id&&setTimeout(()=>{var G;(G=y.current)==null||G.sendOffer(R.userId)},1e3)},_=R=>{R.userId&&R.userId!==e.id&&(D(G=>{const Y=new Map(G);return Y.set(R.userId.toString(),{userId:R.userId.toString(),level:R.level,isSpeaking:R.isSpeaking}),Y}),s(G=>({...G,seats:G.seats.map(Y=>{var ne;return((ne=Y.user)==null?void 0:ne._id)===R.userId?{...Y,isSpeaking:R.isSpeaking}:Y})})))};y.current.onVoiceActivity=R=>{s(G=>({...G,seats:G.seats.map(Y=>{var ne;return((ne=Y.user)==null?void 0:ne._id)===e.id?{...Y,isSpeaking:R.isSpeaking}:Y})})),g&&t.send({type:"voice_activity",data:{userId:e.id,level:R.level,isSpeaking:R.isSpeaking}})};const $=R=>{O()};return t.onMessage("voice_room_message",h),t.onMessage("voice_room_update",C),t.onMessage("voice_activity",_),t.onMessage("admin_action_update",$),()=>{t.offMessage("voice_room_message",h),t.offMessage("voice_room_update",C),t.offMessage("voice_activity",_),t.offMessage("admin_action_update",$)}},[t,g,e.id]),v.useEffect(()=>{O()},[]),v.useEffect(()=>{const h=_=>{if(g)return _.preventDefault(),_.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},C=()=>{g&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",h),window.addEventListener("unload",C),()=>{window.removeEventListener("beforeunload",h),window.removeEventListener("unload",C)}},[g,e.id]);const X=async h=>{try{const C=await K.sendVoiceRoomMessage(h);t.send({type:"voice_room_message",data:C.messageData})}catch(C){console.error("Error sending message:",C),u(C.message||"خطأ في إرسال الرسالة")}},re=async()=>{try{p(!0),await K.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}}),await O()}catch(h){console.error("Error requesting mic:",h),u(h.message||"خطأ في طلب المايك")}finally{p(!1)}},He=async()=>{try{await K.cancelMicRequest(),t.send({type:"voice_room_update",data:{action:"mic_request_cancelled",userId:e.id}}),await O()}catch(h){console.error("Error cancelling mic request:",h),u(h.message||"خطأ في إلغاء طلب المايك")}},Ye=async h=>{try{if(p(!0),u(null),await K.joinVoiceSeat(h),x(!0),N(h),m(!1),y.current&&(e!=null&&e.id))try{const C=`voice-room-${(n==null?void 0:n.id)||"default"}`;await y.current.joinRoom(C,e.id),L(!0)}catch(C){console.error("❌ WebRTC initialization failed:",C),u(`فشل في بدء المحادثة الصوتية: ${C.message}`)}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",h.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:h}}),await O()}catch(C){console.error("Error joining seat:",C),u(C.message||"خطأ في الانضمام للمقعد"),L(!1)}finally{p(!1)}},Ie=async()=>{var h;try{p(!0),await K.leaveSeat(),x(!1),N(null),m(!1),y.current&&y.current.leaveRoom(),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:w}})}catch(C){console.error("Error leaving seat:",C),(h=C.message)!=null&&h.includes("لست في أي مقعد")||u(C.message||"خطأ في مغادرة المقعد")}finally{p(!1)}},fe=async()=>{try{if(!g){u("يجب أن تكون في مقعد لاستخدام المايك");return}if(!y.current){u("خدمة الصوت غير متاحة - جاري إعادة الاتصال...");return}const h=!S;y.current.setMute(h),m(h);try{await K.toggleMute(h)}catch(C){console.warn("Failed to update server mute state:",C)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:h}})}catch(h){console.error("Error toggling mute:",h),u("خطأ في تبديل كتم المايك"),m(!S)}};return i?r.jsx("div",{className:"flex items-center justify-center h-96",children:r.jsxs("div",{className:"text-center",children:[r.jsx(xn,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),r.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})}):c?r.jsxs("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center",children:[r.jsx("p",{className:"text-red-400 mb-4",children:c}),r.jsx("button",{onClick:O,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors",children:"إعادة المحاولة"})]}):n?r.jsxs("div",{className:`max-w-7xl mx-auto p-4 sm:p-6 space-y-6 ${g?"pb-24 sm:pb-6":""}`,children:[r.jsx("div",{className:"bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-xl p-4 sm:p-6 border border-purple-500/20",children:r.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[r.jsxs("div",{children:[r.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-white mb-2 flex items-center gap-3",children:[r.jsx(ts,{className:"w-6 sm:w-8 h-6 sm:h-8 text-purple-400"}),"INFINITY ROOM"]}),r.jsx("p",{className:"text-gray-300 text-sm sm:text-base",children:"غرفة صوتية للمحادثة مع الأصدقاء"})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[r.jsxs("div",{className:"flex items-center gap-4 text-gray-300",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(kn,{className:"w-4 sm:w-5 h-4 sm:h-5"}),r.jsxs("span",{className:"text-sm",children:[n.seats.filter(h=>h.user).length,"/",n.maxSeats]})]}),r.jsxs("div",{className:"flex items-center gap-2 bg-black/20 px-2 sm:px-3 py-1 rounded-lg",children:[M?r.jsx(Yh,{className:"w-3 sm:w-4 h-3 sm:h-4 text-green-400"}):r.jsx(Kh,{className:"w-3 sm:w-4 h-3 sm:h-4 text-red-400"}),r.jsx("span",{className:"text-xs sm:text-sm",children:M?"متصل":"غير متصل"})]})]}),g&&r.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto",children:[r.jsx("button",{onClick:fe,className:`w-full sm:w-auto p-3 sm:p-2 rounded-lg transition-colors text-sm font-medium ${S?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:r.jsxs("div",{className:"flex items-center justify-center gap-2",children:[S?r.jsx(Xr,{className:"w-5 h-5"}):r.jsx(vt,{className:"w-5 h-5"}),r.jsx("span",{className:"sm:hidden",children:S?"إلغاء الكتم":"كتم المايك"})]})}),r.jsx("button",{onClick:Ie,className:"w-full sm:w-auto px-4 py-3 sm:py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors text-sm font-medium",children:"مغادرة المقعد"})]})]})]})}),g&&r.jsx("div",{className:"hidden sm:block mb-6",children:r.jsx("div",{className:"bg-gradient-to-r from-gray-800/50 to-purple-900/30 rounded-xl p-4 border border-purple-500/20",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center gap-3",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),r.jsxs("span",{className:"text-green-400 font-medium",children:["متصل - مقعد ",w]})]})}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("button",{onClick:fe,className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 font-medium ${S?"bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/25":"bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/25"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:[S?r.jsx(Xr,{className:"w-4 h-4"}):r.jsx(vt,{className:"w-4 h-4"}),r.jsx("span",{children:S?"إلغاء كتم المايك":"كتم المايك"})]}),r.jsx("button",{onClick:Ie,className:"flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-all duration-200 font-medium shadow-lg shadow-red-600/25",title:"مغادرة المقعد",disabled:d,children:r.jsx("span",{children:d?"جاري المغادرة...":"مغادرة المقعد"})})]})]})})}),r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx(Vp,{seats:n.seats,waitingQueue:n.waitingQueue,currentUser:e,isInSeat:g,currentSeatNumber:w,isInWaitingQueue:j,isConnecting:d,onJoinSeat:Ye,onRequestMic:re,onCancelMicRequest:He})}),r.jsx("div",{className:"lg:col-span-1",children:r.jsx(qp,{messages:l,currentUser:e,isInWaitingQueue:j,onSendMessage:X,onRequestMic:re})})]})]}):r.jsx("div",{className:"text-center text-gray-400",children:r.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},Wp=({user:e,onLogout:t,wsService:n})=>{var w;const[,s]=A0(),[l,o]=v.useState(()=>{const N=localStorage.getItem("isInVoiceRoom")==="true",j=localStorage.getItem("activeTab");return N?"voice":j||"games"}),[i,a]=v.useState(!1),[c,u]=v.useState(!1);v.useEffect(()=>{const N=()=>{a(window.innerWidth<=768)};return N(),window.addEventListener("resize",N),()=>window.removeEventListener("resize",N)},[]);const g=N=>{o(N),localStorage.setItem("activeTab",N)},x=()=>{localStorage.removeItem("token"),s("/login"),t()};return console.log("USER DATA IN MAIN DASHBOARD:",e),!e||!e.username?r.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-red-900 via-yellow-900 to-black text-white",children:[r.jsx("h1",{className:"text-3xl font-bold mb-4",children:"⚠️ لا توجد بيانات لاعب!"}),r.jsx("pre",{className:"bg-black/60 rounded-lg p-4 text-left text-xs max-w-xl overflow-x-auto mb-4",children:JSON.stringify(e,null,2)}),r.jsx("p",{className:"mb-4",children:"يرجى التأكد من أن حسابك يحتوي على اسم مستخدم وصورة ورصيد."}),r.jsx("button",{onClick:t,className:"px-6 py-2 bg-red-600 rounded-lg text-white font-bold",children:"تسجيل الخروج"})]}):i?r.jsx(Fp,{userData:e,onLogout:x,wsService:n}):r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"}),r.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"}),r.jsx("div",{className:"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"})]}),r.jsx("header",{className:"relative z-10 bg-black/30 backdrop-blur-xl border-b border-white/10 shadow-2xl",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsxs("div",{className:"flex justify-between items-center h-20",children:[r.jsx("div",{className:"flex items-center space-x-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg",children:r.jsx(Tr,{className:"w-7 h-7 text-white"})}),r.jsxs("div",{children:[r.jsxs("h1",{className:"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:[e.username," - Infinity Box"]}),r.jsx("p",{className:"text-xs text-gray-400",children:"عالم الألعاب المثير"})]})]})}),r.jsx("div",{className:"flex items-center space-x-6",children:r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-yellow-500/50 shadow-lg hover:from-yellow-500/40 hover:to-orange-500/40 transition-all duration-300",children:[r.jsx(Ll,{className:"w-6 h-6 text-yellow-300"}),r.jsx("span",{className:"text-yellow-300 font-bold text-lg",children:((w=e.goldCoins)==null?void 0:w.toLocaleString())||0})]}),r.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500/30 to-purple-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-blue-500/50 shadow-lg hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-300",children:[r.jsx(m0,{className:"w-6 h-6 text-blue-300"}),r.jsx("span",{className:"text-blue-300 font-bold text-lg",children:e.pearls||0})]})]})}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("button",{onClick:()=>u(!c),className:"relative p-2 bg-white/10 backdrop-blur-sm rounded-lg hover:bg-white/20 transition-all duration-300 border border-white/20",children:[r.jsx(zh,{className:"w-5 h-5 text-white"}),r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),r.jsxs("div",{className:"flex items-center space-x-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-sm rounded-xl px-4 py-2 border border-purple-500/30 hover:from-purple-500/30 hover:to-blue-500/30 transition-all duration-300 shadow-lg",children:[r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:e.profileImage||"/images/default-avatar.png",alt:e.username,className:"w-12 h-12 rounded-full border-2 border-white/30 shadow-lg object-cover",onError:N=>{N.currentTarget.src="/images/default-avatar.png"}}),r.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),r.jsxs("div",{className:"text-right",children:[r.jsx("p",{className:"font-bold text-white text-lg",children:e.username}),r.jsxs("p",{className:"text-xs text-yellow-300",children:["مستوى ",e.level||1," - ",e.experience||0," XP"]})]})]}),r.jsxs("button",{onClick:x,className:"flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 backdrop-blur-sm rounded-lg transition-all duration-300 border border-red-500/30 text-red-400 hover:text-red-300",children:[r.jsx(p0,{className:"w-4 h-4"}),r.jsx("span",{className:"text-sm font-medium",children:"خروج"})]})]})]})})}),r.jsx("nav",{className:"relative z-10 bg-black/20 backdrop-blur-xl border-b border-white/10 shadow-lg",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsxs("div",{className:"flex space-x-8",children:[r.jsx("button",{onClick:()=>g("games"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="games"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎮 الألعاب"}),r.jsx("button",{onClick:()=>g("leaderboard"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="leaderboard"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🏆 المتصدرين"}),r.jsx("button",{onClick:()=>g("voice"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="voice"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎤 الغرفة الصوتية"}),r.jsx("button",{onClick:()=>g("profile"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="profile"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"👤 الملف الشخصي"}),e.isAdmin&&r.jsx("button",{onClick:()=>g("admin"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="admin"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"⚙️ الإدارة"})]})})}),r.jsxs("main",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[l==="games"&&r.jsx(Qs,{setActiveTab:g}),l==="leaderboard"&&r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(es,{className:"w-12 h-12 text-yellow-400 mr-4"}),r.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"🏆 المتصدرين"})]}),r.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:r.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة قائمة المتصدرين..."})})]}),l==="voice"&&n&&r.jsx(Hp,{user:e,wsService:n}),l==="profile"&&r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(yn,{className:"w-12 h-12 text-blue-400 mr-4"}),r.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"👤 الملف الشخصي"})]}),r.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:r.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة الملف الشخصي..."})})]}),l==="admin"&&e.isAdmin&&r.jsx(xi,{userData:e,onLogout:x})]}),c&&r.jsxs("div",{className:"fixed top-20 right-4 z-50 w-80 bg-black/90 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl",children:[r.jsx("div",{className:"p-4 border-b border-white/10",children:r.jsx("h3",{className:"text-lg font-semibold text-white",children:"الإشعارات"})}),r.jsx("div",{className:"p-4",children:r.jsx("p",{className:"text-gray-400 text-center",children:"لا توجد إشعارات جديدة"})})]})]})};function Qp(){const[e,t]=v.useState(!1),[n,s]=v.useState(null),[l,o]=v.useState(!0),[i]=v.useState(()=>new _0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`));v.useEffect(()=>{localStorage.removeItem("activeTab");const u=localStorage.getItem("token");console.log("🔍 App: Checking token:",u?"Token exists":"No token found"),u?(console.log("🔄 App: Attempting to get current user..."),K.getCurrentUser().then(g=>{if(console.log("✅ App: User data received:",g),g&&typeof g=="object")s(g),localStorage.setItem("isAdmin",g.isAdmin?"true":"false");else{const w=localStorage.getItem("isAdmin")==="true";s({id:"",username:"",isAdmin:w})}console.log("🔓 App: Setting authenticated to true"),t(!0);const x=localStorage.getItem("token");x&&i.connect(x).then(()=>{console.log("✅ WebSocket connected on app load")}).catch(w=>{console.error("❌ Failed to connect to WebSocket on app load:",w)})}).catch(g=>{console.log("❌ App: Error getting user:",g),g.message.includes("MULTIPLE_LOGIN")&&alert("تم تسجيل الدخول من جهاز آخر. سيتم تسجيل خروجك من هذا الجهاز."),console.log("🔒 App: Setting authenticated to false"),t(!1),s(null),localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}).finally(()=>{console.log("⏹️ App: Loading finished"),o(!1)})):(console.log("🔒 App: No token found, setting authenticated to false"),o(!1))},[]);const a=async u=>{s(u),t(!0);try{const g=localStorage.getItem("token");g&&await i.connect(g)}catch(g){console.error("Failed to connect to WebSocket:",g)}},c=()=>{localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),i.disconnect(),t(!1),s(null)};return l?(console.log("⏳ App: Showing loading screen"),r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 flex items-center justify-center",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-white text-lg",children:"جاري التحميل..."})]})})):e?(console.log("🏠 App: Showing MainDashboard (authenticated)"),n?r.jsx(Wp,{user:n,onLogout:c,wsService:i}):null):(console.log("🔐 App: Showing AuthPage (not authenticated)"),r.jsx(rp,{onAuthSuccess:a}))}o0(document.getElementById("root")).render(r.jsx(v.StrictMode,{children:r.jsx(Qp,{})}));
