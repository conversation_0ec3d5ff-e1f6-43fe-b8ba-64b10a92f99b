function X0(e,t){for(var n=0;n<t.length;n++){const s=t[n];if(typeof s!="string"&&!Array.isArray(s)){for(const l in s)if(l!=="default"&&!(l in e)){const o=Object.getOwnPropertyDescriptor(s,l);o&&Object.defineProperty(e,l,o.get?o:{enumerable:!0,get:()=>s[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))s(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(l){if(l.ep)return;l.ep=!0;const o=n(l);fetch(l.href,o)}})();function em(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Gc={exports:{}},gl={},Dc={exports:{}},Z={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ns=Symbol.for("react.element"),tm=Symbol.for("react.portal"),nm=Symbol.for("react.fragment"),rm=Symbol.for("react.strict_mode"),sm=Symbol.for("react.profiler"),lm=Symbol.for("react.provider"),om=Symbol.for("react.context"),im=Symbol.for("react.forward_ref"),am=Symbol.for("react.suspense"),cm=Symbol.for("react.memo"),dm=Symbol.for("react.lazy"),Ma=Symbol.iterator;function um(e){return e===null||typeof e!="object"?null:(e=Ma&&e[Ma]||e["@@iterator"],typeof e=="function"?e:null)}var Jc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Zc=Object.assign,Kc={};function sr(e,t,n){this.props=e,this.context=t,this.refs=Kc,this.updater=n||Jc}sr.prototype.isReactComponent={};sr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};sr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Yc(){}Yc.prototype=sr.prototype;function xi(e,t,n){this.props=e,this.context=t,this.refs=Kc,this.updater=n||Jc}var yi=xi.prototype=new Yc;yi.constructor=xi;Zc(yi,sr.prototype);yi.isPureReactComponent=!0;var Ea=Array.isArray,Xc=Object.prototype.hasOwnProperty,vi={current:null},ed={key:!0,ref:!0,__self:!0,__source:!0};function td(e,t,n){var s,l={},o=null,i=null;if(t!=null)for(s in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Xc.call(t,s)&&!ed.hasOwnProperty(s)&&(l[s]=t[s]);var a=arguments.length-2;if(a===1)l.children=n;else if(1<a){for(var c=Array(a),d=0;d<a;d++)c[d]=arguments[d+2];l.children=c}if(e&&e.defaultProps)for(s in a=e.defaultProps,a)l[s]===void 0&&(l[s]=a[s]);return{$$typeof:ns,type:e,key:o,ref:i,props:l,_owner:vi.current}}function mm(e,t){return{$$typeof:ns,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function wi(e){return typeof e=="object"&&e!==null&&e.$$typeof===ns}function fm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ia=/\/+/g;function $l(e,t){return typeof e=="object"&&e!==null&&e.key!=null?fm(""+e.key):t.toString(36)}function Ts(e,t,n,s,l){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case ns:case tm:i=!0}}if(i)return i=e,l=l(i),e=s===""?"."+$l(i,0):s,Ea(l)?(n="",e!=null&&(n=e.replace(Ia,"$&/")+"/"),Ts(l,t,n,"",function(d){return d})):l!=null&&(wi(l)&&(l=mm(l,n+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Ia,"$&/")+"/")+e)),t.push(l)),1;if(i=0,s=s===""?".":s+":",Ea(e))for(var a=0;a<e.length;a++){o=e[a];var c=s+$l(o,a);i+=Ts(o,t,n,c,l)}else if(c=um(e),typeof c=="function")for(e=c.call(e),a=0;!(o=e.next()).done;)o=o.value,c=s+$l(o,a++),i+=Ts(o,t,n,c,l);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function hs(e,t,n){if(e==null)return e;var s=[],l=0;return Ts(e,s,"","",function(o){return t.call(n,o,l++)}),s}function hm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ue={current:null},Ls={transition:null},pm={ReactCurrentDispatcher:Ue,ReactCurrentBatchConfig:Ls,ReactCurrentOwner:vi};function nd(){throw Error("act(...) is not supported in production builds of React.")}Z.Children={map:hs,forEach:function(e,t,n){hs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return hs(e,function(){t++}),t},toArray:function(e){return hs(e,function(t){return t})||[]},only:function(e){if(!wi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Z.Component=sr;Z.Fragment=nm;Z.Profiler=sm;Z.PureComponent=xi;Z.StrictMode=rm;Z.Suspense=am;Z.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=pm;Z.act=nd;Z.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var s=Zc({},e.props),l=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=vi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)Xc.call(t,c)&&!ed.hasOwnProperty(c)&&(s[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)s.children=n;else if(1<c){a=Array(c);for(var d=0;d<c;d++)a[d]=arguments[d+2];s.children=a}return{$$typeof:ns,type:e.type,key:l,ref:o,props:s,_owner:i}};Z.createContext=function(e){return e={$$typeof:om,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:lm,_context:e},e.Consumer=e};Z.createElement=td;Z.createFactory=function(e){var t=td.bind(null,e);return t.type=e,t};Z.createRef=function(){return{current:null}};Z.forwardRef=function(e){return{$$typeof:im,render:e}};Z.isValidElement=wi;Z.lazy=function(e){return{$$typeof:dm,_payload:{_status:-1,_result:e},_init:hm}};Z.memo=function(e,t){return{$$typeof:cm,type:e,compare:t===void 0?null:t}};Z.startTransition=function(e){var t=Ls.transition;Ls.transition={};try{e()}finally{Ls.transition=t}};Z.unstable_act=nd;Z.useCallback=function(e,t){return Ue.current.useCallback(e,t)};Z.useContext=function(e){return Ue.current.useContext(e)};Z.useDebugValue=function(){};Z.useDeferredValue=function(e){return Ue.current.useDeferredValue(e)};Z.useEffect=function(e,t){return Ue.current.useEffect(e,t)};Z.useId=function(){return Ue.current.useId()};Z.useImperativeHandle=function(e,t,n){return Ue.current.useImperativeHandle(e,t,n)};Z.useInsertionEffect=function(e,t){return Ue.current.useInsertionEffect(e,t)};Z.useLayoutEffect=function(e,t){return Ue.current.useLayoutEffect(e,t)};Z.useMemo=function(e,t){return Ue.current.useMemo(e,t)};Z.useReducer=function(e,t,n){return Ue.current.useReducer(e,t,n)};Z.useRef=function(e){return Ue.current.useRef(e)};Z.useState=function(e){return Ue.current.useState(e)};Z.useSyncExternalStore=function(e,t,n){return Ue.current.useSyncExternalStore(e,t,n)};Z.useTransition=function(){return Ue.current.useTransition()};Z.version="18.3.1";Dc.exports=Z;var y=Dc.exports;const gm=em(y),xm=X0({__proto__:null,default:gm},[y]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ym=y,vm=Symbol.for("react.element"),wm=Symbol.for("react.fragment"),bm=Object.prototype.hasOwnProperty,jm=ym.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Nm={key:!0,ref:!0,__self:!0,__source:!0};function rd(e,t,n){var s,l={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(s in t)bm.call(t,s)&&!Nm.hasOwnProperty(s)&&(l[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)l[s]===void 0&&(l[s]=t[s]);return{$$typeof:vm,type:e,key:o,ref:i,props:l,_owner:jm.current}}gl.Fragment=wm;gl.jsx=rd;gl.jsxs=rd;Gc.exports=gl;var r=Gc.exports,sd={exports:{}},Je={},ld={exports:{}},od={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(h,C){var T=h.length;h.push(C);e:for(;0<T;){var F=T-1>>>1,P=h[F];if(0<l(P,C))h[F]=C,h[T]=P,T=F;else break e}}function n(h){return h.length===0?null:h[0]}function s(h){if(h.length===0)return null;var C=h[0],T=h.pop();if(T!==C){h[0]=T;e:for(var F=0,P=h.length,W=P>>>1;F<W;){var Y=2*(F+1)-1,se=h[Y],me=Y+1,ge=h[me];if(0>l(se,T))me<P&&0>l(ge,se)?(h[F]=ge,h[me]=T,F=me):(h[F]=se,h[Y]=T,F=Y);else if(me<P&&0>l(ge,T))h[F]=ge,h[me]=T,F=me;else break e}}return C}function l(h,C){var T=h.sortIndex-C.sortIndex;return T!==0?T:h.id-C.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var c=[],d=[],g=1,w=null,b=3,N=!1,j=!1,k=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,u=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(h){for(var C=n(d);C!==null;){if(C.callback===null)s(d);else if(C.startTime<=h)s(d),C.sortIndex=C.expirationTime,t(c,C);else break;C=n(d)}}function M(h){if(k=!1,p(h),!j)if(n(c)!==null)j=!0,Ee(L);else{var C=n(d);C!==null&&pe(M,C.startTime-h)}}function L(h,C){j=!1,k&&(k=!1,m(U),U=-1),N=!0;var T=b;try{for(p(C),w=n(c);w!==null&&(!(w.expirationTime>C)||h&&!O());){var F=w.callback;if(typeof F=="function"){w.callback=null,b=w.priorityLevel;var P=F(w.expirationTime<=C);C=e.unstable_now(),typeof P=="function"?w.callback=P:w===n(c)&&s(c),p(C)}else s(c);w=n(c)}if(w!==null)var W=!0;else{var Y=n(d);Y!==null&&pe(M,Y.startTime-C),W=!1}return W}finally{w=null,b=T,N=!1}}var R=!1,z=null,U=-1,D=5,v=-1;function O(){return!(e.unstable_now()-v<D)}function X(){if(z!==null){var h=e.unstable_now();v=h;var C=!0;try{C=z(!0,h)}finally{C?ae():(R=!1,z=null)}}else R=!1}var ae;if(typeof u=="function")ae=function(){u(X)};else if(typeof MessageChannel<"u"){var Pe=new MessageChannel,Me=Pe.port2;Pe.port1.onmessage=X,ae=function(){Me.postMessage(null)}}else ae=function(){S(X,0)};function Ee(h){z=h,R||(R=!0,ae())}function pe(h,C){U=S(function(){h(e.unstable_now())},C)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(h){h.callback=null},e.unstable_continueExecution=function(){j||N||(j=!0,Ee(L))},e.unstable_forceFrameRate=function(h){0>h||125<h?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<h?Math.floor(1e3/h):5},e.unstable_getCurrentPriorityLevel=function(){return b},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(h){switch(b){case 1:case 2:case 3:var C=3;break;default:C=b}var T=b;b=C;try{return h()}finally{b=T}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(h,C){switch(h){case 1:case 2:case 3:case 4:case 5:break;default:h=3}var T=b;b=h;try{return C()}finally{b=T}},e.unstable_scheduleCallback=function(h,C,T){var F=e.unstable_now();switch(typeof T=="object"&&T!==null?(T=T.delay,T=typeof T=="number"&&0<T?F+T:F):T=F,h){case 1:var P=-1;break;case 2:P=250;break;case 5:P=**********;break;case 4:P=1e4;break;default:P=5e3}return P=T+P,h={id:g++,callback:C,priorityLevel:h,startTime:T,expirationTime:P,sortIndex:-1},T>F?(h.sortIndex=T,t(d,h),n(c)===null&&h===n(d)&&(k?(m(U),U=-1):k=!0,pe(M,T-F))):(h.sortIndex=P,t(c,h),j||N||(j=!0,Ee(L))),h},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(h){var C=b;return function(){var T=b;b=C;try{return h.apply(this,arguments)}finally{b=T}}}})(od);ld.exports=od;var km=ld.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sm=y,De=km;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var id=new Set,Rr={};function jn(e,t){Zn(e,t),Zn(e+"Capture",t)}function Zn(e,t){for(Rr[e]=t,e=0;e<t.length;e++)id.add(t[e])}var Et=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),po=Object.prototype.hasOwnProperty,Cm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Aa={},_a={};function Mm(e){return po.call(_a,e)?!0:po.call(Aa,e)?!1:Cm.test(e)?_a[e]=!0:(Aa[e]=!0,!1)}function Em(e,t,n,s){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return s?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Im(e,t,n,s){if(t===null||typeof t>"u"||Em(e,t,n,s))return!0;if(s)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function $e(e,t,n,s,l,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=s,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new $e(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new $e(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new $e(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new $e(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new $e(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new $e(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new $e(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new $e(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new $e(e,5,!1,e.toLowerCase(),null,!1,!1)});var bi=/[\-:]([a-z])/g;function ji(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(bi,ji);Ce[t]=new $e(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(bi,ji);Ce[t]=new $e(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(bi,ji);Ce[t]=new $e(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new $e(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new $e("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new $e(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ni(e,t,n,s){var l=Ce.hasOwnProperty(t)?Ce[t]:null;(l!==null?l.type!==0:s||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Im(t,n,l,s)&&(n=null),s||l===null?Mm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,s=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,s?e.setAttributeNS(s,t,n):e.setAttribute(t,n))))}var Tt=Sm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ps=Symbol.for("react.element"),Tn=Symbol.for("react.portal"),Ln=Symbol.for("react.fragment"),ki=Symbol.for("react.strict_mode"),go=Symbol.for("react.profiler"),ad=Symbol.for("react.provider"),cd=Symbol.for("react.context"),Si=Symbol.for("react.forward_ref"),xo=Symbol.for("react.suspense"),yo=Symbol.for("react.suspense_list"),Ci=Symbol.for("react.memo"),Pt=Symbol.for("react.lazy"),dd=Symbol.for("react.offscreen"),Ta=Symbol.iterator;function ur(e){return e===null||typeof e!="object"?null:(e=Ta&&e[Ta]||e["@@iterator"],typeof e=="function"?e:null)}var ue=Object.assign,Fl;function vr(e){if(Fl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Fl=t&&t[1]||""}return`
`+Fl+e}var Bl=!1;function Vl(e,t){if(!e||Bl)return"";Bl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(d){var s=d}Reflect.construct(e,[],t)}else{try{t.call()}catch(d){s=d}e.call(t.prototype)}else{try{throw Error()}catch(d){s=d}e()}}catch(d){if(d&&s&&typeof d.stack=="string"){for(var l=d.stack.split(`
`),o=s.stack.split(`
`),i=l.length-1,a=o.length-1;1<=i&&0<=a&&l[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(l[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||l[i]!==o[a]){var c=`
`+l[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=a);break}}}finally{Bl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?vr(e):""}function Am(e){switch(e.tag){case 5:return vr(e.type);case 16:return vr("Lazy");case 13:return vr("Suspense");case 19:return vr("SuspenseList");case 0:case 2:case 15:return e=Vl(e.type,!1),e;case 11:return e=Vl(e.type.render,!1),e;case 1:return e=Vl(e.type,!0),e;default:return""}}function vo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ln:return"Fragment";case Tn:return"Portal";case go:return"Profiler";case ki:return"StrictMode";case xo:return"Suspense";case yo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case cd:return(e.displayName||"Context")+".Consumer";case ad:return(e._context.displayName||"Context")+".Provider";case Si:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ci:return t=e.displayName||null,t!==null?t:vo(e.type)||"Memo";case Pt:t=e._payload,e=e._init;try{return vo(e(t))}catch{}}return null}function _m(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return vo(t);case 8:return t===ki?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Dt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ud(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Tm(e){var t=ud(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),s=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){s=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return s},setValue:function(i){s=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function gs(e){e._valueTracker||(e._valueTracker=Tm(e))}function md(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),s="";return e&&(s=ud(e)?e.checked?"true":"false":e.value),e=s,e!==n?(t.setValue(e),!0):!1}function Qs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function wo(e,t){var n=t.checked;return ue({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function La(e,t){var n=t.defaultValue==null?"":t.defaultValue,s=t.checked!=null?t.checked:t.defaultChecked;n=Dt(t.value!=null?t.value:n),e._wrapperState={initialChecked:s,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function fd(e,t){t=t.checked,t!=null&&Ni(e,"checked",t,!1)}function bo(e,t){fd(e,t);var n=Dt(t.value),s=t.type;if(n!=null)s==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?jo(e,t.type,n):t.hasOwnProperty("defaultValue")&&jo(e,t.type,Dt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Pa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var s=t.type;if(!(s!=="submit"&&s!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function jo(e,t,n){(t!=="number"||Qs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var wr=Array.isArray;function Hn(e,t,n,s){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&s&&(e[n].defaultSelected=!0)}else{for(n=""+Dt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,s&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function No(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return ue({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ra(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(wr(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Dt(n)}}function hd(e,t){var n=Dt(t.value),s=Dt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),s!=null&&(e.defaultValue=""+s)}function za(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function pd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ko(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?pd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var xs,gd=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,s,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,s,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(xs=xs||document.createElement("div"),xs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=xs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function zr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Nr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Lm=["Webkit","ms","Moz","O"];Object.keys(Nr).forEach(function(e){Lm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Nr[t]=Nr[e]})});function xd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Nr.hasOwnProperty(e)&&Nr[e]?(""+t).trim():t+"px"}function yd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var s=n.indexOf("--")===0,l=xd(n,t[n],s);n==="float"&&(n="cssFloat"),s?e.setProperty(n,l):e[n]=l}}var Pm=ue({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function So(e,t){if(t){if(Pm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function Co(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Mo=null;function Mi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Eo=null,Wn=null,Qn=null;function Oa(e){if(e=ls(e)){if(typeof Eo!="function")throw Error(_(280));var t=e.stateNode;t&&(t=bl(t),Eo(e.stateNode,e.type,t))}}function vd(e){Wn?Qn?Qn.push(e):Qn=[e]:Wn=e}function wd(){if(Wn){var e=Wn,t=Qn;if(Qn=Wn=null,Oa(e),t)for(e=0;e<t.length;e++)Oa(t[e])}}function bd(e,t){return e(t)}function jd(){}var ql=!1;function Nd(e,t,n){if(ql)return e(t,n);ql=!0;try{return bd(e,t,n)}finally{ql=!1,(Wn!==null||Qn!==null)&&(jd(),wd())}}function Or(e,t){var n=e.stateNode;if(n===null)return null;var s=bl(n);if(s===null)return null;n=s[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var Io=!1;if(Et)try{var mr={};Object.defineProperty(mr,"passive",{get:function(){Io=!0}}),window.addEventListener("test",mr,mr),window.removeEventListener("test",mr,mr)}catch{Io=!1}function Rm(e,t,n,s,l,o,i,a,c){var d=Array.prototype.slice.call(arguments,3);try{t.apply(n,d)}catch(g){this.onError(g)}}var kr=!1,Gs=null,Ds=!1,Ao=null,zm={onError:function(e){kr=!0,Gs=e}};function Om(e,t,n,s,l,o,i,a,c){kr=!1,Gs=null,Rm.apply(zm,arguments)}function Um(e,t,n,s,l,o,i,a,c){if(Om.apply(this,arguments),kr){if(kr){var d=Gs;kr=!1,Gs=null}else throw Error(_(198));Ds||(Ds=!0,Ao=d)}}function Nn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function kd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ua(e){if(Nn(e)!==e)throw Error(_(188))}function $m(e){var t=e.alternate;if(!t){if(t=Nn(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,s=t;;){var l=n.return;if(l===null)break;var o=l.alternate;if(o===null){if(s=l.return,s!==null){n=s;continue}break}if(l.child===o.child){for(o=l.child;o;){if(o===n)return Ua(l),e;if(o===s)return Ua(l),t;o=o.sibling}throw Error(_(188))}if(n.return!==s.return)n=l,s=o;else{for(var i=!1,a=l.child;a;){if(a===n){i=!0,n=l,s=o;break}if(a===s){i=!0,s=l,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,s=l;break}if(a===s){i=!0,s=o,n=l;break}a=a.sibling}if(!i)throw Error(_(189))}}if(n.alternate!==s)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Sd(e){return e=$m(e),e!==null?Cd(e):null}function Cd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Cd(e);if(t!==null)return t;e=e.sibling}return null}var Md=De.unstable_scheduleCallback,$a=De.unstable_cancelCallback,Fm=De.unstable_shouldYield,Bm=De.unstable_requestPaint,xe=De.unstable_now,Vm=De.unstable_getCurrentPriorityLevel,Ei=De.unstable_ImmediatePriority,Ed=De.unstable_UserBlockingPriority,Js=De.unstable_NormalPriority,qm=De.unstable_LowPriority,Id=De.unstable_IdlePriority,xl=null,vt=null;function Hm(e){if(vt&&typeof vt.onCommitFiberRoot=="function")try{vt.onCommitFiberRoot(xl,e,void 0,(e.current.flags&128)===128)}catch{}}var ct=Math.clz32?Math.clz32:Gm,Wm=Math.log,Qm=Math.LN2;function Gm(e){return e>>>=0,e===0?32:31-(Wm(e)/Qm|0)|0}var ys=64,vs=4194304;function br(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Zs(e,t){var n=e.pendingLanes;if(n===0)return 0;var s=0,l=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~l;a!==0?s=br(a):(o&=i,o!==0&&(s=br(o)))}else i=n&~l,i!==0?s=br(i):o!==0&&(s=br(o));if(s===0)return 0;if(t!==0&&t!==s&&!(t&l)&&(l=s&-s,o=t&-t,l>=o||l===16&&(o&4194240)!==0))return t;if(s&4&&(s|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=s;0<t;)n=31-ct(t),l=1<<n,s|=e[n],t&=~l;return s}function Dm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jm(e,t){for(var n=e.suspendedLanes,s=e.pingedLanes,l=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-ct(o),a=1<<i,c=l[i];c===-1?(!(a&n)||a&s)&&(l[i]=Dm(a,t)):c<=t&&(e.expiredLanes|=a),o&=~a}}function _o(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ad(){var e=ys;return ys<<=1,!(ys&4194240)&&(ys=64),e}function Hl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function rs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ct(t),e[t]=n}function Zm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-ct(n),o=1<<l;t[l]=0,s[l]=-1,e[l]=-1,n&=~o}}function Ii(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var s=31-ct(n),l=1<<s;l&t|e[s]&t&&(e[s]|=t),n&=~l}}var ne=0;function _d(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Td,Ai,Ld,Pd,Rd,To=!1,ws=[],Ft=null,Bt=null,Vt=null,Ur=new Map,$r=new Map,zt=[],Km="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fa(e,t){switch(e){case"focusin":case"focusout":Ft=null;break;case"dragenter":case"dragleave":Bt=null;break;case"mouseover":case"mouseout":Vt=null;break;case"pointerover":case"pointerout":Ur.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":$r.delete(t.pointerId)}}function fr(e,t,n,s,l,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:s,nativeEvent:o,targetContainers:[l]},t!==null&&(t=ls(t),t!==null&&Ai(t)),e):(e.eventSystemFlags|=s,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Ym(e,t,n,s,l){switch(t){case"focusin":return Ft=fr(Ft,e,t,n,s,l),!0;case"dragenter":return Bt=fr(Bt,e,t,n,s,l),!0;case"mouseover":return Vt=fr(Vt,e,t,n,s,l),!0;case"pointerover":var o=l.pointerId;return Ur.set(o,fr(Ur.get(o)||null,e,t,n,s,l)),!0;case"gotpointercapture":return o=l.pointerId,$r.set(o,fr($r.get(o)||null,e,t,n,s,l)),!0}return!1}function zd(e){var t=ln(e.target);if(t!==null){var n=Nn(t);if(n!==null){if(t=n.tag,t===13){if(t=kd(n),t!==null){e.blockedOn=t,Rd(e.priority,function(){Ld(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ps(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Lo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var s=new n.constructor(n.type,n);Mo=s,n.target.dispatchEvent(s),Mo=null}else return t=ls(n),t!==null&&Ai(t),e.blockedOn=n,!1;t.shift()}return!0}function Ba(e,t,n){Ps(e)&&n.delete(t)}function Xm(){To=!1,Ft!==null&&Ps(Ft)&&(Ft=null),Bt!==null&&Ps(Bt)&&(Bt=null),Vt!==null&&Ps(Vt)&&(Vt=null),Ur.forEach(Ba),$r.forEach(Ba)}function hr(e,t){e.blockedOn===t&&(e.blockedOn=null,To||(To=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Xm)))}function Fr(e){function t(l){return hr(l,e)}if(0<ws.length){hr(ws[0],e);for(var n=1;n<ws.length;n++){var s=ws[n];s.blockedOn===e&&(s.blockedOn=null)}}for(Ft!==null&&hr(Ft,e),Bt!==null&&hr(Bt,e),Vt!==null&&hr(Vt,e),Ur.forEach(t),$r.forEach(t),n=0;n<zt.length;n++)s=zt[n],s.blockedOn===e&&(s.blockedOn=null);for(;0<zt.length&&(n=zt[0],n.blockedOn===null);)zd(n),n.blockedOn===null&&zt.shift()}var Gn=Tt.ReactCurrentBatchConfig,Ks=!0;function ef(e,t,n,s){var l=ne,o=Gn.transition;Gn.transition=null;try{ne=1,_i(e,t,n,s)}finally{ne=l,Gn.transition=o}}function tf(e,t,n,s){var l=ne,o=Gn.transition;Gn.transition=null;try{ne=4,_i(e,t,n,s)}finally{ne=l,Gn.transition=o}}function _i(e,t,n,s){if(Ks){var l=Lo(e,t,n,s);if(l===null)eo(e,t,s,Ys,n),Fa(e,s);else if(Ym(l,e,t,n,s))s.stopPropagation();else if(Fa(e,s),t&4&&-1<Km.indexOf(e)){for(;l!==null;){var o=ls(l);if(o!==null&&Td(o),o=Lo(e,t,n,s),o===null&&eo(e,t,s,Ys,n),o===l)break;l=o}l!==null&&s.stopPropagation()}else eo(e,t,s,null,n)}}var Ys=null;function Lo(e,t,n,s){if(Ys=null,e=Mi(s),e=ln(e),e!==null)if(t=Nn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=kd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ys=e,null}function Od(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vm()){case Ei:return 1;case Ed:return 4;case Js:case qm:return 16;case Id:return 536870912;default:return 16}default:return 16}}var Ut=null,Ti=null,Rs=null;function Ud(){if(Rs)return Rs;var e,t=Ti,n=t.length,s,l="value"in Ut?Ut.value:Ut.textContent,o=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(s=1;s<=i&&t[n-s]===l[o-s];s++);return Rs=l.slice(e,1<s?1-s:void 0)}function zs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function bs(){return!0}function Va(){return!1}function Ze(e){function t(n,s,l,o,i){this._reactName=n,this._targetInst=l,this.type=s,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?bs:Va,this.isPropagationStopped=Va,this}return ue(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=bs)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=bs)},persist:function(){},isPersistent:bs}),t}var lr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Li=Ze(lr),ss=ue({},lr,{view:0,detail:0}),nf=Ze(ss),Wl,Ql,pr,yl=ue({},ss,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Pi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==pr&&(pr&&e.type==="mousemove"?(Wl=e.screenX-pr.screenX,Ql=e.screenY-pr.screenY):Ql=Wl=0,pr=e),Wl)},movementY:function(e){return"movementY"in e?e.movementY:Ql}}),qa=Ze(yl),rf=ue({},yl,{dataTransfer:0}),sf=Ze(rf),lf=ue({},ss,{relatedTarget:0}),Gl=Ze(lf),of=ue({},lr,{animationName:0,elapsedTime:0,pseudoElement:0}),af=Ze(of),cf=ue({},lr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),df=Ze(cf),uf=ue({},lr,{data:0}),Ha=Ze(uf),mf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ff={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=hf[e])?!!t[e]:!1}function Pi(){return pf}var gf=ue({},ss,{key:function(e){if(e.key){var t=mf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=zs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?ff[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Pi,charCode:function(e){return e.type==="keypress"?zs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?zs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),xf=Ze(gf),yf=ue({},yl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wa=Ze(yf),vf=ue({},ss,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Pi}),wf=Ze(vf),bf=ue({},lr,{propertyName:0,elapsedTime:0,pseudoElement:0}),jf=Ze(bf),Nf=ue({},yl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),kf=Ze(Nf),Sf=[9,13,27,32],Ri=Et&&"CompositionEvent"in window,Sr=null;Et&&"documentMode"in document&&(Sr=document.documentMode);var Cf=Et&&"TextEvent"in window&&!Sr,$d=Et&&(!Ri||Sr&&8<Sr&&11>=Sr),Qa=" ",Ga=!1;function Fd(e,t){switch(e){case"keyup":return Sf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Pn=!1;function Mf(e,t){switch(e){case"compositionend":return Bd(t);case"keypress":return t.which!==32?null:(Ga=!0,Qa);case"textInput":return e=t.data,e===Qa&&Ga?null:e;default:return null}}function Ef(e,t){if(Pn)return e==="compositionend"||!Ri&&Fd(e,t)?(e=Ud(),Rs=Ti=Ut=null,Pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return $d&&t.locale!=="ko"?null:t.data;default:return null}}var If={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Da(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!If[e.type]:t==="textarea"}function Vd(e,t,n,s){vd(s),t=Xs(t,"onChange"),0<t.length&&(n=new Li("onChange","change",null,n,s),e.push({event:n,listeners:t}))}var Cr=null,Br=null;function Af(e){Xd(e,0)}function vl(e){var t=On(e);if(md(t))return e}function _f(e,t){if(e==="change")return t}var qd=!1;if(Et){var Dl;if(Et){var Jl="oninput"in document;if(!Jl){var Ja=document.createElement("div");Ja.setAttribute("oninput","return;"),Jl=typeof Ja.oninput=="function"}Dl=Jl}else Dl=!1;qd=Dl&&(!document.documentMode||9<document.documentMode)}function Za(){Cr&&(Cr.detachEvent("onpropertychange",Hd),Br=Cr=null)}function Hd(e){if(e.propertyName==="value"&&vl(Br)){var t=[];Vd(t,Br,e,Mi(e)),Nd(Af,t)}}function Tf(e,t,n){e==="focusin"?(Za(),Cr=t,Br=n,Cr.attachEvent("onpropertychange",Hd)):e==="focusout"&&Za()}function Lf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return vl(Br)}function Pf(e,t){if(e==="click")return vl(t)}function Rf(e,t){if(e==="input"||e==="change")return vl(t)}function zf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ut=typeof Object.is=="function"?Object.is:zf;function Vr(e,t){if(ut(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),s=Object.keys(t);if(n.length!==s.length)return!1;for(s=0;s<n.length;s++){var l=n[s];if(!po.call(t,l)||!ut(e[l],t[l]))return!1}return!0}function Ka(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ya(e,t){var n=Ka(e);e=0;for(var s;n;){if(n.nodeType===3){if(s=e+n.textContent.length,e<=t&&s>=t)return{node:n,offset:t-e};e=s}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ka(n)}}function Wd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Wd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Qd(){for(var e=window,t=Qs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Qs(e.document)}return t}function zi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Of(e){var t=Qd(),n=e.focusedElem,s=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Wd(n.ownerDocument.documentElement,n)){if(s!==null&&zi(n)){if(t=s.start,e=s.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,o=Math.min(s.start,l);s=s.end===void 0?o:Math.min(s.end,l),!e.extend&&o>s&&(l=s,s=o,o=l),l=Ya(n,o);var i=Ya(n,s);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),o>s?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Uf=Et&&"documentMode"in document&&11>=document.documentMode,Rn=null,Po=null,Mr=null,Ro=!1;function Xa(e,t,n){var s=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ro||Rn==null||Rn!==Qs(s)||(s=Rn,"selectionStart"in s&&zi(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Mr&&Vr(Mr,s)||(Mr=s,s=Xs(Po,"onSelect"),0<s.length&&(t=new Li("onSelect","select",null,t,n),e.push({event:t,listeners:s}),t.target=Rn)))}function js(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var zn={animationend:js("Animation","AnimationEnd"),animationiteration:js("Animation","AnimationIteration"),animationstart:js("Animation","AnimationStart"),transitionend:js("Transition","TransitionEnd")},Zl={},Gd={};Et&&(Gd=document.createElement("div").style,"AnimationEvent"in window||(delete zn.animationend.animation,delete zn.animationiteration.animation,delete zn.animationstart.animation),"TransitionEvent"in window||delete zn.transitionend.transition);function wl(e){if(Zl[e])return Zl[e];if(!zn[e])return e;var t=zn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gd)return Zl[e]=t[n];return e}var Dd=wl("animationend"),Jd=wl("animationiteration"),Zd=wl("animationstart"),Kd=wl("transitionend"),Yd=new Map,ec="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Zt(e,t){Yd.set(e,t),jn(t,[e])}for(var Kl=0;Kl<ec.length;Kl++){var Yl=ec[Kl],$f=Yl.toLowerCase(),Ff=Yl[0].toUpperCase()+Yl.slice(1);Zt($f,"on"+Ff)}Zt(Dd,"onAnimationEnd");Zt(Jd,"onAnimationIteration");Zt(Zd,"onAnimationStart");Zt("dblclick","onDoubleClick");Zt("focusin","onFocus");Zt("focusout","onBlur");Zt(Kd,"onTransitionEnd");Zn("onMouseEnter",["mouseout","mouseover"]);Zn("onMouseLeave",["mouseout","mouseover"]);Zn("onPointerEnter",["pointerout","pointerover"]);Zn("onPointerLeave",["pointerout","pointerover"]);jn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));jn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));jn("onBeforeInput",["compositionend","keypress","textInput","paste"]);jn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));jn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));jn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var jr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Bf=new Set("cancel close invalid load scroll toggle".split(" ").concat(jr));function tc(e,t,n){var s=e.type||"unknown-event";e.currentTarget=n,Um(s,t,void 0,e),e.currentTarget=null}function Xd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var s=e[n],l=s.event;s=s.listeners;e:{var o=void 0;if(t)for(var i=s.length-1;0<=i;i--){var a=s[i],c=a.instance,d=a.currentTarget;if(a=a.listener,c!==o&&l.isPropagationStopped())break e;tc(l,a,d),o=c}else for(i=0;i<s.length;i++){if(a=s[i],c=a.instance,d=a.currentTarget,a=a.listener,c!==o&&l.isPropagationStopped())break e;tc(l,a,d),o=c}}}if(Ds)throw e=Ao,Ds=!1,Ao=null,e}function le(e,t){var n=t[Fo];n===void 0&&(n=t[Fo]=new Set);var s=e+"__bubble";n.has(s)||(eu(t,e,2,!1),n.add(s))}function Xl(e,t,n){var s=0;t&&(s|=4),eu(n,e,s,t)}var Ns="_reactListening"+Math.random().toString(36).slice(2);function qr(e){if(!e[Ns]){e[Ns]=!0,id.forEach(function(n){n!=="selectionchange"&&(Bf.has(n)||Xl(n,!1,e),Xl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ns]||(t[Ns]=!0,Xl("selectionchange",!1,t))}}function eu(e,t,n,s){switch(Od(t)){case 1:var l=ef;break;case 4:l=tf;break;default:l=_i}n=l.bind(null,t,n,e),l=void 0,!Io||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),s?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function eo(e,t,n,s,l){var o=s;if(!(t&1)&&!(t&2)&&s!==null)e:for(;;){if(s===null)return;var i=s.tag;if(i===3||i===4){var a=s.stateNode.containerInfo;if(a===l||a.nodeType===8&&a.parentNode===l)break;if(i===4)for(i=s.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;i=i.return}for(;a!==null;){if(i=ln(a),i===null)return;if(c=i.tag,c===5||c===6){s=o=i;continue e}a=a.parentNode}}s=s.return}Nd(function(){var d=o,g=Mi(n),w=[];e:{var b=Yd.get(e);if(b!==void 0){var N=Li,j=e;switch(e){case"keypress":if(zs(n)===0)break e;case"keydown":case"keyup":N=xf;break;case"focusin":j="focus",N=Gl;break;case"focusout":j="blur",N=Gl;break;case"beforeblur":case"afterblur":N=Gl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=qa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=sf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=wf;break;case Dd:case Jd:case Zd:N=af;break;case Kd:N=jf;break;case"scroll":N=nf;break;case"wheel":N=kf;break;case"copy":case"cut":case"paste":N=df;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=Wa}var k=(t&4)!==0,S=!k&&e==="scroll",m=k?b!==null?b+"Capture":null:b;k=[];for(var u=d,p;u!==null;){p=u;var M=p.stateNode;if(p.tag===5&&M!==null&&(p=M,m!==null&&(M=Or(u,m),M!=null&&k.push(Hr(u,M,p)))),S)break;u=u.return}0<k.length&&(b=new N(b,j,null,n,g),w.push({event:b,listeners:k}))}}if(!(t&7)){e:{if(b=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",b&&n!==Mo&&(j=n.relatedTarget||n.fromElement)&&(ln(j)||j[It]))break e;if((N||b)&&(b=g.window===g?g:(b=g.ownerDocument)?b.defaultView||b.parentWindow:window,N?(j=n.relatedTarget||n.toElement,N=d,j=j?ln(j):null,j!==null&&(S=Nn(j),j!==S||j.tag!==5&&j.tag!==6)&&(j=null)):(N=null,j=d),N!==j)){if(k=qa,M="onMouseLeave",m="onMouseEnter",u="mouse",(e==="pointerout"||e==="pointerover")&&(k=Wa,M="onPointerLeave",m="onPointerEnter",u="pointer"),S=N==null?b:On(N),p=j==null?b:On(j),b=new k(M,u+"leave",N,n,g),b.target=S,b.relatedTarget=p,M=null,ln(g)===d&&(k=new k(m,u+"enter",j,n,g),k.target=p,k.relatedTarget=S,M=k),S=M,N&&j)t:{for(k=N,m=j,u=0,p=k;p;p=_n(p))u++;for(p=0,M=m;M;M=_n(M))p++;for(;0<u-p;)k=_n(k),u--;for(;0<p-u;)m=_n(m),p--;for(;u--;){if(k===m||m!==null&&k===m.alternate)break t;k=_n(k),m=_n(m)}k=null}else k=null;N!==null&&nc(w,b,N,k,!1),j!==null&&S!==null&&nc(w,S,j,k,!0)}}e:{if(b=d?On(d):window,N=b.nodeName&&b.nodeName.toLowerCase(),N==="select"||N==="input"&&b.type==="file")var L=_f;else if(Da(b))if(qd)L=Rf;else{L=Lf;var R=Tf}else(N=b.nodeName)&&N.toLowerCase()==="input"&&(b.type==="checkbox"||b.type==="radio")&&(L=Pf);if(L&&(L=L(e,d))){Vd(w,L,n,g);break e}R&&R(e,b,d),e==="focusout"&&(R=b._wrapperState)&&R.controlled&&b.type==="number"&&jo(b,"number",b.value)}switch(R=d?On(d):window,e){case"focusin":(Da(R)||R.contentEditable==="true")&&(Rn=R,Po=d,Mr=null);break;case"focusout":Mr=Po=Rn=null;break;case"mousedown":Ro=!0;break;case"contextmenu":case"mouseup":case"dragend":Ro=!1,Xa(w,n,g);break;case"selectionchange":if(Uf)break;case"keydown":case"keyup":Xa(w,n,g)}var z;if(Ri)e:{switch(e){case"compositionstart":var U="onCompositionStart";break e;case"compositionend":U="onCompositionEnd";break e;case"compositionupdate":U="onCompositionUpdate";break e}U=void 0}else Pn?Fd(e,n)&&(U="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(U="onCompositionStart");U&&($d&&n.locale!=="ko"&&(Pn||U!=="onCompositionStart"?U==="onCompositionEnd"&&Pn&&(z=Ud()):(Ut=g,Ti="value"in Ut?Ut.value:Ut.textContent,Pn=!0)),R=Xs(d,U),0<R.length&&(U=new Ha(U,e,null,n,g),w.push({event:U,listeners:R}),z?U.data=z:(z=Bd(n),z!==null&&(U.data=z)))),(z=Cf?Mf(e,n):Ef(e,n))&&(d=Xs(d,"onBeforeInput"),0<d.length&&(g=new Ha("onBeforeInput","beforeinput",null,n,g),w.push({event:g,listeners:d}),g.data=z))}Xd(w,t)})}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Xs(e,t){for(var n=t+"Capture",s=[];e!==null;){var l=e,o=l.stateNode;l.tag===5&&o!==null&&(l=o,o=Or(e,n),o!=null&&s.unshift(Hr(e,o,l)),o=Or(e,t),o!=null&&s.push(Hr(e,o,l))),e=e.return}return s}function _n(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function nc(e,t,n,s,l){for(var o=t._reactName,i=[];n!==null&&n!==s;){var a=n,c=a.alternate,d=a.stateNode;if(c!==null&&c===s)break;a.tag===5&&d!==null&&(a=d,l?(c=Or(n,o),c!=null&&i.unshift(Hr(n,c,a))):l||(c=Or(n,o),c!=null&&i.push(Hr(n,c,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Vf=/\r\n?/g,qf=/\u0000|\uFFFD/g;function rc(e){return(typeof e=="string"?e:""+e).replace(Vf,`
`).replace(qf,"")}function ks(e,t,n){if(t=rc(t),rc(e)!==t&&n)throw Error(_(425))}function el(){}var zo=null,Oo=null;function Uo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var $o=typeof setTimeout=="function"?setTimeout:void 0,Hf=typeof clearTimeout=="function"?clearTimeout:void 0,sc=typeof Promise=="function"?Promise:void 0,Wf=typeof queueMicrotask=="function"?queueMicrotask:typeof sc<"u"?function(e){return sc.resolve(null).then(e).catch(Qf)}:$o;function Qf(e){setTimeout(function(){throw e})}function to(e,t){var n=t,s=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(s===0){e.removeChild(l),Fr(t);return}s--}else n!=="$"&&n!=="$?"&&n!=="$!"||s++;n=l}while(n);Fr(t)}function qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function lc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var or=Math.random().toString(36).slice(2),xt="__reactFiber$"+or,Wr="__reactProps$"+or,It="__reactContainer$"+or,Fo="__reactEvents$"+or,Gf="__reactListeners$"+or,Df="__reactHandles$"+or;function ln(e){var t=e[xt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[It]||n[xt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=lc(e);e!==null;){if(n=e[xt])return n;e=lc(e)}return t}e=n,n=e.parentNode}return null}function ls(e){return e=e[xt]||e[It],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function On(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function bl(e){return e[Wr]||null}var Bo=[],Un=-1;function Kt(e){return{current:e}}function oe(e){0>Un||(e.current=Bo[Un],Bo[Un]=null,Un--)}function re(e,t){Un++,Bo[Un]=e.current,e.current=t}var Jt={},Le=Kt(Jt),Ve=Kt(!1),gn=Jt;function Kn(e,t){var n=e.type.contextTypes;if(!n)return Jt;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===t)return s.__reactInternalMemoizedMaskedChildContext;var l={},o;for(o in n)l[o]=t[o];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function qe(e){return e=e.childContextTypes,e!=null}function tl(){oe(Ve),oe(Le)}function oc(e,t,n){if(Le.current!==Jt)throw Error(_(168));re(Le,t),re(Ve,n)}function tu(e,t,n){var s=e.stateNode;if(t=t.childContextTypes,typeof s.getChildContext!="function")return n;s=s.getChildContext();for(var l in s)if(!(l in t))throw Error(_(108,_m(e)||"Unknown",l));return ue({},n,s)}function nl(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Jt,gn=Le.current,re(Le,e),re(Ve,Ve.current),!0}function ic(e,t,n){var s=e.stateNode;if(!s)throw Error(_(169));n?(e=tu(e,t,gn),s.__reactInternalMemoizedMergedChildContext=e,oe(Ve),oe(Le),re(Le,e)):oe(Ve),re(Ve,n)}var kt=null,jl=!1,no=!1;function nu(e){kt===null?kt=[e]:kt.push(e)}function Jf(e){jl=!0,nu(e)}function Yt(){if(!no&&kt!==null){no=!0;var e=0,t=ne;try{var n=kt;for(ne=1;e<n.length;e++){var s=n[e];do s=s(!0);while(s!==null)}kt=null,jl=!1}catch(l){throw kt!==null&&(kt=kt.slice(e+1)),Md(Ei,Yt),l}finally{ne=t,no=!1}}return null}var $n=[],Fn=0,rl=null,sl=0,Ye=[],Xe=0,xn=null,St=1,Ct="";function rn(e,t){$n[Fn++]=sl,$n[Fn++]=rl,rl=e,sl=t}function ru(e,t,n){Ye[Xe++]=St,Ye[Xe++]=Ct,Ye[Xe++]=xn,xn=e;var s=St;e=Ct;var l=32-ct(s)-1;s&=~(1<<l),n+=1;var o=32-ct(t)+l;if(30<o){var i=l-l%5;o=(s&(1<<i)-1).toString(32),s>>=i,l-=i,St=1<<32-ct(t)+l|n<<l|s,Ct=o+e}else St=1<<o|n<<l|s,Ct=e}function Oi(e){e.return!==null&&(rn(e,1),ru(e,1,0))}function Ui(e){for(;e===rl;)rl=$n[--Fn],$n[Fn]=null,sl=$n[--Fn],$n[Fn]=null;for(;e===xn;)xn=Ye[--Xe],Ye[Xe]=null,Ct=Ye[--Xe],Ye[Xe]=null,St=Ye[--Xe],Ye[Xe]=null}var Ge=null,Qe=null,ie=!1,at=null;function su(e,t){var n=et(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ac(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ge=e,Qe=qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ge=e,Qe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=xn!==null?{id:St,overflow:Ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=et(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ge=e,Qe=null,!0):!1;default:return!1}}function Vo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function qo(e){if(ie){var t=Qe;if(t){var n=t;if(!ac(e,t)){if(Vo(e))throw Error(_(418));t=qt(n.nextSibling);var s=Ge;t&&ac(e,t)?su(s,n):(e.flags=e.flags&-4097|2,ie=!1,Ge=e)}}else{if(Vo(e))throw Error(_(418));e.flags=e.flags&-4097|2,ie=!1,Ge=e}}}function cc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ge=e}function Ss(e){if(e!==Ge)return!1;if(!ie)return cc(e),ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Uo(e.type,e.memoizedProps)),t&&(t=Qe)){if(Vo(e))throw lu(),Error(_(418));for(;t;)su(e,t),t=qt(t.nextSibling)}if(cc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Qe=qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Qe=null}}else Qe=Ge?qt(e.stateNode.nextSibling):null;return!0}function lu(){for(var e=Qe;e;)e=qt(e.nextSibling)}function Yn(){Qe=Ge=null,ie=!1}function $i(e){at===null?at=[e]:at.push(e)}var Zf=Tt.ReactCurrentBatchConfig;function gr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var s=n.stateNode}if(!s)throw Error(_(147,e));var l=s,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=l.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function Cs(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function dc(e){var t=e._init;return t(e._payload)}function ou(e){function t(m,u){if(e){var p=m.deletions;p===null?(m.deletions=[u],m.flags|=16):p.push(u)}}function n(m,u){if(!e)return null;for(;u!==null;)t(m,u),u=u.sibling;return null}function s(m,u){for(m=new Map;u!==null;)u.key!==null?m.set(u.key,u):m.set(u.index,u),u=u.sibling;return m}function l(m,u){return m=Gt(m,u),m.index=0,m.sibling=null,m}function o(m,u,p){return m.index=p,e?(p=m.alternate,p!==null?(p=p.index,p<u?(m.flags|=2,u):p):(m.flags|=2,u)):(m.flags|=1048576,u)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,u,p,M){return u===null||u.tag!==6?(u=co(p,m.mode,M),u.return=m,u):(u=l(u,p),u.return=m,u)}function c(m,u,p,M){var L=p.type;return L===Ln?g(m,u,p.props.children,M,p.key):u!==null&&(u.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Pt&&dc(L)===u.type)?(M=l(u,p.props),M.ref=gr(m,u,p),M.return=m,M):(M=qs(p.type,p.key,p.props,null,m.mode,M),M.ref=gr(m,u,p),M.return=m,M)}function d(m,u,p,M){return u===null||u.tag!==4||u.stateNode.containerInfo!==p.containerInfo||u.stateNode.implementation!==p.implementation?(u=uo(p,m.mode,M),u.return=m,u):(u=l(u,p.children||[]),u.return=m,u)}function g(m,u,p,M,L){return u===null||u.tag!==7?(u=un(p,m.mode,M,L),u.return=m,u):(u=l(u,p),u.return=m,u)}function w(m,u,p){if(typeof u=="string"&&u!==""||typeof u=="number")return u=co(""+u,m.mode,p),u.return=m,u;if(typeof u=="object"&&u!==null){switch(u.$$typeof){case ps:return p=qs(u.type,u.key,u.props,null,m.mode,p),p.ref=gr(m,null,u),p.return=m,p;case Tn:return u=uo(u,m.mode,p),u.return=m,u;case Pt:var M=u._init;return w(m,M(u._payload),p)}if(wr(u)||ur(u))return u=un(u,m.mode,p,null),u.return=m,u;Cs(m,u)}return null}function b(m,u,p,M){var L=u!==null?u.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return L!==null?null:a(m,u,""+p,M);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ps:return p.key===L?c(m,u,p,M):null;case Tn:return p.key===L?d(m,u,p,M):null;case Pt:return L=p._init,b(m,u,L(p._payload),M)}if(wr(p)||ur(p))return L!==null?null:g(m,u,p,M,null);Cs(m,p)}return null}function N(m,u,p,M,L){if(typeof M=="string"&&M!==""||typeof M=="number")return m=m.get(p)||null,a(u,m,""+M,L);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case ps:return m=m.get(M.key===null?p:M.key)||null,c(u,m,M,L);case Tn:return m=m.get(M.key===null?p:M.key)||null,d(u,m,M,L);case Pt:var R=M._init;return N(m,u,p,R(M._payload),L)}if(wr(M)||ur(M))return m=m.get(p)||null,g(u,m,M,L,null);Cs(u,M)}return null}function j(m,u,p,M){for(var L=null,R=null,z=u,U=u=0,D=null;z!==null&&U<p.length;U++){z.index>U?(D=z,z=null):D=z.sibling;var v=b(m,z,p[U],M);if(v===null){z===null&&(z=D);break}e&&z&&v.alternate===null&&t(m,z),u=o(v,u,U),R===null?L=v:R.sibling=v,R=v,z=D}if(U===p.length)return n(m,z),ie&&rn(m,U),L;if(z===null){for(;U<p.length;U++)z=w(m,p[U],M),z!==null&&(u=o(z,u,U),R===null?L=z:R.sibling=z,R=z);return ie&&rn(m,U),L}for(z=s(m,z);U<p.length;U++)D=N(z,m,U,p[U],M),D!==null&&(e&&D.alternate!==null&&z.delete(D.key===null?U:D.key),u=o(D,u,U),R===null?L=D:R.sibling=D,R=D);return e&&z.forEach(function(O){return t(m,O)}),ie&&rn(m,U),L}function k(m,u,p,M){var L=ur(p);if(typeof L!="function")throw Error(_(150));if(p=L.call(p),p==null)throw Error(_(151));for(var R=L=null,z=u,U=u=0,D=null,v=p.next();z!==null&&!v.done;U++,v=p.next()){z.index>U?(D=z,z=null):D=z.sibling;var O=b(m,z,v.value,M);if(O===null){z===null&&(z=D);break}e&&z&&O.alternate===null&&t(m,z),u=o(O,u,U),R===null?L=O:R.sibling=O,R=O,z=D}if(v.done)return n(m,z),ie&&rn(m,U),L;if(z===null){for(;!v.done;U++,v=p.next())v=w(m,v.value,M),v!==null&&(u=o(v,u,U),R===null?L=v:R.sibling=v,R=v);return ie&&rn(m,U),L}for(z=s(m,z);!v.done;U++,v=p.next())v=N(z,m,U,v.value,M),v!==null&&(e&&v.alternate!==null&&z.delete(v.key===null?U:v.key),u=o(v,u,U),R===null?L=v:R.sibling=v,R=v);return e&&z.forEach(function(X){return t(m,X)}),ie&&rn(m,U),L}function S(m,u,p,M){if(typeof p=="object"&&p!==null&&p.type===Ln&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case ps:e:{for(var L=p.key,R=u;R!==null;){if(R.key===L){if(L=p.type,L===Ln){if(R.tag===7){n(m,R.sibling),u=l(R,p.props.children),u.return=m,m=u;break e}}else if(R.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Pt&&dc(L)===R.type){n(m,R.sibling),u=l(R,p.props),u.ref=gr(m,R,p),u.return=m,m=u;break e}n(m,R);break}else t(m,R);R=R.sibling}p.type===Ln?(u=un(p.props.children,m.mode,M,p.key),u.return=m,m=u):(M=qs(p.type,p.key,p.props,null,m.mode,M),M.ref=gr(m,u,p),M.return=m,m=M)}return i(m);case Tn:e:{for(R=p.key;u!==null;){if(u.key===R)if(u.tag===4&&u.stateNode.containerInfo===p.containerInfo&&u.stateNode.implementation===p.implementation){n(m,u.sibling),u=l(u,p.children||[]),u.return=m,m=u;break e}else{n(m,u);break}else t(m,u);u=u.sibling}u=uo(p,m.mode,M),u.return=m,m=u}return i(m);case Pt:return R=p._init,S(m,u,R(p._payload),M)}if(wr(p))return j(m,u,p,M);if(ur(p))return k(m,u,p,M);Cs(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,u!==null&&u.tag===6?(n(m,u.sibling),u=l(u,p),u.return=m,m=u):(n(m,u),u=co(p,m.mode,M),u.return=m,m=u),i(m)):n(m,u)}return S}var Xn=ou(!0),iu=ou(!1),ll=Kt(null),ol=null,Bn=null,Fi=null;function Bi(){Fi=Bn=ol=null}function Vi(e){var t=ll.current;oe(ll),e._currentValue=t}function Ho(e,t,n){for(;e!==null;){var s=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,s!==null&&(s.childLanes|=t)):s!==null&&(s.childLanes&t)!==t&&(s.childLanes|=t),e===n)break;e=e.return}}function Dn(e,t){ol=e,Fi=Bn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Be=!0),e.firstContext=null)}function nt(e){var t=e._currentValue;if(Fi!==e)if(e={context:e,memoizedValue:t,next:null},Bn===null){if(ol===null)throw Error(_(308));Bn=e,ol.dependencies={lanes:0,firstContext:e}}else Bn=Bn.next=e;return t}var on=null;function qi(e){on===null?on=[e]:on.push(e)}function au(e,t,n,s){var l=t.interleaved;return l===null?(n.next=n,qi(t)):(n.next=l.next,l.next=n),t.interleaved=n,At(e,s)}function At(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Rt=!1;function Hi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function cu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Mt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ht(e,t,n){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,ee&2){var l=s.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),s.pending=t,At(e,n)}return l=s.interleaved,l===null?(t.next=t,qi(s)):(t.next=l.next,l.next=t),s.interleaved=t,At(e,n)}function Os(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,Ii(e,n)}}function uc(e,t){var n=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,n===s)){var l=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?l=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?l=o=t:o=o.next=t}else l=o=t;n={baseState:s.baseState,firstBaseUpdate:l,lastBaseUpdate:o,shared:s.shared,effects:s.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function il(e,t,n,s){var l=e.updateQueue;Rt=!1;var o=l.firstBaseUpdate,i=l.lastBaseUpdate,a=l.shared.pending;if(a!==null){l.shared.pending=null;var c=a,d=c.next;c.next=null,i===null?o=d:i.next=d,i=c;var g=e.alternate;g!==null&&(g=g.updateQueue,a=g.lastBaseUpdate,a!==i&&(a===null?g.firstBaseUpdate=d:a.next=d,g.lastBaseUpdate=c))}if(o!==null){var w=l.baseState;i=0,g=d=c=null,a=o;do{var b=a.lane,N=a.eventTime;if((s&b)===b){g!==null&&(g=g.next={eventTime:N,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var j=e,k=a;switch(b=t,N=n,k.tag){case 1:if(j=k.payload,typeof j=="function"){w=j.call(N,w,b);break e}w=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=k.payload,b=typeof j=="function"?j.call(N,w,b):j,b==null)break e;w=ue({},w,b);break e;case 2:Rt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,b=l.effects,b===null?l.effects=[a]:b.push(a))}else N={eventTime:N,lane:b,tag:a.tag,payload:a.payload,callback:a.callback,next:null},g===null?(d=g=N,c=w):g=g.next=N,i|=b;if(a=a.next,a===null){if(a=l.shared.pending,a===null)break;b=a,a=b.next,b.next=null,l.lastBaseUpdate=b,l.shared.pending=null}}while(!0);if(g===null&&(c=w),l.baseState=c,l.firstBaseUpdate=d,l.lastBaseUpdate=g,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else o===null&&(l.shared.lanes=0);vn|=i,e.lanes=i,e.memoizedState=w}}function mc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var s=e[t],l=s.callback;if(l!==null){if(s.callback=null,s=n,typeof l!="function")throw Error(_(191,l));l.call(s)}}}var os={},wt=Kt(os),Qr=Kt(os),Gr=Kt(os);function an(e){if(e===os)throw Error(_(174));return e}function Wi(e,t){switch(re(Gr,t),re(Qr,e),re(wt,os),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ko(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ko(t,e)}oe(wt),re(wt,t)}function er(){oe(wt),oe(Qr),oe(Gr)}function du(e){an(Gr.current);var t=an(wt.current),n=ko(t,e.type);t!==n&&(re(Qr,e),re(wt,n))}function Qi(e){Qr.current===e&&(oe(wt),oe(Qr))}var ce=Kt(0);function al(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ro=[];function Gi(){for(var e=0;e<ro.length;e++)ro[e]._workInProgressVersionPrimary=null;ro.length=0}var Us=Tt.ReactCurrentDispatcher,so=Tt.ReactCurrentBatchConfig,yn=0,de=null,ve=null,je=null,cl=!1,Er=!1,Dr=0,Kf=0;function Ae(){throw Error(_(321))}function Di(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ut(e[n],t[n]))return!1;return!0}function Ji(e,t,n,s,l,o){if(yn=o,de=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Us.current=e===null||e.memoizedState===null?th:nh,e=n(s,l),Er){o=0;do{if(Er=!1,Dr=0,25<=o)throw Error(_(301));o+=1,je=ve=null,t.updateQueue=null,Us.current=rh,e=n(s,l)}while(Er)}if(Us.current=dl,t=ve!==null&&ve.next!==null,yn=0,je=ve=de=null,cl=!1,t)throw Error(_(300));return e}function Zi(){var e=Dr!==0;return Dr=0,e}function gt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return je===null?de.memoizedState=je=e:je=je.next=e,je}function rt(){if(ve===null){var e=de.alternate;e=e!==null?e.memoizedState:null}else e=ve.next;var t=je===null?de.memoizedState:je.next;if(t!==null)je=t,ve=e;else{if(e===null)throw Error(_(310));ve=e,e={memoizedState:ve.memoizedState,baseState:ve.baseState,baseQueue:ve.baseQueue,queue:ve.queue,next:null},je===null?de.memoizedState=je=e:je=je.next=e}return je}function Jr(e,t){return typeof t=="function"?t(e):t}function lo(e){var t=rt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var s=ve,l=s.baseQueue,o=n.pending;if(o!==null){if(l!==null){var i=l.next;l.next=o.next,o.next=i}s.baseQueue=l=o,n.pending=null}if(l!==null){o=l.next,s=s.baseState;var a=i=null,c=null,d=o;do{var g=d.lane;if((yn&g)===g)c!==null&&(c=c.next={lane:0,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null}),s=d.hasEagerState?d.eagerState:e(s,d.action);else{var w={lane:g,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null};c===null?(a=c=w,i=s):c=c.next=w,de.lanes|=g,vn|=g}d=d.next}while(d!==null&&d!==o);c===null?i=s:c.next=a,ut(s,t.memoizedState)||(Be=!0),t.memoizedState=s,t.baseState=i,t.baseQueue=c,n.lastRenderedState=s}if(e=n.interleaved,e!==null){l=e;do o=l.lane,de.lanes|=o,vn|=o,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function oo(e){var t=rt(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var s=n.dispatch,l=n.pending,o=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do o=e(o,i.action),i=i.next;while(i!==l);ut(o,t.memoizedState)||(Be=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,s]}function uu(){}function mu(e,t){var n=de,s=rt(),l=t(),o=!ut(s.memoizedState,l);if(o&&(s.memoizedState=l,Be=!0),s=s.queue,Ki(pu.bind(null,n,s,e),[e]),s.getSnapshot!==t||o||je!==null&&je.memoizedState.tag&1){if(n.flags|=2048,Zr(9,hu.bind(null,n,s,l,t),void 0,null),Ne===null)throw Error(_(349));yn&30||fu(n,t,l)}return l}function fu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=de.updateQueue,t===null?(t={lastEffect:null,stores:null},de.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function hu(e,t,n,s){t.value=n,t.getSnapshot=s,gu(t)&&xu(e)}function pu(e,t,n){return n(function(){gu(t)&&xu(e)})}function gu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ut(e,n)}catch{return!0}}function xu(e){var t=At(e,1);t!==null&&dt(t,e,1,-1)}function fc(e){var t=gt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Jr,lastRenderedState:e},t.queue=e,e=e.dispatch=eh.bind(null,de,e),[t.memoizedState,e]}function Zr(e,t,n,s){return e={tag:e,create:t,destroy:n,deps:s,next:null},t=de.updateQueue,t===null?(t={lastEffect:null,stores:null},de.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(s=n.next,n.next=e,e.next=s,t.lastEffect=e)),e}function yu(){return rt().memoizedState}function $s(e,t,n,s){var l=gt();de.flags|=e,l.memoizedState=Zr(1|t,n,void 0,s===void 0?null:s)}function Nl(e,t,n,s){var l=rt();s=s===void 0?null:s;var o=void 0;if(ve!==null){var i=ve.memoizedState;if(o=i.destroy,s!==null&&Di(s,i.deps)){l.memoizedState=Zr(t,n,o,s);return}}de.flags|=e,l.memoizedState=Zr(1|t,n,o,s)}function hc(e,t){return $s(8390656,8,e,t)}function Ki(e,t){return Nl(2048,8,e,t)}function vu(e,t){return Nl(4,2,e,t)}function wu(e,t){return Nl(4,4,e,t)}function bu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ju(e,t,n){return n=n!=null?n.concat([e]):null,Nl(4,4,bu.bind(null,t,e),n)}function Yi(){}function Nu(e,t){var n=rt();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&Di(t,s[1])?s[0]:(n.memoizedState=[e,t],e)}function ku(e,t){var n=rt();t=t===void 0?null:t;var s=n.memoizedState;return s!==null&&t!==null&&Di(t,s[1])?s[0]:(e=e(),n.memoizedState=[e,t],e)}function Su(e,t,n){return yn&21?(ut(n,t)||(n=Ad(),de.lanes|=n,vn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Be=!0),e.memoizedState=n)}function Yf(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var s=so.transition;so.transition={};try{e(!1),t()}finally{ne=n,so.transition=s}}function Cu(){return rt().memoizedState}function Xf(e,t,n){var s=Qt(e);if(n={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null},Mu(e))Eu(t,n);else if(n=au(e,t,n,s),n!==null){var l=Oe();dt(n,e,s,l),Iu(n,t,s)}}function eh(e,t,n){var s=Qt(e),l={lane:s,action:n,hasEagerState:!1,eagerState:null,next:null};if(Mu(e))Eu(t,l);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(l.hasEagerState=!0,l.eagerState=a,ut(a,i)){var c=t.interleaved;c===null?(l.next=l,qi(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=au(e,t,l,s),n!==null&&(l=Oe(),dt(n,e,s,l),Iu(n,t,s))}}function Mu(e){var t=e.alternate;return e===de||t!==null&&t===de}function Eu(e,t){Er=cl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Iu(e,t,n){if(n&4194240){var s=t.lanes;s&=e.pendingLanes,n|=s,t.lanes=n,Ii(e,n)}}var dl={readContext:nt,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useInsertionEffect:Ae,useLayoutEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useMutableSource:Ae,useSyncExternalStore:Ae,useId:Ae,unstable_isNewReconciler:!1},th={readContext:nt,useCallback:function(e,t){return gt().memoizedState=[e,t===void 0?null:t],e},useContext:nt,useEffect:hc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,$s(4194308,4,bu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return $s(4194308,4,e,t)},useInsertionEffect:function(e,t){return $s(4,2,e,t)},useMemo:function(e,t){var n=gt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var s=gt();return t=n!==void 0?n(t):t,s.memoizedState=s.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},s.queue=e,e=e.dispatch=Xf.bind(null,de,e),[s.memoizedState,e]},useRef:function(e){var t=gt();return e={current:e},t.memoizedState=e},useState:fc,useDebugValue:Yi,useDeferredValue:function(e){return gt().memoizedState=e},useTransition:function(){var e=fc(!1),t=e[0];return e=Yf.bind(null,e[1]),gt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var s=de,l=gt();if(ie){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),Ne===null)throw Error(_(349));yn&30||fu(s,t,n)}l.memoizedState=n;var o={value:n,getSnapshot:t};return l.queue=o,hc(pu.bind(null,s,o,e),[e]),s.flags|=2048,Zr(9,hu.bind(null,s,o,n,t),void 0,null),n},useId:function(){var e=gt(),t=Ne.identifierPrefix;if(ie){var n=Ct,s=St;n=(s&~(1<<32-ct(s)-1)).toString(32)+n,t=":"+t+"R"+n,n=Dr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Kf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},nh={readContext:nt,useCallback:Nu,useContext:nt,useEffect:Ki,useImperativeHandle:ju,useInsertionEffect:vu,useLayoutEffect:wu,useMemo:ku,useReducer:lo,useRef:yu,useState:function(){return lo(Jr)},useDebugValue:Yi,useDeferredValue:function(e){var t=rt();return Su(t,ve.memoizedState,e)},useTransition:function(){var e=lo(Jr)[0],t=rt().memoizedState;return[e,t]},useMutableSource:uu,useSyncExternalStore:mu,useId:Cu,unstable_isNewReconciler:!1},rh={readContext:nt,useCallback:Nu,useContext:nt,useEffect:Ki,useImperativeHandle:ju,useInsertionEffect:vu,useLayoutEffect:wu,useMemo:ku,useReducer:oo,useRef:yu,useState:function(){return oo(Jr)},useDebugValue:Yi,useDeferredValue:function(e){var t=rt();return ve===null?t.memoizedState=e:Su(t,ve.memoizedState,e)},useTransition:function(){var e=oo(Jr)[0],t=rt().memoizedState;return[e,t]},useMutableSource:uu,useSyncExternalStore:mu,useId:Cu,unstable_isNewReconciler:!1};function ot(e,t){if(e&&e.defaultProps){t=ue({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Wo(e,t,n,s){t=e.memoizedState,n=n(s,t),n=n==null?t:ue({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var kl={isMounted:function(e){return(e=e._reactInternals)?Nn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var s=Oe(),l=Qt(e),o=Mt(s,l);o.payload=t,n!=null&&(o.callback=n),t=Ht(e,o,l),t!==null&&(dt(t,e,l,s),Os(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var s=Oe(),l=Qt(e),o=Mt(s,l);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Ht(e,o,l),t!==null&&(dt(t,e,l,s),Os(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Oe(),s=Qt(e),l=Mt(n,s);l.tag=2,t!=null&&(l.callback=t),t=Ht(e,l,s),t!==null&&(dt(t,e,s,n),Os(t,e,s))}};function pc(e,t,n,s,l,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,o,i):t.prototype&&t.prototype.isPureReactComponent?!Vr(n,s)||!Vr(l,o):!0}function Au(e,t,n){var s=!1,l=Jt,o=t.contextType;return typeof o=="object"&&o!==null?o=nt(o):(l=qe(t)?gn:Le.current,s=t.contextTypes,o=(s=s!=null)?Kn(e,l):Jt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=kl,e.stateNode=t,t._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=o),t}function gc(e,t,n,s){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,s),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,s),t.state!==e&&kl.enqueueReplaceState(t,t.state,null)}function Qo(e,t,n,s){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Hi(e);var o=t.contextType;typeof o=="object"&&o!==null?l.context=nt(o):(o=qe(t)?gn:Le.current,l.context=Kn(e,o)),l.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Wo(e,t,o,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&kl.enqueueReplaceState(l,l.state,null),il(e,n,l,s),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function tr(e,t){try{var n="",s=t;do n+=Am(s),s=s.return;while(s);var l=n}catch(o){l=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:l,digest:null}}function io(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Go(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var sh=typeof WeakMap=="function"?WeakMap:Map;function _u(e,t,n){n=Mt(-1,n),n.tag=3,n.payload={element:null};var s=t.value;return n.callback=function(){ml||(ml=!0,ri=s),Go(e,t)},n}function Tu(e,t,n){n=Mt(-1,n),n.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var l=t.value;n.payload=function(){return s(l)},n.callback=function(){Go(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){Go(e,t),typeof s!="function"&&(Wt===null?Wt=new Set([this]):Wt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function xc(e,t,n){var s=e.pingCache;if(s===null){s=e.pingCache=new sh;var l=new Set;s.set(t,l)}else l=s.get(t),l===void 0&&(l=new Set,s.set(t,l));l.has(n)||(l.add(n),e=yh.bind(null,e,t,n),t.then(e,e))}function yc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vc(e,t,n,s,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Mt(-1,1),t.tag=2,Ht(n,t,1))),n.lanes|=1),e)}var lh=Tt.ReactCurrentOwner,Be=!1;function ze(e,t,n,s){t.child=e===null?iu(t,null,n,s):Xn(t,e.child,n,s)}function wc(e,t,n,s,l){n=n.render;var o=t.ref;return Dn(t,l),s=Ji(e,t,n,s,o,l),n=Zi(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,_t(e,t,l)):(ie&&n&&Oi(t),t.flags|=1,ze(e,t,s,l),t.child)}function bc(e,t,n,s,l){if(e===null){var o=n.type;return typeof o=="function"&&!oa(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Lu(e,t,o,s,l)):(e=qs(n.type,null,s,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&l)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:Vr,n(i,s)&&e.ref===t.ref)return _t(e,t,l)}return t.flags|=1,e=Gt(o,s),e.ref=t.ref,e.return=t,t.child=e}function Lu(e,t,n,s,l){if(e!==null){var o=e.memoizedProps;if(Vr(o,s)&&e.ref===t.ref)if(Be=!1,t.pendingProps=s=o,(e.lanes&l)!==0)e.flags&131072&&(Be=!0);else return t.lanes=e.lanes,_t(e,t,l)}return Do(e,t,n,s,l)}function Pu(e,t,n){var s=t.pendingProps,l=s.children,o=e!==null?e.memoizedState:null;if(s.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},re(qn,We),We|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,re(qn,We),We|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=o!==null?o.baseLanes:n,re(qn,We),We|=s}else o!==null?(s=o.baseLanes|n,t.memoizedState=null):s=n,re(qn,We),We|=s;return ze(e,t,l,n),t.child}function Ru(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Do(e,t,n,s,l){var o=qe(n)?gn:Le.current;return o=Kn(t,o),Dn(t,l),n=Ji(e,t,n,s,o,l),s=Zi(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,_t(e,t,l)):(ie&&s&&Oi(t),t.flags|=1,ze(e,t,n,l),t.child)}function jc(e,t,n,s,l){if(qe(n)){var o=!0;nl(t)}else o=!1;if(Dn(t,l),t.stateNode===null)Fs(e,t),Au(t,n,s),Qo(t,n,s,l),s=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var c=i.context,d=n.contextType;typeof d=="object"&&d!==null?d=nt(d):(d=qe(n)?gn:Le.current,d=Kn(t,d));var g=n.getDerivedStateFromProps,w=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function";w||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==s||c!==d)&&gc(t,i,s,d),Rt=!1;var b=t.memoizedState;i.state=b,il(t,s,i,l),c=t.memoizedState,a!==s||b!==c||Ve.current||Rt?(typeof g=="function"&&(Wo(t,n,g,s),c=t.memoizedState),(a=Rt||pc(t,n,a,s,b,c,d))?(w||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=s,t.memoizedState=c),i.props=s,i.state=c,i.context=d,s=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),s=!1)}else{i=t.stateNode,cu(e,t),a=t.memoizedProps,d=t.type===t.elementType?a:ot(t.type,a),i.props=d,w=t.pendingProps,b=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=nt(c):(c=qe(n)?gn:Le.current,c=Kn(t,c));var N=n.getDerivedStateFromProps;(g=typeof N=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==w||b!==c)&&gc(t,i,s,c),Rt=!1,b=t.memoizedState,i.state=b,il(t,s,i,l);var j=t.memoizedState;a!==w||b!==j||Ve.current||Rt?(typeof N=="function"&&(Wo(t,n,N,s),j=t.memoizedState),(d=Rt||pc(t,n,d,s,b,j,c)||!1)?(g||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(s,j,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(s,j,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=1024),t.memoizedProps=s,t.memoizedState=j),i.props=s,i.state=j,i.context=c,s=d):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&b===e.memoizedState||(t.flags|=1024),s=!1)}return Jo(e,t,n,s,o,l)}function Jo(e,t,n,s,l,o){Ru(e,t);var i=(t.flags&128)!==0;if(!s&&!i)return l&&ic(t,n,!1),_t(e,t,o);s=t.stateNode,lh.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:s.render();return t.flags|=1,e!==null&&i?(t.child=Xn(t,e.child,null,o),t.child=Xn(t,null,a,o)):ze(e,t,a,o),t.memoizedState=s.state,l&&ic(t,n,!0),t.child}function zu(e){var t=e.stateNode;t.pendingContext?oc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&oc(e,t.context,!1),Wi(e,t.containerInfo)}function Nc(e,t,n,s,l){return Yn(),$i(l),t.flags|=256,ze(e,t,n,s),t.child}var Zo={dehydrated:null,treeContext:null,retryLane:0};function Ko(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ou(e,t,n){var s=t.pendingProps,l=ce.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(l&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),re(ce,l&1),e===null)return qo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=s.children,e=s.fallback,o?(s=t.mode,o=t.child,i={mode:"hidden",children:i},!(s&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Ml(i,s,0,null),e=un(e,s,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Ko(n),t.memoizedState=Zo,e):Xi(t,i));if(l=e.memoizedState,l!==null&&(a=l.dehydrated,a!==null))return oh(e,t,i,s,a,l,n);if(o){o=s.fallback,i=t.mode,l=e.child,a=l.sibling;var c={mode:"hidden",children:s.children};return!(i&1)&&t.child!==l?(s=t.child,s.childLanes=0,s.pendingProps=c,t.deletions=null):(s=Gt(l,c),s.subtreeFlags=l.subtreeFlags&14680064),a!==null?o=Gt(a,o):(o=un(o,i,n,null),o.flags|=2),o.return=t,s.return=t,s.sibling=o,t.child=s,s=o,o=t.child,i=e.child.memoizedState,i=i===null?Ko(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=Zo,s}return o=e.child,e=o.sibling,s=Gt(o,{mode:"visible",children:s.children}),!(t.mode&1)&&(s.lanes=n),s.return=t,s.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=s,t.memoizedState=null,s}function Xi(e,t){return t=Ml({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ms(e,t,n,s){return s!==null&&$i(s),Xn(t,e.child,null,n),e=Xi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function oh(e,t,n,s,l,o,i){if(n)return t.flags&256?(t.flags&=-257,s=io(Error(_(422))),Ms(e,t,i,s)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=s.fallback,l=t.mode,s=Ml({mode:"visible",children:s.children},l,0,null),o=un(o,l,i,null),o.flags|=2,s.return=t,o.return=t,s.sibling=o,t.child=s,t.mode&1&&Xn(t,e.child,null,i),t.child.memoizedState=Ko(i),t.memoizedState=Zo,o);if(!(t.mode&1))return Ms(e,t,i,null);if(l.data==="$!"){if(s=l.nextSibling&&l.nextSibling.dataset,s)var a=s.dgst;return s=a,o=Error(_(419)),s=io(o,s,void 0),Ms(e,t,i,s)}if(a=(i&e.childLanes)!==0,Be||a){if(s=Ne,s!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(s.suspendedLanes|i)?0:l,l!==0&&l!==o.retryLane&&(o.retryLane=l,At(e,l),dt(s,e,l,-1))}return la(),s=io(Error(_(421))),Ms(e,t,i,s)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=vh.bind(null,e),l._reactRetry=t,null):(e=o.treeContext,Qe=qt(l.nextSibling),Ge=t,ie=!0,at=null,e!==null&&(Ye[Xe++]=St,Ye[Xe++]=Ct,Ye[Xe++]=xn,St=e.id,Ct=e.overflow,xn=t),t=Xi(t,s.children),t.flags|=4096,t)}function kc(e,t,n){e.lanes|=t;var s=e.alternate;s!==null&&(s.lanes|=t),Ho(e.return,t,n)}function ao(e,t,n,s,l){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:s,tail:n,tailMode:l}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=s,o.tail=n,o.tailMode=l)}function Uu(e,t,n){var s=t.pendingProps,l=s.revealOrder,o=s.tail;if(ze(e,t,s.children,n),s=ce.current,s&2)s=s&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&kc(e,n,t);else if(e.tag===19)kc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(re(ce,s),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&al(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),ao(t,!1,l,n,o);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&al(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}ao(t,!0,n,null,o);break;case"together":ao(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fs(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function _t(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),vn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=Gt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Gt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ih(e,t,n){switch(t.tag){case 3:zu(t),Yn();break;case 5:du(t);break;case 1:qe(t.type)&&nl(t);break;case 4:Wi(t,t.stateNode.containerInfo);break;case 10:var s=t.type._context,l=t.memoizedProps.value;re(ll,s._currentValue),s._currentValue=l;break;case 13:if(s=t.memoizedState,s!==null)return s.dehydrated!==null?(re(ce,ce.current&1),t.flags|=128,null):n&t.child.childLanes?Ou(e,t,n):(re(ce,ce.current&1),e=_t(e,t,n),e!==null?e.sibling:null);re(ce,ce.current&1);break;case 19:if(s=(n&t.childLanes)!==0,e.flags&128){if(s)return Uu(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),re(ce,ce.current),s)break;return null;case 22:case 23:return t.lanes=0,Pu(e,t,n)}return _t(e,t,n)}var $u,Yo,Fu,Bu;$u=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Yo=function(){};Fu=function(e,t,n,s){var l=e.memoizedProps;if(l!==s){e=t.stateNode,an(wt.current);var o=null;switch(n){case"input":l=wo(e,l),s=wo(e,s),o=[];break;case"select":l=ue({},l,{value:void 0}),s=ue({},s,{value:void 0}),o=[];break;case"textarea":l=No(e,l),s=No(e,s),o=[];break;default:typeof l.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=el)}So(n,s);var i;n=null;for(d in l)if(!s.hasOwnProperty(d)&&l.hasOwnProperty(d)&&l[d]!=null)if(d==="style"){var a=l[d];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else d!=="dangerouslySetInnerHTML"&&d!=="children"&&d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(Rr.hasOwnProperty(d)?o||(o=[]):(o=o||[]).push(d,null));for(d in s){var c=s[d];if(a=l!=null?l[d]:void 0,s.hasOwnProperty(d)&&c!==a&&(c!=null||a!=null))if(d==="style")if(a){for(i in a)!a.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&a[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(o||(o=[]),o.push(d,n)),n=c;else d==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,a=a?a.__html:void 0,c!=null&&a!==c&&(o=o||[]).push(d,c)):d==="children"?typeof c!="string"&&typeof c!="number"||(o=o||[]).push(d,""+c):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&(Rr.hasOwnProperty(d)?(c!=null&&d==="onScroll"&&le("scroll",e),o||a===c||(o=[])):(o=o||[]).push(d,c))}n&&(o=o||[]).push("style",n);var d=o;(t.updateQueue=d)&&(t.flags|=4)}};Bu=function(e,t,n,s){n!==s&&(t.flags|=4)};function xr(e,t){if(!ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function _e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,s=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags&14680064,s|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,s|=l.subtreeFlags,s|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=s,e.childLanes=n,t}function ah(e,t,n){var s=t.pendingProps;switch(Ui(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _e(t),null;case 1:return qe(t.type)&&tl(),_e(t),null;case 3:return s=t.stateNode,er(),oe(Ve),oe(Le),Gi(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(Ss(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,at!==null&&(oi(at),at=null))),Yo(e,t),_e(t),null;case 5:Qi(t);var l=an(Gr.current);if(n=t.type,e!==null&&t.stateNode!=null)Fu(e,t,n,s,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!s){if(t.stateNode===null)throw Error(_(166));return _e(t),null}if(e=an(wt.current),Ss(t)){s=t.stateNode,n=t.type;var o=t.memoizedProps;switch(s[xt]=t,s[Wr]=o,e=(t.mode&1)!==0,n){case"dialog":le("cancel",s),le("close",s);break;case"iframe":case"object":case"embed":le("load",s);break;case"video":case"audio":for(l=0;l<jr.length;l++)le(jr[l],s);break;case"source":le("error",s);break;case"img":case"image":case"link":le("error",s),le("load",s);break;case"details":le("toggle",s);break;case"input":La(s,o),le("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!o.multiple},le("invalid",s);break;case"textarea":Ra(s,o),le("invalid",s)}So(n,o),l=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?s.textContent!==a&&(o.suppressHydrationWarning!==!0&&ks(s.textContent,a,e),l=["children",a]):typeof a=="number"&&s.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&ks(s.textContent,a,e),l=["children",""+a]):Rr.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&le("scroll",s)}switch(n){case"input":gs(s),Pa(s,o,!0);break;case"textarea":gs(s),za(s);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(s.onclick=el)}s=l,t.updateQueue=s,s!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=pd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=i.createElement(n,{is:s.is}):(e=i.createElement(n),n==="select"&&(i=e,s.multiple?i.multiple=!0:s.size&&(i.size=s.size))):e=i.createElementNS(e,n),e[xt]=t,e[Wr]=s,$u(e,t,!1,!1),t.stateNode=e;e:{switch(i=Co(n,s),n){case"dialog":le("cancel",e),le("close",e),l=s;break;case"iframe":case"object":case"embed":le("load",e),l=s;break;case"video":case"audio":for(l=0;l<jr.length;l++)le(jr[l],e);l=s;break;case"source":le("error",e),l=s;break;case"img":case"image":case"link":le("error",e),le("load",e),l=s;break;case"details":le("toggle",e),l=s;break;case"input":La(e,s),l=wo(e,s),le("invalid",e);break;case"option":l=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},l=ue({},s,{value:void 0}),le("invalid",e);break;case"textarea":Ra(e,s),l=No(e,s),le("invalid",e);break;default:l=s}So(n,l),a=l;for(o in a)if(a.hasOwnProperty(o)){var c=a[o];o==="style"?yd(e,c):o==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&gd(e,c)):o==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&zr(e,c):typeof c=="number"&&zr(e,""+c):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Rr.hasOwnProperty(o)?c!=null&&o==="onScroll"&&le("scroll",e):c!=null&&Ni(e,o,c,i))}switch(n){case"input":gs(e),Pa(e,s,!1);break;case"textarea":gs(e),za(e);break;case"option":s.value!=null&&e.setAttribute("value",""+Dt(s.value));break;case"select":e.multiple=!!s.multiple,o=s.value,o!=null?Hn(e,!!s.multiple,o,!1):s.defaultValue!=null&&Hn(e,!!s.multiple,s.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=el)}switch(n){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return _e(t),null;case 6:if(e&&t.stateNode!=null)Bu(e,t,e.memoizedProps,s);else{if(typeof s!="string"&&t.stateNode===null)throw Error(_(166));if(n=an(Gr.current),an(wt.current),Ss(t)){if(s=t.stateNode,n=t.memoizedProps,s[xt]=t,(o=s.nodeValue!==n)&&(e=Ge,e!==null))switch(e.tag){case 3:ks(s.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ks(s.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else s=(n.nodeType===9?n:n.ownerDocument).createTextNode(s),s[xt]=t,t.stateNode=s}return _e(t),null;case 13:if(oe(ce),s=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ie&&Qe!==null&&t.mode&1&&!(t.flags&128))lu(),Yn(),t.flags|=98560,o=!1;else if(o=Ss(t),s!==null&&s.dehydrated!==null){if(e===null){if(!o)throw Error(_(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(_(317));o[xt]=t}else Yn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;_e(t),o=!1}else at!==null&&(oi(at),at=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(t.child.flags|=8192,t.mode&1&&(e===null||ce.current&1?we===0&&(we=3):la())),t.updateQueue!==null&&(t.flags|=4),_e(t),null);case 4:return er(),Yo(e,t),e===null&&qr(t.stateNode.containerInfo),_e(t),null;case 10:return Vi(t.type._context),_e(t),null;case 17:return qe(t.type)&&tl(),_e(t),null;case 19:if(oe(ce),o=t.memoizedState,o===null)return _e(t),null;if(s=(t.flags&128)!==0,i=o.rendering,i===null)if(s)xr(o,!1);else{if(we!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=al(e),i!==null){for(t.flags|=128,xr(o,!1),s=i.updateQueue,s!==null&&(t.updateQueue=s,t.flags|=4),t.subtreeFlags=0,s=n,n=t.child;n!==null;)o=n,e=s,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return re(ce,ce.current&1|2),t.child}e=e.sibling}o.tail!==null&&xe()>nr&&(t.flags|=128,s=!0,xr(o,!1),t.lanes=4194304)}else{if(!s)if(e=al(i),e!==null){if(t.flags|=128,s=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),xr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!ie)return _e(t),null}else 2*xe()-o.renderingStartTime>nr&&n!==1073741824&&(t.flags|=128,s=!0,xr(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=xe(),t.sibling=null,n=ce.current,re(ce,s?n&1|2:n&1),t):(_e(t),null);case 22:case 23:return sa(),s=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(t.flags|=8192),s&&t.mode&1?We&1073741824&&(_e(t),t.subtreeFlags&6&&(t.flags|=8192)):_e(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function ch(e,t){switch(Ui(t),t.tag){case 1:return qe(t.type)&&tl(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return er(),oe(Ve),oe(Le),Gi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Qi(t),null;case 13:if(oe(ce),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Yn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(ce),null;case 4:return er(),null;case 10:return Vi(t.type._context),null;case 22:case 23:return sa(),null;case 24:return null;default:return null}}var Es=!1,Te=!1,dh=typeof WeakSet=="function"?WeakSet:Set,B=null;function Vn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(s){he(e,t,s)}else n.current=null}function Xo(e,t,n){try{n()}catch(s){he(e,t,s)}}var Sc=!1;function uh(e,t){if(zo=Ks,e=Qd(),zi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var s=n.getSelection&&n.getSelection();if(s&&s.rangeCount!==0){n=s.anchorNode;var l=s.anchorOffset,o=s.focusNode;s=s.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,c=-1,d=0,g=0,w=e,b=null;t:for(;;){for(var N;w!==n||l!==0&&w.nodeType!==3||(a=i+l),w!==o||s!==0&&w.nodeType!==3||(c=i+s),w.nodeType===3&&(i+=w.nodeValue.length),(N=w.firstChild)!==null;)b=w,w=N;for(;;){if(w===e)break t;if(b===n&&++d===l&&(a=i),b===o&&++g===s&&(c=i),(N=w.nextSibling)!==null)break;w=b,b=w.parentNode}w=N}n=a===-1||c===-1?null:{start:a,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oo={focusedElem:e,selectionRange:n},Ks=!1,B=t;B!==null;)if(t=B,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,B=e;else for(;B!==null;){t=B;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var k=j.memoizedProps,S=j.memoizedState,m=t.stateNode,u=m.getSnapshotBeforeUpdate(t.elementType===t.type?k:ot(t.type,k),S);m.__reactInternalSnapshotBeforeUpdate=u}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(M){he(t,t.return,M)}if(e=t.sibling,e!==null){e.return=t.return,B=e;break}B=t.return}return j=Sc,Sc=!1,j}function Ir(e,t,n){var s=t.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var l=s=s.next;do{if((l.tag&e)===e){var o=l.destroy;l.destroy=void 0,o!==void 0&&Xo(t,n,o)}l=l.next}while(l!==s)}}function Sl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var s=n.create;n.destroy=s()}n=n.next}while(n!==t)}}function ei(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Vu(e){var t=e.alternate;t!==null&&(e.alternate=null,Vu(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[xt],delete t[Wr],delete t[Fo],delete t[Gf],delete t[Df])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function qu(e){return e.tag===5||e.tag===3||e.tag===4}function Cc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||qu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ti(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=el));else if(s!==4&&(e=e.child,e!==null))for(ti(e,t,n),e=e.sibling;e!==null;)ti(e,t,n),e=e.sibling}function ni(e,t,n){var s=e.tag;if(s===5||s===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(ni(e,t,n),e=e.sibling;e!==null;)ni(e,t,n),e=e.sibling}var ke=null,it=!1;function Lt(e,t,n){for(n=n.child;n!==null;)Hu(e,t,n),n=n.sibling}function Hu(e,t,n){if(vt&&typeof vt.onCommitFiberUnmount=="function")try{vt.onCommitFiberUnmount(xl,n)}catch{}switch(n.tag){case 5:Te||Vn(n,t);case 6:var s=ke,l=it;ke=null,Lt(e,t,n),ke=s,it=l,ke!==null&&(it?(e=ke,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ke.removeChild(n.stateNode));break;case 18:ke!==null&&(it?(e=ke,n=n.stateNode,e.nodeType===8?to(e.parentNode,n):e.nodeType===1&&to(e,n),Fr(e)):to(ke,n.stateNode));break;case 4:s=ke,l=it,ke=n.stateNode.containerInfo,it=!0,Lt(e,t,n),ke=s,it=l;break;case 0:case 11:case 14:case 15:if(!Te&&(s=n.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){l=s=s.next;do{var o=l,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&Xo(n,t,i),l=l.next}while(l!==s)}Lt(e,t,n);break;case 1:if(!Te&&(Vn(n,t),s=n.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=n.memoizedProps,s.state=n.memoizedState,s.componentWillUnmount()}catch(a){he(n,t,a)}Lt(e,t,n);break;case 21:Lt(e,t,n);break;case 22:n.mode&1?(Te=(s=Te)||n.memoizedState!==null,Lt(e,t,n),Te=s):Lt(e,t,n);break;default:Lt(e,t,n)}}function Mc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new dh),t.forEach(function(s){var l=wh.bind(null,e,s);n.has(s)||(n.add(s),s.then(l,l))})}}function lt(e,t){var n=t.deletions;if(n!==null)for(var s=0;s<n.length;s++){var l=n[s];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:ke=a.stateNode,it=!1;break e;case 3:ke=a.stateNode.containerInfo,it=!0;break e;case 4:ke=a.stateNode.containerInfo,it=!0;break e}a=a.return}if(ke===null)throw Error(_(160));Hu(o,i,l),ke=null,it=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(d){he(l,t,d)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Wu(t,e),t=t.sibling}function Wu(e,t){var n=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(lt(t,e),pt(e),s&4){try{Ir(3,e,e.return),Sl(3,e)}catch(k){he(e,e.return,k)}try{Ir(5,e,e.return)}catch(k){he(e,e.return,k)}}break;case 1:lt(t,e),pt(e),s&512&&n!==null&&Vn(n,n.return);break;case 5:if(lt(t,e),pt(e),s&512&&n!==null&&Vn(n,n.return),e.flags&32){var l=e.stateNode;try{zr(l,"")}catch(k){he(e,e.return,k)}}if(s&4&&(l=e.stateNode,l!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&fd(l,o),Co(a,i);var d=Co(a,o);for(i=0;i<c.length;i+=2){var g=c[i],w=c[i+1];g==="style"?yd(l,w):g==="dangerouslySetInnerHTML"?gd(l,w):g==="children"?zr(l,w):Ni(l,g,w,d)}switch(a){case"input":bo(l,o);break;case"textarea":hd(l,o);break;case"select":var b=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!o.multiple;var N=o.value;N!=null?Hn(l,!!o.multiple,N,!1):b!==!!o.multiple&&(o.defaultValue!=null?Hn(l,!!o.multiple,o.defaultValue,!0):Hn(l,!!o.multiple,o.multiple?[]:"",!1))}l[Wr]=o}catch(k){he(e,e.return,k)}}break;case 6:if(lt(t,e),pt(e),s&4){if(e.stateNode===null)throw Error(_(162));l=e.stateNode,o=e.memoizedProps;try{l.nodeValue=o}catch(k){he(e,e.return,k)}}break;case 3:if(lt(t,e),pt(e),s&4&&n!==null&&n.memoizedState.isDehydrated)try{Fr(t.containerInfo)}catch(k){he(e,e.return,k)}break;case 4:lt(t,e),pt(e);break;case 13:lt(t,e),pt(e),l=e.child,l.flags&8192&&(o=l.memoizedState!==null,l.stateNode.isHidden=o,!o||l.alternate!==null&&l.alternate.memoizedState!==null||(na=xe())),s&4&&Mc(e);break;case 22:if(g=n!==null&&n.memoizedState!==null,e.mode&1?(Te=(d=Te)||g,lt(t,e),Te=d):lt(t,e),pt(e),s&8192){if(d=e.memoizedState!==null,(e.stateNode.isHidden=d)&&!g&&e.mode&1)for(B=e,g=e.child;g!==null;){for(w=B=g;B!==null;){switch(b=B,N=b.child,b.tag){case 0:case 11:case 14:case 15:Ir(4,b,b.return);break;case 1:Vn(b,b.return);var j=b.stateNode;if(typeof j.componentWillUnmount=="function"){s=b,n=b.return;try{t=s,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(k){he(s,n,k)}}break;case 5:Vn(b,b.return);break;case 22:if(b.memoizedState!==null){Ic(w);continue}}N!==null?(N.return=b,B=N):Ic(w)}g=g.sibling}e:for(g=null,w=e;;){if(w.tag===5){if(g===null){g=w;try{l=w.stateNode,d?(o=l.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=w.stateNode,c=w.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,a.style.display=xd("display",i))}catch(k){he(e,e.return,k)}}}else if(w.tag===6){if(g===null)try{w.stateNode.nodeValue=d?"":w.memoizedProps}catch(k){he(e,e.return,k)}}else if((w.tag!==22&&w.tag!==23||w.memoizedState===null||w===e)&&w.child!==null){w.child.return=w,w=w.child;continue}if(w===e)break e;for(;w.sibling===null;){if(w.return===null||w.return===e)break e;g===w&&(g=null),w=w.return}g===w&&(g=null),w.sibling.return=w.return,w=w.sibling}}break;case 19:lt(t,e),pt(e),s&4&&Mc(e);break;case 21:break;default:lt(t,e),pt(e)}}function pt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(qu(n)){var s=n;break e}n=n.return}throw Error(_(160))}switch(s.tag){case 5:var l=s.stateNode;s.flags&32&&(zr(l,""),s.flags&=-33);var o=Cc(e);ni(e,o,l);break;case 3:case 4:var i=s.stateNode.containerInfo,a=Cc(e);ti(e,a,i);break;default:throw Error(_(161))}}catch(c){he(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function mh(e,t,n){B=e,Qu(e)}function Qu(e,t,n){for(var s=(e.mode&1)!==0;B!==null;){var l=B,o=l.child;if(l.tag===22&&s){var i=l.memoizedState!==null||Es;if(!i){var a=l.alternate,c=a!==null&&a.memoizedState!==null||Te;a=Es;var d=Te;if(Es=i,(Te=c)&&!d)for(B=l;B!==null;)i=B,c=i.child,i.tag===22&&i.memoizedState!==null?Ac(l):c!==null?(c.return=i,B=c):Ac(l);for(;o!==null;)B=o,Qu(o),o=o.sibling;B=l,Es=a,Te=d}Ec(e)}else l.subtreeFlags&8772&&o!==null?(o.return=l,B=o):Ec(e)}}function Ec(e){for(;B!==null;){var t=B;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Te||Sl(5,t);break;case 1:var s=t.stateNode;if(t.flags&4&&!Te)if(n===null)s.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ot(t.type,n.memoizedProps);s.componentDidUpdate(l,n.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&mc(t,o,s);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}mc(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var d=t.alternate;if(d!==null){var g=d.memoizedState;if(g!==null){var w=g.dehydrated;w!==null&&Fr(w)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}Te||t.flags&512&&ei(t)}catch(b){he(t,t.return,b)}}if(t===e){B=null;break}if(n=t.sibling,n!==null){n.return=t.return,B=n;break}B=t.return}}function Ic(e){for(;B!==null;){var t=B;if(t===e){B=null;break}var n=t.sibling;if(n!==null){n.return=t.return,B=n;break}B=t.return}}function Ac(e){for(;B!==null;){var t=B;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Sl(4,t)}catch(c){he(t,n,c)}break;case 1:var s=t.stateNode;if(typeof s.componentDidMount=="function"){var l=t.return;try{s.componentDidMount()}catch(c){he(t,l,c)}}var o=t.return;try{ei(t)}catch(c){he(t,o,c)}break;case 5:var i=t.return;try{ei(t)}catch(c){he(t,i,c)}}}catch(c){he(t,t.return,c)}if(t===e){B=null;break}var a=t.sibling;if(a!==null){a.return=t.return,B=a;break}B=t.return}}var fh=Math.ceil,ul=Tt.ReactCurrentDispatcher,ea=Tt.ReactCurrentOwner,tt=Tt.ReactCurrentBatchConfig,ee=0,Ne=null,ye=null,Se=0,We=0,qn=Kt(0),we=0,Kr=null,vn=0,Cl=0,ta=0,Ar=null,Fe=null,na=0,nr=1/0,Nt=null,ml=!1,ri=null,Wt=null,Is=!1,$t=null,fl=0,_r=0,si=null,Bs=-1,Vs=0;function Oe(){return ee&6?xe():Bs!==-1?Bs:Bs=xe()}function Qt(e){return e.mode&1?ee&2&&Se!==0?Se&-Se:Zf.transition!==null?(Vs===0&&(Vs=Ad()),Vs):(e=ne,e!==0||(e=window.event,e=e===void 0?16:Od(e.type)),e):1}function dt(e,t,n,s){if(50<_r)throw _r=0,si=null,Error(_(185));rs(e,n,s),(!(ee&2)||e!==Ne)&&(e===Ne&&(!(ee&2)&&(Cl|=n),we===4&&Ot(e,Se)),He(e,s),n===1&&ee===0&&!(t.mode&1)&&(nr=xe()+500,jl&&Yt()))}function He(e,t){var n=e.callbackNode;Jm(e,t);var s=Zs(e,e===Ne?Se:0);if(s===0)n!==null&&$a(n),e.callbackNode=null,e.callbackPriority=0;else if(t=s&-s,e.callbackPriority!==t){if(n!=null&&$a(n),t===1)e.tag===0?Jf(_c.bind(null,e)):nu(_c.bind(null,e)),Wf(function(){!(ee&6)&&Yt()}),n=null;else{switch(_d(s)){case 1:n=Ei;break;case 4:n=Ed;break;case 16:n=Js;break;case 536870912:n=Id;break;default:n=Js}n=e0(n,Gu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Gu(e,t){if(Bs=-1,Vs=0,ee&6)throw Error(_(327));var n=e.callbackNode;if(Jn()&&e.callbackNode!==n)return null;var s=Zs(e,e===Ne?Se:0);if(s===0)return null;if(s&30||s&e.expiredLanes||t)t=hl(e,s);else{t=s;var l=ee;ee|=2;var o=Ju();(Ne!==e||Se!==t)&&(Nt=null,nr=xe()+500,dn(e,t));do try{gh();break}catch(a){Du(e,a)}while(!0);Bi(),ul.current=o,ee=l,ye!==null?t=0:(Ne=null,Se=0,t=we)}if(t!==0){if(t===2&&(l=_o(e),l!==0&&(s=l,t=li(e,l))),t===1)throw n=Kr,dn(e,0),Ot(e,s),He(e,xe()),n;if(t===6)Ot(e,s);else{if(l=e.current.alternate,!(s&30)&&!hh(l)&&(t=hl(e,s),t===2&&(o=_o(e),o!==0&&(s=o,t=li(e,o))),t===1))throw n=Kr,dn(e,0),Ot(e,s),He(e,xe()),n;switch(e.finishedWork=l,e.finishedLanes=s,t){case 0:case 1:throw Error(_(345));case 2:sn(e,Fe,Nt);break;case 3:if(Ot(e,s),(s&130023424)===s&&(t=na+500-xe(),10<t)){if(Zs(e,0)!==0)break;if(l=e.suspendedLanes,(l&s)!==s){Oe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=$o(sn.bind(null,e,Fe,Nt),t);break}sn(e,Fe,Nt);break;case 4:if(Ot(e,s),(s&4194240)===s)break;for(t=e.eventTimes,l=-1;0<s;){var i=31-ct(s);o=1<<i,i=t[i],i>l&&(l=i),s&=~o}if(s=l,s=xe()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*fh(s/1960))-s,10<s){e.timeoutHandle=$o(sn.bind(null,e,Fe,Nt),s);break}sn(e,Fe,Nt);break;case 5:sn(e,Fe,Nt);break;default:throw Error(_(329))}}}return He(e,xe()),e.callbackNode===n?Gu.bind(null,e):null}function li(e,t){var n=Ar;return e.current.memoizedState.isDehydrated&&(dn(e,t).flags|=256),e=hl(e,t),e!==2&&(t=Fe,Fe=n,t!==null&&oi(t)),e}function oi(e){Fe===null?Fe=e:Fe.push.apply(Fe,e)}function hh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var s=0;s<n.length;s++){var l=n[s],o=l.getSnapshot;l=l.value;try{if(!ut(o(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Ot(e,t){for(t&=~ta,t&=~Cl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ct(t),s=1<<n;e[n]=-1,t&=~s}}function _c(e){if(ee&6)throw Error(_(327));Jn();var t=Zs(e,0);if(!(t&1))return He(e,xe()),null;var n=hl(e,t);if(e.tag!==0&&n===2){var s=_o(e);s!==0&&(t=s,n=li(e,s))}if(n===1)throw n=Kr,dn(e,0),Ot(e,t),He(e,xe()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,sn(e,Fe,Nt),He(e,xe()),null}function ra(e,t){var n=ee;ee|=1;try{return e(t)}finally{ee=n,ee===0&&(nr=xe()+500,jl&&Yt())}}function wn(e){$t!==null&&$t.tag===0&&!(ee&6)&&Jn();var t=ee;ee|=1;var n=tt.transition,s=ne;try{if(tt.transition=null,ne=1,e)return e()}finally{ne=s,tt.transition=n,ee=t,!(ee&6)&&Yt()}}function sa(){We=qn.current,oe(qn)}function dn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Hf(n)),ye!==null)for(n=ye.return;n!==null;){var s=n;switch(Ui(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&tl();break;case 3:er(),oe(Ve),oe(Le),Gi();break;case 5:Qi(s);break;case 4:er();break;case 13:oe(ce);break;case 19:oe(ce);break;case 10:Vi(s.type._context);break;case 22:case 23:sa()}n=n.return}if(Ne=e,ye=e=Gt(e.current,null),Se=We=t,we=0,Kr=null,ta=Cl=vn=0,Fe=Ar=null,on!==null){for(t=0;t<on.length;t++)if(n=on[t],s=n.interleaved,s!==null){n.interleaved=null;var l=s.next,o=n.pending;if(o!==null){var i=o.next;o.next=l,s.next=i}n.pending=s}on=null}return e}function Du(e,t){do{var n=ye;try{if(Bi(),Us.current=dl,cl){for(var s=de.memoizedState;s!==null;){var l=s.queue;l!==null&&(l.pending=null),s=s.next}cl=!1}if(yn=0,je=ve=de=null,Er=!1,Dr=0,ea.current=null,n===null||n.return===null){we=1,Kr=t,ye=null;break}e:{var o=e,i=n.return,a=n,c=t;if(t=Se,a.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var d=c,g=a,w=g.tag;if(!(g.mode&1)&&(w===0||w===11||w===15)){var b=g.alternate;b?(g.updateQueue=b.updateQueue,g.memoizedState=b.memoizedState,g.lanes=b.lanes):(g.updateQueue=null,g.memoizedState=null)}var N=yc(i);if(N!==null){N.flags&=-257,vc(N,i,a,o,t),N.mode&1&&xc(o,d,t),t=N,c=d;var j=t.updateQueue;if(j===null){var k=new Set;k.add(c),t.updateQueue=k}else j.add(c);break e}else{if(!(t&1)){xc(o,d,t),la();break e}c=Error(_(426))}}else if(ie&&a.mode&1){var S=yc(i);if(S!==null){!(S.flags&65536)&&(S.flags|=256),vc(S,i,a,o,t),$i(tr(c,a));break e}}o=c=tr(c,a),we!==4&&(we=2),Ar===null?Ar=[o]:Ar.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=_u(o,c,t);uc(o,m);break e;case 1:a=c;var u=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof u.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Wt===null||!Wt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var M=Tu(o,a,t);uc(o,M);break e}}o=o.return}while(o!==null)}Ku(n)}catch(L){t=L,ye===n&&n!==null&&(ye=n=n.return);continue}break}while(!0)}function Ju(){var e=ul.current;return ul.current=dl,e===null?dl:e}function la(){(we===0||we===3||we===2)&&(we=4),Ne===null||!(vn&268435455)&&!(Cl&268435455)||Ot(Ne,Se)}function hl(e,t){var n=ee;ee|=2;var s=Ju();(Ne!==e||Se!==t)&&(Nt=null,dn(e,t));do try{ph();break}catch(l){Du(e,l)}while(!0);if(Bi(),ee=n,ul.current=s,ye!==null)throw Error(_(261));return Ne=null,Se=0,we}function ph(){for(;ye!==null;)Zu(ye)}function gh(){for(;ye!==null&&!Fm();)Zu(ye)}function Zu(e){var t=Xu(e.alternate,e,We);e.memoizedProps=e.pendingProps,t===null?Ku(e):ye=t,ea.current=null}function Ku(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=ch(n,t),n!==null){n.flags&=32767,ye=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{we=6,ye=null;return}}else if(n=ah(n,t,We),n!==null){ye=n;return}if(t=t.sibling,t!==null){ye=t;return}ye=t=e}while(t!==null);we===0&&(we=5)}function sn(e,t,n){var s=ne,l=tt.transition;try{tt.transition=null,ne=1,xh(e,t,n,s)}finally{tt.transition=l,ne=s}return null}function xh(e,t,n,s){do Jn();while($t!==null);if(ee&6)throw Error(_(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Zm(e,o),e===Ne&&(ye=Ne=null,Se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Is||(Is=!0,e0(Js,function(){return Jn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=tt.transition,tt.transition=null;var i=ne;ne=1;var a=ee;ee|=4,ea.current=null,uh(e,n),Wu(n,e),Of(Oo),Ks=!!zo,Oo=zo=null,e.current=n,mh(n),Bm(),ee=a,ne=i,tt.transition=o}else e.current=n;if(Is&&(Is=!1,$t=e,fl=l),o=e.pendingLanes,o===0&&(Wt=null),Hm(n.stateNode),He(e,xe()),t!==null)for(s=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],s(l.value,{componentStack:l.stack,digest:l.digest});if(ml)throw ml=!1,e=ri,ri=null,e;return fl&1&&e.tag!==0&&Jn(),o=e.pendingLanes,o&1?e===si?_r++:(_r=0,si=e):_r=0,Yt(),null}function Jn(){if($t!==null){var e=_d(fl),t=tt.transition,n=ne;try{if(tt.transition=null,ne=16>e?16:e,$t===null)var s=!1;else{if(e=$t,$t=null,fl=0,ee&6)throw Error(_(331));var l=ee;for(ee|=4,B=e.current;B!==null;){var o=B,i=o.child;if(B.flags&16){var a=o.deletions;if(a!==null){for(var c=0;c<a.length;c++){var d=a[c];for(B=d;B!==null;){var g=B;switch(g.tag){case 0:case 11:case 15:Ir(8,g,o)}var w=g.child;if(w!==null)w.return=g,B=w;else for(;B!==null;){g=B;var b=g.sibling,N=g.return;if(Vu(g),g===d){B=null;break}if(b!==null){b.return=N,B=b;break}B=N}}}var j=o.alternate;if(j!==null){var k=j.child;if(k!==null){j.child=null;do{var S=k.sibling;k.sibling=null,k=S}while(k!==null)}}B=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,B=i;else e:for(;B!==null;){if(o=B,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Ir(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,B=m;break e}B=o.return}}var u=e.current;for(B=u;B!==null;){i=B;var p=i.child;if(i.subtreeFlags&2064&&p!==null)p.return=i,B=p;else e:for(i=u;B!==null;){if(a=B,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Sl(9,a)}}catch(L){he(a,a.return,L)}if(a===i){B=null;break e}var M=a.sibling;if(M!==null){M.return=a.return,B=M;break e}B=a.return}}if(ee=l,Yt(),vt&&typeof vt.onPostCommitFiberRoot=="function")try{vt.onPostCommitFiberRoot(xl,e)}catch{}s=!0}return s}finally{ne=n,tt.transition=t}}return!1}function Tc(e,t,n){t=tr(n,t),t=_u(e,t,1),e=Ht(e,t,1),t=Oe(),e!==null&&(rs(e,1,t),He(e,t))}function he(e,t,n){if(e.tag===3)Tc(e,e,n);else for(;t!==null;){if(t.tag===3){Tc(t,e,n);break}else if(t.tag===1){var s=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(Wt===null||!Wt.has(s))){e=tr(n,e),e=Tu(t,e,1),t=Ht(t,e,1),e=Oe(),t!==null&&(rs(t,1,e),He(t,e));break}}t=t.return}}function yh(e,t,n){var s=e.pingCache;s!==null&&s.delete(t),t=Oe(),e.pingedLanes|=e.suspendedLanes&n,Ne===e&&(Se&n)===n&&(we===4||we===3&&(Se&130023424)===Se&&500>xe()-na?dn(e,0):ta|=n),He(e,t)}function Yu(e,t){t===0&&(e.mode&1?(t=vs,vs<<=1,!(vs&130023424)&&(vs=4194304)):t=1);var n=Oe();e=At(e,t),e!==null&&(rs(e,t,n),He(e,n))}function vh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Yu(e,n)}function wh(e,t){var n=0;switch(e.tag){case 13:var s=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(_(314))}s!==null&&s.delete(t),Yu(e,n)}var Xu;Xu=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ve.current)Be=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Be=!1,ih(e,t,n);Be=!!(e.flags&131072)}else Be=!1,ie&&t.flags&1048576&&ru(t,sl,t.index);switch(t.lanes=0,t.tag){case 2:var s=t.type;Fs(e,t),e=t.pendingProps;var l=Kn(t,Le.current);Dn(t,n),l=Ji(null,t,s,e,l,n);var o=Zi();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,qe(s)?(o=!0,nl(t)):o=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Hi(t),l.updater=kl,t.stateNode=l,l._reactInternals=t,Qo(t,s,e,n),t=Jo(null,t,s,!0,o,n)):(t.tag=0,ie&&o&&Oi(t),ze(null,t,l,n),t=t.child),t;case 16:s=t.elementType;e:{switch(Fs(e,t),e=t.pendingProps,l=s._init,s=l(s._payload),t.type=s,l=t.tag=jh(s),e=ot(s,e),l){case 0:t=Do(null,t,s,e,n);break e;case 1:t=jc(null,t,s,e,n);break e;case 11:t=wc(null,t,s,e,n);break e;case 14:t=bc(null,t,s,ot(s.type,e),n);break e}throw Error(_(306,s,""))}return t;case 0:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:ot(s,l),Do(e,t,s,l,n);case 1:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:ot(s,l),jc(e,t,s,l,n);case 3:e:{if(zu(t),e===null)throw Error(_(387));s=t.pendingProps,o=t.memoizedState,l=o.element,cu(e,t),il(t,s,null,n);var i=t.memoizedState;if(s=i.element,o.isDehydrated)if(o={element:s,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){l=tr(Error(_(423)),t),t=Nc(e,t,s,n,l);break e}else if(s!==l){l=tr(Error(_(424)),t),t=Nc(e,t,s,n,l);break e}else for(Qe=qt(t.stateNode.containerInfo.firstChild),Ge=t,ie=!0,at=null,n=iu(t,null,s,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Yn(),s===l){t=_t(e,t,n);break e}ze(e,t,s,n)}t=t.child}return t;case 5:return du(t),e===null&&qo(t),s=t.type,l=t.pendingProps,o=e!==null?e.memoizedProps:null,i=l.children,Uo(s,l)?i=null:o!==null&&Uo(s,o)&&(t.flags|=32),Ru(e,t),ze(e,t,i,n),t.child;case 6:return e===null&&qo(t),null;case 13:return Ou(e,t,n);case 4:return Wi(t,t.stateNode.containerInfo),s=t.pendingProps,e===null?t.child=Xn(t,null,s,n):ze(e,t,s,n),t.child;case 11:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:ot(s,l),wc(e,t,s,l,n);case 7:return ze(e,t,t.pendingProps,n),t.child;case 8:return ze(e,t,t.pendingProps.children,n),t.child;case 12:return ze(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(s=t.type._context,l=t.pendingProps,o=t.memoizedProps,i=l.value,re(ll,s._currentValue),s._currentValue=i,o!==null)if(ut(o.value,i)){if(o.children===l.children&&!Ve.current){t=_t(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var c=a.firstContext;c!==null;){if(c.context===s){if(o.tag===1){c=Mt(-1,n&-n),c.tag=2;var d=o.updateQueue;if(d!==null){d=d.shared;var g=d.pending;g===null?c.next=c:(c.next=g.next,g.next=c),d.pending=c}}o.lanes|=n,c=o.alternate,c!==null&&(c.lanes|=n),Ho(o.return,n,t),a.lanes|=n;break}c=c.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(_(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ho(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}ze(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,s=t.pendingProps.children,Dn(t,n),l=nt(l),s=s(l),t.flags|=1,ze(e,t,s,n),t.child;case 14:return s=t.type,l=ot(s,t.pendingProps),l=ot(s.type,l),bc(e,t,s,l,n);case 15:return Lu(e,t,t.type,t.pendingProps,n);case 17:return s=t.type,l=t.pendingProps,l=t.elementType===s?l:ot(s,l),Fs(e,t),t.tag=1,qe(s)?(e=!0,nl(t)):e=!1,Dn(t,n),Au(t,s,l),Qo(t,s,l,n),Jo(null,t,s,!0,e,n);case 19:return Uu(e,t,n);case 22:return Pu(e,t,n)}throw Error(_(156,t.tag))};function e0(e,t){return Md(e,t)}function bh(e,t,n,s){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function et(e,t,n,s){return new bh(e,t,n,s)}function oa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function jh(e){if(typeof e=="function")return oa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Si)return 11;if(e===Ci)return 14}return 2}function Gt(e,t){var n=e.alternate;return n===null?(n=et(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function qs(e,t,n,s,l,o){var i=2;if(s=e,typeof e=="function")oa(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Ln:return un(n.children,l,o,t);case ki:i=8,l|=8;break;case go:return e=et(12,n,t,l|2),e.elementType=go,e.lanes=o,e;case xo:return e=et(13,n,t,l),e.elementType=xo,e.lanes=o,e;case yo:return e=et(19,n,t,l),e.elementType=yo,e.lanes=o,e;case dd:return Ml(n,l,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ad:i=10;break e;case cd:i=9;break e;case Si:i=11;break e;case Ci:i=14;break e;case Pt:i=16,s=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=et(i,n,t,l),t.elementType=e,t.type=s,t.lanes=o,t}function un(e,t,n,s){return e=et(7,e,s,t),e.lanes=n,e}function Ml(e,t,n,s){return e=et(22,e,s,t),e.elementType=dd,e.lanes=n,e.stateNode={isHidden:!1},e}function co(e,t,n){return e=et(6,e,null,t),e.lanes=n,e}function uo(e,t,n){return t=et(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Nh(e,t,n,s,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Hl(0),this.expirationTimes=Hl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Hl(0),this.identifierPrefix=s,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ia(e,t,n,s,l,o,i,a,c){return e=new Nh(e,t,n,a,c),t===1?(t=1,o===!0&&(t|=8)):t=0,o=et(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:s,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Hi(o),e}function kh(e,t,n){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Tn,key:s==null?null:""+s,children:e,containerInfo:t,implementation:n}}function t0(e){if(!e)return Jt;e=e._reactInternals;e:{if(Nn(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(qe(n))return tu(e,n,t)}return t}function n0(e,t,n,s,l,o,i,a,c){return e=ia(n,s,!0,e,l,o,i,a,c),e.context=t0(null),n=e.current,s=Oe(),l=Qt(n),o=Mt(s,l),o.callback=t??null,Ht(n,o,l),e.current.lanes=l,rs(e,l,s),He(e,s),e}function El(e,t,n,s){var l=t.current,o=Oe(),i=Qt(l);return n=t0(n),t.context===null?t.context=n:t.pendingContext=n,t=Mt(o,i),t.payload={element:e},s=s===void 0?null:s,s!==null&&(t.callback=s),e=Ht(l,t,i),e!==null&&(dt(e,l,i,o),Os(e,l,i)),i}function pl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Lc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function aa(e,t){Lc(e,t),(e=e.alternate)&&Lc(e,t)}function Sh(){return null}var r0=typeof reportError=="function"?reportError:function(e){console.error(e)};function ca(e){this._internalRoot=e}Il.prototype.render=ca.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));El(e,t,null,null)};Il.prototype.unmount=ca.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;wn(function(){El(null,e,null,null)}),t[It]=null}};function Il(e){this._internalRoot=e}Il.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<zt.length&&t!==0&&t<zt[n].priority;n++);zt.splice(n,0,e),n===0&&zd(e)}};function da(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Al(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Pc(){}function Ch(e,t,n,s,l){if(l){if(typeof s=="function"){var o=s;s=function(){var d=pl(i);o.call(d)}}var i=n0(t,s,e,0,null,!1,!1,"",Pc);return e._reactRootContainer=i,e[It]=i.current,qr(e.nodeType===8?e.parentNode:e),wn(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof s=="function"){var a=s;s=function(){var d=pl(c);a.call(d)}}var c=ia(e,0,!1,null,null,!1,!1,"",Pc);return e._reactRootContainer=c,e[It]=c.current,qr(e.nodeType===8?e.parentNode:e),wn(function(){El(t,c,n,s)}),c}function _l(e,t,n,s,l){var o=n._reactRootContainer;if(o){var i=o;if(typeof l=="function"){var a=l;l=function(){var c=pl(i);a.call(c)}}El(t,i,e,l)}else i=Ch(n,t,e,l,s);return pl(i)}Td=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=br(t.pendingLanes);n!==0&&(Ii(t,n|1),He(t,xe()),!(ee&6)&&(nr=xe()+500,Yt()))}break;case 13:wn(function(){var s=At(e,1);if(s!==null){var l=Oe();dt(s,e,1,l)}}),aa(e,1)}};Ai=function(e){if(e.tag===13){var t=At(e,134217728);if(t!==null){var n=Oe();dt(t,e,134217728,n)}aa(e,134217728)}};Ld=function(e){if(e.tag===13){var t=Qt(e),n=At(e,t);if(n!==null){var s=Oe();dt(n,e,t,s)}aa(e,t)}};Pd=function(){return ne};Rd=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};Eo=function(e,t,n){switch(t){case"input":if(bo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var s=n[t];if(s!==e&&s.form===e.form){var l=bl(s);if(!l)throw Error(_(90));md(s),bo(s,l)}}}break;case"textarea":hd(e,n);break;case"select":t=n.value,t!=null&&Hn(e,!!n.multiple,t,!1)}};bd=ra;jd=wn;var Mh={usingClientEntryPoint:!1,Events:[ls,On,bl,vd,wd,ra]},yr={findFiberByHostInstance:ln,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Eh={bundleType:yr.bundleType,version:yr.version,rendererPackageName:yr.rendererPackageName,rendererConfig:yr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Tt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Sd(e),e===null?null:e.stateNode},findFiberByHostInstance:yr.findFiberByHostInstance||Sh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var As=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!As.isDisabled&&As.supportsFiber)try{xl=As.inject(Eh),vt=As}catch{}}Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mh;Je.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!da(t))throw Error(_(200));return kh(e,t,null,n)};Je.createRoot=function(e,t){if(!da(e))throw Error(_(299));var n=!1,s="",l=r0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(s=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ia(e,1,!1,null,null,n,!1,s,l),e[It]=t.current,qr(e.nodeType===8?e.parentNode:e),new ca(t)};Je.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Sd(t),e=e===null?null:e.stateNode,e};Je.flushSync=function(e){return wn(e)};Je.hydrate=function(e,t,n){if(!Al(t))throw Error(_(200));return _l(null,e,t,!0,n)};Je.hydrateRoot=function(e,t,n){if(!da(e))throw Error(_(405));var s=n!=null&&n.hydratedSources||null,l=!1,o="",i=r0;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=n0(t,null,e,1,n??null,l,!1,o,i),e[It]=t.current,qr(e),s)for(e=0;e<s.length;e++)n=s[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Il(t)};Je.render=function(e,t,n){if(!Al(t))throw Error(_(200));return _l(null,e,t,!1,n)};Je.unmountComponentAtNode=function(e){if(!Al(e))throw Error(_(40));return e._reactRootContainer?(wn(function(){_l(null,null,e,!1,function(){e._reactRootContainer=null,e[It]=null})}),!0):!1};Je.unstable_batchedUpdates=ra;Je.unstable_renderSubtreeIntoContainer=function(e,t,n,s){if(!Al(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return _l(e,t,n,!1,s)};Je.version="18.3.1-next-f1338f8080-20240426";function s0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s0)}catch(e){console.error(e)}}s0(),sd.exports=Je;var Ih=sd.exports,l0,Rc=Ih;l0=Rc.createRoot,Rc.hydrateRoot;/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ah=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o0=(...e)=>e.filter((t,n,s)=>!!t&&s.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var _h={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Th=y.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:l="",children:o,iconNode:i,...a},c)=>y.createElement("svg",{ref:c,..._h,width:t,height:t,stroke:e,strokeWidth:s?Number(n)*24/Number(t):n,className:o0("lucide",l),...a},[...i.map(([d,g])=>y.createElement(d,g)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=(e,t)=>{const n=y.forwardRef(({className:s,...l},o)=>y.createElement(Th,{ref:o,iconNode:t,className:o0(`lucide-${Ah(e)}`,s),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ii=$("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i0=$("Apple",[["path",{d:"M12 20.94c1.5 0 2.75 1.06 4 1.06 3 0 6-8 6-12.22A4.91 4.91 0 0 0 17 5c-2.22 0-4 1.44-5 2-1-.56-2.78-2-5-2a4.9 4.9 0 0 0-5 4.78C2 14 5 22 8 22c1.25 0 2.5-1.06 4-1.06Z",key:"3s7exb"}],["path",{d:"M10 2c1 .5 2 2 2 5",key:"fcco2y"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lh=$("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ph=$("ArrowLeftRight",[["path",{d:"M8 3 4 7l4 4",key:"9rb6wj"}],["path",{d:"M4 7h16",key:"6tx8e3"}],["path",{d:"m16 21 4-4-4-4",key:"siv7j2"}],["path",{d:"M20 17H4",key:"h6l3hr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rh=$("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ai=$("Box",[["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a0=$("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zh=$("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oh=$("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uh=$("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $h=$("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c0=$("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d0=$("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ci=$("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hs=$("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mn=$("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fh=$("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=$("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bh=$("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tr=$("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zc=$("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const di=$("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yr=$("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ua=$("Gamepad2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u0=$("Gem",[["path",{d:"M6 3h12l4 6-10 13L2 9Z",key:"1pcd5k"}],["path",{d:"M11 3 8 9l4 13 4-13-3-6",key:"1fcu3u"}],["path",{d:"M2 9h20",key:"16fsjt"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vh=$("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qh=$("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m0=$("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lr=$("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=$("Infinity",[["path",{d:"M12 12c-2-2.67-4-4-6-4a4 4 0 1 0 0 8c2 0 4-1.33 6-4Zm0 0c2 2.67 4 4 6 4a4 4 0 0 0 0-8c-2 0-4 1.33-6 4Z",key:"1z0uae"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f0=$("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ui=$("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=$("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mi=$("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fn=$("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=$("MessageSquareOff",[["path",{d:"M21 15V5a2 2 0 0 0-2-2H9",key:"43el77"}],["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M3.6 3.6c-.4.3-.6.8-.6 1.4v16l4-4h10",key:"pwpm4a"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oc=$("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=$("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=$("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=$("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hh=$("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g0=$("Puzzle",[["path",{d:"M19.439 7.85c-.049.322.059.648.289.878l1.568 1.568c.47.47.706 1.087.706 1.704s-.235 1.233-.706 1.704l-1.611 1.611a.98.98 0 0 1-.837.276c-.47-.07-.802-.48-.968-.925a2.501 2.501 0 1 0-3.214 3.214c.446.166.855.497.925.968a.979.979 0 0 1-.276.837l-1.61 1.61a2.404 2.404 0 0 1-1.705.707 2.402 2.402 0 0 1-1.704-.706l-1.568-1.568a1.026 1.026 0 0 0-.877-.29c-.493.074-.84.504-1.02.968a2.5 2.5 0 1 1-3.237-3.237c.464-.18.894-.527.967-1.02a1.026 1.026 0 0 0-.289-.877l-1.568-1.568A2.402 2.402 0 0 1 1.998 12c0-.617.236-1.234.706-1.704L4.23 8.77c.24-.24.581-.353.917-.303.515.077.877.528 1.073 1.01a2.5 2.5 0 1 0 3.259-3.259c-.482-.196-.933-.558-1.01-1.073-.05-.336.062-.676.303-.917l1.525-1.525A2.402 2.402 0 0 1 12 1.998c.617 0 1.234.236 1.704.706l1.568 1.568c.23.23.556.338.877.29.493-.074.84-.504 1.02-.968a2.5 2.5 0 1 1 3.237 3.237c-.464.18-.894.527-.967 1.02Z",key:"i0oyt7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hn=$("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wh=$("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fi=$("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pr=$("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uc=$("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=$("Smile",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 14s1.5 2 4 2 4-2 4-2",key:"1y1vjs"}],["line",{x1:"9",x2:"9.01",y1:"9",y2:"9",key:"yxxnd0"}],["line",{x1:"15",x2:"15.01",y1:"9",y2:"9",key:"1p4y9e"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cn=$("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=$("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qh=$("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v0=$("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w0=$("Trees",[["path",{d:"M10 10v.2A3 3 0 0 1 8.9 16H5a3 3 0 0 1-1-5.8V10a3 3 0 0 1 6 0Z",key:"1l6gj6"}],["path",{d:"M7 16v6",key:"1a82de"}],["path",{d:"M13 19v3",key:"13sx9i"}],["path",{d:"M12 19h8.3a1 1 0 0 0 .7-1.7L18 14h.3a1 1 0 0 0 .7-1.7L16 9h.2a1 1 0 0 0 .8-1.7L13 3l-1.4 1.5",key:"1sj9kv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hi=$("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=$("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=$("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dh=$("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=$("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pi=$("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pn=$("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bn=$("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jh=$("Volume1",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=$("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mo=$("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zh=$("WifiOff",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kh=$("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ma=$("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b0=$("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),fo={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1},Yh=(fo==null?void 0:fo.VITE_API_URL)||"";class Xh{constructor(){this.token=null,this.token=localStorage.getItem("token")}async request(t,n={}){const s=`${Yh}${t}`,l={headers:{"Content-Type":"application/json",...this.token&&{Authorization:`Bearer ${this.token}`},...n.headers},...n};try{const o=await fetch(s,l);if(!o.ok){const i=await o.json().catch(()=>({}));throw o.status===401&&i.code==="MULTIPLE_LOGIN"&&(this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),window.location.reload()),new Error(i.message||`HTTP error! status: ${o.status}`)}return await o.json()}catch(o){throw console.error("API request failed:",o),o}}async login(t,n){const s=await this.request("/api/auth/login",{method:"POST",body:JSON.stringify({username:t,password:n})});return this.token=s.token,localStorage.setItem("token",s.token),localStorage.setItem("username",s.user.username),localStorage.setItem("isAdmin",s.user.isAdmin?"true":"false"),{...s,username:s.user.username,isAdmin:s.user.isAdmin}}async register(t,n,s){return this.request("/api/auth/register",{method:"POST",body:JSON.stringify({username:t,email:n,password:s})})}async logout(){try{await this.request("/api/auth/logout",{method:"POST"})}catch{}this.token=null,localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}async getCurrentUser(){return this.request("/api/user")}async updateProfile(t){return this.request("/api/users/profile",{method:"PUT",body:JSON.stringify(t)})}async uploadAvatar(t){const n=new FormData;return n.append("avatar",t),this.request("/api/users/upload-avatar",{method:"POST",headers:{},body:n})}async getAllUsersAdmin(){return this.request("/api/admin/users")}async searchUsersAdmin(t){return this.request(`/api/users/admin/search?q=${encodeURIComponent(t)}`)}async updateUserAdmin(t,n){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(n)})}async updateCurrency(t,n){return this.request("/api/user/currency",{method:"PUT",body:JSON.stringify({goldCoins:t,pearls:n})})}async getNotifications(){return this.request("/api/notifications")}async markNotificationAsRead(t){return this.request(`/api/notifications/${t}/read`,{method:"PUT"})}async markAllNotificationsAsRead(){return this.request("/api/notifications/mark-all-read",{method:"PUT"})}async getMessages(t){return this.request(`/api/messages/${t}`)}async sendMessage(t,n,s="text"){return this.request("/api/messages",{method:"POST",body:JSON.stringify({recipientId:t,content:n,messageType:s})})}async getFriends(){return this.request("/api/profile/friends")}async getFriendRequests(){return this.request("/api/profile/friend-requests")}async sendFriendRequest(t){return this.request("/api/profile/friend-request",{method:"POST",body:JSON.stringify({friendId:t})})}async acceptFriendRequest(t){return this.request("/api/profile/accept-friend",{method:"POST",body:JSON.stringify({friendshipId:t})})}async checkFriendship(t){return this.request(`/api/friends/check/${t}`)}async searchUserById(t){return this.request(`/api/users/search-by-id/${t}`)}async exchangeGoldToPearls(t){return this.request("/api/profile/exchange-gold-to-pearls",{method:"POST",body:JSON.stringify({goldAmount:t})})}async sendItem(t,n,s){return this.request("/api/profile/send-item",{method:"POST",body:JSON.stringify({toUserId:t,itemType:n,message:s})})}async getGifts(){return this.request("/api/profile/gifts")}async sendGift(t,n,s,l){return this.request("/api/profile/send-gift",{method:"POST",body:JSON.stringify({toUserId:t,giftType:n,amount:s,message:l})})}async claimGift(t){return this.request("/api/profile/claim-gift",{method:"POST",body:JSON.stringify({giftId:t})})}async getItems(){return this.request("/api/profile/items")}async getVoiceRoom(){return this.request("/api/voice-room")}async joinVoiceSeat(t){return this.request("/api/voice-room/join-seat",{method:"POST",body:JSON.stringify({seatNumber:t})})}async leaveVoiceSeat(){return this.request("/api/voice-room/leave-seat",{method:"POST"})}async leaveSeat(){return this.leaveVoiceSeat()}async requestMic(){return this.request("/api/voice-room/request-mic",{method:"POST"})}async cancelMicRequest(){return this.request("/api/voice-room/cancel-mic-request",{method:"POST"})}async sendVoiceRoomMessage(t){return this.request("/api/voice-room/send-message",{method:"POST",body:JSON.stringify({content:t})})}async getVoiceRoomMessages(){return this.request("/api/voice-room/messages")}async toggleMute(t){return this.request("/api/voice-room/toggle-mute",{method:"POST",body:JSON.stringify({isMuted:t})})}async kickUserFromVoiceRoom(t,n){return this.request("/api/voice-room/admin/kick",{method:"POST",body:JSON.stringify({userId:t,durationInMinutes:n})})}async muteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/mute",{method:"POST",body:JSON.stringify({userId:t})})}async unmuteUserInVoiceRoom(t){return this.request("/api/voice-room/admin/unmute",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromSeat(t){return this.request("/api/voice-room/admin/remove-seat",{method:"POST",body:JSON.stringify({userId:t})})}async removeUserFromQueue(t){return this.request("/api/voice-room/admin/remove-queue",{method:"POST",body:JSON.stringify({userId:t})})}async banUserFromChat(t){return this.request("/api/voice-room/admin/ban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async unbanUserFromChat(t){return this.request("/api/voice-room/admin/unban-chat",{method:"POST",body:JSON.stringify({userId:t})})}async getUserItems(t){return this.request(`/api/user-items/${t}`)}async getShield(t){return this.request(`/api/profile/shield/${t}`)}async activateShield(t){return this.request("/api/profile/activate-shield",{method:"POST",body:JSON.stringify({shieldType:t})})}async getTransactions(t=1,n=20){return this.request(`/api/profile/transactions?page=${t}&limit=${n}`)}async chargeBalance(t){return this.request("/api/profile/charge-balance",{method:"POST",body:JSON.stringify({amount:t})})}async activateItem(t){return this.request("/api/profile/activate-item",{method:"POST",body:JSON.stringify({itemId:t})})}async getGameSettings(){return this.request("/api/game/settings")}async updateGameSettings(t){return this.request("/api/game/settings",{method:"POST",body:t})}async getSuspiciousActivities(){return this.request("/api/admin/suspicious-activities")}async getPlayerId(t){return this.request(`/api/admin/users/${t}/player-id`)}async updatePlayerId(t,n){return this.request(`/api/admin/users/${t}/player-id`,{method:"PUT",body:JSON.stringify({playerId:n})})}async getUsersWithIds(t=1,n=12,s=""){return this.request(`/api/users/admin/users-with-ids?page=${t}&limit=${n}&search=${s}`)}async updateUser(t,n){return this.request(`/api/users/admin/update/${t}`,{method:"PUT",body:JSON.stringify(n)})}async deleteUser(t){return this.request(`/api/users/admin/delete/${t}`,{method:"DELETE"})}async deleteUserImage(t){return this.request(`/api/users/admin/delete-image/${t}`,{method:"DELETE"})}async manageUserImage(t,n,s,l){return this.request("/api/users/admin/manage-user-image",{method:"PUT",body:JSON.stringify({targetUserId:t,action:n,imageData:s,imageType:l})})}async debugAllUsers(){return this.request("/api/admin/debug/all-users")}async updateBalance(t,n,s,l){return this.request("/api/users/update-balance",{method:"POST",body:JSON.stringify({balanceChange:t,gameType:n,sessionId:s,gameResult:l})})}async getGameProfile(){return this.request("/api/users/profile")}async endGameSession(t){return this.request("/api/games/session-end",{method:"POST",body:t})}async getPlayerStats(){return this.request("/api/games/player-stats")}clearLocalData(){localStorage.removeItem("token"),localStorage.removeItem("userData"),localStorage.removeItem("adminToken"),localStorage.removeItem("selectedUser"),localStorage.removeItem("userCache"),console.log("🧹 Cleared all local storage data")}}const K=new Xh,$c={ar:{login:"تسجيل الدخول",username:"اسم المستخدم",password:"كلمة المرور",loginButton:"دخول",switchToRegister:"ليس لديك حساب؟ إنشاء حساب",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",requiredFields:"الرجاء إدخال اسم المستخدم وكلمة المرور",loginFailed:"فشل في تسجيل الدخول"},en:{login:"Login",username:"Username",password:"Password",loginButton:"Login",switchToRegister:"Don't have an account? Register",loginDesc:"Profitable activities that blend with fun and friendships",requiredFields:"Please enter username and password",loginFailed:"Login failed"},ur:{login:"لاگ ان",username:"صارف نام",password:"پاس ورڈ",loginButton:"داخل ہوں",switchToRegister:"اکاؤنٹ نہیں ہے؟ رجسٹر کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",requiredFields:"براہ کرم صارف نام اور پاس ورڈ درج کریں",loginFailed:"لاگ ان ناکام"},es:{login:"Iniciar Sesión",username:"Nombre de Usuario",password:"Contraseña",loginButton:"Entrar",switchToRegister:"¿No tienes cuenta? Regístrate",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",requiredFields:"Por favor ingresa nombre de usuario y contraseña",loginFailed:"Error al iniciar sesión"}},ep=({onLoginSuccess:e,onSwitchToRegister:t})=>{const[n,s]=y.useState({username:"",password:""}),[l,o]=y.useState(!1),[i,a]=y.useState(!1),[c,d]=y.useState(""),g=localStorage.getItem("selectedLanguage")||"ar",w=j=>{var k;return((k=$c[g])==null?void 0:k[j])||$c.ar[j]||j},b=async j=>{if(j.preventDefault(),d(""),!n.username.trim()||!n.password.trim()){d(w("requiredFields"));return}a(!0);try{const k=await K.login(n.username,n.password);e(k)}catch(k){d(k.message||w("loginFailed"))}finally{a(!1)}},N=j=>{const{name:k,value:S}=j.target;s(m=>({...m,[k]:S})),c&&d("")};return r.jsx("div",{className:"w-full max-w-sm mx-auto",children:r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-800/20 via-blue-900/25 to-slate-800/20 rounded-2xl blur-lg group-hover:blur-xl transition-all duration-500 animate-pulse"}),r.jsx("div",{className:"relative bg-gradient-to-br from-blue-900/40 via-blue-800/30 to-slate-800/40 backdrop-blur-2xl rounded-2xl p-5 border border-blue-400/30 shadow-xl hover:shadow-blue-500/30 transition-all duration-500 hover:border-blue-300/50",children:r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsxs("div",{className:"relative mx-auto mb-4",children:[r.jsxs("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full flex items-center justify-center mx-auto relative shadow-xl shadow-blue-500/40 hover:shadow-blue-500/60 transition-all duration-500 hover:scale-105 group",children:[r.jsx(ai,{className:"w-8 h-8 text-white drop-shadow-lg"}),r.jsx(Ll,{className:"w-4 h-4 text-white absolute -top-0.5 -right-0.5 animate-spin",style:{animationDuration:"8s"}})]}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 rounded-full blur-lg opacity-30 animate-pulse"})]}),r.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent mb-2 drop-shadow-lg",children:w("login")}),r.jsxs("p",{className:"text-purple-200 font-medium text-sm",children:["🌟 ",w("loginDesc")," 🌟"]})]}),c&&r.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:r.jsx("p",{className:"text-red-300 text-sm text-center",children:c})}),r.jsxs("form",{onSubmit:b,className:"space-y-4",children:[r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:r.jsx(mi,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),r.jsx("input",{type:"text",name:"username",value:n.username,onChange:N,placeholder:w("username"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"username",required:!0}),r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]}),r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none z-10",children:r.jsx(ui,{className:"h-4 w-4 text-blue-300 group-focus-within:text-white transition-colors duration-300"})}),r.jsx("input",{type:l?"text":"password",name:"password",value:n.password,onChange:N,placeholder:w("password"),className:"w-full bg-blue-900/20 border border-blue-400/30 rounded-xl px-4 py-3 pr-10 pl-10 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 hover:border-blue-300/50 transition-all duration-300 text-sm backdrop-blur-sm",autoComplete:"current-password",required:!0}),r.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center z-10 group/eye",children:l?r.jsx(di,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"}):r.jsx(Yr,{className:"h-4 w-4 text-blue-300 hover:text-white group-hover/eye:scale-110 transition-all duration-300"})}),r.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"})]})]}),r.jsxs("button",{type:"submit",disabled:i,className:"relative w-full group overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl transition-all duration-500 group-hover:from-blue-600 group-hover:via-blue-700 group-hover:to-blue-800"}),r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-700 via-blue-800 to-blue-900 rounded-xl blur-md opacity-40 group-hover:opacity-60 transition-opacity duration-500"}),r.jsx("div",{className:"relative bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 hover:from-blue-500 hover:via-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 transform hover:scale-105 active:scale-95 flex items-center justify-center gap-2 text-sm shadow-xl",children:i?r.jsxs(r.Fragment,{children:[r.jsx(f0,{className:"w-4 h-4 animate-spin"}),r.jsx("span",{className:"animate-pulse",children:w("loggingIn")||"جاري تسجيل الدخول..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(ai,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),r.jsxs("span",{className:"group-hover:tracking-wider transition-all duration-300",children:["🚀 ",w("loginButton")]})]})}),r.jsx("div",{className:"absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:r.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"})})]})]}),r.jsxs("div",{className:"mt-5 space-y-4",children:[r.jsx("div",{className:"text-center",children:r.jsxs("div",{className:"bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10",children:[r.jsx("p",{className:"text-blue-200 mb-2 text-sm",children:w("switchToRegister")}),r.jsxs("button",{onClick:t,className:"group relative inline-flex items-center gap-2 bg-gradient-to-r from-blue-800/20 to-blue-900/20 hover:from-blue-700/30 hover:to-blue-800/30 text-blue-300 hover:text-white font-bold py-2 px-4 rounded-lg border border-blue-400/30 hover:border-blue-300/60 transition-all duration-300 hover:scale-105",children:[r.jsx(pn,{className:"w-4 h-4 group-hover:rotate-12 transition-transform duration-300"}),r.jsx("span",{className:"group-hover:tracking-wider transition-all duration-300 text-sm",children:"✨ إنشاء حساب جديد"})]})]})}),r.jsx("div",{className:"bg-gradient-to-br from-blue-800/20 via-blue-900/15 to-slate-800/20 backdrop-blur-sm rounded-xl p-4 border border-blue-400/20",children:r.jsxs("div",{className:"text-center space-y-3",children:[r.jsx("h3",{className:"text-blue-200 font-bold text-sm mb-3",children:"📞 للاستفسار وشحن العملات في INFINITY BOX"}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[r.jsx(mi,{className:"w-3 h-3"}),r.jsx("span",{className:"font-medium",children:"<EMAIL>"})]}),r.jsxs("div",{className:"flex items-center justify-center gap-2 text-blue-300 text-xs",children:[r.jsx(Hh,{className:"w-3 h-3"}),r.jsx("span",{className:"font-medium",children:"00966554593007"})]})]}),r.jsx("div",{className:"pt-2 border-t border-blue-400/20",children:r.jsx("p",{className:"text-blue-400/70 text-xs",children:"© 2024 INFINITY BOX - جميع الحقوق محفوظة"})})]})})]})]})})]})})},Fc={ar:{register:"إنشاء حساب",username:"اسم المستخدم",email:"البريد الإلكتروني",password:"كلمة المرور",confirmPassword:"تأكيد كلمة المرور",registerButton:"إنشاء حساب",switchToLogin:"لديك حساب؟ تسجيل الدخول",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",usernameRequired:"اسم المستخدم مطلوب",usernameMinLength:"اسم المستخدم يجب أن يكون 3 أحرف على الأقل",emailRequired:"البريد الإلكتروني مطلوب",emailInvalid:"البريد الإلكتروني غير صالح",passwordRequired:"كلمة المرور مطلوبة",passwordMinLength:"كلمة المرور يجب أن تكون 6 أحرف على الأقل",passwordMismatch:"كلمات المرور غير متطابقة",registering:"جاري إنشاء الحساب...",registerFailed:"فشل في إنشاء الحساب"},en:{register:"Register",username:"Username",email:"Email",password:"Password",confirmPassword:"Confirm Password",registerButton:"Create Account",switchToLogin:"Have an account? Login",registerDesc:"Join us and enjoy a unique experience",usernameRequired:"Username is required",usernameMinLength:"Username must be at least 3 characters",emailRequired:"Email is required",emailInvalid:"Invalid email address",passwordRequired:"Password is required",passwordMinLength:"Password must be at least 6 characters",passwordMismatch:"Passwords do not match",registering:"Creating account...",registerFailed:"Failed to create account"},ur:{register:"رجسٹر",username:"صارف نام",email:"ای میل",password:"پاس ورڈ",confirmPassword:"پاس ورڈ کی تصدیق",registerButton:"اکاؤنٹ بنائیں",switchToLogin:"اکاؤنٹ ہے؟ لاگ ان کریں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",usernameRequired:"صارف نام ضروری ہے",usernameMinLength:"صارف نام کم از کم 3 حروف کا ہونا چاہیے",emailRequired:"ای میل ضروری ہے",emailInvalid:"غلط ای میل ایڈریس",passwordRequired:"پاس ورڈ ضروری ہے",passwordMinLength:"پاس ورڈ کم از کم 6 حروف کا ہونا چاہیے",passwordMismatch:"پاس ورڈ میل نہیں کھاتے",registering:"اکاؤنٹ بنایا جا رہا ہے...",registerFailed:"اکاؤنٹ بنانے میں ناکامی"},es:{register:"Registrarse",username:"Nombre de Usuario",email:"Correo Electrónico",password:"Contraseña",confirmPassword:"Confirmar Contraseña",registerButton:"Crear Cuenta",switchToLogin:"¿Tienes cuenta? Inicia sesión",registerDesc:"Únete a nosotros y disfruta de una experiencia única",usernameRequired:"El nombre de usuario es requerido",usernameMinLength:"El nombre de usuario debe tener al menos 3 caracteres",emailRequired:"El correo electrónico es requerido",emailInvalid:"Dirección de correo electrónico inválida",passwordRequired:"La contraseña es requerida",passwordMinLength:"La contraseña debe tener al menos 6 caracteres",passwordMismatch:"Las contraseñas no coinciden",registering:"Creando cuenta...",registerFailed:"Error al crear la cuenta"}},tp=({onRegisterSuccess:e,onSwitchToLogin:t})=>{const[n,s]=y.useState({username:"",email:"",password:"",confirmPassword:""}),[l,o]=y.useState(!1),[i,a]=y.useState(!1),[c,d]=y.useState(!1),[g,w]=y.useState(""),b=localStorage.getItem("selectedLanguage")||"ar",N=m=>{var u;return((u=Fc[b])==null?void 0:u[m])||Fc.ar[m]||m},j=()=>n.username.trim()?n.username.length<3?N("usernameMinLength"):n.email.trim()?/\S+@\S+\.\S+/.test(n.email)?n.password?n.password.length<6?N("passwordMinLength"):n.password!==n.confirmPassword?N("passwordMismatch"):null:N("passwordRequired"):N("emailInvalid"):N("emailRequired"):N("usernameRequired"),k=async m=>{m.preventDefault(),w("");const u=j();if(u){w(u);return}d(!0);try{const p=await K.register(n.username,n.email,n.password);p.token&&p.user&&(localStorage.setItem("token",p.token),localStorage.setItem("userData",JSON.stringify(p.user))),p.isNewUser&&p.welcomeMessage?alert(p.welcomeMessage):p.rewards&&alert(`🎉 مرحباً بك في المنصة!

هدية الترحيب:
🪙 ${p.rewards.goldCoins.toLocaleString()} عملة ذهبية
🦪 ${p.rewards.pearls} لآلئ

استمتع باللعب واربح المزيد!`),e(p.user)}catch(p){w(p.message||N("registerFailed"))}finally{d(!1)}},S=m=>{const{name:u,value:p}=m.target;s(M=>({...M,[u]:p})),g&&w("")};return r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 shadow-2xl",children:[r.jsxs("div",{className:"text-center mb-8",children:[r.jsxs("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4 relative",children:[r.jsx(Pl,{className:"w-10 h-10 text-white"}),r.jsx(Ll,{className:"w-5 h-5 text-white absolute -top-1 -right-1"})]}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:N("register")}),r.jsx("p",{className:"text-gray-300",children:N("registerDesc")})]}),g&&r.jsx("div",{className:"bg-red-500/20 border border-red-500/50 rounded-xl p-4 mb-6",children:r.jsx("p",{className:"text-red-300 text-sm text-center",children:g})}),r.jsxs("form",{onSubmit:k,className:"space-y-6",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(pn,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"text",name:"username",value:n.username,onChange:S,placeholder:N("username"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"username",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(mi,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:"email",name:"email",value:n.email,onChange:S,placeholder:N("email"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"email",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(ui,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:l?"text":"password",name:"password",value:n.password,onChange:S,placeholder:N("password"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",autoComplete:"new-password",required:!0}),r.jsx("button",{type:"button",onClick:()=>o(!l),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:l?r.jsx(di,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):r.jsx(Yr,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx(ui,{className:"h-5 w-5 text-gray-400"})}),r.jsx("input",{type:i?"text":"password",name:"confirmPassword",value:n.confirmPassword,onChange:S,placeholder:N("confirmPassword"),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 pr-12 pl-12 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",required:!0}),r.jsx("button",{type:"button",onClick:()=>a(!i),className:"absolute inset-y-0 left-0 pl-3 flex items-center",children:i?r.jsx(di,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"}):r.jsx(Yr,{className:"h-5 w-5 text-gray-400 hover:text-white transition-colors"})})]})]}),r.jsx("button",{type:"submit",disabled:c,className:"w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50 disabled:cursor-not-allowed text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 flex items-center justify-center gap-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx(f0,{className:"w-5 h-5 animate-spin"}),N("registering")]}):r.jsxs(r.Fragment,{children:[r.jsx(ai,{className:"w-5 h-5"}),N("registerButton")]})})]}),r.jsxs("div",{className:"mt-6 text-center",children:[r.jsx("p",{className:"text-gray-300",children:N("switchToLogin")}),r.jsx("button",{onClick:t,className:"text-blue-400 hover:text-blue-300 font-semibold transition-colors mt-2",children:N("login")})]})]})})},Bc={ar:{welcome:"مرحباً بك",login:"تسجيل الدخول",register:"إنشاء حساب",selectLanguage:"اختر اللغة",loginDesc:"أنشطة مربحة تمتزج مع المرح والصداقات",registerDesc:"انضم إلينا واستمتع بتجربة فريدة",registerSuccess:"تم إنشاء الحساب بنجاح!",welcomeMessage:"مرحباً بك في INFINITY BOX - يمكنك الآن تسجيل الدخول"},en:{welcome:"Welcome",login:"Login",register:"Register",selectLanguage:"Select Language",loginDesc:"Profitable activities that blend with fun and friendships",registerDesc:"Join us and enjoy a unique experience",registerSuccess:"Account created successfully!",welcomeMessage:"Welcome to INFINITY BOX - you can now login"},ur:{welcome:"خوش آمدید",login:"لاگ ان",register:"رجسٹر",selectLanguage:"زبان منتخب کریں",loginDesc:"منافع بخش سرگرمیاں جو تفریح اور دوستی کے ساتھ ملتی ہیں",registerDesc:"ہمارے ساتھ شامل ہوں اور منفرد تجربہ کا لطف اٹھائیں",registerSuccess:"اکاؤنٹ کامیابی سے بن گیا!",welcomeMessage:"INFINITY BOX میں خوش آمدید - اب آپ لاگ ان کر سکتے ہیں"},es:{welcome:"Bienvenido",login:"Iniciar Sesión",register:"Registrarse",selectLanguage:"Seleccionar Idioma",loginDesc:"Actividades rentables que se mezclan con diversión y amistades",registerDesc:"Únete a nosotros y disfruta de una experiencia única",registerSuccess:"¡Cuenta creada exitosamente!",welcomeMessage:"Bienvenido a INFINITY BOX - ahora puedes iniciar sesión"}},np=({onAuthSuccess:e})=>{const[t,n]=y.useState("login"),[s,l]=y.useState(!1),[o,i]=y.useState(()=>localStorage.getItem("selectedLanguage")||"ar"),[a,c]=y.useState(!1),d=k=>({ar:"🇸🇦",en:"🇺🇸",ur:"🇵🇰",es:"🇪🇸"})[k]||"🌍",g=k=>({ar:"العربية",en:"English",ur:"اردو",es:"Español"})[k]||k,w=k=>{var S;return((S=Bc[o])==null?void 0:S[k])||Bc.ar[k]||k},b=k=>{i(k),localStorage.setItem("selectedLanguage",k),document.documentElement.dir=["ar","ur"].includes(k)?"rtl":"ltr",document.documentElement.lang=k,c(!1)};y.useEffect(()=>{document.documentElement.dir=["ar","ur"].includes(o)?"rtl":"ltr",document.documentElement.lang=o},[o]);const N=k=>{if(k.isAdmin&&window.confirm("مرحباً أيها المشرف! هل تريد الذهاب إلى لوحة التحكم؟ (إلغاء للذهاب إلى اللعبة)")){window.location.href="/admin.html";return}e(k)},j=k=>{k?N(k):(l(!0),setTimeout(()=>{l(!1),n("login")},3e3))};return r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 text-white relative overflow-hidden",children:[r.jsx("div",{className:"absolute top-4 right-4 z-50",children:r.jsxs("button",{onClick:()=>c(!0),className:"flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full hover:bg-white/20 transition-all duration-300",children:[r.jsx(qh,{className:"w-5 h-5"}),r.jsx("span",{className:"text-2xl",children:d(o)}),r.jsx("span",{className:"hidden sm:inline",children:g(o)})]})}),a&&r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:r.jsxs("div",{className:"bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 max-w-md w-full",children:[r.jsx("h3",{className:"text-xl font-bold text-center mb-6",children:w("selectLanguage")}),r.jsx("div",{className:"grid grid-cols-2 gap-4",children:["ar","en","ur","es"].map(k=>r.jsxs("button",{onClick:()=>b(k),className:`flex items-center gap-3 p-4 rounded-xl transition-all duration-300 ${o===k?"bg-blue-500/30 border-2 border-blue-400":"bg-white/5 border border-white/10 hover:bg-white/10"}`,children:[r.jsx("span",{className:"text-3xl",children:d(k)}),r.jsx("span",{className:"font-medium",children:g(k)})]},k))}),r.jsx("button",{onClick:()=>c(!1),className:"w-full mt-6 py-3 bg-gray-500/20 hover:bg-gray-500/30 rounded-xl transition-all duration-300",children:"إغلاق / Close"})]})}),r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900/50 via-purple-900/30 to-indigo-900/50"}),r.jsxs("div",{className:"absolute -inset-10 opacity-30",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),r.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-72 h-72 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"}),r.jsx("div",{className:"absolute top-1/2 right-1/3 w-64 h-64 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-3000"})]}),r.jsxs("div",{className:"absolute inset-0 opacity-20",children:[r.jsx("div",{className:"absolute top-10 left-10 w-32 h-32 bg-white rounded-full filter blur-xl animate-bounce delay-500"}),r.jsx("div",{className:"absolute bottom-20 right-20 w-24 h-24 bg-yellow-300 rounded-full filter blur-lg animate-bounce delay-1500"}),r.jsx("div",{className:"absolute top-1/3 right-10 w-20 h-20 bg-green-400 rounded-full filter blur-lg animate-bounce delay-2500"})]}),r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((k,S)=>r.jsx("div",{className:`${S%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${S*150}ms`,animationDuration:`${2+S%3}s`}},S))})}),r.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"})]}),r.jsxs("div",{className:"relative z-10 min-h-screen flex flex-col",children:[r.jsxs("header",{className:"text-center py-12 relative",children:[r.jsx("div",{className:"relative z-10",children:r.jsx("div",{className:"inline-flex items-center justify-center mb-8",children:r.jsxs("div",{className:"relative group",children:[r.jsxs("div",{className:"w-32 h-32 sphere-3d rounded-full flex items-center justify-center relative group-hover:scale-110 transition-all duration-500 rotate-y",children:[r.jsx("div",{className:"color-layer"}),r.jsxs("div",{className:"text-center relative z-10",children:[r.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight mb-1 drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"INFINITY"}),r.jsx("div",{className:"bg-gradient-to-r from-yellow-200 via-amber-100 to-blue-200 bg-clip-text text-transparent font-black text-lg leading-tight drop-shadow-2xl",style:{textShadow:"2px 2px 4px rgba(0,0,0,0.8), 0 0 10px rgba(255,255,255,0.3)"},children:"BOX"})]}),r.jsx(Ll,{className:"w-6 h-6 text-white absolute -top-2 -right-2 animate-spin bg-gradient-to-r from-yellow-600 to-blue-700 rounded-full p-1 z-20",style:{animationDuration:"12s"}})]}),r.jsx("div",{className:"absolute inset-0 sphere-glow rounded-full blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500 rotate-y-reverse"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-white/10 rotate-y-fast"}),r.jsx("div",{className:"absolute -inset-2 rounded-full border border-white/5 rotate-y-slow"})]})})}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-5",children:r.jsx("div",{className:"text-8xl font-black text-white animate-spin",style:{animationDuration:"25s"},children:"∞"})})]}),r.jsx("div",{className:"flex-1 flex items-start justify-center px-4 pt-8 pb-8",children:r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx("div",{className:"w-full",children:s?r.jsx("div",{className:"w-full max-w-md mx-auto",children:r.jsxs("div",{className:"bg-green-500/20 border border-green-500/50 rounded-3xl p-8 text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:w("registerSuccess")}),r.jsx("p",{className:"text-green-300",children:w("welcomeMessage")})]})}):t==="login"?r.jsx(ep,{onLoginSuccess:N,onSwitchToRegister:()=>n("register")}):r.jsx(tp,{onRegisterSuccess:j,onSwitchToLogin:()=>n("login")})})})})})]})]})};function rp(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,s,l,o,i=[],a="",c=e.split("/");for(c[0]||c.shift();l=c.shift();)n=l[0],n==="*"?(i.push(n),a+=l[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(s=l.indexOf("?",1),o=l.indexOf(".",1),i.push(l.substring(1,~s?s:~o?o:l.length)),a+=~s&&!~o?"(?:/([^/]+?))?":"/([^/]+?)",~o&&(a+=(~s?"?":"")+"\\"+l.substring(o))):a+="/"+l;return{keys:i,pattern:new RegExp("^"+a+(t?"(?=$|/)":"/?$"),"i")}}var j0={exports:{}},N0={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rr=y;function sp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var lp=typeof Object.is=="function"?Object.is:sp,op=rr.useState,ip=rr.useEffect,ap=rr.useLayoutEffect,cp=rr.useDebugValue;function dp(e,t){var n=t(),s=op({inst:{value:n,getSnapshot:t}}),l=s[0].inst,o=s[1];return ap(function(){l.value=n,l.getSnapshot=t,ho(l)&&o({inst:l})},[e,n,t]),ip(function(){return ho(l)&&o({inst:l}),e(function(){ho(l)&&o({inst:l})})},[e]),cp(n),n}function ho(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lp(e,n)}catch{return!0}}function up(e,t){return t()}var mp=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?up:dp;N0.useSyncExternalStore=rr.useSyncExternalStore!==void 0?rr.useSyncExternalStore:mp;j0.exports=N0;var fp=j0.exports;const hp=xm.useInsertionEffect,pp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",gp=pp?y.useLayoutEffect:y.useEffect,xp=hp||gp,k0=e=>{const t=y.useRef([e,(...n)=>t[0](...n)]).current;return xp(()=>{t[0]=e}),t[1]},yp="popstate",fa="pushState",ha="replaceState",vp="hashchange",Vc=[yp,fa,ha,vp],wp=e=>{for(const t of Vc)addEventListener(t,e);return()=>{for(const t of Vc)removeEventListener(t,e)}},S0=(e,t)=>fp.useSyncExternalStore(wp,e,t),bp=()=>location.search,jp=({ssrSearch:e=""}={})=>S0(bp,()=>e),qc=()=>location.pathname,Np=({ssrPath:e}={})=>S0(qc,e?()=>e:qc),kp=(e,{replace:t=!1,state:n=null}={})=>history[t?ha:fa](n,"",e),Sp=(e={})=>[Np(e),kp],Hc=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[Hc]>"u"){for(const e of[fa,ha]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),s=new Event(e);return s.arguments=arguments,dispatchEvent(s),n}}Object.defineProperty(window,Hc,{value:!0})}const Cp=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",C0=(e="")=>e==="/"?"":e,Mp=(e,t)=>e[0]==="~"?e.slice(1):C0(t)+e,Ep=(e="",t)=>Cp(Wc(C0(e)),Wc(t)),Wc=e=>{try{return decodeURI(e)}catch{return e}},Ip={hook:Sp,searchHook:jp,parser:rp,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},Ap=y.createContext(Ip),M0=()=>y.useContext(Ap),_p={};y.createContext(_p);const E0=e=>{const[t,n]=e.hook(e);return[Ep(e.base,t),k0((s,l)=>n(Mp(s,e.base),l))]},I0=()=>E0(M0());y.forwardRef((e,t)=>{const n=M0(),[s,l]=E0(n),{to:o="",href:i=o,onClick:a,asChild:c,children:d,className:g,replace:w,state:b,...N}=e,j=k0(S=>{S.ctrlKey||S.metaKey||S.altKey||S.shiftKey||S.button!==0||(a==null||a(S),S.defaultPrevented||(S.preventDefault(),l(i,e)))}),k=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return c&&y.isValidElement(d)?y.cloneElement(d,{onClick:j,href:k}):y.createElement("a",{...N,onClick:j,href:k,className:g!=null&&g.call?g(s===i):g,children:d,ref:t})});const Ws=({setActiveTab:e})=>{I0();const t=[{icon:r.jsx(b0,{className:"w-8 h-8"}),title:"تحدي السرعة",description:"اختبر سرعة ردود أفعالك في تحدي مثير",color:"from-yellow-500 to-orange-500",onClick:()=>window.open("/speed-challenge.html","_blank")},{icon:r.jsx(y0,{className:"w-8 h-8"}),title:"صناديق الحظ",description:"اكسر الصناديق واجمع الكنوز والجواهر",color:"from-green-500 to-emerald-500",onClick:()=>window.open("/game8.html","_blank")},{icon:r.jsx(g0,{className:"w-8 h-8"}),title:"ألغاز العقل",description:"حل الألغاز المعقدة واختبر ذكاءك",color:"from-blue-500 to-cyan-500",onClick:()=>window.open("/mind-puzzles.html","_blank")},{icon:r.jsx(i0,{className:"w-8 h-8"}),title:"قطف الفواكه",description:"اقطف الفواكه الساقطة واجمع النقاط",color:"from-red-500 to-yellow-500",onClick:()=>window.open("/fruit-catching.html","_blank")},{icon:r.jsx(a0,{className:"w-8 h-8"}),title:"لعبة الذاكرة",description:"اختبر ذاكرتك وطابق البطاقات",color:"from-purple-500 to-pink-500",onClick:()=>window.open("/memory-match.html","_blank")},{icon:r.jsx(w0,{className:"w-8 h-8"}),title:"لعبة الغابة",description:"اكتشف الحيوانات وتعلم أسماءها",color:"from-green-600 to-emerald-600",onClick:()=>window.open("/forest-game.html","_blank")}];return r.jsx("div",{className:"space-y-8",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8",children:r.jsxs("section",{children:[r.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-yellow-500 to-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(ua,{className:"w-5 h-5 text-white"})}),r.jsx("h2",{className:"text-xl lg:text-2xl font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:"قاعة الألعاب"})]}),r.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:t.map((n,s)=>r.jsxs("button",{onClick:n.onClick,className:"group p-2 sm:p-3 crystal-game-card rounded-xl text-center",children:[r.jsx("div",{className:`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-r ${n.color} rounded-full flex items-center justify-center mb-2 sm:mb-3 group-hover:scale-110 transition-transform duration-300 mx-auto shadow-lg ring-4 ring-white/20`,children:n.icon}),r.jsx("h3",{className:"text-xs sm:text-sm font-semibold text-white mb-1",children:n.title}),r.jsx("p",{className:"text-gray-300 text-xs leading-tight",children:n.description})]},s))})]})})})},Tp=()=>{const[e,t]=y.useState([]),[n,s]=y.useState(1),[l,o]=y.useState(""),[i,a]=y.useState(null),[c,d]=y.useState(!1),[g,w]=y.useState(null),[b,N]=y.useState(""),[j,k]=y.useState(null),[S,m]=y.useState(null);y.useState(!1),y.useState(null);const[u,p]=y.useState(!1),[M,L]=y.useState(null);y.useEffect(()=>{z()},[]);const R=(h,C)=>{L({type:h,text:C}),setTimeout(()=>L(null),5e3)},z=async(h=1,C="")=>{var T;d(!0);try{const F=await K.getUsersWithImages(h,12,C);console.log("📥 Loaded users data:",F.users),(T=F.users)==null||T.forEach(P=>{P.profileImage&&console.log(`👤 User ${P.username} has profileImage:`,P.profileImage.substring(0,50)+"...")}),t(F.users||[]),a(F.pagination),s(h),o(C)}catch(F){console.error("❌ Error loading users:",F),R("error","خطأ في تحميل المستخدمين: "+F.message)}finally{d(!1)}},U=()=>{const h=document.getElementById("searchInput"),C=(h==null?void 0:h.value.trim())||"";z(1,C)},D=(h,C)=>{console.log("🖼️ Image action for user:",h),console.log("🔍 User ID fields:",{id:h.id,userId:h.userId,_id:h._id});const T=h.id||h.userId||h._id;if(!T){R("error","خطأ: لا يمكن العثور على معرف المستخدم");return}const F={...h,id:T,userId:T};w(F),N(C),k(null),m(null),p(!0)},v=async h=>{var T;const C=(T=h.target.files)==null?void 0:T[0];if(C){if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(C.type)){R("error","نوع الملف غير مدعوم. يرجى اختيار صورة بصيغة JPG, PNG, GIF, أو WebP");return}const P=5*1024*1024;if(C.size>P){R("error","حجم الصورة كبير جداً. الحد الأقصى 5MB");return}k(C);try{const W=await ae(C,400,.9);m(W),R("info","تم تحسين الصورة وضغطها للحصول على أفضل جودة")}catch(W){console.error("Error compressing preview:",W);const Y=new FileReader;Y.onload=se=>{var me;m((me=se.target)==null?void 0:me.result)},Y.readAsDataURL(C),R("info","تم تحميل الصورة بنجاح")}}},O=h=>new Promise((C,T)=>{const F=new FileReader;F.readAsDataURL(h),F.onload=()=>C(F.result),F.onerror=P=>T(P)}),X=(h,C=500)=>new Promise((T,F)=>{try{if(console.log("📊 Original file size:",Math.round(h.size/1024),"KB"),h.size<=C*1024){console.log("✅ File size acceptable, using original"),O(h).then(T).catch(F);return}const P=new FileReader;P.onload=()=>{try{const W=P.result;console.log("✅ File converted to base64, size:",Math.round(W.length*.75/1024),"KB (estimated)"),T(W)}catch(W){console.error("❌ Error processing file:",W),F(W)}},P.onerror=W=>{console.error("❌ FileReader error:",W),F(W)},P.readAsDataURL(h)}catch(P){console.error("❌ Error in compressImageSafe:",P),F(P)}}),ae=(h,C=800,T=.8)=>new Promise((F,P)=>{try{console.log("🔄 Attempting safe compression..."),X(h,500).then(F).catch(W=>{console.warn("⚠️ Safe compression failed, trying Canvas method:",W);try{const Y=(()=>{try{return document.createElement("canvas")}catch(ge){return console.warn("❌ Canvas creation failed:",ge),null}})();if(!Y){console.warn("❌ Canvas not available, using fallback"),O(h).then(F).catch(P);return}const se=Y.getContext("2d");if(!se){console.warn("❌ Canvas context not available, using fallback"),O(h).then(F).catch(P);return}const me=new window.Image;me.onload=()=>{try{let{width:ge,height:st}=me;ge>C&&(st=st*C/ge,ge=C),Y.width=ge,Y.height=st,se.imageSmoothingEnabled=!0,se.imageSmoothingQuality="high",se.drawImage(me,0,0,ge,st);const Xt=Y.toDataURL("image/jpeg",T);console.log("✅ Canvas compression successful"),F(Xt)}catch(ge){console.error("❌ Canvas processing error:",ge),O(h).then(F).catch(P)}},me.onerror=()=>{console.error("❌ Image load error, using fallback"),O(h).then(F).catch(P)},me.src=URL.createObjectURL(h)}catch(Y){console.error("❌ Canvas method failed:",Y),O(h).then(F).catch(P)}})}catch(W){console.error("❌ Error in compressImage:",W),O(h).then(F).catch(P)}}),Pe=async h=>{if(h.preventDefault(),!(!g||!b))try{const C=g.userId||g.id;if(!C){R("error","خطأ: معرف المستخدم غير صحيح");return}console.log("🔄 Managing image for user:",C,"action:",b);let T=null,F=null;if(b.startsWith("change_")&&j)try{R("info","جاري ضغط الصورة..."),T=await ae(j,800,.85),F="image/jpeg",console.log("✅ Image compressed successfully for upload"),R("info","جاري رفع الصورة المحسنة...")}catch(P){console.error("❌ Error compressing for upload:",P),R("info","فشل ضغط الصورة، جاري استخدام الصورة الأصلية...");try{T=await O(j),F=j.type,console.log("✅ Using original file as fallback")}catch(W){throw console.error("❌ Error with fallback method:",W),new Error("فشل في معالجة الصورة: "+W.message)}}console.log("📤 Sending image management request:",{userId:C,action:b,hasImageData:!!T,imageType:F}),await K.manageUserImage(C,b,T||void 0,F||void 0),R("success","تم تحديث الصورة بنجاح"),console.log("🔄 Reloading users data after image update..."),await z(n,l),Me()}catch(C){console.error("❌ Image management error:",C),R("error","خطأ في إدارة الصورة: "+C.message)}},Me=()=>{p(!1),w(null),N(""),k(null),m(null)},Ee=h=>h.profileImage||h.avatar?"موجودة":"غير موجودة",pe=h=>{var T;const C=h.profileImage||h.avatar;return console.log(`🖼️ Getting image for user ${h.username}:`,{hasProfileImage:!!h.profileImage,hasAvatar:!!h.avatar,profileImageLength:((T=h.profileImage)==null?void 0:T.length)||0,finalImageUrl:C?"HAS_IMAGE":"NO_IMAGE"}),C||"/images/default-avatar.png"};return r.jsxs("div",{className:"space-y-8",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-8",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center",children:r.jsx(Lr,{className:"w-5 h-5 text-white"})}),r.jsx("h2",{className:"text-2xl font-bold text-white",children:"إدارة صور المستخدمين"})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(bn,{className:"w-8 h-8 text-blue-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.length}),r.jsx("p",{className:"text-sm text-gray-400",children:"إجمالي المستخدمين"})]})]})}),r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Lr,{className:"w-8 h-8 text-green-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(h=>h.profileImage||h.avatar).length}),r.jsx("p",{className:"text-sm text-gray-400",children:"لديهم صور"})]})]})}),r.jsx("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Yr,{className:"w-8 h-8 text-purple-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-white",children:e.filter(h=>!h.profileImage&&!h.avatar).length}),r.jsx("p",{className:"text-sm text-gray-400",children:"بدون صور"})]})]})})]}),M&&r.jsx("div",{className:`p-4 rounded-2xl border ${M.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":M.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:r.jsxs("div",{className:"flex items-center gap-2",children:[M.type==="success"&&r.jsx(ci,{className:"w-5 h-5"}),M.type==="error"&&r.jsx(Hs,{className:"w-5 h-5"}),M.type==="info"&&r.jsx(hi,{className:"w-5 h-5"}),r.jsx("span",{children:M.text})]})}),r.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-3xl border border-white/10 shadow-2xl overflow-hidden",children:[r.jsx("div",{className:"p-6 border-b border-white/10",children:r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsxs("div",{className:"relative flex-1",children:[r.jsx(fi,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{id:"searchInput",type:"text",placeholder:"البحث عن مستخدم...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-12 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent",onKeyPress:h=>h.key==="Enter"&&U()})]}),r.jsxs("button",{onClick:U,className:"bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-6 py-3 text-purple-300 transition-colors flex items-center gap-2",children:[r.jsx(fi,{className:"w-4 h-4"}),"بحث"]}),r.jsxs("button",{onClick:()=>z(),className:"bg-green-500/20 hover:bg-green-500/30 border border-green-500/50 rounded-xl px-6 py-3 text-green-300 transition-colors flex items-center gap-2",children:[r.jsx(hn,{className:"w-4 h-4"}),"تحديث"]})]})}),r.jsxs("div",{className:"p-6",children:[c?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(hn,{className:"w-8 h-8 text-purple-400 animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-gray-300",children:"جاري التحميل..."})]}):e.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(bn,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد مستخدمين"}),r.jsx("p",{className:"text-gray-300",children:"لم يتم العثور على أي مستخدمين"})]}):r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(h=>r.jsxs("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10 hover:bg-white/10 transition-all duration-200",children:[r.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[r.jsx("img",{src:pe(h),alt:"صورة المستخدم",className:"w-16 h-16 rounded-full object-cover border-2 border-purple-500"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-semibold text-white",children:h.displayName||h.username}),r.jsxs("p",{className:"text-sm text-gray-400",children:["المعرف: ",h.playerId||"غير محدد"]})]})]}),r.jsx("div",{className:"space-y-3 mb-4",children:r.jsxs("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-lg",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("img",{src:pe(h),alt:"الصورة الشخصية",className:"w-8 h-8 rounded-lg object-cover"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-sm font-medium text-white",children:"الصورة الشخصية"}),r.jsx("p",{className:"text-xs text-gray-400",children:Ee(h)})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>D(h,"remove_avatar"),className:"p-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg text-red-300 transition-colors",title:"حذف",children:r.jsx(v0,{className:"w-4 h-4"})}),r.jsx("button",{onClick:()=>D(h,"change_avatar"),className:"p-2 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/50 rounded-lg text-yellow-300 transition-colors",title:"تغيير",children:r.jsx(p0,{className:"w-4 h-4"})})]})]})})]},h.userId))}),i&&i.totalPages>1&&r.jsxs("div",{className:"flex justify-center items-center gap-2 mt-8",children:[i.hasPrevPage&&r.jsx("button",{onClick:()=>z(n-1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"السابق"}),Array.from({length:i.totalPages},(h,C)=>C+1).map(h=>r.jsx("button",{onClick:()=>z(h,l),className:`px-4 py-2 rounded-lg transition-colors ${h===n?"bg-purple-500 text-white":"bg-white/10 hover:bg-white/20 border border-white/20 text-white"}`,children:h},h)),i.hasNextPage&&r.jsx("button",{onClick:()=>z(n+1,l),className:"px-4 py-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg text-white transition-colors",children:"التالي"})]})]})]}),u&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h3",{className:"text-xl font-bold text-white",children:["إدارة صور المستخدم ",g==null?void 0:g.userId]}),r.jsx("button",{onClick:Me,className:"p-2 rounded-lg hover:bg-white/10 transition-colors",children:r.jsx(ma,{className:"w-5 h-5 text-gray-400"})})]}),r.jsxs("form",{onSubmit:Pe,className:"space-y-6",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"الإجراء"}),r.jsxs("select",{value:b,onChange:h=>N(h.target.value),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-purple-500",required:!0,children:[r.jsx("option",{value:"",children:"اختر الإجراء"}),r.jsx("option",{value:"remove_avatar",children:"حذف الصورة الشخصية"}),r.jsx("option",{value:"change_avatar",children:"تغيير الصورة الشخصية"})]})]}),b.startsWith("change_")&&r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اختر الصورة"}),r.jsxs("div",{className:"relative",children:[r.jsx("input",{type:"file",accept:"image/*",onChange:v,className:"hidden",id:"imageFile"}),r.jsxs("label",{htmlFor:"imageFile",className:"w-full bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-xl px-4 py-3 text-purple-300 cursor-pointer transition-colors flex items-center justify-center gap-2",children:[r.jsx(Gh,{className:"w-5 h-5"}),"اختيار ملف"]})]}),S&&r.jsx("div",{className:"mt-4",children:r.jsx("img",{src:S,alt:"معاينة الصورة",className:"w-full max-h-40 object-cover rounded-xl"})})]}),r.jsxs("div",{className:"flex gap-4",children:[r.jsx("button",{type:"submit",className:"flex-1 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),r.jsx("button",{type:"button",onClick:Me,className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})]})})]})},gi=({userData:e,onLogout:t})=>{const[n,s]=y.useState("users"),[l,o]=y.useState([]),[i,a]=y.useState(null),[c,d]=y.useState(""),[g,w]=y.useState(!1),[b,N]=y.useState(null),[j,k]=y.useState({numBoxes:10,winRatio:.3}),[S,m]=y.useState([]);y.useEffect(()=>{e!=null&&e.isAdmin&&(p(),M(),L())},[e]);const u=(v,O)=>{N({type:v,text:O}),setTimeout(()=>N(null),5e3)},p=async()=>{w(!0);try{const v=await K.getAllUsersAdmin();console.log("📥 Loaded users:",v.users);const O=(v.users||[]).map(X=>({...X,id:X.id||X.userId||X._id,userId:X.userId||X.id||X._id}));console.log("✅ Processed users:",O),o(O)}catch(v){console.error("❌ Error loading users:",v),u("error","خطأ في تحميل المستخدمين")}finally{w(!1)}},M=async()=>{try{const v=await K.getGameSettings();k(v)}catch(v){console.error("خطأ في تحميل إعدادات اللعبة:",v)}},L=async()=>{try{const v=await K.getSuspiciousActivities();m(v||[])}catch(v){console.error("خطأ في تحميل النشاطات المشبوهة:",v),m([])}},R=async v=>{if(!v.trim()){p();return}w(!0);try{const O=await K.searchUsersAdmin(v);o(O.users||[])}catch{u("error","خطأ في البحث")}finally{w(!1)}},z=async(v,O)=>{try{if(!v||v==="undefined"||v===""){console.error("❌ Invalid userId:",v),console.error("❌ Updates object:",O),u("error","خطأ: معرف المستخدم غير صحيح. يرجى إعادة تحميل الصفحة.");return}if(console.log("🔄 Updating user:",v,"with updates:",O),O.playerId){if(!/^\d{6}$/.test(O.playerId)){u("error","معرف اللاعب يجب أن يكون 6 أرقام فقط");return}await K.updatePlayerId(v,O.playerId),u("success","تم تحديث معرف اللاعب بنجاح")}const{playerId:X,...ae}=O;Object.keys(ae).length>0&&(await K.updateUserAdmin(v,ae),u("success","تم تحديث المستخدم بنجاح")),p(),a(null)}catch{u("error","خطأ في تحديث المستخدم")}},U=async()=>{try{await K.updateGameSettings(j),u("success","تم حفظ إعدادات اللعبة بنجاح")}catch{u("error","خطأ في حفظ الإعدادات")}},D=l.filter(v=>v.username.toLowerCase().includes(c.toLowerCase())||v.email.toLowerCase().includes(c.toLowerCase()));return e!=null&&e.isAdmin?r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white",children:[r.jsx("div",{className:"absolute inset-0 overflow-hidden",children:r.jsxs("div",{className:"absolute -inset-10 opacity-20",children:[r.jsx("div",{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-3/4 right-1/4 w-96 h-96 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"}),r.jsx("div",{className:"absolute bottom-1/4 left-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-2000"})]})}),r.jsxs("div",{className:"relative z-10 px-4 py-4",children:[r.jsxs("div",{className:"mb-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center relative",children:[r.jsx(Tr,{className:"w-6 h-6 text-white"}),r.jsx(Ll,{className:"w-3 h-3 text-white absolute -top-1 -right-1"})]}),r.jsxs("div",{children:[r.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"لوحة تحكم المشرف"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"INFINITY BOX"})]})]}),r.jsx("button",{onClick:t,className:"bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 hover:text-red-200 transition-all duration-200 text-sm",children:"خروج"})]}),r.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl px-3 py-2 border border-white/20 flex items-center gap-2",children:[r.jsx(Tr,{className:"w-4 h-4 text-yellow-400"}),r.jsxs("span",{className:"text-sm text-gray-300",children:["مرحباً، ",e.username]})]})]}),b&&r.jsx("div",{className:`mb-6 p-4 rounded-2xl border ${b.type==="success"?"bg-green-500/20 border-green-500/50 text-green-300":b.type==="error"?"bg-red-500/20 border-red-500/50 text-red-300":"bg-blue-500/20 border-blue-500/50 text-blue-300"}`,children:r.jsxs("div",{className:"flex items-center gap-2",children:[b.type==="success"&&r.jsx(ci,{className:"w-5 h-5"}),b.type==="error"&&r.jsx(Hs,{className:"w-5 h-5"}),b.type==="info"&&r.jsx(hi,{className:"w-5 h-5"}),r.jsx("span",{children:b.text})]})}),r.jsx("div",{className:"grid grid-cols-2 gap-2 mb-6",children:[{id:"users",label:"إدارة المستخدمين",shortLabel:"المستخدمين",icon:bn,color:"from-blue-500 to-cyan-500"},{id:"game",label:"إعدادات اللعبة",shortLabel:"الألعاب",icon:Uc,color:"from-green-500 to-emerald-500"},{id:"suspicious",label:"النشاطات المشبوهة",shortLabel:"المشبوهة",icon:ii,color:"from-red-500 to-pink-500"},{id:"images",label:"إدارة الصور",shortLabel:"الصور",icon:Lr,color:"from-purple-500 to-indigo-500"}].map(v=>r.jsxs("button",{onClick:()=>s(v.id),className:`flex flex-col items-center gap-2 px-3 py-3 rounded-xl transition-all duration-200 ${n===v.id?`bg-gradient-to-r ${v.color} text-white shadow-lg`:"bg-white/10 hover:bg-white/20 text-gray-300"}`,children:[r.jsx(v.icon,{className:"w-6 h-6"}),r.jsx("span",{className:"text-sm font-medium",children:v.shortLabel})]},v.id))}),r.jsxs("div",{className:"bg-white/5 backdrop-blur-md rounded-2xl border border-white/10 shadow-2xl overflow-hidden",children:[n==="users"&&r.jsxs("div",{className:"p-4",children:[r.jsxs("div",{className:"flex flex-col gap-4 mb-6",children:[r.jsx("div",{className:"flex items-center justify-between",children:r.jsxs("h2",{className:"text-lg font-bold text-white flex items-center gap-2",children:[r.jsx(bn,{className:"w-5 h-5 text-blue-400"}),"المستخدمين (",D.length,")"]})}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("button",{onClick:()=>{console.log("🧹 Clearing local data..."),K.clearLocalData(),u("info","تم تنظيف البيانات المحلية. سيتم إعادة تحميل الصفحة..."),setTimeout(()=>window.location.reload(),1e3)},className:"flex-1 flex items-center justify-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg px-3 py-2 text-red-300 transition-colors text-sm",children:[r.jsx(Hs,{className:"w-4 h-4"}),"تنظيف البيانات"]}),r.jsxs("button",{onClick:p,className:"flex-1 flex items-center justify-center gap-2 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-3 py-2 text-blue-300 transition-colors text-sm",children:[r.jsx(hn,{className:"w-4 h-4"}),"تحديث"]})]})]}),r.jsxs("div",{className:"relative mb-4",children:[r.jsx(fi,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),r.jsx("input",{type:"text",value:c,onChange:v=>{d(v.target.value),R(v.target.value)},placeholder:"البحث عن المستخدمين...",className:"w-full bg-white/10 border border-white/20 rounded-xl px-10 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm",autoComplete:"off"})]}),g?r.jsxs("div",{className:"text-center py-8",children:[r.jsx(hn,{className:"w-6 h-6 text-blue-400 animate-spin mx-auto mb-3"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"جاري التحميل..."})]}):r.jsx("div",{className:"space-y-3",children:D.map(v=>r.jsxs("div",{className:"bg-white/10 rounded-xl p-4 border border-white/20 hover:bg-white/15 transition-all duration-200",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-sm font-bold",children:v.username.charAt(0).toUpperCase()}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("h3",{className:"font-semibold text-white flex items-center gap-2 text-sm",children:[r.jsx("span",{className:"truncate",children:v.username}),v.isAdmin&&r.jsx(Tr,{className:"w-3 h-3 text-yellow-400 flex-shrink-0"})]}),r.jsx("p",{className:"text-xs text-gray-400 truncate",children:v.email}),r.jsxs("p",{className:"text-xs text-blue-400",children:["المعرف: ",v.playerId]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[r.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[r.jsx(Tl,{className:"w-3 h-3 text-yellow-400"}),r.jsx("span",{className:"text-yellow-400 font-semibold text-sm",children:v.goldCoins||v.coins||0})]}),r.jsx("p",{className:"text-xs text-gray-400",children:"العملات"})]}),r.jsxs("div",{className:"bg-white/5 rounded-lg p-2 text-center",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[r.jsx(cn,{className:"w-3 h-3 text-purple-400"}),r.jsx("span",{className:"text-purple-400 font-semibold text-sm",children:v.pearls||0})]}),r.jsx("p",{className:"text-xs text-gray-400",children:"اللآلئ"})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsxs("button",{onClick:()=>a(v),className:"flex-1 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/50 rounded-lg px-2 py-2 text-blue-300 text-xs transition-colors flex items-center justify-center gap-1",children:[r.jsx(p0,{className:"w-3 h-3"}),"تعديل"]}),r.jsxs("button",{onClick:()=>z(v.id||v._id,{isAdmin:!v.isAdmin}),className:`flex-1 border rounded-lg px-2 py-2 text-xs transition-colors flex items-center justify-center gap-1 ${v.isAdmin?"bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300":"bg-green-500/20 hover:bg-green-500/30 border-green-500/50 text-green-300"}`,children:[v.isAdmin?r.jsx(pi,{className:"w-3 h-3"}):r.jsx(Dh,{className:"w-3 h-3"}),v.isAdmin?"إلغاء":"ترقية"]})]})]},v.id))})]}),n==="game"&&r.jsxs("div",{className:"p-4",children:[r.jsxs("h2",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(Uc,{className:"w-5 h-5 text-green-400"}),"إعدادات اللعبة"]}),r.jsx("div",{className:"space-y-4",children:r.jsxs("div",{className:"bg-white/5 rounded-xl p-4 border border-white/10",children:[r.jsx("h3",{className:"text-base font-semibold text-white mb-4",children:"إعدادات صناديق الحظ"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"عدد الصناديق في الجولة"}),r.jsx("input",{type:"number",min:"5",max:"100",value:j.numBoxes,onChange:v=>k(O=>({...O,numBoxes:parseInt(v.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"نسبة الفوز (من 0.1 إلى 0.9)"}),r.jsx("input",{type:"number",min:"0.1",max:"0.9",step:"0.1",value:j.winRatio,onChange:v=>k(O=>({...O,winRatio:parseFloat(v.target.value)})),className:"w-full bg-white/10 border border-white/20 rounded-lg px-3 py-3 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"})]}),r.jsxs("button",{onClick:U,className:"w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-sm",children:[r.jsx(Wh,{className:"w-4 h-4"}),"حفظ الإعدادات"]})]})]})})]}),n==="suspicious"&&r.jsxs("div",{className:"p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h2",{className:"text-2xl font-bold text-white flex items-center gap-2",children:[r.jsx(ii,{className:"w-6 h-6 text-red-400"}),"النشاطات المشبوهة"]}),r.jsxs("button",{onClick:L,className:"flex items-center gap-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-xl px-4 py-2 text-red-300 transition-colors",children:[r.jsx(hn,{className:"w-4 h-4"}),"تحديث"]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[r.jsx("div",{className:"bg-red-500/20 rounded-2xl p-6 border border-red-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(hi,{className:"w-8 h-8 text-red-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-red-300",children:S.length}),r.jsx("p",{className:"text-sm text-red-400",children:"مستخدمين مشبوهين"})]})]})}),r.jsx("div",{className:"bg-orange-500/20 rounded-2xl p-6 border border-orange-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Uh,{className:"w-8 h-8 text-orange-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-orange-300",children:S.filter(v=>v.riskLevel==="high").length}),r.jsx("p",{className:"text-sm text-orange-400",children:"مخاطر عالية"})]})]})}),r.jsx("div",{className:"bg-yellow-500/20 rounded-2xl p-6 border border-yellow-500/50",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx(Yr,{className:"w-8 h-8 text-yellow-400"}),r.jsxs("div",{children:[r.jsx("p",{className:"text-2xl font-bold text-yellow-300",children:S.filter(v=>v.riskLevel==="medium").length}),r.jsx("p",{className:"text-sm text-yellow-400",children:"تحت المراقبة"})]})]})})]}),S.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx(ci,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"لا توجد نشاطات مشبوهة"}),r.jsx("p",{className:"text-gray-300",children:"جميع المستخدمين يتصرفون بشكل طبيعي"})]}):r.jsx("div",{className:"space-y-4",children:S.map((v,O)=>{var X,ae;return r.jsx("div",{className:"bg-white/5 rounded-2xl p-6 border border-white/10",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-lg font-bold",children:((ae=(X=v.username)==null?void 0:X.charAt(0))==null?void 0:ae.toUpperCase())||"?"}),r.jsxs("div",{children:[r.jsx("h3",{className:"font-semibold text-white",children:v.username}),r.jsxs("p",{className:"text-sm text-gray-400",children:["آخر نشاط: ",v.lastActivity]})]})]}),r.jsx("div",{className:`px-3 py-1 rounded-full text-xs font-semibold ${v.riskLevel==="high"?"bg-red-500/20 text-red-300":v.riskLevel==="medium"?"bg-yellow-500/20 text-yellow-300":"bg-green-500/20 text-green-300"}`,children:v.riskLevel==="high"?"خطر عالي":v.riskLevel==="medium"?"خطر متوسط":"خطر منخفض"})]})},O)})})]}),n==="images"&&r.jsxs("div",{className:"p-8",children:[r.jsxs("div",{className:"mb-6",children:[r.jsxs("h2",{className:"text-2xl font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(Lr,{className:"w-6 h-6 text-purple-400"}),"إدارة الصور"]}),r.jsxs("div",{className:"bg-purple-500/10 border border-purple-500/30 rounded-xl p-4 mb-6",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[r.jsx(Lr,{className:"w-5 h-5 text-purple-400"}),r.jsx("h3",{className:"font-semibold text-purple-300",children:"إدارة صور المستخدمين"})]}),r.jsx("p",{className:"text-gray-300 text-sm",children:"عرض وإدارة جميع صور المستخدمين، حذف الصور غير المناسبة، والبحث في المستخدمين."})]})]}),r.jsx(Tp,{})]})]})]}),i&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-slate-800 rounded-3xl p-8 max-w-md w-full border border-white/20",children:[r.jsx("h3",{className:"text-xl font-bold text-white mb-6",children:"تعديل المستخدم"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"معرف اللاعب"}),r.jsx("input",{type:"text",value:i.playerId,onChange:v=>a(O=>O?{...O,playerId:v.target.value}:null),maxLength:6,pattern:"\\d{6}",className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"6 أرقام فقط"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اسم المستخدم"}),r.jsx("input",{type:"text",value:i.username,onChange:v=>a(O=>O?{...O,username:v.target.value}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"username"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"العملات الذهبية"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"number",value:i.goldCoins||i.coins||0,onChange:v=>a(O=>O?{...O,goldCoins:parseInt(v.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),r.jsxs("div",{className:"flex gap-2 flex-wrap",children:[r.jsx("button",{type:"button",onClick:()=>a(v=>v?{...v,goldCoins:(v.goldCoins||v.coins||0)+1e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+1,000"}),r.jsx("button",{type:"button",onClick:()=>a(v=>v?{...v,goldCoins:(v.goldCoins||v.coins||0)+5e3}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+5,000"}),r.jsx("button",{type:"button",onClick:()=>a(v=>v?{...v,goldCoins:(v.goldCoins||v.coins||0)+1e4}:null),className:"px-3 py-1 bg-yellow-500/20 border border-yellow-500/50 rounded-lg text-yellow-300 text-sm hover:bg-yellow-500/30 transition-colors",children:"+10,000"})]})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اللآلئ"}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("input",{type:"number",value:i.pearls||0,onChange:v=>a(O=>O?{...O,pearls:parseInt(v.target.value)}:null),className:"w-full bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500",autoComplete:"off"}),r.jsxs("div",{className:"flex gap-2 flex-wrap",children:[r.jsx("button",{type:"button",onClick:()=>a(v=>v?{...v,pearls:(v.pearls||0)+1}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+1"}),r.jsx("button",{type:"button",onClick:()=>a(v=>v?{...v,pearls:(v.pearls||0)+5}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+5"}),r.jsx("button",{type:"button",onClick:()=>a(v=>v?{...v,pearls:(v.pearls||0)+10}:null),className:"px-3 py-1 bg-purple-500/20 border border-purple-500/50 rounded-lg text-purple-300 text-sm hover:bg-purple-500/30 transition-colors",children:"+10"})]})]})]}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("input",{type:"checkbox",id:"isAdmin",checked:i.isAdmin,onChange:v=>a(O=>O?{...O,isAdmin:v.target.checked}:null),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"}),r.jsx("label",{htmlFor:"isAdmin",className:"text-sm font-medium text-gray-300",children:"صلاحيات المشرف"})]})]}),r.jsxs("div",{className:"flex gap-4 mt-8",children:[r.jsx("button",{onClick:()=>{const v=i.id||i.userId;if(console.log("🔍 Selected user data:",i),console.log("🔍 Extracted userId:",v),!v){u("error","خطأ: لا يمكن العثور على معرف المستخدم. يرجى إعادة تحميل الصفحة.");return}z(v,i)},className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"حفظ التغييرات"}),r.jsx("button",{onClick:()=>a(null),className:"flex-1 bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/50 text-gray-300 font-semibold py-3 px-6 rounded-xl transition-all duration-200",children:"إلغاء"})]})]})})]}):r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center",children:r.jsxs("div",{className:"bg-red-500/20 border border-red-500/50 rounded-3xl p-8 text-center max-w-md",children:[r.jsx(Hs,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"غير مصرح"}),r.jsx("p",{className:"text-red-300 mb-6",children:"ليس لديك صلاحيات للوصول إلى لوحة تحكم المشرف"}),r.jsx("button",{onClick:t,className:"bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-xl transition-colors",children:"العودة لتسجيل الدخول"})]})})};class A0{constructor(t){this.url=t,this.ws=null,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.messageHandlers=new Map}connect(t){return new Promise((n,s)=>{try{this.ws=new WebSocket(`${this.url}?token=${t}`),this.ws.onopen=()=>{console.log("🔌 WebSocket connected"),this.reconnectAttempts=0,n()},this.ws.onmessage=l=>{try{const o=JSON.parse(l.data);this.handleMessage(o)}catch(o){console.error("Error parsing WebSocket message:",o)}},this.ws.onclose=()=>{console.log("🛑 WebSocket disconnected"),this.handleReconnect()},this.ws.onerror=l=>{console.error("WebSocket error:",l),s(l)}}catch(l){s(l)}})}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`),setTimeout(()=>{this.connect(this.getTokenFromUrl()).catch(t=>{console.error("Reconnection failed:",t)})},this.reconnectDelay*this.reconnectAttempts)):console.error("❌ Max reconnection attempts reached")}getTokenFromUrl(){return new URLSearchParams(window.location.search).get("token")||""}handleMessage(t){const n=this.messageHandlers.get(t.type);n&&n.forEach(s=>s(t.data))}sendMessage(t,n){if(this.ws&&this.ws.readyState===WebSocket.OPEN){const s={type:t,data:n,timestamp:Date.now()};this.ws.send(JSON.stringify(s))}else console.warn("WebSocket is not connected")}sendPrivateMessage(t,n){this.sendMessage("private_message",{messageData:t,recipientId:n})}onMessage(t,n){this.messageHandlers.has(t)||this.messageHandlers.set(t,[]),this.messageHandlers.get(t).push(n)}offMessage(t,n){const s=this.messageHandlers.get(t);if(s){const l=s.indexOf(n);l>-1&&s.splice(l,1)}}addMessageListener(t){["voice_room_message","voice_room_update","admin_action_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.onMessage(s,t)})}removeMessageListener(t){["voice_room_message","voice_room_update","webrtc_offer","webrtc_answer","webrtc_ice_candidate","new_message","private_message"].forEach(s=>{this.offMessage(s,t)})}send(t){this.sendMessage(t.type,t.data)}disconnect(){this.ws&&(this.ws.close(),this.ws=null)}}const Lp=`
  @keyframes spin-slow {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
  }

  .animate-spin-slow {
    animation: spin-slow 3s linear infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }
`;if(typeof document<"u"){const e=document.createElement("style");e.textContent=Lp,document.head.appendChild(e)}const Pp=({userData:e,isOwner:t,onUpdateProfile:n,onLogout:s})=>{var ja,Na,ka,Sa,Ca;const[l,o]=y.useState("overview"),[i,a]=y.useState(!1),[c,d]=y.useState(""),[g,w]=y.useState((e==null?void 0:e.gender)||"male"),[b,N]=y.useState(!1),[j,k]=y.useState({gems:0,stars:0,coins:0,bombs:0,bats:0,snakes:0}),[S,m]=y.useState(!1),[u,p]=y.useState(!1),[M,L]=y.useState(""),[R,z]=y.useState(!1),[U,D]=y.useState(null),[v,O]=y.useState(""),[X,ae]=y.useState(null),[Pe,Me]=y.useState(!1),[Ee,pe]=y.useState(""),[h,C]=y.useState(null),[T,F]=y.useState(100),[P,W]=y.useState("gold"),[Y,se]=y.useState(""),[me,ge]=y.useState(!1),[st,Xt]=y.useState([]),[kn,en]=y.useState([]),[Rl,Ie]=y.useState([]),[bt,Sn]=y.useState(!1),[be,Cn]=y.useState(1e4),[is,ir]=y.useState(!1),[mt,zl]=y.useState(250),[Mn,as]=y.useState({"1_dollar":!0,"5_dollar":!0}),[fe,ft]=y.useState(""),[tn,cs]=y.useState(""),[Ke,ar]=y.useState(!1),[jt,ht]=y.useState([]),[ds,us]=y.useState(!1),[En,x]=y.useState(!1),[I,Q]=y.useState(null),[H,V]=y.useState([]),[te,Re]=y.useState("");y.useState(!1);const[cr,In]=y.useState(!1),[An,Ol]=y.useState(!1);(f=>f==="female"?{primary:"from-pink-500 to-red-400",secondary:"bg-pink-50",accent:"text-pink-600",button:"bg-pink-500 hover:bg-pink-600",border:"border-pink-200"}:{primary:"from-blue-500 to-yellow-400",secondary:"bg-blue-50",accent:"text-blue-600",button:"bg-blue-500 hover:bg-blue-600",border:"border-blue-200"})((e==null?void 0:e.gender)||"male"),y.useEffect(()=>{let f=!0;const A=setTimeout(async()=>{if(t&&(e!=null&&e.id)&&f)try{const q=localStorage.getItem("token");if(!q)return;const J=await fetch("/api/profile/me",{method:"GET",headers:{Authorization:`Bearer ${q}`,"Content-Type":"application/json"}});if(J.ok&&f){const G=J.headers.get("content-type");if(G&&G.includes("application/json")){const nn=await J.json();n&&f&&n(nn)}}}catch(q){console.error("❌ Error fetching complete user data:",q)}},100);return()=>{f=!1,clearTimeout(A)}},[t,e==null?void 0:e.id]),y.useEffect(()=>{e!=null&&e.id&&(L0(),G0(),t&&pa())},[e==null?void 0:e.id,t]),y.useEffect(()=>{if(t&&(e!=null&&e.id))return T0()},[t,e==null?void 0:e.id]);const dr=new A0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`);y.useEffect(()=>{let f=!0;if(t&&(e!=null&&e.id)&&f){ga(),ms(),R0(),fs(),$0();const E=setInterval(()=>{f&&(ms(),fs())},3e5);return()=>{f=!1,clearInterval(E)}}},[t,e==null?void 0:e.id]);const T0=()=>{console.log("🔧 Setting up message listener...");const f=E=>{if(console.log("📨 WebSocket message received:",E),E.messageData){const A=E.messageData.sender._id,q=E.messageData.recipient._id,J=e==null?void 0:e.id;if(console.log("📋 Message details:",{senderId:A,recipientId:q,currentUserId:J,showChat:En,chatUserId:(I==null?void 0:I.id)||(I==null?void 0:I._id)}),q===J)if(console.log("✅ Message is for me, processing..."),En&&I&&(A===I.id||A===I._id)){console.log("💬 Adding message to open chat"),V(G=>G.some(Y0=>Y0._id===E.messageData._id)?(console.log("⚠️ Message already exists, skipping"),G):(console.log("✅ Adding new message to chat"),[...G,E.messageData])),setTimeout(Ul,100);try{const G=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");G.volume=.5,G.play().catch(()=>{})}catch{}}else{console.log("🔔 Chat not open, refreshing notifications"),fs();try{const G=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");G.volume=.3,G.play().catch(()=>{})}catch{}}else console.log("ℹ️ Message not for me, ignoring")}else console.log("⚠️ No messageData in WebSocket message")};return dr.offMessage("new_message",f),dr.onMessage("new_message",f),console.log("✅ Message listener added"),()=>{console.log("🧹 Cleaning up message listener"),dr.offMessage("new_message",f)}},L0=async()=>{try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for fetching user items");return}const E=await fetch(`/api/user-items/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const A=await E.json();k(A.items),console.log("✅ User items fetched successfully:",A.items)}else console.error("❌ Failed to fetch user items:",E.status,E.statusText)}catch(f){console.error("Error fetching user items:",f)}},pa=async()=>{try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for fetching user shield");return}const E=await fetch(`/api/profile/shield/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const A=await E.json();D(A.shield),console.log("✅ User shield fetched successfully:",A.shield)}else console.error("❌ Failed to fetch user shield:",E.status,E.statusText)}catch(f){console.error("Error fetching user shield:",f)}},P0=async(f,E)=>{if(!R){z(!0);try{const A=localStorage.getItem("token"),q=await fetch("/api/profile/activate-shield",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A}`},body:JSON.stringify({shieldType:f})}),J=await q.json();q.ok?(D(J.shield),n&&J.newBalance!==void 0&&n({goldCoins:J.newBalance}),alert(J.message),await pa()):alert(J.message||"فشل في تفعيل الدرع الواقي")}catch(A){console.error("Error activating shield:",A),alert("حدث خطأ في تفعيل الدرع الواقي")}finally{z(!1)}}},ga=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/friends",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();Xt(A),console.log("✅ Friends fetched:",A.length)}}catch(f){console.error("Error fetching friends:",f)}},ms=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/friend-requests",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();en(A),console.log("✅ Friend requests fetched:",A.length)}}catch(f){console.error("Error fetching friend requests:",f)}},R0=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/profile/gifts",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();Ie(A),console.log("✅ Gifts fetched:",A.length)}}catch(f){console.error("Error fetching gifts:",f)}},xa=async()=>{if(!v.trim()){pe("يرجى إدخال رقم اللاعب");return}if(v.length!==6){pe("رقم اللاعب يجب أن يكون 6 أرقام");return}Me(!0),pe(""),ae(null);try{const f=localStorage.getItem("token"),E=await fetch(`/api/users/search-by-id/${v}`,{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();ae(A),console.log("✅ User found:",A.username)}else pe("لم يتم العثور على لاعب بهذا الرقم")}catch(f){console.error("Error searching for friend:",f),pe("حدث خطأ أثناء البحث")}finally{Me(!1)}},z0=async f=>{try{const E=localStorage.getItem("token"),A=await fetch("/api/profile/friend-request",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({friendId:f})});if(A.ok){const q=await A.json();alert(q.message),ae(null),O(""),await fs()}else{const q=await A.json();alert(q.message||"فشل في إرسال طلب الصداقة")}}catch(E){console.error("Error sending friend request:",E),alert("حدث خطأ في إرسال طلب الصداقة")}},O0=async()=>{if(h){ge(!0);try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/send-gift",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({toUserId:h.id,giftType:P,amount:T,message:Y})});if(E.ok){const A=await E.json();alert(A.message),C(null),se(""),F(100),n&&A.fromUserBalance&&n(A.fromUserBalance)}else{const A=await E.json();alert(A.message||"فشل في إرسال الهدية")}}catch(f){console.error("Error sending gift:",f),alert("حدث خطأ في إرسال الهدية")}finally{ge(!1)}}},U0=async f=>{try{const E=localStorage.getItem("token"),A=await fetch("/api/profile/accept-friend",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({friendshipId:f})});if(A.ok){const q=await A.json();alert(q.message),await ms(),await ga()}else{const q=await A.json();alert(q.message||"فشل في قبول طلب الصداقة")}}catch(E){console.error("Error accepting friend request:",E),alert("حدث خطأ في قبول طلب الصداقة")}},$0=async()=>{try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/free-charges",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();as(A.availableCharges)}}catch(f){console.error("Error fetching free charges:",f)}},ya=async(f,E=!1,A)=>{Sn(!0);try{const q=localStorage.getItem("token"),J=await fetch("/api/profile/charge-balance",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${q}`},body:JSON.stringify({amount:f,isFree:E,chargeType:A})});if(J.ok){const G=await J.json();alert(G.message),n&&G.newBalance!==void 0&&n({goldCoins:G.newBalance}),E&&A&&as(nn=>({...nn,[A]:!1}))}else{const G=await J.json();alert(G.message||"فشل في شحن الرصيد")}}catch(q){console.error("Error charging balance:",q),alert("حدث خطأ في شحن الرصيد")}finally{Sn(!1)}},F0=async()=>{if(be<1e4){alert("الحد الأدنى للتحويل هو 10,000 عملة ذهبية");return}if(be%1e4!==0){alert("يجب أن تكون الكمية مضاعفات 10,000");return}if(((e==null?void 0:e.goldCoins)||0)<be){alert("رصيد العملات الذهبية غير كافي");return}ir(!0);try{const f=localStorage.getItem("token"),E=await fetch("/api/profile/exchange-gold-to-pearls",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({goldAmount:be})});if(E.ok){const A=await E.json();alert(A.message),n&&A.newBalance&&n(A.newBalance),Cn(1e4)}else{const A=await E.json();alert(A.message||"فشل في تحويل العملات")}}catch(f){console.error("Error exchanging gold to pearls:",f),alert("حدث خطأ في تحويل العملات")}finally{ir(!1)}},B0=async()=>{if(mt<250){alert("الحد الأدنى للسحب هو 250 لؤلؤة ($25)");return}if(((e==null?void 0:e.pearls)||0)<mt){alert("رصيد اللآلئ غير كافي");return}const A=`https://wa.me/1234567890?text=${`طلب سحب دولارات%0Aالمبلغ: $${mt/10}%0Aاللآلئ المطلوبة: ${mt}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}`}`;window.open(A,"_blank")},V0=async()=>{if(!fe){alert("يرجى اختيار عنصر للإرسال");return}if(!tn||tn.length!==6){alert("يرجى إدخال رقم لاعب صحيح (6 أرقام)");return}ar(!0);try{const f=localStorage.getItem("token"),E=await fetch(`/api/users/search-by-id/${tn}`,{headers:{Authorization:`Bearer ${f}`}});if(!E.ok){alert("لم يتم العثور على لاعب بهذا الرقم");return}const A=await E.json(),q=await fetch("/api/profile/send-item",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({toUserId:A.id,itemType:fe,message:`عنصر ${va(fe)} من ${e==null?void 0:e.username}`})});if(q.ok){const J=await q.json();alert(J.message),ft(""),cs("")}else{const J=await q.json();alert(J.message||"فشل في إرسال العنصر")}}catch(f){console.error("Error sending item:",f),alert("حدث خطأ في إرسال العنصر")}finally{ar(!1)}},va=f=>({bomb:"قنبلة مدمرة",bat:"خفاش مؤذي",snake:"ثعبان سام",gem:"جوهرة نادرة",star:"نجمة ذهبية",coin:"عملة خاصة",gold:"عملات ذهبية"})[f]||f,wa=(f,E)=>{const q=`https://wa.me/1234567890?text=${`طلب شحن رصيد%0Aالمبلغ المطلوب: ${f} عملة ذهبية%0Aالسعر: ${E}%0Aاسم المستخدم: ${e==null?void 0:e.username}%0Aرقم اللاعب: ${e==null?void 0:e.playerId}%0Aالرصيد الحالي: ${(e==null?void 0:e.goldCoins)||0} عملة`}`;window.open(q,"_blank")},fs=async()=>{try{const f=localStorage.getItem("token");if(!f)return;const E=await fetch("/api/notifications",{headers:{Authorization:`Bearer ${f}`}});if(E.ok){const A=await E.json();ht(A),console.log("✅ Notifications fetched:",A.length);const q=A.filter(G=>{var nn;return!G.isRead&&G.type==="item_received"&&((nn=G.data)==null?void 0:nn.newBalance)});if(q.length>0&&n){const G=q[0];n(G.data.newBalance),console.log("💰 Balance updated from item notification:",G.data.newBalance)}A.filter(G=>G.type==="friend_request"&&!G.isRead).length>0&&(console.log("🤝 New friend request notifications found, refreshing friend requests"),await ms())}}catch(f){console.error("Error fetching notifications:",f)}},q0=async f=>{try{const E=localStorage.getItem("token");(await fetch(`/api/notifications/${f}/read`,{method:"PUT",headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}})).ok&&(ht(q=>q.map(J=>J._id===f?{...J,isRead:!0}:J)),console.log("✅ Notification marked as read:",f))}catch(E){console.error("Error marking notification as read:",E)}},H0=async()=>{try{const f=localStorage.getItem("token");(await fetch("/api/notifications/mark-all-read",{method:"PUT",headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}})).ok&&(ht(A=>A.map(q=>({...q,isRead:!0}))),console.log("✅ All notifications marked as read"))}catch(f){console.error("Error marking all notifications as read:",f)}},W0=async f=>{try{const E=localStorage.getItem("token");if(!E){console.log("❌ No token found");return}console.log("📥 Fetching messages for user:",f);const A=await fetch(`/api/messages/${f}`,{headers:{Authorization:`Bearer ${E}`}});if(A.ok){const q=await A.json();console.log("✅ Messages fetched:",q.length),V(q)}else{const q=await A.json();console.error("❌ Failed to fetch messages:",q)}}catch(E){console.error("Error fetching messages:",E)}},ba=async()=>{if(!te.trim()||!I){console.log("❌ Missing message or chat user:",{newMessage:te,chatUser:I});return}const f=I.id||I._id;if(!f){console.error("❌ No recipient ID found:",I),alert("خطأ: لا يمكن تحديد المستقبل");return}console.log("📤 Sending message:",{recipientId:f,content:te});try{const E=localStorage.getItem("token"),A=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({recipientId:f,content:te})});if(A.ok){const q=await A.json();console.log("✅ Message sent successfully:",q),V([...H,q.messageData]),Re(""),Ul(),dr&&dr.sendPrivateMessage(q.messageData,f);try{const J=new Audio("data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT");J.volume=.3,J.play().catch(()=>{})}catch{}}else{const q=await A.json();console.error("❌ Message send failed:",q),alert(q.message||"فشل في إرسال الرسالة")}}catch(E){console.error("Error sending message:",E),alert("حدث خطأ في إرسال الرسالة")}},Ul=()=>{setTimeout(()=>{const f=document.getElementById("messages-container");f&&f.scrollTo({top:f.scrollHeight,behavior:"smooth"})},100)},Q0=f=>{console.log("💬 Opening chat with user:",f);const E=f.id||f._id;console.log("📋 User ID for messages:",E),Q(f),x(!0),V([]),E?W0(E).then(()=>{Ul()}):console.error("❌ No user ID found for chat")},G0=async()=>{if(!t&&(e!=null&&e.id))try{const f=localStorage.getItem("token");if(!f){console.warn("No token found for checking friendship");return}const E=await fetch(`/api/friends/check/${e.id}`,{headers:{Authorization:`Bearer ${f}`,"Content-Type":"application/json"}});if(E.ok){const A=await E.json();m(A.isFriend),console.log("✅ Friendship status checked:",A.isFriend)}else console.error("❌ Failed to check friendship:",E.status),m(!1)}catch(f){console.error("Error checking friendship:",f),m(!1)}},D0=async()=>{if(M.trim())try{const f=localStorage.getItem("token"),E=await fetch("/api/messages",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${f}`},body:JSON.stringify({recipientId:e.id,content:M})});if(E.ok)L(""),p(!1),alert("تم إرسال الرسالة بنجاح!");else{const A=await E.json();alert(A.message||"فشل في إرسال الرسالة")}}catch(f){console.error("Error sending message:",f),alert("خطأ في إرسال الرسالة")}},J0=f=>{var A;const E=(A=f.target.files)==null?void 0:A[0];if(E){if(E.size>5*1024*1024){alert("حجم الصورة كبير جداً. يجب أن يكون أقل من 5 ميجابايت");return}const q=new FileReader;q.onload=J=>{var G;d((G=J.target)==null?void 0:G.result)},q.readAsDataURL(E)}},Z0=async()=>{var f,E;N(!0);try{const A=localStorage.getItem("token"),q={gender:g};c&&c!==(e==null?void 0:e.profileImage)&&(q.profileImage=c),console.log("🔄 Updating profile with data:",{hasProfileImage:!!q.profileImage,gender:q.gender,selectedImageLength:(c==null?void 0:c.length)||0,currentImageLength:((f=e==null?void 0:e.profileImage)==null?void 0:f.length)||0});const J=await fetch("/api/profile/update",{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A}`},body:JSON.stringify(q)});if(J.ok){const G=await J.json();console.log("✅ Profile updated successfully:",{hasProfileImage:!!G.profileImage,profileImageLength:((E=G.profileImage)==null?void 0:E.length)||0}),n==null||n(G),d(""),a(!1),alert("تم تحديث الملف الشخصي بنجاح!")}else{const G=await J.json();console.error("❌ Profile update failed:",G),alert(G.message||"فشل في تحديث الملف الشخصي")}}catch(A){console.error("Error updating profile:",A),alert("حدث خطأ في تحديث الملف الشخصي")}finally{N(!1)}},K0=()=>c||(e!=null&&e.profileImage?e.profileImage:(e==null?void 0:e.gender)==="female"?"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiNGRjY5QjQiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQzEzLjEgMiAxNCAyLjkgMTQgNFMxMy4xIDYgMTIgNiAxMCA1LjEgMTAgNFMxMC45IDIgMTIgMlpNMjEgOVYxMUgyMFYxMkMxOS4xIDEyIDE4LjQgMTIuNiAxOC4xIDEzLjNDMTcuMSAxMS45IDE1LjEgMTEgMTMuOCAxMC43QzE0IDEwLjUgMTQuMSAxMC4yIDE0LjEgMTBDMTQgOS4xIDEzLjYgOC40IDEzIDhDMTMuNCA3LjYgMTMuNyA3IDE0IDYuOUMxNS40IDcuNyAxNi4yIDkuMSAxNiAzMEMxOC40IDI5IDEwLjUgMzAgOFMxMS42IDI5IDEwIDI5LjdIMThDMTggMjguNSAxOC4zIDI3LjUgMTguOSAyNi43QzE5LjMgMjcuMSAxOS44IDI3LjMgMjAuNSAyNy4zSDE5QzE5IDI3IDEwLjMgMjcgMTAuNSAyNy4zSDE5LjQgMjEgOVoiLz4KPHN2Zz4KPHN2Zz4K":"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiByeD0iNTAiIGZpbGw9IiMzQjgyRjYiLz4KPHN2ZyB4PSIyNSIgeT0iMjUiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJ3aGl0ZSI+CjxwYXRoIGQ9Ik0xMiAyQTMgMyAwIDAgMSAxNSA1QTMgMyAwIDAgMSAxMiA4QTMgMyAwIDAgMSA5IDVBMyAzIDAgMCAxIDEyIDJNMjEgMjFWMjBDMjEgMTYuMTMgMTcuODcgMTMgMTQgMTNIMTBDNi4xMyAxMyAzIDE2LjEzIDMgMjBWMjFIMjFaIi8+Cjwvc3ZnPgo=");return r.jsxs("div",{className:"max-w-md mx-auto bg-gradient-to-br from-gray-900 via-gray-800 to-black min-h-screen shadow-2xl overflow-hidden flex flex-col",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 p-4 text-white relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 opacity-20",children:[r.jsx("div",{className:"absolute top-0 left-0 w-20 h-20 bg-blue-400/10 rounded-full blur-xl animate-pulse"}),r.jsx("div",{className:"absolute top-10 right-0 w-16 h-16 bg-indigo-400/5 rounded-full blur-lg animate-bounce"}),r.jsx("div",{className:"absolute bottom-0 left-1/2 w-24 h-24 bg-slate-400/5 rounded-full blur-2xl animate-pulse delay-1000"})]}),r.jsxs("div",{className:"absolute inset-0 opacity-15",children:[r.jsx("div",{className:"absolute top-0 left-0 w-40 h-40 bg-white/30 rounded-full -translate-x-20 -translate-y-20 blur-xl"}),r.jsx("div",{className:"absolute bottom-0 right-0 w-32 h-32 bg-white/20 rounded-full translate-x-12 translate-y-12 blur-lg"}),r.jsx("div",{className:"absolute top-1/2 left-1/2 w-24 h-24 bg-white/10 rounded-full -translate-x-12 -translate-y-12 blur-md"})]}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("div",{className:"flex flex-col items-center mb-3",children:[r.jsxs("div",{className:"relative group",children:[r.jsx("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-blue-500 via-indigo-500 to-slate-500 animate-spin-slow opacity-60 blur-sm"}),r.jsxs("div",{className:"relative w-16 h-16 rounded-full overflow-hidden border-2 border-white/70 shadow-lg bg-gradient-to-br from-blue-400 to-indigo-500 ring-1 ring-white/40 backdrop-blur-sm transform group-hover:scale-105 transition-all duration-300",children:[r.jsx("img",{src:K0(),alt:"الصورة الشخصية",className:"w-full h-full object-cover"}),r.jsx("div",{className:"absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border border-white shadow-sm animate-pulse"})]}),t&&r.jsx("button",{onClick:()=>{var f;return i?(f=document.getElementById("imageUpload"))==null?void 0:f.click():a(!0)},className:"absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center shadow-lg border border-white/60 hover:scale-110 transition-all duration-300",children:r.jsx(Oh,{className:"w-3 h-3 text-white"})}),i&&r.jsx("input",{id:"imageUpload",type:"file",accept:"image/*",onChange:J0,className:"hidden"})]}),r.jsxs("div",{className:"text-center mt-2",children:[r.jsx("h2",{className:"text-lg font-bold text-white mb-1 drop-shadow-md",children:e==null?void 0:e.username}),r.jsxs("p",{className:"text-white/60 text-xs bg-white/10 px-2 py-0.5 rounded-full backdrop-blur-sm",children:["ID: ",e==null?void 0:e.playerId]}),r.jsxs("div",{className:"flex items-center justify-center mt-1 gap-1",children:[r.jsxs("div",{className:"flex items-center bg-blue-500/20 px-1.5 py-0.5 rounded-full",children:[r.jsx(cn,{className:"w-2.5 h-2.5 text-blue-300 mr-1"}),r.jsxs("span",{className:"text-blue-200 text-xs font-medium",children:["Lv.",(e==null?void 0:e.level)||1]})]}),r.jsxs("div",{className:"flex items-center bg-indigo-500/20 px-1.5 py-0.5 rounded-full",children:[r.jsx(es,{className:"w-2.5 h-2.5 text-indigo-300 mr-1"}),r.jsx("span",{className:"text-indigo-200 text-xs font-medium",children:(e==null?void 0:e.points)||0})]})]})]}),i&&r.jsxs("div",{className:"mt-3 flex space-x-2 rtl:space-x-reverse",children:[r.jsx("button",{onClick:()=>w("male"),className:`px-3 py-1 rounded-full text-xs ${g==="male"?"bg-white text-blue-600":"bg-white/20 text-white"}`,children:"ذكر"}),r.jsx("button",{onClick:()=>w("female"),className:`px-3 py-1 rounded-full text-xs ${g==="female"?"bg-white text-pink-600":"bg-white/20 text-white"}`,children:"أنثى"})]})]}),t?r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"grid grid-cols-3 gap-1.5 text-center mt-3",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(Tl,{className:"w-3 h-3 text-yellow-400"})}),r.jsx("div",{className:"text-sm font-bold text-yellow-200",children:(e==null?void 0:e.goldCoins)||0}),r.jsx("div",{className:"text-xs text-yellow-300/80",children:"ذهب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-indigo-700/40 rounded-lg p-2 backdrop-blur-sm border border-indigo-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(u0,{className:"w-3 h-3 text-purple-400"})}),r.jsx("div",{className:"text-sm font-bold text-purple-200",children:(e==null?void 0:e.pearls)||0}),r.jsx("div",{className:"text-xs text-purple-300/80",children:"لؤلؤ"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-blue-700/40 rounded-lg p-2 backdrop-blur-sm border border-blue-400/20 hover:scale-105 transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(cn,{className:"w-3 h-3 text-blue-400"})}),r.jsxs("div",{className:"text-sm font-bold text-blue-200",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-blue-300/80",children:"مستوى"})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-1.5 text-center mt-1.5",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-green-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-green-400/20",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(ii,{className:"w-3 h-3 text-green-400"})}),r.jsx("div",{className:"text-sm font-bold text-green-200",children:(e==null?void 0:e.gamesPlayed)||0}),r.jsx("div",{className:"text-xs text-green-300/80",children:"ألعاب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-700/40 to-red-700/40 rounded-lg p-1.5 backdrop-blur-sm border border-red-400/20",children:[r.jsx("div",{className:"flex items-center justify-center mb-0.5",children:r.jsx(m0,{className:"w-3 h-3 text-red-400"})}),r.jsx("div",{className:"text-sm font-bold text-red-200",children:((ja=e==null?void 0:e.friends)==null?void 0:ja.length)||0}),r.jsx("div",{className:"text-xs text-red-300/80",children:"أصدقاء"})]})]}),r.jsx("div",{className:"mt-3 flex justify-center",children:r.jsxs("button",{onClick:()=>us(!0),className:"relative bg-white/20 hover:bg-white/30 rounded-lg p-3 backdrop-blur-sm transition-all duration-300 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"🔔"}),r.jsx("span",{className:"text-white text-sm font-medium",children:"الإشعارات"}),jt.filter(f=>!f.isRead).length>0&&r.jsx("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-bold",children:jt.filter(f=>!f.isRead).length})]})})]}):r.jsxs("div",{className:"space-y-3",children:[r.jsxs("div",{className:"bg-white/20 rounded-lg p-3 backdrop-blur-sm text-center",children:[r.jsxs("div",{className:"text-lg font-bold text-black",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-white/80",children:"مستوى"})]}),S&&r.jsxs("button",{onClick:()=>p(!0),className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-2 px-4 rounded-lg flex items-center justify-center gap-2 hover:from-blue-600 hover:to-purple-700 transition-all duration-300 shadow-lg",children:[r.jsx(fn,{className:"w-4 h-4"}),r.jsx("span",{children:"إرسال رسالة"})]})]})]}),i&&r.jsxs("div",{className:"absolute top-4 right-4 flex space-x-2 rtl:space-x-reverse",children:[r.jsx("button",{onClick:Z0,disabled:b,className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:r.jsx($h,{className:"w-4 h-4 text-white"})}),r.jsx("button",{onClick:()=>{a(!1),d(""),w((e==null?void 0:e.gender)||"male")},className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:r.jsx(ma,{className:"w-4 h-4 text-white"})})]})]}),r.jsx("div",{className:"bg-gradient-to-r from-gray-900/95 via-purple-900/95 to-gray-900/95 backdrop-blur-md border-b border-purple-500/30 z-20 mx-4 rounded-xl mt-4 shadow-xl flex-shrink-0",children:r.jsx("div",{className:"flex overflow-x-auto scrollbar-hide p-2",children:[{id:"overview",label:"عام",icon:pn,color:"from-blue-500 to-cyan-500"},...t?[{id:"friends",label:"أصدقاء",icon:bn,color:"from-green-500 to-emerald-500"},{id:"gifts",label:"هدايا",icon:Vh,color:"from-pink-500 to-rose-500"},{id:"items",label:"عناصر",icon:cn,color:"from-yellow-500 to-orange-500"},{id:"charge",label:"شحن",icon:Bh,color:"from-purple-500 to-violet-500"},{id:"exchange",label:"تبديل",icon:Ph,color:"from-indigo-500 to-blue-500"}]:[]].map(f=>r.jsxs("button",{onClick:()=>o(f.id),className:`flex-shrink-0 flex flex-col items-center px-4 py-3 min-w-[70px] transition-all duration-500 rounded-xl relative overflow-hidden group ${l===f.id?`bg-gradient-to-br ${f.color} text-white shadow-2xl transform scale-110 animate-glow`:"text-gray-300 hover:bg-gray-800/60 hover:text-white hover:scale-105"}`,children:[l===f.id&&r.jsx("div",{className:`absolute inset-0 bg-gradient-to-br ${f.color} opacity-20 blur-xl`}),r.jsxs("div",{className:"relative z-10 flex flex-col items-center",children:[r.jsx(f.icon,{className:`w-5 h-5 mb-1 transition-all duration-300 ${l===f.id?"animate-bounce":"group-hover:scale-110"}`}),r.jsx("span",{className:"text-xs font-medium",children:f.label})]}),l===f.id&&r.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-white rounded-full"})]},f.id))})}),r.jsxs("div",{className:"flex-1 flex flex-col",children:[l==="overview"&&r.jsxs("div",{className:"flex-1 flex flex-col p-4 gap-4",children:[r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/90 via-blue-800/90 to-indigo-800/90 rounded-xl p-3 border border-blue-400/30 shadow-lg backdrop-blur-sm relative overflow-hidden flex-shrink-0",children:[r.jsx("div",{className:"absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-full blur-xl animate-pulse"}),r.jsx("div",{className:"absolute bottom-0 left-0 w-12 h-12 bg-indigo-500/10 rounded-full blur-lg animate-float"}),r.jsxs("div",{className:"relative z-10",children:[r.jsxs("h3",{className:"font-bold text-blue-300 mb-3 text-base flex items-center gap-2",children:[r.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center",children:r.jsx(pn,{className:"w-3 h-3 text-white"})}),"معلومات الحساب"]}),r.jsxs("div",{className:"grid gap-2",children:[r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-pink-500 to-purple-500 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs",children:"👤"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الجنس"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:(e==null?void 0:e.gender)==="female"?"👩 أنثى":"👨 ذكر"})]}),r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-blue-500 to-indigo-500 rounded flex items-center justify-center",children:r.jsx(zh,{className:"w-3 h-3 text-white"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"الانضمام"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.joinedAt).toLocaleDateString("ar-EG")})]}),r.jsxs("div",{className:"flex justify-between items-center p-2 bg-gradient-to-r from-blue-900/40 to-indigo-900/40 rounded-lg backdrop-blur-sm border border-blue-400/20",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-5 h-5 bg-gradient-to-br from-green-500 to-emerald-500 rounded flex items-center justify-center",children:r.jsx(mn,{className:"w-3 h-3 text-white"})}),r.jsx("span",{className:"text-blue-100 font-medium text-sm",children:"آخر نشاط"})]}),r.jsx("span",{className:"text-blue-200 font-bold text-sm",children:new Date(e==null?void 0:e.lastActive).toLocaleDateString("ar-EG")})]})]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 gap-3 flex-shrink-0",children:[r.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-blue-800/80 rounded-xl p-3 shadow-lg border border-blue-400/30 backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:r.jsx(es,{className:"w-4 h-4 text-white"})}),r.jsx("div",{className:"text-lg font-bold text-blue-200",children:(e==null?void 0:e.experience)||0}),r.jsx("div",{className:"text-xs text-blue-300 font-medium",children:"خبرة"})]})}),r.jsx("div",{className:"bg-gradient-to-br from-slate-800/80 to-indigo-800/80 rounded-xl p-3 shadow-lg border border-indigo-400/30 backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-2 shadow-md",children:r.jsx(cn,{className:"w-4 h-4 text-white"})}),r.jsxs("div",{className:"text-lg font-bold text-indigo-200",children:["Lv.",(e==null?void 0:e.level)||1]}),r.jsx("div",{className:"text-xs text-indigo-300 font-medium",children:"مستوى"})]})})]}),!t&&r.jsx("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 rounded-xl p-3 border border-blue-400/30 shadow-md backdrop-blur-sm flex-shrink-0",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-6 h-6 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full flex items-center justify-center shadow-sm",children:r.jsx(pn,{className:"w-3 h-3 text-white"})}),r.jsxs("div",{children:[r.jsx("h4",{className:"font-bold text-blue-300 text-sm",children:"ملف عام"}),r.jsx("p",{className:"text-xs text-blue-200",children:"معلومات أساسية فقط"})]})]})})]}),t&&l==="friends"&&r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"👥"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إدارة الأصدقاء"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"أضف وتفاعل مع أصدقائك في المنصة"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx(Pl,{className:"w-6 h-6 text-blue-300"}),r.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إضافة صديق جديد"})]}),r.jsxs("div",{className:"flex gap-3 mb-4",children:[r.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:v,onChange:f=>{const E=f.target.value.replace(/\D/g,"");O(E),pe("")},className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:6,onKeyPress:f=>f.key==="Enter"&&xa()}),r.jsxs("button",{onClick:xa,disabled:Pe||v.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:[Pe?"⏳":"🔍"," ",Pe?"جاري البحث...":"بحث"]})]}),Ee&&r.jsx("div",{className:"bg-red-500/20 border border-red-400/30 rounded-xl p-3 mb-4",children:r.jsx("p",{className:"text-red-200 text-sm text-center",children:Ee})}),X&&r.jsx("div",{className:"bg-green-500/20 border border-green-400/30 rounded-xl p-4 mb-4",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:X.profileImage?r.jsx("img",{src:X.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(ka=(Na=X.username)==null?void 0:Na.charAt(0))==null?void 0:ka.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-green-200 font-bold",children:X.username}),r.jsxs("p",{className:"text-green-300 text-xs",children:["رقم اللاعب: ",X.playerId]})]})]}),r.jsx("button",{onClick:()=>z0(X.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"➕ إضافة صديق"})]})})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"👫"}),"قائمة الأصدقاء (",st.length,")"]}),st.length===0?r.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"😔"}),"لا توجد أصدقاء حالياً"]}):r.jsx("div",{className:"space-y-3",children:st.map(f=>{var E,A;return r.jsxs("div",{className:"flex items-center justify-between p-3 bg-slate-900/40 rounded-xl",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:f.profileImage?r.jsx("img",{src:f.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(A=(E=f.username)==null?void 0:E.charAt(0))==null?void 0:A.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-slate-200 font-medium",children:f.username}),r.jsxs("p",{className:"text-slate-400 text-xs",children:["رقم اللاعب: ",f.playerId]})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>Q0(f),className:"bg-gradient-to-r from-blue-500 to-cyan-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all",children:"💬 محادثة"}),r.jsx("button",{onClick:()=>C(f),className:"bg-gradient-to-r from-purple-500 to-pink-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-purple-600 hover:to-pink-700 transition-all",children:"🎁 هدية"})]})]},f.id)})})]}),r.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/60 to-green-800/60 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-emerald-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📩"}),"طلبات الصداقة (",kn.length,")"]}),kn.length===0?r.jsxs("div",{className:"text-center py-6 text-emerald-300 text-sm bg-emerald-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"📭"}),"لا توجد طلبات صداقة جديدة"]}):r.jsx("div",{className:"space-y-3",children:kn.map(f=>{var E,A;return r.jsxs("div",{className:"flex items-center justify-between p-3 bg-emerald-900/40 rounded-xl",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full flex items-center justify-center text-white font-bold overflow-hidden",children:f.requester.profileImage?r.jsx("img",{src:f.requester.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):(A=(E=f.requester.username)==null?void 0:E.charAt(0))==null?void 0:A.toUpperCase()}),r.jsxs("div",{children:[r.jsx("p",{className:"text-emerald-200 font-medium",children:f.requester.username}),r.jsxs("p",{className:"text-emerald-400 text-xs",children:["رقم اللاعب: ",f.requester.playerId]}),r.jsx("p",{className:"text-emerald-500 text-xs",children:new Date(f.requestedAt).toLocaleDateString("ar")})]})]}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>U0(f.id),className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all",children:"✅ قبول"}),r.jsx("button",{className:"bg-gradient-to-r from-red-500 to-red-600 text-white px-3 py-1 rounded-lg text-xs font-bold hover:from-red-600 hover:to-red-700 transition-all",children:"❌ رفض"})]})]},f.id)})})]})]}),t&&l==="gifts"&&r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🎁"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام إدارة الهدايا"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"أرسل واستقبل الهدايا المتنوعة مع الأصدقاء"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("div",{className:"flex items-center gap-3 mb-5",children:[r.jsx(Pr,{className:"w-6 h-6 text-blue-300"}),r.jsx("h4",{className:"font-bold text-blue-200 text-lg",children:"إرسال هدية جديدة"})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-yellow-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"💰"}),"العملات الذهبية"]}),r.jsx("div",{className:"grid grid-cols-1 gap-3",children:r.jsxs("button",{onClick:()=>ft("gold"),className:`p-4 border rounded-xl hover:bg-yellow-700/50 transition-all duration-300 flex items-center gap-4 shadow-lg ${fe==="gold"?"bg-yellow-700/60 border-yellow-300":"bg-yellow-800/40 border-yellow-400/30"}`,children:[r.jsx("div",{className:"text-3xl drop-shadow-lg",children:"🪙"}),r.jsxs("div",{className:"text-right flex-1",children:[r.jsx("div",{className:"text-sm font-bold text-yellow-200",children:"عملات ذهبية"}),r.jsx("div",{className:"text-xs text-yellow-300",children:"للشراء والاستخدام في المنصة"})]}),fe==="gold"&&r.jsx("div",{className:"text-yellow-300",children:"✓"})]})})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-red-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"⚠️"}),"العناصر الضارة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{onClick:()=>ft("bomb"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${fe==="bomb"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💣"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"قنبلة مدمرة"}),fe==="bomb"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ft("bat"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${fe==="bat"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🦇"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"خفاش مؤذي"}),fe==="bat"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ft("snake"),className:`p-4 border rounded-xl hover:bg-red-700/50 transition-all duration-300 text-center shadow-lg ${fe==="snake"?"bg-red-700/60 border-red-300":"bg-red-800/40 border-red-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🐍"}),r.jsx("div",{className:"text-xs font-bold text-red-200",children:"ثعبان سام"}),fe==="snake"&&r.jsx("div",{className:"text-red-300 mt-1",children:"✓"})]})]})]}),r.jsxs("div",{className:"mb-5",children:[r.jsxs("h5",{className:"text-base font-bold text-emerald-300 mb-3 flex items-center gap-2",children:[r.jsx("span",{className:"text-xl",children:"✨"}),"العناصر المفيدة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-3",children:[r.jsxs("button",{onClick:()=>ft("gem"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${fe==="gem"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"💎"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"جوهرة نادرة"}),fe==="gem"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ft("star"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${fe==="star"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"⭐"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"نجمة ذهبية"}),fe==="star"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]}),r.jsxs("button",{onClick:()=>ft("coin"),className:`p-4 border rounded-xl hover:bg-emerald-700/50 transition-all duration-300 text-center shadow-lg ${fe==="coin"?"bg-emerald-700/60 border-emerald-300":"bg-emerald-800/40 border-emerald-400/30"}`,children:[r.jsx("div",{className:"text-3xl mb-2 drop-shadow-lg",children:"🪙"}),r.jsx("div",{className:"text-xs font-bold text-emerald-200",children:"عملة خاصة"}),fe==="coin"&&r.jsx("div",{className:"text-emerald-300 mt-1",children:"✓"})]})]})]}),r.jsxs("div",{className:"space-y-4",children:[fe&&r.jsx("div",{className:"bg-blue-900/30 p-3 rounded-xl border border-blue-400/30",children:r.jsxs("p",{className:"text-blue-200 text-sm text-center",children:["العنصر المختار: ",r.jsx("span",{className:"font-bold text-blue-100",children:va(fe)})]})}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("input",{type:"text",placeholder:"رقم اللاعب (6 أرقام)",value:tn,onChange:f=>{const E=f.target.value.replace(/\D/g,"");cs(E)},maxLength:6,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("button",{onClick:V0,disabled:Ke||!fe||tn.length!==6,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:Ke?"⏳ جاري الإرسال...":"🎁 إرسال"})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-slate-800/60 to-blue-800/60 p-6 rounded-2xl border border-slate-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-slate-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📦"}),"الهدايا المستلمة"]}),r.jsxs("div",{className:"text-center py-6 text-slate-300 text-sm bg-slate-900/30 rounded-xl",children:[r.jsx("div",{className:"text-3xl mb-2",children:"🎈"}),"لا توجد هدايا جديدة في الوقت الحالي"]})]})]}),t&&l==="items"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-4xl mb-3",children:"⚡"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-700 mb-2",children:"العناصر المجمعة"}),r.jsx("p",{className:"text-sm text-gray-500",children:"العناصر التي حصلت عليها من الألعاب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-emerald-800/80 to-green-800/80 p-6 rounded-2xl border border-emerald-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-emerald-300 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"⭐"}),"العناصر المفيدة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💎"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"جوهرة نادرة"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 500 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.gems})]}),r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"⭐"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"نجمة ذهبية"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 200 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.stars})]}),r.jsxs("div",{className:"text-center p-4 bg-emerald-800/40 rounded-xl border border-emerald-400/30 backdrop-blur-sm hover:bg-emerald-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("div",{className:"text-sm font-bold text-emerald-200 mb-1",children:"عملة خاصة"}),r.jsx("div",{className:"text-xs text-emerald-300 bg-emerald-900/30 px-2 py-1 rounded-lg mb-2",children:"مكافأة 100 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-emerald-600 to-green-600 rounded-xl py-2 shadow-md",children:j.coins})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-red-800/80 to-rose-800/80 p-6 rounded-2xl border border-red-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-red-300 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"💣"}),"العناصر الضارة"]}),r.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"💣"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"قنبلة مدمرة"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 100 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bombs})]}),r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🦇"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"خفاش مؤذي"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 50 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.bats})]}),r.jsxs("div",{className:"text-center p-4 bg-red-800/40 rounded-xl border border-red-400/30 backdrop-blur-sm hover:bg-red-700/50 transition-all duration-300 transform hover:scale-105 shadow-lg",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🐍"}),r.jsx("div",{className:"text-sm font-bold text-red-200 mb-1",children:"ثعبان سام"}),r.jsx("div",{className:"text-xs text-red-300 bg-red-900/30 px-2 py-1 rounded-lg mb-2",children:"خسارة 75 🪙"}),r.jsx("div",{className:"text-xl font-bold text-white bg-gradient-to-r from-red-600 to-rose-600 rounded-xl py-2 shadow-md",children:j.snakes})]})]})]}),r.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-amber-200 mb-3 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"💡"}),"نصائح مهمة"]}),r.jsx("p",{className:"text-amber-100 text-sm leading-relaxed",children:"اجمع العناصر المفيدة من الألعاب • أرسلها كهدايا للأصدقاء • بادلها بعملات ذهبية قيمة"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/80 to-indigo-800/80 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-3xl drop-shadow-lg",children:"🛡️"}),"نظام الحماية المتطور"]}),r.jsx("p",{className:"text-blue-100 text-sm mb-6 leading-relaxed",children:"احمِ نفسك من العناصر الضارة والهجمات الخطيرة في الألعاب والهدايا"}),r.jsxs("div",{className:"grid grid-cols-1 gap-5",children:[r.jsxs("div",{className:"bg-blue-800/40 p-5 rounded-xl border border-blue-400/30 backdrop-blur-sm shadow-lg",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-bold text-blue-200 text-base",children:"🥇 درع ذهبي أساسي"}),r.jsx("span",{className:"text-xs text-blue-100 bg-blue-600/40 px-3 py-1 rounded-full font-medium",children:"24 ساعة"})]}),r.jsx("p",{className:"text-sm text-blue-100 mb-4 leading-relaxed",children:"حماية قوية ضد القنابل المدمرة والخفافيش المؤذية والثعابين السامة"}),U!=null&&U.isActive?r.jsxs("div",{className:"bg-green-600/40 p-3 rounded-xl border border-green-400/30 text-center",children:[r.jsx("div",{className:"text-green-200 text-sm font-bold",children:"🛡️ الدرع نشط"}),r.jsxs("div",{className:"text-green-100 text-xs mt-1",children:["ينتهي في: ",U.expiresAt?new Date(U.expiresAt).toLocaleString("ar"):"غير محدد"]})]}):r.jsx("button",{onClick:()=>P0("gold"),disabled:R,className:"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:R?"⏳ جاري التفعيل...":"🛡️ تفعيل الحماية (5,000 🪙)"})]}),r.jsxs("div",{className:"bg-purple-800/40 p-5 rounded-xl border border-purple-400/30 backdrop-blur-sm shadow-lg",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h5",{className:"font-bold text-purple-200 text-base",children:"👑 درع متقدم مميز"}),r.jsx("span",{className:"text-xs text-purple-100 bg-purple-600/40 px-3 py-1 rounded-full font-medium",children:"7 أيام"})]}),r.jsx("p",{className:"text-sm text-purple-100 mb-4 leading-relaxed",children:"حماية مميزة وشاملة لمدة أسبوع كامل ضد جميع العناصر الضارة والهجمات"}),r.jsx("button",{onClick:()=>alert("الدرع المميز غير متاح حالياً. استخدم الدرع الأساسي."),className:"w-full bg-gradient-to-r from-gray-500 to-gray-600 text-white py-3 rounded-xl text-sm font-bold cursor-not-allowed opacity-60",disabled:!0,children:"👑 قريباً - الحماية المميزة"})]})]})]})]}),t&&l==="charge"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"💰"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"شحن الرصيد الذهبي"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"اشحن عملاتك الذهبية بأفضل الأسعار"})]}),r.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[r.jsx("div",{className:"bg-gradient-to-br from-yellow-800/60 to-amber-800/60 p-6 rounded-2xl border border-yellow-400/30 shadow-xl backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("h4",{className:"font-bold text-yellow-200 mb-2 text-lg",children:"5,000 عملة ذهبية"}),r.jsx("p",{className:"text-yellow-100 text-base mb-4 font-semibold",children:"💵 $1 USD فقط"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>ya(5e3,!0,"1_dollar"),disabled:bt||!Mn["1_dollar"],className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:bt?"⏳":Mn["1_dollar"]?"🆓 مجاني":"✅ مستخدم"}),r.jsx("button",{onClick:()=>wa(5e3,"$1 USD"),className:"flex-1 bg-gradient-to-r from-yellow-500 to-amber-600 text-white py-3 rounded-xl text-xs font-bold hover:from-yellow-600 hover:to-amber-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})}),r.jsx("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"text-4xl mb-3 drop-shadow-lg",children:"🪙"}),r.jsx("h4",{className:"font-bold text-green-200 mb-2 text-lg",children:"27,200 عملة ذهبية"}),r.jsx("p",{className:"text-green-100 text-base mb-1 font-semibold",children:"💵 $5 USD"}),r.jsx("p",{className:"text-sm text-green-300 bg-green-900/30 px-3 py-1 rounded-lg mb-4 font-medium",children:"🎉 وفر 8% أكثر!"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>ya(27200,!0,"5_dollar"),disabled:bt||!Mn["5_dollar"],className:"flex-1 bg-gradient-to-r from-blue-500 to-cyan-600 text-white py-3 rounded-xl text-xs font-bold hover:from-blue-600 hover:to-cyan-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:bt?"⏳":Mn["5_dollar"]?"🆓 مجاني":"✅ مستخدم"}),r.jsx("button",{onClick:()=>wa(27200,"$5 USD"),className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-xs font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md",children:"📱 شحن مدفوع"})]})]})})]})]}),t&&l==="exchange"&&r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-5xl mb-4 drop-shadow-lg",children:"🔄"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"نظام تبديل العملات"}),r.jsx("p",{className:"text-gray-300 text-sm",children:"اللآلئ مخصصة حصرياً للتحويل إلى دولارات نقدية"})]}),r.jsxs("div",{className:"space-y-5",children:[r.jsxs("div",{className:"bg-gradient-to-br from-blue-800/60 to-indigo-800/60 p-6 rounded-2xl border border-blue-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-blue-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🪙➡️🦪"}),"تحويل ذهب إلى لآلئ"]}),r.jsx("p",{className:"text-blue-100 text-sm mb-4 bg-blue-900/30 px-3 py-2 rounded-lg",children:"معدل التحويل: 10,000 🪙 = 1 🦪"}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("input",{type:"number",placeholder:"10000",value:be,onChange:f=>Cn(Math.max(1e4,parseInt(f.target.value)||1e4)),min:"10000",step:"10000",max:(e==null?void 0:e.goldCoins)||0,className:"flex-1 px-4 py-3 bg-blue-900/30 border border-blue-400/30 rounded-xl text-white placeholder-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500"}),r.jsx("span",{className:"text-blue-200 font-medium",children:"🪙 ➡️ 🦪"})]}),r.jsxs("div",{className:"text-center mb-4",children:[r.jsxs("p",{className:"text-blue-200 text-sm",children:["ستحصل على: ",r.jsxs("span",{className:"font-bold text-blue-100",children:[Math.floor(be/1e4)," 🦪"]})]}),r.jsxs("p",{className:"text-blue-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.goldCoins)||0," 🪙"]})]}),r.jsx("button",{onClick:F0,disabled:is||be<1e4||((e==null?void 0:e.goldCoins)||0)<be,className:"w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 rounded-xl text-sm font-bold hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:is?"⏳ جاري التحويل...":"🔄 تحويل إلى لآلئ"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-green-800/60 to-emerald-800/60 p-6 rounded-2xl border border-green-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-green-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"🦪➡️💵"}),"سحب دولارات نقدية"]}),r.jsx("div",{className:"bg-green-900/30 p-4 rounded-xl mb-4",children:r.jsxs("p",{className:"text-green-100 text-sm leading-relaxed",children:[r.jsx("strong",{className:"text-green-200",children:"💰 معدل التحويل:"})," 10 🦪 = $1 USD",r.jsx("br",{}),r.jsx("strong",{className:"text-green-200",children:"🎯 الحد الأدنى للسحب:"})," $25 USD (250 🦪)"]})}),r.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[r.jsx("input",{type:"number",placeholder:"250",value:mt,onChange:f=>zl(Math.max(250,parseInt(f.target.value)||250)),min:"250",max:(e==null?void 0:e.pearls)||0,className:"flex-1 px-4 py-3 bg-green-900/30 border border-green-400/30 rounded-xl text-white placeholder-green-300 focus:outline-none focus:ring-2 focus:ring-green-500"}),r.jsx("span",{className:"text-green-200 font-medium",children:"🦪 ➡️ $"})]}),r.jsxs("div",{className:"text-center mb-4",children:[r.jsxs("p",{className:"text-green-200 text-sm",children:["ستحصل على: ",r.jsxs("span",{className:"font-bold text-green-100",children:["$",mt/10," USD"]})]}),r.jsxs("p",{className:"text-green-300 text-xs",children:["رصيدك الحالي: ",(e==null?void 0:e.pearls)||0," 🦪"]})]}),r.jsx("button",{onClick:B0,disabled:mt<250||((e==null?void 0:e.pearls)||0)<mt,className:"w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 rounded-xl text-sm font-bold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 shadow-md disabled:opacity-50 disabled:cursor-not-allowed",children:"📱 طلب سحب عبر واتساب"})]}),r.jsxs("div",{className:"bg-gradient-to-br from-amber-800/60 to-yellow-800/60 p-6 rounded-2xl border border-amber-400/30 shadow-xl backdrop-blur-sm",children:[r.jsxs("h4",{className:"font-bold text-amber-200 mb-4 text-lg flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:"📝"}),"معلومات مهمة"]}),r.jsxs("div",{className:"space-y-2 text-amber-100 text-sm leading-relaxed",children:[r.jsxs("p",{children:["• ",r.jsx("strong",{children:"اللآلئ 🦪"})," - مخصصة حصرياً للتحويل إلى دولارات نقدية"]}),r.jsxs("p",{children:["• ",r.jsx("strong",{children:"العملات الذهبية 🪙"})," - للشراء والتبادل داخل المنصة"]}),r.jsxs("p",{children:["• ",r.jsx("strong",{children:"العناصر الخاصة"})," - تُكسب من الألعاب والتحديات"]})]})]})]})]})]}),u&&r.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-slate-800 to-blue-800 rounded-2xl p-6 w-full max-w-md shadow-2xl border border-blue-400/30",children:[r.jsxs("h3",{className:"text-xl font-bold text-white mb-4 text-center",children:["إرسال رسالة إلى ",e==null?void 0:e.username]}),r.jsx("textarea",{value:M,onChange:f=>L(f.target.value),placeholder:"اكتب رسالتك هنا...",className:"w-full p-3 rounded-xl bg-slate-800/40 border border-blue-400/30 text-white placeholder-blue-300 resize-none h-32 focus:outline-none focus:ring-2 focus:ring-blue-500",maxLength:500}),r.jsxs("div",{className:"text-right text-xs text-blue-300 mt-1 mb-4",children:[M.length,"/500"]}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>{p(!1),L("")},className:"flex-1 bg-slate-600 hover:bg-slate-700 text-white py-2 px-4 rounded-xl transition-colors",children:"إلغاء"}),r.jsxs("button",{onClick:D0,disabled:!M.trim(),className:"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-500 disabled:to-gray-600 text-white py-2 px-4 rounded-xl transition-all flex items-center justify-center gap-2",children:[r.jsx(Pr,{className:"w-4 h-4"}),"إرسال"]})]})]})}),h&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-purple-900 to-pink-900 rounded-2xl p-6 w-full max-w-md border border-purple-400/30 shadow-2xl",children:[r.jsxs("div",{className:"text-center mb-6",children:[r.jsx("div",{className:"text-4xl mb-3",children:"🎁"}),r.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"إرسال هدية"}),r.jsxs("p",{className:"text-purple-200 text-sm",children:["إلى: ",h.username]})]}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"نوع الهدية:"}),r.jsxs("div",{className:"flex gap-2",children:[r.jsx("button",{onClick:()=>W("gold"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${P==="gold"?"bg-gradient-to-r from-yellow-500 to-orange-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"🪙 عملات ذهبية"}),r.jsx("button",{onClick:()=>W("pearls"),className:`flex-1 py-2 px-4 rounded-lg text-sm font-bold transition-all ${P==="pearls"?"bg-gradient-to-r from-blue-500 to-cyan-600 text-white":"bg-purple-800/50 text-purple-200 hover:bg-purple-700/50"}`,children:"💎 لآلئ"})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"الكمية:"}),r.jsx("input",{type:"number",value:T,onChange:f=>F(Math.max(1,parseInt(f.target.value)||1)),min:"1",max:P==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls,className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500"}),r.jsxs("p",{className:"text-purple-300 text-xs mt-1",children:["الحد الأقصى: ",P==="gold"?e==null?void 0:e.goldCoins:e==null?void 0:e.pearls]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-purple-200 text-sm font-medium mb-2",children:"رسالة (اختيارية):"}),r.jsx("textarea",{value:Y,onChange:f=>se(f.target.value),placeholder:"اكتب رسالة مع الهدية...",className:"w-full px-4 py-2 bg-purple-800/50 border border-purple-400/30 rounded-lg text-white placeholder-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-500 resize-none",rows:3,maxLength:200})]})]}),r.jsxs("div",{className:"flex gap-3 mt-6",children:[r.jsx("button",{onClick:()=>{C(null),se(""),F(100)},className:"flex-1 bg-gray-600 text-white py-3 rounded-xl font-bold hover:bg-gray-700 transition-all",children:"إلغاء"}),r.jsx("button",{onClick:O0,disabled:me||T<=0,className:"flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-xl font-bold hover:from-purple-600 hover:to-pink-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed",children:me?"⏳ جاري الإرسال...":"🎁 إرسال الهدية"})]})]})}),ds&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-gradient-to-br from-slate-900 to-purple-900 rounded-2xl p-6 w-full max-w-md max-h-[80vh] overflow-hidden border border-purple-400/30 shadow-2xl",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("h3",{className:"text-xl font-bold text-white flex items-center gap-2",children:["🔔 الإشعارات",jt.filter(f=>!f.isRead).length>0&&r.jsx("span",{className:"bg-red-500 text-white text-xs px-2 py-1 rounded-full",children:jt.filter(f=>!f.isRead).length})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[jt.filter(f=>!f.isRead).length>0&&r.jsx("button",{onClick:H0,className:"text-blue-400 hover:text-blue-300 text-xs font-medium transition-colors",children:"تحديد الكل كمقروء"}),r.jsx("button",{onClick:()=>us(!1),className:"text-gray-400 hover:text-white transition-colors",children:"✕"})]})]}),r.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:jt.length===0?r.jsxs("div",{className:"text-center py-8 text-gray-400",children:[r.jsx("div",{className:"text-4xl mb-2",children:"📭"}),r.jsx("p",{children:"لا توجد إشعارات"})]}):jt.map(f=>r.jsx("div",{onClick:()=>{f.isRead||q0(f._id)},className:`p-3 rounded-xl border transition-all cursor-pointer hover:bg-opacity-80 ${f.isRead?"bg-slate-800/50 border-slate-600/30":"bg-blue-900/30 border-blue-400/30 shadow-lg"}`,children:r.jsxs("div",{className:"flex items-start gap-3",children:[r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:"text-2xl",children:[f.type==="gift_received"&&"🎁",f.type==="item_received"&&"📦",f.type==="friend_request"&&"👥",f.type==="message"&&"💬"]}),!f.isRead&&r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-slate-900 animate-pulse"})]}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h4",{className:`font-bold text-sm ${f.isRead?"text-gray-300":"text-white"}`,children:f.title}),r.jsx("p",{className:`text-xs mt-1 ${f.isRead?"text-gray-400":"text-gray-200"}`,children:f.message}),r.jsxs("div",{className:"flex items-center justify-between mt-2",children:[r.jsx("span",{className:"text-gray-400 text-xs",children:new Date(f.createdAt).toLocaleString("ar")}),!f.isRead&&r.jsx("span",{className:"text-blue-400 text-xs font-bold",children:"جديد"})]})]})]})},f._id))})]})}),En&&I&&r.jsxs("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex flex-col",onClick:f=>{f.target===f.currentTarget&&Ol(!1)},children:[r.jsxs("div",{className:"bg-gradient-to-r from-green-600 to-green-700 px-4 py-3 flex items-center gap-3 shadow-lg",children:[r.jsx("button",{onClick:()=>x(!1),className:"text-white hover:bg-white/20 rounded-full p-2 transition-colors",children:r.jsx(c0,{className:"w-5 h-5"})}),r.jsx("div",{className:"w-10 h-10 rounded-full overflow-hidden bg-white/20 border-2 border-white/30",children:I.profileImage?r.jsx("img",{src:I.profileImage,alt:"صورة",className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold text-lg",children:(Ca=(Sa=I.username)==null?void 0:Sa.charAt(0))==null?void 0:Ca.toUpperCase()})}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"font-bold text-white text-lg",children:I.username}),r.jsxs("p",{className:"text-green-100 text-xs opacity-90",children:["رقم اللاعب: ",I.playerId]}),r.jsx("p",{className:"text-green-100 text-xs opacity-75",children:"🕐 المحادثات تختفي بعد 3 أيام"})]}),r.jsx("div",{className:"text-green-100",children:r.jsx(fn,{className:"w-6 h-6"})})]}),r.jsxs("div",{className:"flex-1 overflow-y-auto px-4 py-6 space-y-4",id:"messages-container",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,backgroundColor:"#0f172a"},children:[H.length===0?r.jsxs("div",{className:"text-center py-20",children:[r.jsx("div",{className:"w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(fn,{className:"w-10 h-10 text-white/50"})}),r.jsx("p",{className:"text-white/70 text-lg font-medium",children:"ابدأ محادثة جديدة"}),r.jsx("p",{className:"text-white/50 text-sm mt-2",children:"أرسل رسالة لبدء المحادثة"}),r.jsx("div",{className:"mt-4 p-3 bg-yellow-500/20 rounded-lg border border-yellow-500/30",children:r.jsx("p",{className:"text-yellow-200 text-xs",children:"🕐 تنبيه: المحادثات تُحذف تلقائياً بعد 3 أيام للحفاظ على الخصوصية"})})]}):H.map((f,E)=>{var J,G;const A=((J=f.sender)==null?void 0:J._id)===(e==null?void 0:e.id),q=E===0||new Date(f.createdAt).getTime()-new Date((G=H[E-1])==null?void 0:G.createdAt).getTime()>3e5;return r.jsxs("div",{children:[q&&r.jsx("div",{className:"text-center my-4",children:r.jsx("span",{className:"bg-black/30 text-white/70 px-3 py-1 rounded-full text-xs",children:new Date(f.createdAt).toLocaleDateString("ar",{weekday:"short",hour:"2-digit",minute:"2-digit"})})}),r.jsx("div",{className:`flex ${A?"justify-end":"justify-start"} mb-2`,children:r.jsxs("div",{className:`max-w-[80%] px-4 py-3 rounded-2xl shadow-lg relative ${A?"bg-gradient-to-r from-green-500 to-green-600 text-white rounded-br-md":"bg-white text-gray-800 rounded-bl-md"}`,children:[r.jsx("p",{className:"text-sm leading-relaxed break-words whitespace-pre-wrap",children:f.content}),r.jsxs("div",{className:`flex items-center justify-end mt-1 text-xs ${A?"text-green-100":"text-gray-500"}`,children:[r.jsx("span",{children:new Date(f.createdAt).toLocaleTimeString("ar",{hour:"2-digit",minute:"2-digit"})}),A&&r.jsx("div",{className:"ml-1 text-green-200",children:"✓✓"})]}),r.jsx("div",{className:`absolute bottom-0 w-4 h-4 ${A?"-right-2 bg-gradient-to-r from-green-500 to-green-600":"-left-2 bg-white"}`,style:{clipPath:A?"polygon(0 0, 100% 0, 0 100%)":"polygon(100% 0, 0 0, 100% 100%)"}})]})})]},f._id)}),cr&&r.jsx("div",{className:"flex justify-start mb-2",children:r.jsx("div",{className:"bg-white px-4 py-3 rounded-2xl rounded-bl-md shadow-lg",children:r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsxs("div",{className:"flex gap-1",children:[r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),r.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),r.jsx("span",{className:"text-xs text-gray-500 mr-2",children:"يكتب..."})]})})})]}),r.jsxs("div",{className:"bg-gray-100 px-4 py-3 flex items-end gap-3 relative",children:[An&&r.jsx("div",{className:"absolute bottom-full left-4 right-4 mb-2 bg-white rounded-2xl shadow-2xl border border-gray-200 p-4 max-h-48 overflow-y-auto",children:r.jsx("div",{className:"grid grid-cols-8 gap-2",children:["😀","😃","😄","😁","😆","😅","😂","🤣","😊","😇","🙂","🙃","😉","😌","😍","🥰","😘","😗","😙","😚","😋","😛","😝","😜","🤪","🤨","🧐","🤓","😎","🤩","🥳","😏","😒","😞","😔","😟","😕","🙁","☹️","😣","😖","😫","😩","🥺","😢","😭","😤","😠","😡","🤬","🤯","😳","🥵","🥶","😱","😨","😰","😥","😓","🤗","🤔","🤭","🤫","🤥","😶","😐","😑","😬","🙄","😯","😦","😧","😮","😲","🥱","😴","🤤","😪","😵","🤐","🥴","🤢","🤮","🤧","😷","🤒","🤕","🤑","🤠","😈","👿","👹","👺","🤡","💩","👻","💀","☠️","👽","👾","🤖","🎃","😺","😸","😹","😻","😼","😽","🙀","😿","😾","❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","👍","👎","👌","🤌","🤏","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤝","🙏","✍️","💪","🦾","🦿","🦵"].map((f,E)=>r.jsx("button",{onClick:()=>{Re(A=>A+f),Ol(!1)},className:"text-xl hover:bg-gray-100 rounded-lg p-1 transition-colors",children:f},E))})}),r.jsx("button",{onClick:()=>Ol(!An),className:`text-gray-500 hover:text-gray-700 transition-colors p-2 ${An?"bg-gray-200 rounded-full":""}`,children:r.jsx("span",{className:"text-xl",children:"😊"})}),r.jsx("div",{className:"flex-1 relative",children:r.jsx("textarea",{value:te,onChange:f=>{Re(f.target.value),f.target.style.height="auto",f.target.style.height=Math.min(f.target.scrollHeight,120)+"px"},placeholder:"اكتب رسالة...",className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-3xl text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 resize-none max-h-[120px] min-h-[48px]",onKeyPress:f=>{f.key==="Enter"&&!f.shiftKey&&(f.preventDefault(),te.trim()&&ba())},rows:1,autoFocus:!0})}),r.jsx("button",{onClick:ba,disabled:!te.trim(),className:`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${te.trim()?"bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl transform hover:scale-105":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:r.jsx(Pr,{className:"w-5 h-5"})})]})]})]})};class _0{constructor(t){this.localStream=null,this.peerConnections=new Map,this.remoteUsers=new Map,this.isJoined=!1,this.isMuted=!1,this.roomId=null,this.userId=null,this.processingOffers=new Set,this.connectionAttempts=new Map,this.connectionMonitorInterval=null,this.audioContext=null,this.analyser=null,this.voiceActivityThreshold=25,this.isMonitoringVoice=!1,this.isSpeaking=!1,this.lastVoiceActivitySent=0,this.voiceActivityDebounce=500,this.iceServers=[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"},{urls:"turn:openrelay.metered.ca:80",username:"openrelayproject",credential:"openrelayproject"}],this.wsService=t,this.setupWebSocketHandlers()}setupWebSocketHandlers(){this.wsService.onMessage("webrtc_offer",this.handleOffer.bind(this)),this.wsService.onMessage("webrtc_answer",this.handleAnswer.bind(this)),this.wsService.onMessage("webrtc_ice_candidate",this.handleIceCandidate.bind(this)),this.wsService.onMessage("user_joined_voice",this.handleUserJoined.bind(this)),this.wsService.onMessage("user_left_voice",this.handleUserLeft.bind(this))}async joinRoom(t,n){var s;try{this.roomId=t,this.userId=n,this.localStream=await navigator.mediaDevices.getUserMedia({audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0},video:!1}),this.startVoiceActivityDetection(),this.wsService.send({type:"join_voice_room",data:{roomId:t,userId:n}}),this.isJoined=!0,this.startConnectionMonitoring()}catch(l){throw console.error("❌ Error joining voice room:",l),(s=this.onError)==null||s.call(this,l),l}}async leaveRoom(){var t;try{this.peerConnections.forEach((n,s)=>{n.close()}),this.peerConnections.clear(),this.localStream&&(this.localStream.getTracks().forEach(n=>n.stop()),this.localStream=null),this.stopVoiceActivityDetection(),this.roomId&&this.userId&&this.wsService.send({type:"leave_voice_room",data:{roomId:this.roomId,userId:this.userId}}),this.isJoined=!1,this.remoteUsers.clear(),this.processingOffers.clear(),this.connectionAttempts.clear(),this.stopConnectionMonitoring()}catch(n){console.error("❌ Error leaving voice room:",n),(t=this.onError)==null||t.call(this,n)}}async toggleMute(){if(!this.localStream)return!1;const t=this.localStream.getAudioTracks()[0];return t?(t.enabled=!t.enabled,this.isMuted=!t.enabled,this.isMuted):!1}setMute(t){if(!this.localStream)return;const n=this.localStream.getAudioTracks()[0];n&&(n.enabled=!t,this.isMuted=t,console.log(`🎤 Local audio ${t?"muted":"unmuted"}`))}setRemoteAudioMuted(t){this.peerConnections.forEach((n,s)=>{const l=n.getRemoteStreams()[0];l&&l.getAudioTracks().forEach(i=>{i.enabled=!t})})}async createPeerConnection(t){const n=new RTCPeerConnection({iceServers:this.iceServers});return this.localStream&&this.localStream.getTracks().forEach(s=>{n.addTrack(s,this.localStream)}),n.ontrack=s=>{var a;const[l]=s.streams;console.log("🎵 Received remote stream from:",t);const o=new Audio;o.srcObject=l,o.volume=.8,o.autoplay=!0,o.play().then(()=>{console.log("✅ Remote audio playing from:",t)}).catch(c=>{console.warn("⚠️ Audio play failed, trying user interaction:",c),document.addEventListener("click",()=>{o.play().catch(()=>{})},{once:!0})});const i=this.remoteUsers.get(t)||{id:t,isSpeaking:!1,isMuted:!1,audioLevel:0};this.remoteUsers.set(t,i),(a=this.onUserJoined)==null||a.call(this,i)},n.onicecandidate=s=>{s.candidate&&this.wsService.send({type:"webrtc_ice_candidate",data:{candidate:s.candidate,targetUserId:t,fromUserId:this.userId}})},n.onconnectionstatechange=()=>{console.log(`🔗 Connection state with ${t}: ${n.connectionState}`),n.connectionState==="connected"?(console.log(`✅ Successfully connected to ${t}`),this.processingOffers.delete(t),this.connectionAttempts.delete(t)):(n.connectionState==="failed"||n.connectionState==="disconnected")&&(console.log(`❌ Connection failed/disconnected with ${t}`),this.peerConnections.delete(t),this.processingOffers.delete(t))},n.oniceconnectionstatechange=()=>{console.log(`🧊 ICE state with ${t}: ${n.iceConnectionState}`),(n.iceConnectionState==="connected"||n.iceConnectionState==="completed")&&console.log(`🎉 ICE connection established with ${t}`)},this.peerConnections.set(t,n),n}async handleOffer(t){try{const{offer:n,fromUserId:s}=t;if(this.processingOffers.has(s)){console.log("⏭️ Already processing offer from:",s);return}const l=this.connectionAttempts.get(s)||0;if(l>=3){console.log("🛑 Too many connection attempts with:",s);return}this.processingOffers.add(s),this.connectionAttempts.set(s,l+1),console.log("📥 Received offer from:",s);const o=this.peerConnections.get(s);if(o&&o.connectionState==="connected"){console.log("✅ Connection already established with:",s),this.processingOffers.delete(s);return}o&&o.signalingState!=="closed"&&(console.log("🔄 Closing existing connection before creating new one"),o.close(),this.peerConnections.delete(s)),console.log("🔄 Creating peer connection and answer for:",s);const i=await this.createPeerConnection(s);try{await i.setRemoteDescription(n),console.log("✅ Set remote description (offer)");const a=await i.createAnswer();await i.setLocalDescription(a),console.log("✅ Created and set local description (answer)"),console.log("📤 Sending WebRTC answer to:",s),this.wsService.send({type:"webrtc_answer",data:{answer:a,targetUserId:s,fromUserId:this.userId}}),setTimeout(()=>{this.processingOffers.delete(s)},2e3)}catch(a){console.error("❌ SDP error in offer handling:",a),i.close(),this.peerConnections.delete(s),this.processingOffers.delete(s)}}catch(n){console.error("❌ Error handling offer:",n),this.processingOffers.delete(t.fromUserId)}}async handleAnswer(t){try{const{answer:n,fromUserId:s}=t;console.log("📥 Received answer from:",s);const l=this.peerConnections.get(s);if(!l){console.warn("⚠️ No peer connection found for:",s);return}if(l.signalingState==="have-local-offer")try{await l.setRemoteDescription(n),console.log("✅ Set remote description (answer) for:",s),console.log("🔗 WebRTC connection should be established with:",s),setTimeout(()=>{l.connectionState==="connected"?console.log("🎉 Connection confirmed with:",s):console.log("⏳ Waiting for connection to stabilize with:",s)},1e3)}catch(o){console.warn("⚠️ SDP error, recreating connection:",o.message),this.peerConnections.delete(s),this.processingOffers.delete(s),setTimeout(()=>{this.userId<s&&this.handleUserJoined({userId:s})},2e3)}else l.signalingState==="stable"?console.log("ℹ️ Connection already stable with:",s):(console.warn("⚠️ Peer connection not in correct state for answer:",l.signalingState),console.log("🔄 Current state:",l.signalingState,"Connection state:",l.connectionState))}catch(n){console.error("❌ Error handling answer:",n)}}async handleIceCandidate(t){try{const{candidate:n,fromUserId:s}=t,l=this.peerConnections.get(s);l&&l.remoteDescription?await l.addIceCandidate(n):l&&setTimeout(()=>this.handleIceCandidate(t),100)}catch(n){n.message.includes("ICE candidate")||console.error("❌ Error handling ICE candidate:",n)}}async handleUserJoined(t){try{const{userId:n}=t;if(n===this.userId)return;const s=this.peerConnections.get(n);if(s&&s.connectionState==="connected"){console.log("✅ Already connected to:",n);return}if(this.processingOffers.has(n)){console.log("⏭️ Already processing connection with:",n);return}if(console.log("👤 User joined voice room:",n),this.userId<n){const o=this.connectionAttempts.get(n)||0;if(o>=3){console.log("🛑 Too many offer attempts to:",n);return}this.connectionAttempts.set(n,o+1),console.log("🔄 Creating peer connection and offer for:",n);const i=await this.createPeerConnection(n),a=await i.createOffer();await i.setLocalDescription(a),console.log("📤 Sending WebRTC offer to:",n),this.wsService.send({type:"webrtc_offer",data:{offer:a,targetUserId:n,fromUserId:this.userId}})}else console.log("⏳ Waiting for offer from:",n),await this.createPeerConnection(n)}catch(n){console.error("❌ Error handling user joined:",n)}}handleUserLeft(t){var l;const{userId:n}=t;console.log("👋 User left voice room:",n);const s=this.peerConnections.get(n);s&&(s.close(),this.peerConnections.delete(n)),this.processingOffers.delete(n),this.connectionAttempts.delete(n),this.remoteUsers.delete(n),(l=this.onUserLeft)==null||l.call(this,n)}startVoiceActivityDetection(){if(!(!this.localStream||this.isMonitoringVoice))try{this.audioContext=new AudioContext;const t=this.audioContext.createMediaStreamSource(this.localStream);this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=256,t.connect(this.analyser),this.isMonitoringVoice=!0,this.monitorVoiceActivity()}catch(t){console.error("❌ Error starting voice activity detection:",t)}}monitorVoiceActivity(){if(!this.analyser||!this.isMonitoringVoice)return;const t=new Uint8Array(this.analyser.frequencyBinCount),n=()=>{if(!this.isMonitoringVoice)return;this.analyser.getByteFrequencyData(t);const s=t.reduce((d,g)=>d+g,0)/t.length,l=Math.round(s*10)/10,o=l>this.voiceActivityThreshold,i=Date.now(),a=o!==this.isSpeaking,c=i-this.lastVoiceActivitySent>this.voiceActivityDebounce;(a||c)&&this.onVoiceActivity&&this.userId&&(this.onVoiceActivity({userId:this.userId,level:l,isSpeaking:o}),this.lastVoiceActivitySent=i,this.isSpeaking=o),requestAnimationFrame(n)};n()}stopVoiceActivityDetection(){this.isMonitoringVoice=!1,this.audioContext&&(this.audioContext.close(),this.audioContext=null),this.analyser=null}get isConnected(){return this.isJoined}get mutedState(){return this.isMuted}get connectedUsers(){return Array.from(this.remoteUsers.values())}async sendOffer(t){try{if(t===this.userId)return;console.log("🔄 Creating peer connection and offer for:",t);const n=await this.createPeerConnection(t),s=await n.createOffer();await n.setLocalDescription(s),console.log("📤 Sending WebRTC offer to:",t),this.wsService.send({type:"webrtc_offer",data:{offer:s,targetUserId:t,fromUserId:this.userId}})}catch(n){console.error("❌ Error sending offer:",n)}}checkConnectionsAndRetry(){this.peerConnections.forEach((t,n)=>{(t.connectionState==="failed"||t.connectionState==="disconnected")&&(console.log("🔄 Retrying connection with:",n),this.peerConnections.delete(n),this.processingOffers.delete(n),setTimeout(()=>{this.userId<n&&this.handleUserJoined({userId:n})},1e3))})}startConnectionMonitoring(){this.connectionMonitorInterval||(this.connectionMonitorInterval=setInterval(()=>{this.checkConnectionsAndRetry()},5e3))}stopConnectionMonitoring(){this.connectionMonitorInterval&&(clearInterval(this.connectionMonitorInterval),this.connectionMonitorInterval=null)}cleanup(){this.stopConnectionMonitoring(),this.leaveRoom().catch(console.error)}}class Rp{constructor(){this.commonPhrases=["السلام عليكم","وعليكم السلام","أهلاً وسهلاً","مرحباً بكم","حياكم الله","أهلاً بك","مساء الخير","صباح الخير","تصبحون على خير","ليلة سعيدة","كيف حالكم؟","كيف الأحوال؟","إن شاء الله","ما شاء الله","بارك الله فيك","جزاك الله خيراً","الله يعطيك العافية","تسلم إيدك","الله يبارك فيك","ربي يحفظك","ممتاز!","رائع جداً","أحسنت","بالتوفيق","الله يوفقك","نعم صحيح","أوافقك الرأي","هذا صحيح","بالضبط","تماماً","من أين أنت؟","كم عمرك؟","ما اسمك؟","أين تسكن؟","ما هو عملك؟","هل أنت متزوج؟","كم طفل لديك؟","ما هوايتك؟","مع السلامة","إلى اللقاء","نراكم قريباً","الله معكم","في أمان الله","وداعاً","إلى اللقاء قريباً","هيا نلعب","من يريد اللعب؟","لعبة جميلة","أحب هذه اللعبة","فزت!","خسرت","لعبة أخرى؟","تحدي جديد","لا تحزن","كله خير","الله معك","ستتحسن الأمور","لا تيأس","كن قوياً","أنا معك","سأساعدك","شكراً لك","عفواً","لا شكر على واجب","أعتذر","آسف","لا بأس","لا مشكلة","بالعكس","أنا جائع","ما رأيكم في الطعام؟","هل تناولتم الطعام؟","طعام لذيذ","أحب هذا الطبق","وجبة شهية","الجو جميل اليوم","الطقس حار","الطقس بارد","يبدو أنه سيمطر","الشمس مشرقة","الجو معتدل"],this.recentMessages=[]}addMessage(t){this.recentMessages.unshift(t),this.recentMessages.length>50&&(this.recentMessages=this.recentMessages.slice(0,50))}getSuggestions(t){if(!t||t.length<2)return[];const n=t.toLowerCase().trim(),s=[];return this.commonPhrases.forEach(l=>{l.toLowerCase().includes(n)&&s.push(l)}),this.recentMessages.forEach(l=>{l.toLowerCase().includes(n)&&!s.includes(l)&&l.length>t.length&&s.push(l)}),s.sort((l,o)=>{const i=l.length-o.length;if(i!==0)return i;const a=this.commonPhrases.includes(l),c=this.commonPhrases.includes(o);return a&&!c?-1:!a&&c?1:0}).slice(0,5)}getQuickSuggestions(){return["السلام عليكم","كيف حالكم؟","أهلاً وسهلاً","شكراً لك","مع السلامة"]}addCustomPhrase(t){this.commonPhrases.includes(t)||this.commonPhrases.push(t)}}const Qc=new Rp,zp=({onEmojiSelect:e,onClose:t})=>{const n=y.useRef(null),[s,l]=y.useState("faces"),o={faces:{name:"الوجوه",icon:r.jsx(x0,{className:"w-4 h-4"}),emojis:["😀","😃","😄","😁","😆","😅","🤣","😂","🙂","🙃","😉","😊","😇","🥰","😍","🤩","😘","😗","😚","😙","😋","😛","😜","🤪","😝","🤑","🤗","🤭","🤫","🤔","🤐","🤨","😐","😑","😶","😏","😒","🙄","😬","🤥","😔","😪","🤤","😴","😷","🤒","🤕","🤢","🤮","🤧","🥵","🥶","🥴","😵","🤯","🤠","🥳","😎","🤓","🧐"]},hearts:{name:"القلوب",icon:r.jsx(m0,{className:"w-4 h-4"}),emojis:["❤️","🧡","💛","💚","💙","💜","🖤","🤍","🤎","💔","❣️","💕","💞","💓","💗","💖","💘","💝","💟","♥️","💋","💌","💐","🌹","🌺","🌻","🌷","🌸","💒","💍"]},gestures:{name:"الإيماءات",icon:r.jsx(Qh,{className:"w-4 h-4"}),emojis:["👍","👎","👌","✌️","🤞","🤟","🤘","🤙","👈","👉","👆","🖕","👇","☝️","👋","🤚","🖐️","✋","🖖","👏","🙌","🤲","🤝","🙏","✍️","💪","🦾","🦿","🦵","🦶"]},symbols:{name:"الرموز",icon:r.jsx(cn,{className:"w-4 h-4"}),emojis:["💯","💢","💥","💫","💦","💨","🕳️","💣","💬","👁️‍🗨️","🗨️","🗯️","💭","💤","💮","♨️","💈","🛑","⭐","🌟","✨","⚡","🔥","💎","🏆","🥇","🥈","🥉","🎖️","🏅"]},food:{name:"الطعام",icon:r.jsx(Fh,{className:"w-4 h-4"}),emojis:["🍎","🍊","🍋","🍌","🍉","🍇","🍓","🫐","🍈","🍒","🍑","🥭","🍍","🥥","🥝","🍅","🍆","🥑","🥦","🥬","☕","🍵","🧃","🥤","🍺","🍻","🥂","🍷","🥃","🍸"]},games:{name:"الألعاب",icon:r.jsx(ua,{className:"w-4 h-4"}),emojis:["🎮","🕹️","🎯","🎲","🃏","🀄","🎰","🎳","🏓","🏸","⚽","🏀","🏈","⚾","🥎","🎾","🏐","🏉","🥏","🎱"]}};return y.useEffect(()=>{const i=a=>{n.current&&!n.current.contains(a.target)&&t()};return document.addEventListener("mousedown",i),()=>document.removeEventListener("mousedown",i)},[t]),r.jsxs("div",{ref:n,onClick:i=>i.stopPropagation(),className:"absolute bottom-12 left-0 right-0 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-2xl z-50 max-w-sm",children:[r.jsxs("div",{className:"flex border-b border-gray-600 p-2 overflow-x-auto",children:[Object.entries(o).map(([i,a])=>r.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),l(i)},className:`p-2 rounded-lg transition-colors flex-shrink-0 ${s===i?"bg-purple-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,title:a.name,children:a.icon},i)),r.jsx("button",{onClick:i=>{i.preventDefault(),i.stopPropagation(),t()},className:"ml-auto p-2 text-gray-400 hover:text-white flex-shrink-0",children:"✕"})]}),r.jsx("div",{className:"p-3 max-h-48 overflow-y-auto",children:r.jsx("div",{className:"grid grid-cols-8 gap-1",children:o[s].emojis.map((i,a)=>r.jsx("button",{onClick:c=>{c.preventDefault(),c.stopPropagation(),e(i),t()},className:"w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-700 transition-colors text-lg",title:i,children:i},a))})})]})},Op=({suggestions:e,onSuggestionSelect:t,isVisible:n})=>!n||e.length===0?null:r.jsx("div",{className:"absolute bottom-full left-0 right-0 mb-1 bg-gray-800/95 backdrop-blur-md border border-gray-600 rounded-lg shadow-lg z-40 max-h-32 overflow-y-auto",children:e.map((s,l)=>r.jsx("button",{onClick:()=>t(s),className:"w-full text-left px-3 py-2 text-sm text-gray-200 hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-b-0",children:s},l))}),Up=({user:e,wsService:t})=>{var En;const[n,s]=y.useState(null),[l,o]=y.useState([]),[i,a]=y.useState(!0),[c,d]=y.useState(null),[g,w]=y.useState(!1),[b,N]=y.useState(null),[j,k]=y.useState(!1),[S,m]=y.useState(!1),[u,p]=y.useState(!1),[M,L]=y.useState(""),[R,z]=y.useState(!1),[U,D]=y.useState(null),[v,O]=y.useState(null),[X,ae]=y.useState("30"),[Pe,Me]=y.useState(!1),[Ee,pe]=y.useState(!1),[h,C]=y.useState("#ffffff"),[T,F]=y.useState([]),[P,W]=y.useState(!1),Y=[{name:"أبيض",value:"#ffffff"},{name:"أحمر",value:"#ef4444"},{name:"أزرق",value:"#3b82f6"},{name:"أخضر",value:"#10b981"},{name:"أصفر",value:"#f59e0b"},{name:"بنفسجي",value:"#8b5cf6"},{name:"وردي",value:"#ec4899"},{name:"برتقالي",value:"#f97316"},{name:"سماوي",value:"#06b6d4"},{name:"ذهبي",value:"#eab308"}],[se,me]=y.useState(null),[ge,st]=y.useState(!1),[Xt,kn]=y.useState("prompt"),[en,Rl]=y.useState(!1),Ie=y.useRef(null),bt=y.useRef(new Map),Sn=y.useRef(null),be=y.useRef(null),Cn=async()=>{try{a(!0);const[x,I]=await Promise.all([K.getVoiceRoom(),K.getVoiceRoomMessages()]);s(x);const Q=I.map(te=>({...te,sender:{...te.sender,role:te.sender.role||(te.sender.isAdmin?"admin":"member"),isAdmin:te.sender.isAdmin||!1,gender:te.sender.gender||"male"}}));o(Q);const H=x.seats.find(te=>te.user&&te.user._id===e.id);H?(console.log("✅ User is in seat:",H.seatNumber),w(!0),N(H.seatNumber),m(H.isMuted)):(console.log("❌ User is not in any seat"),w(!1),N(null));const V=x.waitingQueue.some(te=>te.user._id===e.id);k(V),d(null)}catch(x){if(console.error("Error loading voice room:",x),x.message&&x.message.includes("مطرود من الغرفة الصوتية")){d(x.message),s({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,maxUsers:100,seats:[],waitingQueue:[],listeners:[],settings:{},isActive:!1}),o([]);return}d(x.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1)}};y.useEffect(()=>(Ie.current=new _0(t),Ie.current.onRemoteStreamAdded=(x,I)=>{const Q=new Audio;Q.srcObject=I,Q.autoplay=!0,Q.muted=en,bt.current.set(x,Q)},Ie.current.onRemoteStreamRemoved=x=>{const I=bt.current.get(x);I&&(I.pause(),I.srcObject=null,bt.current.delete(x))},Ie.current.onVoiceActivity=x=>{s(I=>({...I,seats:I.seats.map(Q=>{var H;return((H=Q.user)==null?void 0:H._id)===e.id?{...Q,isSpeaking:x.isSpeaking}:Q})})),g&&t.send({type:"voice_activity",data:{userId:e.id,level:x.level,isSpeaking:x.isSpeaking}})},()=>{var x;(x=Ie.current)==null||x.cleanup()}),[t,e.id]),y.useEffect(()=>{const x=V=>{const te={...V,sender:{...V.sender,role:V.sender.role||(V.sender.isAdmin?"admin":"member"),isAdmin:V.sender.isAdmin||!1,gender:V.sender.gender||"male"}};V.sender._id!==e.id&&(o(Re=>[...Re,te]),setTimeout(()=>{var Re;(Re=Sn.current)==null||Re.scrollIntoView({behavior:"smooth"})},100))},I=V=>{Cn().then(()=>{V.action==="seat_joined"&&g&&V.userId!==e.id&&setTimeout(()=>{var te;(te=Ie.current)==null||te.sendOffer(V.userId)},1e3)})},Q=V=>{const{action:te,targetUserId:Re,adminId:cr,message:In}=V;ar(te,Re),Re===e.id&&(d(In||`تم تطبيق إجراء إداري: ${te}`),te==="kick"&&setTimeout(()=>{window.location.reload()},2e3))},H=V=>{const{userId:te,isSpeaking:Re}=V;s(cr=>({...cr,seats:cr.seats.map(In=>{var An;return((An=In.user)==null?void 0:An._id)===te?{...In,isSpeaking:Re}:In})}))};return t.onMessage("voice_room_message",x),t.onMessage("voice_room_update",I),t.onMessage("admin_action_update",Q),t.onMessage("voice_activity",H),()=>{t.offMessage("voice_room_message",x),t.offMessage("voice_room_update",I),t.offMessage("admin_action_update",Q)}},[t,g,e.id]),y.useEffect(()=>{var x;(x=Sn.current)==null||x.scrollIntoView({behavior:"smooth"})},[l]),y.useEffect(()=>{Cn(),console.log("User role:",e.role),console.log("User isAdmin:",e.isAdmin),console.log("User object:",e)},[]),y.useEffect(()=>{const x=()=>{const H=window.innerHeight<window.screen.height*.75;z(H)},I=()=>z(!0),Q=()=>z(!1);return window.addEventListener("resize",x),be.current&&(be.current.addEventListener("focus",I),be.current.addEventListener("blur",Q)),()=>{window.removeEventListener("resize",x),be.current&&(be.current.removeEventListener("focus",I),be.current.removeEventListener("blur",Q))}},[]),y.useEffect(()=>{const x=I=>{U&&D(null),Pe&&Me(!1),Ee&&pe(!1)};return document.addEventListener("click",x),()=>document.removeEventListener("click",x)},[U,Pe,Ee]),y.useEffect(()=>{const x=Q=>{if(g)return Q.preventDefault(),Q.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},I=()=>{g&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",x),window.addEventListener("unload",I),()=>{window.removeEventListener("beforeunload",x),window.removeEventListener("unload",I)}},[g,e.id]);const is=async x=>{try{const I=await K.sendVoiceRoomMessage(x),Q={_id:I.messageData._id,sender:{_id:e.id,username:e.username,role:e.role,isAdmin:e.isAdmin,gender:e.gender},content:x,timestamp:new Date().toISOString(),messageType:"text",textColor:h};o(H=>[...H,Q]),t.send({type:"voice_room_message",data:{...I.messageData,textColor:h}})}catch(I){console.error("Error sending message:",I),d(I.message||"خطأ في إرسال الرسالة")}},ir=async()=>{try{p(!0),await K.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}})}catch(x){console.error("Error requesting mic:",x),d(x.message||"خطأ في طلب المايك")}finally{p(!1)}},mt=async x=>{try{if(p(!0),await K.joinVoiceSeat(x),w(!0),N(x),m(!1),Ie.current&&(e!=null&&e.id)){console.log("🎤 Starting WebRTC voice chat for user:",e.username);const I=`voice-room-${(n==null?void 0:n.id)||"default"}`;await Ie.current.joinRoom(I,e.id),console.log("✅ WebRTC voice chat started successfully")}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",x.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:x}})}catch(I){console.error("Error joining seat:",I),d(I.message||"خطأ في الانضمام للمقعد")}finally{p(!1)}},zl=async()=>{var x;try{await K.leaveVoiceSeat(),w(!1),N(null),m(!1),Ie.current&&Ie.current.leaveRoom(),localStorage.removeItem("isInVoiceRoom"),localStorage.removeItem("voiceRoomSeat"),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:b}})}catch(I){console.error("Error leaving seat:",I),(x=I.message)!=null&&x.includes("لست في أي مقعد")||d(I.message||"خطأ في مغادرة المقعد")}},Mn=async()=>{try{if(!g){d("يجب أن تكون في مقعد لاستخدام المايك");return}if(!Ie.current){d("خدمة الصوت غير متاحة - جاري إعادة الاتصال..."),await initializeWebRTC();return}const x=!S;Ie.current.setMute(x),m(x);try{await K.toggleMute(x)}catch(I){console.warn("Failed to update server mute state:",I)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:x}})}catch(x){console.error("Error toggling mute:",x),d("خطأ في تبديل كتم المايك"),m(!S)}},as=()=>{try{const x=!en;Rl(x),bt.current.forEach(I=>{I.muted=x}),Ie.current&&Ie.current.setRemoteAudioMuted(x),localStorage.setItem("soundMuted",x.toString())}catch(x){console.error("Error toggling sound:",x),d("خطأ في تبديل كتم الصوت")}},fe=x=>{const I=x.target.value;if(L(I),I.length>=2){const Q=Qc.getSuggestions(I);F(Q),W(Q.length>0)}else W(!1)},ft=x=>{var I;L(x),W(!1),(I=be.current)==null||I.focus()},tn=x=>{var I;L(Q=>Q+x),Me(!1),(I=be.current)==null||I.focus()},cs=async x=>{if(x.preventDefault(),!M.trim())return;const I=M.trim();L(""),W(!1),Qc.addMessage(I);try{await is(I)}catch{L(I)}},Ke=async(x,I,Q)=>{try{let H;switch(x){case"kick":H=await K.kickUserFromVoiceRoom(I,Q);break;case"mute":H=await K.muteUserInVoiceRoom(I);break;case"unmute":H=await K.unmuteUserInVoiceRoom(I);break;case"removeSeat":H=await K.removeUserFromSeat(I);break;case"removeQueue":H=await K.removeUserFromQueue(I);break;case"banChat":H=await K.banUserFromChat(I);break;case"unbanChat":H=await K.unbanUserFromChat(I);break}D(null),O(null),ar(x,I),t.send({type:"admin_action_update",data:{action:x,targetUserId:I,adminId:e.id,duration:Q,message:H==null?void 0:H.message}})}catch(H){console.error("Error performing admin action:",H),d(H.message||"خطأ في تنفيذ الإجراء الإداري")}},ar=(x,I)=>{s(Q=>{if(!Q)return Q;const H={...Q};switch(x){case"kick":case"removeSeat":H.seats=H.seats.map(V=>V.user&&V.user._id===I?{...V,user:null,isMuted:!1}:V),H.waitingQueue=H.waitingQueue.filter(V=>V.user._id!==I);break;case"removeQueue":H.waitingQueue=H.waitingQueue.filter(V=>V.user._id!==I);break;case"mute":H.seats=H.seats.map(V=>V.user&&V.user._id===I?{...V,isMuted:!0}:V);break;case"unmute":H.seats=H.seats.map(V=>V.user&&V.user._id===I?{...V,isMuted:!1}:V);break;case"banChat":H.seats=H.seats.map(V=>V.user&&V.user._id===I?{...V,user:{...V.user,isChatBanned:!0}}:V),H.waitingQueue=H.waitingQueue.map(V=>V.user._id===I?{...V,user:{...V.user,isChatBanned:!0}}:V);break;case"unbanChat":H.seats=H.seats.map(V=>V.user&&V.user._id===I?{...V,user:{...V.user,isChatBanned:!1}}:V),H.waitingQueue=H.waitingQueue.map(V=>V.user._id===I?{...V,user:{...V.user,isChatBanned:!1}}:V);break}return H})},jt=async()=>{const x=parseInt(X);await Ke("kick",v,x)},ht=x=>{me(x),st(!0)},ds=()=>{me(null),st(!1)};y.useEffect(()=>{const x=I=>{I.key==="Escape"&&ge&&ds()};return document.addEventListener("keydown",x),()=>{document.removeEventListener("keydown",x)}},[ge]),y.useEffect(()=>{(async()=>{try{const Q=await navigator.permissions.query({name:"microphone"});kn(Q.state),Q.addEventListener("change",()=>{kn(Q.state)})}catch{console.log("Permission API not supported")}})(),localStorage.getItem("soundMuted")==="true"&&Rl(!0)},[]);const us=x=>new Date(x).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1});if(i)return r.jsx("div",{className:"flex items-center justify-center h-full",children:r.jsxs("div",{className:"text-center",children:[r.jsx(hn,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),r.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})});if(c){const x=c.includes("مطرود من الغرفة الصوتية");return r.jsx("div",{className:"p-4",children:r.jsxs("div",{className:`border rounded-lg p-6 text-center max-w-md mx-auto ${x?"bg-orange-900/20 border-orange-500/30":"bg-red-900/20 border-red-500/30"}`,children:[r.jsx(d0,{className:`w-12 h-12 mx-auto mb-4 ${x?"text-orange-400":"text-red-400"}`}),x?r.jsxs(r.Fragment,{children:[r.jsx("h3",{className:"text-orange-400 font-bold text-lg mb-2",children:"تم طردك من الغرفة الصوتية"}),r.jsx("p",{className:"text-orange-300 mb-4 text-sm leading-relaxed",children:c}),r.jsx("p",{className:"text-gray-400 text-xs",children:"سيتم السماح لك بالدخول مرة أخرى بعد انتهاء المدة المحددة"})]}):r.jsxs(r.Fragment,{children:[r.jsx("p",{className:"text-red-400 mb-4",children:c}),r.jsx("button",{onClick:Cn,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-white",children:"إعادة المحاولة"})]})]})})}return n?r.jsxs("div",{className:"h-screen flex flex-col bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 text-white overflow-hidden",children:[r.jsxs("div",{className:"bg-black/30 backdrop-blur-sm border-b border-white/10 p-2 flex-shrink-0",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:onBack,className:"p-1.5 hover:bg-white/10 rounded-lg transition-colors",children:r.jsx(ArrowLeft,{className:"w-4 h-4"})}),r.jsxs("div",{children:[r.jsxs("h1",{className:"text-sm font-bold flex items-center gap-1",children:[r.jsx(ts,{className:"w-3 h-3 text-purple-400"}),"INFINITY ROOM"]}),r.jsx("p",{className:"text-xs text-gray-300",children:"غرفة صوتية للمحادثة"})]})]}),r.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[r.jsx(bn,{className:"w-3 h-3 text-gray-400"}),r.jsxs("span",{className:"text-gray-300",children:[((En=n.seats)==null?void 0:En.filter(x=>x.user).length)||0,"/5"]}),(e.role==="admin"||e.isAdmin)&&r.jsx("span",{className:"bg-red-600 text-white px-1.5 py-0.5 rounded text-xs ml-1",children:"ADMIN"})]})]}),g&&r.jsxs("div",{className:"bg-black/20 backdrop-blur-sm rounded-lg p-2 mt-2",children:[r.jsxs("div",{className:"flex items-center gap-1 mb-2",children:[r.jsx("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"}),r.jsxs("span",{className:"text-xs text-green-400 font-medium",children:["مقعد ",b]})]}),r.jsxs("div",{className:"flex items-center gap-1",children:[r.jsxs("button",{onClick:Mn,className:`flex-1 py-1.5 px-2 rounded-md transition-colors flex items-center justify-center gap-1 text-xs font-medium ${S?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,children:[S?r.jsx(Xr,{className:"w-3 h-3"}):r.jsx(yt,{className:"w-3 h-3"}),r.jsx("span",{children:S?"إلغاء كتم":"كتم"})]}),r.jsxs("button",{onClick:as,className:`flex-1 py-1.5 px-2 rounded-md transition-colors flex items-center justify-center gap-1 text-xs font-medium ${en?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"}`,children:[en?r.jsx(mo,{className:"w-3 h-3"}):r.jsx(ts,{className:"w-3 h-3"}),r.jsx("span",{children:en?"تشغيل":"صامت"})]}),r.jsx("button",{onClick:zl,className:"px-3 py-1.5 bg-red-600 hover:bg-red-700 rounded-md text-white transition-colors text-xs font-medium",children:"مغادرة"})]})]}),r.jsxs("div",{className:"flex items-center gap-3 p-2 bg-gray-800/30 rounded-lg mb-3",children:[r.jsx("div",{className:"flex items-center gap-2",children:r.jsx("div",{className:`w-2 h-2 rounded-full ${Xt==="granted"?"bg-green-500":Xt==="denied"?"bg-red-500":"bg-yellow-500"}`})}),Xt==="denied"&&r.jsx("span",{className:"text-xs text-red-400",children:"مرفوض"})]})]}),r.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[r.jsxs("div",{className:"p-2 border-b border-gray-700/50 flex-shrink-0",children:[r.jsx("div",{className:"flex justify-center gap-1.5 mb-1 overflow-x-auto px-1",children:n.seats.map(x=>r.jsx("div",{className:"flex flex-col items-center flex-shrink-0",children:x.user?r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:`relative w-12 h-12 rounded-full p-0.5 ${x.isSpeaking&&!x.isMuted?"bg-gradient-to-r from-green-400 to-green-500 shadow-md shadow-green-500/50 animate-pulse":x.user._id===e.id?"bg-gradient-to-r from-green-500 to-green-600":x.user.role==="admin"||x.user.isAdmin?"bg-gradient-to-r from-red-500 to-red-600 shadow-md shadow-red-500/50":"bg-gradient-to-r from-blue-500 to-purple-600"} shadow-md`,children:[r.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:x.user.profileImage?r.jsx("img",{src:x.user.profileImage,alt:x.user.username,className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-lg",children:x.user.username.charAt(0).toUpperCase()})})}),r.jsx("div",{className:`absolute -bottom-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center shadow-lg ${x.isMuted?"bg-red-600":x.isSpeaking?"bg-green-500 animate-pulse shadow-green-500/50":"bg-green-600"}`,children:x.isMuted?r.jsx(Xr,{className:"w-3 h-3 text-white"}):r.jsx(yt,{className:`w-3 h-3 text-white ${x.isSpeaking?"animate-pulse":""}`})}),x.isSpeaking&&!x.isMuted&&r.jsxs("div",{className:"absolute inset-0 rounded-full",children:[r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),r.jsxs("div",{className:"text-center mt-1 relative",children:[r.jsxs("div",{className:"flex items-center justify-center gap-1",children:[r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("h3",{className:"font-medium text-white text-xs mb-1 truncate max-w-12",children:x.user.username}),r.jsxs("div",{className:"flex gap-1",children:[x.isMuted&&r.jsx("div",{className:"bg-red-600 rounded-full p-1",title:"مكتوم",children:r.jsx(mo,{className:"w-2 h-2 text-white"})}),x.user.isChatBanned&&r.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:r.jsx(_s,{className:"w-2 h-2 text-white"})})]})]}),(e.role==="admin"||e.isAdmin)&&x.user._id!==e.id&&r.jsx("button",{onClick:I=>{I.stopPropagation(),console.log("Admin menu clicked for user:",x.user.username),D(U===x.user._id?null:x.user._id)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:r.jsx(zc,{className:"w-4 h-4"})})]}),U===x.user._id&&(e.role==="admin"||e.isAdmin)&&r.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:r.jsxs("div",{className:"flex flex-col gap-1",children:[r.jsxs("button",{onClick:()=>Ke("removeSeat",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[r.jsx(Lh,{className:"w-3 h-3"}),"إنزال"]}),x.isMuted?r.jsxs("button",{onClick:()=>Ke("unmute",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Jh,{className:"w-3 h-3"}),"إلغاء كتم"]}):r.jsxs("button",{onClick:()=>Ke("mute",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-yellow-400 hover:bg-yellow-900/30 rounded transition-colors",children:[r.jsx(mo,{className:"w-3 h-3"}),"كتم"]}),x.user.isChatBanned?r.jsxs("button",{onClick:()=>Ke("unbanChat",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Oc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):r.jsxs("button",{onClick:()=>Ke("banChat",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[r.jsx(_s,{className:"w-3 h-3"}),"منع الكتابة"]}),r.jsxs("button",{onClick:()=>{D(null),O(x.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[r.jsx(pi,{className:"w-3 h-3"}),"طرد"]})]})})]})]}):r.jsx("div",{className:"flex flex-col items-center",children:r.jsx("button",{onClick:()=>!g&&!j?mt(x.seatNumber):null,disabled:u||g||j,className:"relative w-14 h-14 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:r.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center",children:r.jsx(Pl,{className:"w-5 h-5 text-gray-400"})})})})},x.seatNumber))}),!g&&!j&&n.seats.every(x=>x.user)&&r.jsxs("button",{onClick:ir,disabled:u,className:"w-full py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 rounded-lg text-white font-medium transition-colors flex items-center justify-center gap-2 mb-4",children:[r.jsx(yt,{className:"w-4 h-4"}),u?"جاري الطلب...":"طلب المايك"]}),n.waitingQueue.length>0&&r.jsxs("div",{className:"bg-yellow-900/20 rounded-lg p-2 border border-yellow-500/20",children:[r.jsxs("h3",{className:"text-sm font-bold text-white mb-2 flex items-center gap-2",children:[r.jsx(mn,{className:"w-4 h-4 text-yellow-400"}),"قائمة الانتظار (",n.waitingQueue.length,")"]}),r.jsx("div",{className:"space-y-2",children:n.waitingQueue.map((x,I)=>r.jsxs("div",{className:"flex items-center gap-2 p-2 bg-gray-800/50 rounded text-sm relative",children:[r.jsx("div",{className:"w-6 h-6 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-xs",children:I+1}),r.jsxs("div",{className:"flex items-center gap-2 flex-1",children:[r.jsx("span",{className:"text-white",children:x.user.username}),r.jsx("div",{className:"flex gap-1",children:x.user.isChatBanned&&r.jsx("div",{className:"bg-orange-600 rounded-full p-1",title:"محظور من الكتابة",children:r.jsx(_s,{className:"w-2 h-2 text-white"})})})]}),x.user._id===e.id&&r.jsx("span",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"}),(e.role==="admin"||e.isAdmin)&&x.user._id!==e.id&&r.jsx("button",{onClick:Q=>{Q.stopPropagation(),console.log("Admin menu clicked for queue user:",x.user.username),D(U===`queue_${x.user._id}`?null:`queue_${x.user._id}`)},className:"text-red-500 hover:text-red-300 transition-colors bg-gray-700 rounded-full p-1",title:"إجراءات الإدارة",children:r.jsx(zc,{className:"w-4 h-4"})}),U===`queue_${x.user._id}`&&(e.role==="admin"||e.isAdmin)&&r.jsx("div",{className:"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gray-800 border-2 border-red-500 rounded-lg shadow-2xl z-[9999] p-3 min-w-40",children:r.jsxs("div",{className:"flex flex-col gap-1",children:[r.jsxs("button",{onClick:()=>Ke("removeQueue",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-red-400 hover:bg-red-900/30 rounded transition-colors",children:[r.jsx(v0,{className:"w-3 h-3"}),"إزالة"]}),x.user.isChatBanned?r.jsxs("button",{onClick:()=>Ke("unbanChat",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-green-400 hover:bg-green-900/30 rounded transition-colors",children:[r.jsx(Oc,{className:"w-3 h-3"}),"إلغاء منع الكتابة"]}):r.jsxs("button",{onClick:()=>Ke("banChat",x.user._id),className:"flex items-center gap-2 px-2 py-1 text-xs text-orange-400 hover:bg-orange-900/30 rounded transition-colors",children:[r.jsx(_s,{className:"w-3 h-3"}),"منع الكتابة"]}),r.jsxs("button",{onClick:()=>{D(null),O(x.user._id)},className:"flex items-center gap-2 px-2 py-1 text-xs text-red-500 hover:bg-red-900/50 rounded transition-colors",children:[r.jsx(pi,{className:"w-3 h-3"}),"طرد"]})]})})]},x.user._id))})]})]}),r.jsxs("div",{className:"h-96 flex flex-col",children:[r.jsx("div",{className:"flex-1 overflow-y-auto p-2",children:r.jsxs("div",{className:"flex flex-col space-y-1",children:[l.length===0?r.jsxs("div",{className:"text-center text-gray-400 py-4",children:[r.jsx(fn,{className:"w-6 h-6 mx-auto mb-2 opacity-50"}),r.jsx("p",{className:"text-xs",children:"لا توجد رسائل بعد"})]}):l.map(x=>{const I=H=>H.gender==="female"?"text-pink-400":H.gender==="male"?"text-blue-400":["فاطمة","عائشة","خديجة","زينب","مريم","سارة","نور","هند","ليلى","أمل","رنا","دانا","لينا","ريم","نادية","سلمى","ياسمين","روان","جنى","تالا"].some(Re=>H.username.includes(Re))?"text-pink-400":"text-blue-400",Q=()=>x.messageType==="system"?"bg-blue-900/30 border border-blue-500/30 text-blue-200 ml-auto max-w-[85%]":x.sender.role==="admin"||x.sender.isAdmin?"bg-red-900/60 border-2 border-red-400/50 text-white ml-auto max-w-[75%] shadow-xl shadow-red-500/30 ring-1 ring-red-400/20":x.sender._id===e.id?"bg-purple-900/50 border border-purple-500/30 text-white ml-auto max-w-[75%]":"bg-gray-800/50 border border-gray-600/30 text-gray-200 ml-auto max-w-[75%]";return r.jsxs("div",{className:`px-3 py-1.5 rounded-xl text-sm ${Q()}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-0.5 gap-2",children:[r.jsxs("span",{className:`font-medium text-xs flex-shrink-0 ${x.sender.role==="admin"||x.sender.isAdmin?"text-red-300 font-bold":I(x.sender)}`,children:[x.sender.role==="admin"||x.sender.isAdmin?"👑 ":"",x.sender.username]}),r.jsx("span",{className:"text-xs opacity-60 flex-shrink-0",children:us(x.timestamp)})]}),r.jsx("div",{className:"text-sm leading-snug",style:{color:x.textColor||"#ffffff"},children:x.content})]},x._id)}),r.jsx("div",{ref:Sn})]})}),r.jsx("div",{className:"px-4 py-2 border-t border-gray-700/50 bg-gray-900/30",children:r.jsxs("div",{className:"flex justify-center gap-4",children:[r.jsxs("button",{onClick:()=>ht("/speed-challenge.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"تحدي السرعة",children:[r.jsx(b0,{className:"w-6 h-6 text-yellow-400 group-hover:text-yellow-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"سرعة"})]}),r.jsxs("button",{onClick:()=>ht("/game8.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"صناديق الحظ",children:[r.jsx(y0,{className:"w-6 h-6 text-green-400 group-hover:text-green-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"صناديق"})]}),r.jsxs("button",{onClick:()=>ht("/mind-puzzles.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"ألغاز العقل",children:[r.jsx(g0,{className:"w-6 h-6 text-blue-400 group-hover:text-blue-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ألغاز"})]}),r.jsxs("button",{onClick:()=>ht("/fruit-catching.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"قطف الفواكه",children:[r.jsx(i0,{className:"w-6 h-6 text-red-400 group-hover:text-red-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"فواكه"})]}),r.jsxs("button",{onClick:()=>ht("/memory-match.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الذاكرة",children:[r.jsx(a0,{className:"w-6 h-6 text-purple-400 group-hover:text-purple-300"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"ذاكرة"})]}),r.jsxs("button",{onClick:()=>ht("/forest-game.html"),className:"flex flex-col items-center gap-1 p-2 rounded-lg hover:bg-gray-700/50 transition-colors group",title:"لعبة الغابة",children:[r.jsx(w0,{className:"w-6 h-6 text-green-600 group-hover:text-green-500"}),r.jsx("span",{className:"text-xs text-gray-400 group-hover:text-gray-300",children:"غابة"})]})]})}),r.jsx("div",{className:"p-4 mb-4 border-t border-gray-700/50 bg-gray-900/50",children:r.jsxs("div",{className:"relative",children:[r.jsx(Op,{suggestions:T,onSuggestionSelect:ft,isVisible:P}),Pe&&r.jsx(zp,{onEmojiSelect:tn,onClose:()=>Me(!1)}),Ee&&r.jsxs("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-gray-800/95 backdrop-blur-sm border border-gray-600/50 rounded-lg p-3 shadow-xl",children:[r.jsx("div",{className:"text-xs text-gray-300 mb-2 font-medium",children:"اختر لون النص:"}),r.jsx("div",{className:"grid grid-cols-5 gap-2",children:Y.map(x=>r.jsx("button",{type:"button",onClick:I=>{I.preventDefault(),I.stopPropagation(),C(x.value),pe(!1)},className:`w-8 h-8 rounded-full border-2 transition-all hover:scale-110 ${h===x.value?"border-white shadow-lg":"border-gray-500 hover:border-gray-300"}`,style:{backgroundColor:x.value},title:x.name,children:h===x.value&&r.jsx("div",{className:"w-full h-full rounded-full flex items-center justify-center",children:r.jsx("div",{className:"w-2 h-2 bg-black rounded-full opacity-50"})})},x.value))})]}),r.jsxs("form",{onSubmit:cs,className:"flex gap-2",children:[r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("input",{ref:be,type:"text",value:M,onChange:fe,placeholder:"اكتب رسالتك...",maxLength:500,style:{color:h},className:"w-full px-3 py-2 pr-16 bg-gray-800/50 border border-gray-600/50 rounded-lg placeholder-gray-400 focus:outline-none focus:border-purple-500/50 text-sm",onFocus:()=>W(T.length>0),onBlur:()=>setTimeout(()=>W(!1),200)}),r.jsx("button",{type:"button",onClick:x=>{x.preventDefault(),x.stopPropagation(),pe(!Ee),Me(!1)},className:"absolute left-8 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-400 transition-colors",children:r.jsx("div",{className:"w-4 h-4 rounded-full border-2 border-gray-400",style:{backgroundColor:h}})}),r.jsx("button",{type:"button",onClick:x=>{x.preventDefault(),x.stopPropagation(),Me(!Pe),pe(!1)},className:"absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-yellow-400 transition-colors",children:r.jsx(x0,{className:"w-4 h-4"})})]}),r.jsx("button",{type:"submit",disabled:!M.trim(),className:"px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-lg text-white transition-colors",children:r.jsx(Pr,{className:"w-4 h-4"})}),!j&&r.jsx("button",{type:"button",onClick:ir,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors",children:r.jsx(yt,{className:"w-4 h-4"})})]})]})})]})]}),v&&r.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:r.jsxs("div",{className:"bg-gray-800 rounded-lg border border-red-500 p-6 w-full max-w-sm",children:[r.jsx("h3",{className:"text-white font-bold text-lg mb-4 text-center",children:"اختر مدة الطرد"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-gray-300 text-sm mb-2",children:"المدة:"}),r.jsxs("select",{value:X,onChange:x=>ae(x.target.value),className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500",children:[r.jsx("option",{value:"30",children:"30 دقيقة"}),r.jsx("option",{value:"60",children:"ساعة واحدة"}),r.jsx("option",{value:"180",children:"3 ساعات"}),r.jsx("option",{value:"360",children:"6 ساعات"}),r.jsx("option",{value:"720",children:"12 ساعة"}),r.jsx("option",{value:"1440",children:"يوم واحد"}),r.jsx("option",{value:"4320",children:"3 أيام"}),r.jsx("option",{value:"10080",children:"أسبوع واحد"}),r.jsx("option",{value:"43200",children:"شهر واحد"}),r.jsx("option",{value:"525600",children:"سنة واحدة"})]})]}),r.jsxs("div",{className:"flex gap-3",children:[r.jsx("button",{onClick:()=>O(null),className:"flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg transition-colors",children:"إلغاء"}),r.jsx("button",{onClick:jt,className:"flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors",children:"طرد"})]})]})]})}),ge&&se&&r.jsxs("div",{className:"absolute inset-0 bg-gray-900/95 backdrop-blur-sm z-50 flex flex-col",children:[r.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-800/90 border-b border-gray-700",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),r.jsx("span",{className:"text-white font-medium",children:"اللعبة نشطة"})]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:()=>{se&&window.open(se,"_blank")},className:"px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors",children:"فتح في نافذة جديدة"}),r.jsx("button",{onClick:ds,className:"p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors",title:"إغلاق اللعبة",children:r.jsx(ma,{className:"w-5 h-5"})})]})]}),r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("iframe",{src:se,className:"w-full h-full border-0",title:"لعبة مدمجة",allow:"fullscreen; autoplay; microphone; camera",sandbox:"allow-scripts allow-same-origin allow-forms allow-popups allow-modals"}),r.jsx("div",{className:"absolute inset-0 bg-gray-900 flex items-center justify-center pointer-events-none opacity-0 transition-opacity duration-300",id:"game-loading",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-white",children:"جاري تحميل اللعبة..."})]})})]}),r.jsx("div",{className:"p-3 bg-gray-800/90 border-t border-gray-700",children:r.jsxs("div",{className:"flex items-center justify-between text-sm",children:[r.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[r.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),r.jsx("span",{children:"يمكنك الاستمرار في الدردشة أثناء اللعب"})]}),r.jsx("div",{className:"text-gray-500",children:"اضغط ESC للخروج السريع"})]})})]})]}):r.jsx("div",{className:"p-4 text-center text-gray-400",children:r.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},$p=({userData:e,onLogout:t,onUpdateProfile:n,wsService:s})=>{const[l,o]=y.useState("games"),[i,a]=y.useState(e),[c,d]=y.useState(["games"]);y.useEffect(()=>{e&&(console.log("🔄 MobileDashboard: userData updated from parent:",e),a(e))},[e]);const g=S=>{console.log("🔄 MobileDashboard: Updating profile data:",S);const m={...i,...S};a(m),n&&n(m)};y.useEffect(()=>{a(e)},[e]);const w=S=>{console.log("🔄 Navigating to tab:",S),S!==l&&requestAnimationFrame(()=>{d(m=>[...m,S]),o(S),console.log("✅ Tab changed to:",S)})},b=()=>{if(c.length>1){const S=[...c];S.pop();const m=S[S.length-1];d(S),o(m)}},N=c.length>1,j=()=>{switch(l){case"games":return"مركز الألعاب";case"leaderboard":return"لوحة المتصدرين";case"voice":return"الغرفة الصوتية";case"profile":return"الملف الشخصي";case"admin":return"لوحة الإدارة";default:return"INFINITY BOX"}},k=()=>{switch(l){case"games":return r.jsx(Ws,{setActiveTab:w});case"leaderboard":return r.jsx(Fp,{});case"voice":return s?r.jsx(Up,{user:i,wsService:s}):r.jsx("div",{className:"p-4 text-center text-red-400",children:"خدمة WebSocket غير متاحة"});case"profile":return r.jsx(Pp,{userData:i,onUpdateProfile:g,onLogout:t,isOwner:!0});case"admin":return e!=null&&e.isAdmin?r.jsx(gi,{userData:e,onLogout:t}):r.jsx(Ws,{setActiveTab:w});default:return r.jsx(Ws,{setActiveTab:w})}};return l==="admin"&&(e!=null&&e.isAdmin)?r.jsx(gi,{userData:e,onLogout:t}):r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-950 via-purple-950 to-slate-900 text-white flex flex-col relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute -top-24 -left-24 w-72 h-72 bg-green-800 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"}),r.jsx("div",{className:"absolute -bottom-24 -right-24 w-72 h-72 bg-red-900 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"}),r.jsx("div",{className:"absolute inset-0 opacity-20",children:r.jsx("div",{className:"grid grid-cols-16 gap-6 h-full w-full p-4",children:Array.from({length:80}).map((S,m)=>r.jsx("div",{className:`${m%4===0?"w-1 h-1":"w-0.5 h-0.5"} bg-white rounded-full animate-pulse`,style:{animationDelay:`${m*150}ms`,animationDuration:`${2+m%3}s`}},m))})}),r.jsx("div",{className:"absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-yellow-200 to-yellow-100 rounded-full opacity-30 blur-sm"}),r.jsx("div",{className:"absolute inset-0 flex items-center justify-center opacity-20",children:r.jsx("div",{className:"text-[18rem] md:text-[24rem] font-black text-cyan-500 animate-spin",style:{animationDuration:"40s"},children:"∞"})})]}),r.jsxs("div",{className:"relative z-10 flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-b border-purple-400/30 sticky top-0 z-40",children:[r.jsxs("div",{className:"flex items-center gap-3",children:[N&&r.jsx("button",{onClick:b,className:"p-2 rounded-lg hover:bg-slate-700/50 transition-colors",children:r.jsx(c0,{className:"w-5 h-5 text-white"})}),r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:(i==null?void 0:i.profileImage)||"/images/default-avatar.png",alt:i==null?void 0:i.username,className:"w-9 h-9 rounded-full border-2 border-white/30 object-cover",onError:S=>{S.currentTarget.src="/images/default-avatar.png"}}),r.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),r.jsx("div",{children:r.jsx("h1",{className:"text-lg font-bold bg-gradient-to-r from-yellow-300 to-blue-300 bg-clip-text text-transparent",children:l==="games"?(i==null?void 0:i.username)||"اللاعب":j()})})]}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[r.jsx(Tl,{className:"w-5 h-5 text-yellow-300"}),r.jsx("span",{className:"text-yellow-300 text-sm font-bold",children:(i==null?void 0:i.goldCoins)||0})]}),r.jsxs("div",{className:"flex items-center gap-2 bg-slate-700/50 rounded-lg px-3 py-1",children:[r.jsx(cn,{className:"w-5 h-5 text-emerald-300"}),r.jsx("span",{className:"text-emerald-300 text-sm font-bold",children:(i==null?void 0:i.pearls)||0})]}),r.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-red-600/20 transition-colors",children:r.jsx(h0,{className:"w-5 h-5 text-red-400"})})]})]}),r.jsx("div",{className:"relative z-10 flex-1 overflow-hidden",children:k()}),r.jsx("div",{className:"relative z-10 bg-gradient-to-r from-blue-900/90 via-purple-900/90 to-slate-800/90 backdrop-blur-sm border-t border-purple-400/30 px-2 py-2 sticky bottom-0 z-40",children:r.jsxs("div",{className:"flex items-center justify-around",children:[r.jsxs("button",{onClick:()=>w("games"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="games"?"bg-cyan-700/40 text-cyan-300":"text-gray-400 hover:text-cyan-200 hover:bg-slate-700/50"}`,children:[r.jsx(ua,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الألعاب"})]}),r.jsxs("button",{onClick:()=>w("voice"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="voice"?"bg-purple-700/40 text-purple-300":"text-gray-400 hover:text-purple-200 hover:bg-slate-700/50"}`,children:[r.jsx(ts,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الغرفة الصوتية"})]}),r.jsxs("button",{onClick:()=>w("leaderboard"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="leaderboard"?"bg-amber-700/40 text-amber-300":"text-gray-400 hover:text-amber-200 hover:bg-slate-700/50"}`,children:[r.jsx(es,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"المتصدرين"})]}),r.jsxs("button",{onClick:()=>w("profile"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="profile"?"bg-emerald-700/40 text-emerald-300":"text-gray-400 hover:text-emerald-200 hover:bg-slate-700/50"}`,children:[r.jsx(pn,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الملف الشخصي"})]}),(e==null?void 0:e.isAdmin)&&r.jsxs("button",{onClick:()=>w("admin"),className:`flex flex-col items-center gap-1 p-2 rounded-xl transition-all duration-200 ${l==="admin"?"bg-rose-700/40 text-rose-300":"text-gray-400 hover:text-rose-200 hover:bg-slate-700/50"}`,children:[r.jsx(Tr,{className:"w-6 h-6"}),r.jsx("span",{className:"text-[11px] font-medium",children:"الإدارة"})]})]})})]})},Fp=()=>r.jsx("div",{className:"p-4 h-full overflow-y-auto",children:r.jsxs("div",{className:"text-center py-20",children:[r.jsx(es,{className:"w-16 h-16 text-yellow-400 mx-auto mb-4"}),r.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"لوحة المتصدرين"}),r.jsx("p",{className:"text-gray-400",children:"قريباً..."})]})}),Bp=({seats:e,waitingQueue:t,currentUser:n,isInSeat:s,currentSeatNumber:l,isInWaitingQueue:o,isConnecting:i,onJoinSeat:a,onRequestMic:c,onCancelMicRequest:d})=>{const g=j=>{const k=new Date,S=new Date(j),m=k.getTime()-S.getTime(),u=Math.floor(m/(1e3*60));return u<1?"الآن":u<60?`${u} دقيقة`:`${Math.floor(u/60)} ساعة`},w=()=>{const j=t.findIndex(k=>k.user._id===n.id);return j>=0?j+1:0},b=e.filter(j=>!j.user),N=b.length>0&&!s&&!o;return r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-purple-900/30 rounded-xl p-6 border border-purple-500/20",children:[r.jsxs("h2",{className:"text-xl font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(ts,{className:"w-6 h-6 text-purple-400"}),"المقاعد الصوتية"]}),r.jsx("div",{className:"flex flex-wrap justify-center gap-6",children:e.map(j=>r.jsx("div",{className:"flex flex-col items-center",children:j.user?r.jsxs("div",{className:"relative",children:[r.jsxs("div",{className:`relative w-20 h-20 rounded-full p-1 transition-all duration-300 shadow-lg ${j.user._id===n.id?"bg-gradient-to-r from-green-500 to-green-600 shadow-green-500/30":"bg-gradient-to-r from-blue-500 to-purple-600 shadow-blue-500/30"}`,children:[r.jsx("div",{className:"w-full h-full rounded-full overflow-hidden bg-gray-800",children:j.user.profileImage?r.jsx("img",{src:j.user.profileImage,alt:j.user.username,className:"w-full h-full object-cover"}):r.jsx("div",{className:"w-full h-full bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-xl",children:j.user.username.charAt(0).toUpperCase()})})}),r.jsx("div",{className:`absolute -bottom-1 -right-1 w-7 h-7 rounded-full flex items-center justify-center shadow-lg transition-all duration-300 ${j.isMuted?"bg-red-600":j.isSpeaking?"bg-green-600 animate-pulse shadow-green-500/50":"bg-gray-600"}`,children:j.isMuted?r.jsx(Xr,{className:"w-4 h-4 text-white"}):r.jsx(yt,{className:"w-4 h-4 text-white"})}),j.isSpeaking&&!j.isMuted&&r.jsxs("div",{className:"absolute inset-0 rounded-full",children:[r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping"}),r.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-green-400 animate-ping",style:{animationDelay:"0.5s"}})]})]}),r.jsxs("div",{className:"text-center mt-3 max-w-20",children:[r.jsx("h3",{className:"font-semibold text-white text-sm mb-1 truncate",children:j.user.username}),r.jsxs("p",{className:"text-xs text-gray-400 mb-2",children:["#",j.user.playerId]}),j.joinedAt&&r.jsxs("div",{className:"flex items-center justify-center gap-1 text-xs text-gray-400",children:[r.jsx(mn,{className:"w-3 h-3"}),r.jsx("span",{children:g(j.joinedAt)})]})]})]}):r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("button",{onClick:()=>N?a(j.seatNumber):null,disabled:i||!N,className:"relative w-20 h-20 rounded-full p-1 bg-gradient-to-r from-gray-600 to-gray-700 shadow-lg hover:from-purple-600 hover:to-purple-700 disabled:hover:from-gray-600 disabled:hover:to-gray-700 transition-all duration-300 disabled:cursor-not-allowed",children:r.jsx("div",{className:"w-full h-full rounded-full bg-gray-800/50 border-2 border-dashed border-gray-500 flex items-center justify-center hover:border-purple-400 transition-colors",children:r.jsx(Pl,{className:"w-8 h-8 text-gray-400"})})}),r.jsx("div",{className:"text-center mt-3",children:r.jsx("p",{className:"text-gray-400 text-sm",children:"مقعد فارغ"})})]})},j.seatNumber))}),r.jsxs("div",{className:"mt-6 flex flex-wrap gap-3 justify-center",children:[!s&&!o&&b.length===0&&r.jsxs("button",{onClick:c,disabled:i,className:"px-6 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white font-medium transition-colors flex items-center gap-2",children:[r.jsx(yt,{className:"w-4 h-4"}),i?"جاري الطلب...":"طلب المايك"]}),o&&r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("div",{className:"px-4 py-2 bg-yellow-900/50 border border-yellow-500/50 rounded-lg text-yellow-300 flex items-center gap-2",children:[r.jsx(mn,{className:"w-4 h-4"}),r.jsxs("span",{children:["في قائمة الانتظار (المركز ",w(),")"]})]}),r.jsx("button",{onClick:d,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors",children:"إلغاء الطلب"})]})]})]}),t.length>0&&r.jsxs("div",{className:"bg-gradient-to-br from-yellow-900/20 to-orange-900/20 rounded-xl p-6 border border-yellow-500/20",children:[r.jsxs("h3",{className:"text-lg font-bold text-white mb-4 flex items-center gap-2",children:[r.jsx(mn,{className:"w-5 h-5 text-yellow-400"}),"قائمة انتظار المايك (",t.length,")"]}),r.jsx("div",{className:"space-y-3",children:t.map((j,k)=>r.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg",children:[r.jsx("div",{className:"w-8 h-8 bg-yellow-600 rounded-full flex items-center justify-center text-white font-bold text-sm",children:k+1}),r.jsxs("div",{className:"flex-1",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"font-medium text-white",children:j.user.username}),r.jsxs("span",{className:"text-xs text-gray-400",children:["#",j.user.playerId]})]}),r.jsxs("div",{className:"text-xs text-gray-400",children:["طلب منذ ",g(j.requestedAt)]})]}),j.user._id===n.id&&r.jsx("div",{className:"px-2 py-1 bg-yellow-600 rounded text-xs text-white",children:"أنت"})]},j.user._id))})]})]})},Vp=({messages:e,currentUser:t,isInWaitingQueue:n,onSendMessage:s,onRequestMic:l})=>{const[o,i]=y.useState(""),[a,c]=y.useState(!1),d=y.useRef(null),g=y.useRef(null);y.useEffect(()=>{var S;(S=d.current)==null||S.scrollIntoView({behavior:"smooth"})},[e]),y.useEffect(()=>{var S;(S=g.current)==null||S.focus()},[]);const w=async S=>{var u;if(S.preventDefault(),!o.trim()||a)return;const m=o.trim();i(""),c(!0);try{await s(m)}catch(p){console.error("Error sending message:",p),i(m)}finally{c(!1),(u=g.current)==null||u.focus()}},b=S=>{S.key==="Enter"&&!S.shiftKey&&(S.preventDefault(),w(S))},N=S=>new Date(S).toLocaleTimeString("ar-SA",{hour:"2-digit",minute:"2-digit",hour12:!1}),j=S=>{switch(S){case"system":return r.jsx(d0,{className:"w-4 h-4 text-blue-400"});case"mic_request":return r.jsx(yt,{className:"w-4 h-4 text-yellow-400"});default:return r.jsx(fn,{className:"w-4 h-4 text-gray-400"})}},k=(S,m)=>{const u=m===t.id;switch(S){case"system":return"bg-blue-900/30 border-blue-500/30 text-blue-200";case"mic_request":return"bg-yellow-900/30 border-yellow-500/30 text-yellow-200";default:return u?"bg-purple-900/50 border-purple-500/30 text-white":"bg-gray-800/50 border-gray-600/30 text-gray-200"}};return r.jsxs("div",{className:"bg-gradient-to-br from-gray-900/50 to-blue-900/30 rounded-xl border border-blue-500/20 flex flex-col h-[600px]",children:[r.jsxs("div",{className:"p-4 border-b border-gray-700/50",children:[r.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[r.jsx(fn,{className:"w-5 h-5 text-blue-400"}),"المحادثة النصية"]}),r.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:[e.length," رسالة"]})]}),r.jsx("div",{className:"flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800",children:r.jsxs("div",{className:"flex flex-col space-y-3",children:[e.length===0?r.jsxs("div",{className:"text-center text-gray-400 py-8",children:[r.jsx(fn,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),r.jsx("p",{children:"لا توجد رسائل بعد"}),r.jsx("p",{className:"text-sm mt-1",children:"ابدأ المحادثة!"})]}):e.map(S=>r.jsxs("div",{className:`p-3 rounded-lg border ${k(S.messageType,S.sender._id)} ${S.messageType!=="system"&&S.sender._id===t.id?"max-w-[60%] self-end ml-auto":""}`,children:[r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[j(S.messageType),r.jsx("span",{className:"font-medium text-sm",children:S.sender.username}),r.jsxs("span",{className:"text-xs opacity-60",children:["#",S.sender.playerId]})]}),r.jsxs("div",{className:"flex items-center gap-1 text-xs opacity-60",children:[r.jsx(mn,{className:"w-3 h-3"}),r.jsx("span",{children:N(S.timestamp)})]})]}),r.jsx("div",{className:"text-sm leading-relaxed",children:S.content})]},S._id)),r.jsx("div",{ref:d})]})}),r.jsxs("div",{className:"p-4 border-t border-gray-700/50",children:[r.jsxs("form",{onSubmit:w,className:"flex gap-2",children:[r.jsxs("div",{className:"flex-1 relative",children:[r.jsx("input",{ref:g,type:"text",value:o,onChange:S=>i(S.target.value),onKeyPress:b,placeholder:"اكتب رسالتك هنا...",maxLength:500,disabled:a,className:"w-full px-4 py-2 bg-gray-800/50 border border-gray-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500/50 focus:bg-gray-800/70 transition-all disabled:opacity-50 disabled:cursor-not-allowed"}),r.jsxs("div",{className:"absolute bottom-1 left-2 text-xs text-gray-500",children:[o.length,"/500"]})]}),r.jsxs("button",{type:"submit",disabled:!o.trim()||a,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg text-white transition-colors flex items-center gap-2",title:"إرسال الرسالة",children:[r.jsx(Pr,{className:"w-4 h-4"}),a?"جاري الإرسال...":"إرسال"]}),!n&&r.jsxs("button",{type:"button",onClick:l,className:"px-3 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg text-white transition-colors flex items-center gap-1",title:"طلب المايك",children:[r.jsx(yt,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden sm:inline",children:"طلب المايك"})]})]}),n&&r.jsxs("div",{className:"mt-2 p-2 bg-yellow-900/30 border border-yellow-500/30 rounded-lg text-yellow-200 text-sm flex items-center gap-2",children:[r.jsx(mn,{className:"w-4 h-4"}),r.jsx("span",{children:"أنت في قائمة انتظار المايك"})]}),r.jsxs("div",{className:"mt-2 text-xs text-gray-500 flex items-center gap-4",children:[r.jsx("span",{children:"اضغط Enter للإرسال"}),r.jsx("span",{children:"الحد الأقصى: 500 حرف"})]})]})]})},qp=({user:e,wsService:t})=>{const[n,s]=y.useState(null),[l,o]=y.useState([]),[i,a]=y.useState(!0),[c,d]=y.useState(null),[g,w]=y.useState(!1),[b,N]=y.useState(null),[j,k]=y.useState(!1),[S,m]=y.useState(!1),[u,p]=y.useState(!1),[M,L]=y.useState(!1),[R,z]=y.useState([]),[U,D]=y.useState(new Map),v=y.useRef(null),O=async()=>{try{a(!0);const[h,C]=await Promise.all([K.getVoiceRoom(),K.getVoiceRoomMessages()]);s(h),o(C);const T=h.seats.find(P=>P.user&&P.user._id===e.id);T?(w(!0),N(T.seatNumber),m(T.isMuted)):(w(!1),N(null));const F=h.waitingQueue.some(P=>P.user._id===e.id);k(F),d(null)}catch(h){if(console.error("Error loading voice room:",h),h.message&&h.message.includes("مطرود من الغرفة الصوتية")){d(h.message),s({id:"",name:"INFINITY ROOM",description:"غرفة صوتية للمحادثة مع الأصدقاء",maxSeats:5,seats:[],waitingQueue:[],settings:{allowTextChat:!0,autoKickInactive:!1,inactiveTimeoutMinutes:30},isActive:!1});return}d(h.message||"خطأ في تحميل الغرفة الصوتية")}finally{a(!1)}};y.useEffect(()=>{if(!(e!=null&&e.id)){console.warn("⚠️ No user ID available, skipping WebRTC service setup");return}console.log("🔧 Setting up WebRTC Voice Service with user ID:",e.id);try{v.current=new _0(t)}catch(h){console.error("❌ Error creating WebRTC service:",h);return}return v.current&&(v.current.onUserJoined=h=>{console.log(`👤 User joined voice chat: ${h.id}`),z(C=>[...C.filter(T=>T.id!==h.id),h])},v.current.onUserLeft=h=>{console.log(`👋 User left voice chat: ${h}`),z(C=>C.filter(T=>T.id!==h)),D(C=>{const T=new Map(C);return T.delete(h),T})},v.current.onVoiceActivity=h=>{console.log("🎤 Voice activity changed:",h.isSpeaking?"speaking":"silent",`(level: ${h.level})`),D(C=>{const T=new Map(C);return T.set(h.userId,h),T}),e!=null&&e.id&&g?(console.log("📤 Voice activity sent:",h.isSpeaking?"speaking":"silent",`(userId: ${e.id})`),t.send({type:"voice_activity",data:{userId:e.id,level:h.level,isSpeaking:h.isSpeaking,timestamp:Date.now()}})):(e!=null&&e.id||console.warn("⚠️ No currentUserId available for voice activity"),g||console.log("🔍 User not in seat, voice activity not sent"))},v.current.onError=h=>{console.error("❌ WebRTC error:",h),d(`خطأ في الصوت: ${h.message}`)}),()=>{v.current&&(console.log("🧹 Cleaning up WebRTC service"),v.current.leaveRoom().catch(console.error))}},[t,e==null?void 0:e.id]),y.useEffect(()=>{const h=P=>{o(W=>[...W,P])},C=P=>{P.action&&P.userId&&(P.action==="seat_joined"||P.action==="seat_left"||P.action==="mute_toggled")||O(),P.action==="seat_joined"&&g&&P.userId!==e.id&&setTimeout(()=>{var W;(W=v.current)==null||W.sendOffer(P.userId)},1e3)},T=P=>{P.userId&&P.userId!==e.id&&(D(W=>{const Y=new Map(W);return Y.set(P.userId.toString(),{userId:P.userId.toString(),level:P.level,isSpeaking:P.isSpeaking}),Y}),s(W=>({...W,seats:W.seats.map(Y=>{var se;return((se=Y.user)==null?void 0:se._id)===P.userId?{...Y,isSpeaking:P.isSpeaking}:Y})})))};v.current.onVoiceActivity=P=>{s(W=>({...W,seats:W.seats.map(Y=>{var se;return((se=Y.user)==null?void 0:se._id)===e.id?{...Y,isSpeaking:P.isSpeaking}:Y})})),g&&t.send({type:"voice_activity",data:{userId:e.id,level:P.level,isSpeaking:P.isSpeaking}})};const F=P=>{O()};return t.onMessage("voice_room_message",h),t.onMessage("voice_room_update",C),t.onMessage("voice_activity",T),t.onMessage("admin_action_update",F),()=>{t.offMessage("voice_room_message",h),t.offMessage("voice_room_update",C),t.offMessage("voice_activity",T),t.offMessage("admin_action_update",F)}},[t,g,e.id]),y.useEffect(()=>{O()},[]),y.useEffect(()=>{const h=T=>{if(g)return T.preventDefault(),T.returnValue="أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟","أنت حالياً في الغرفة الصوتية. هل تريد المغادرة؟"},C=()=>{g&&navigator.sendBeacon("/api/voice-room/leave-seat",JSON.stringify({userId:e.id}))};return window.addEventListener("beforeunload",h),window.addEventListener("unload",C),()=>{window.removeEventListener("beforeunload",h),window.removeEventListener("unload",C)}},[g,e.id]);const X=async h=>{try{const C=await K.sendVoiceRoomMessage(h);t.send({type:"voice_room_message",data:C.messageData})}catch(C){console.error("Error sending message:",C),d(C.message||"خطأ في إرسال الرسالة")}},ae=async()=>{try{p(!0),await K.requestMic(),t.send({type:"voice_room_update",data:{action:"mic_requested",userId:e.id}}),await O()}catch(h){console.error("Error requesting mic:",h),d(h.message||"خطأ في طلب المايك")}finally{p(!1)}},Pe=async()=>{try{await K.cancelMicRequest(),t.send({type:"voice_room_update",data:{action:"mic_request_cancelled",userId:e.id}}),await O()}catch(h){console.error("Error cancelling mic request:",h),d(h.message||"خطأ في إلغاء طلب المايك")}},Me=async h=>{try{if(p(!0),d(null),await K.joinVoiceSeat(h),w(!0),N(h),m(!1),v.current&&(e!=null&&e.id))try{const C=`voice-room-${(n==null?void 0:n.id)||"default"}`;await v.current.joinRoom(C,e.id),L(!0)}catch(C){console.error("❌ WebRTC initialization failed:",C),d(`فشل في بدء المحادثة الصوتية: ${C.message}`)}localStorage.setItem("isInVoiceRoom","true"),localStorage.setItem("voiceRoomSeat",h.toString()),t.send({type:"voice_room_update",data:{action:"seat_joined",userId:e.id,seatNumber:h}}),await O()}catch(C){console.error("Error joining seat:",C),d(C.message||"خطأ في الانضمام للمقعد"),L(!1)}finally{p(!1)}},Ee=async()=>{var h;try{p(!0),await K.leaveSeat(),w(!1),N(null),m(!1),v.current&&v.current.leaveRoom(),t.send({type:"voice_room_update",data:{action:"seat_left",userId:e.id,seatNumber:b}})}catch(C){console.error("Error leaving seat:",C),(h=C.message)!=null&&h.includes("لست في أي مقعد")||d(C.message||"خطأ في مغادرة المقعد")}finally{p(!1)}},pe=async()=>{try{if(!g){d("يجب أن تكون في مقعد لاستخدام المايك");return}if(!v.current){d("خدمة الصوت غير متاحة - جاري إعادة الاتصال...");return}const h=!S;v.current.setMute(h),m(h);try{await K.toggleMute(h)}catch(C){console.warn("Failed to update server mute state:",C)}t.send({type:"voice_room_update",data:{action:"mute_toggled",userId:e.id,isMuted:h}})}catch(h){console.error("Error toggling mute:",h),d("خطأ في تبديل كتم المايك"),m(!S)}};return i?r.jsx("div",{className:"flex items-center justify-center h-96",children:r.jsxs("div",{className:"text-center",children:[r.jsx(hn,{className:"w-8 h-8 animate-spin mx-auto mb-4 text-purple-400"}),r.jsx("p",{className:"text-gray-300",children:"جاري تحميل الغرفة الصوتية..."})]})}):c?r.jsxs("div",{className:"bg-red-900/20 border border-red-500/30 rounded-lg p-6 text-center",children:[r.jsx("p",{className:"text-red-400 mb-4",children:c}),r.jsx("button",{onClick:O,className:"px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors",children:"إعادة المحاولة"})]}):n?r.jsxs("div",{className:`max-w-7xl mx-auto p-4 sm:p-6 space-y-6 ${g?"pb-24 sm:pb-6":""}`,children:[r.jsx("div",{className:"bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-xl p-4 sm:p-6 border border-purple-500/20",children:r.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[r.jsxs("div",{children:[r.jsxs("h1",{className:"text-xl sm:text-2xl font-bold text-white mb-2 flex items-center gap-3",children:[r.jsx(ts,{className:"w-6 sm:w-8 h-6 sm:h-8 text-purple-400"}),"INFINITY ROOM"]}),r.jsx("p",{className:"text-gray-300 text-sm sm:text-base",children:"غرفة صوتية للمحادثة مع الأصدقاء"})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4",children:[r.jsxs("div",{className:"flex items-center gap-4 text-gray-300",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx(bn,{className:"w-4 sm:w-5 h-4 sm:h-5"}),r.jsxs("span",{className:"text-sm",children:[n.seats.filter(h=>h.user).length,"/",n.maxSeats]})]}),r.jsxs("div",{className:"flex items-center gap-2 bg-black/20 px-2 sm:px-3 py-1 rounded-lg",children:[M?r.jsx(Kh,{className:"w-3 sm:w-4 h-3 sm:h-4 text-green-400"}):r.jsx(Zh,{className:"w-3 sm:w-4 h-3 sm:h-4 text-red-400"}),r.jsx("span",{className:"text-xs sm:text-sm",children:M?"متصل":"غير متصل"})]})]}),g&&r.jsxs("div",{className:"flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto",children:[r.jsx("button",{onClick:pe,className:`w-full sm:w-auto p-3 sm:p-2 rounded-lg transition-colors text-sm font-medium ${S?"bg-red-600 hover:bg-red-700 text-white":"bg-green-600 hover:bg-green-700 text-white"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:r.jsxs("div",{className:"flex items-center justify-center gap-2",children:[S?r.jsx(Xr,{className:"w-5 h-5"}):r.jsx(yt,{className:"w-5 h-5"}),r.jsx("span",{className:"sm:hidden",children:S?"إلغاء الكتم":"كتم المايك"})]})}),r.jsx("button",{onClick:Ee,className:"w-full sm:w-auto px-4 py-3 sm:py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-colors text-sm font-medium",children:"مغادرة المقعد"})]})]})]})}),g&&r.jsx("div",{className:"hidden sm:block mb-6",children:r.jsx("div",{className:"bg-gradient-to-r from-gray-800/50 to-purple-900/30 rounded-xl p-4 border border-purple-500/20",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center gap-3",children:r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),r.jsxs("span",{className:"text-green-400 font-medium",children:["متصل - مقعد ",b]})]})}),r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsxs("button",{onClick:pe,className:`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200 font-medium ${S?"bg-red-600 hover:bg-red-700 text-white shadow-lg shadow-red-600/25":"bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/25"}`,title:S?"إلغاء كتم المايك":"كتم المايك",children:[S?r.jsx(Xr,{className:"w-4 h-4"}):r.jsx(yt,{className:"w-4 h-4"}),r.jsx("span",{children:S?"إلغاء كتم المايك":"كتم المايك"})]}),r.jsx("button",{onClick:Ee,className:"flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg text-white transition-all duration-200 font-medium shadow-lg shadow-red-600/25",title:"مغادرة المقعد",disabled:u,children:r.jsx("span",{children:u?"جاري المغادرة...":"مغادرة المقعد"})})]})]})})}),r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx(Bp,{seats:n.seats,waitingQueue:n.waitingQueue,currentUser:e,isInSeat:g,currentSeatNumber:b,isInWaitingQueue:j,isConnecting:u,onJoinSeat:Me,onRequestMic:ae,onCancelMicRequest:Pe})}),r.jsx("div",{className:"lg:col-span-1",children:r.jsx(Vp,{messages:l,currentUser:e,isInWaitingQueue:j,onSendMessage:X,onRequestMic:ae})})]})]}):r.jsx("div",{className:"text-center text-gray-400",children:r.jsx("p",{children:"لا توجد بيانات للغرفة الصوتية"})})},Hp=({user:e,onLogout:t,wsService:n})=>{var b;const[,s]=I0(),[l,o]=y.useState(()=>{const N=localStorage.getItem("isInVoiceRoom")==="true",j=localStorage.getItem("activeTab");return N?"voice":j||"games"}),[i,a]=y.useState(!1),[c,d]=y.useState(!1);y.useEffect(()=>{const N=()=>{a(window.innerWidth<=768)};return N(),window.addEventListener("resize",N),()=>window.removeEventListener("resize",N)},[]);const g=N=>{o(N),localStorage.setItem("activeTab",N)},w=()=>{localStorage.removeItem("token"),s("/login"),t()};return console.log("USER DATA IN MAIN DASHBOARD:",e),!e||!e.username?r.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-red-900 via-yellow-900 to-black text-white",children:[r.jsx("h1",{className:"text-3xl font-bold mb-4",children:"⚠️ لا توجد بيانات لاعب!"}),r.jsx("pre",{className:"bg-black/60 rounded-lg p-4 text-left text-xs max-w-xl overflow-x-auto mb-4",children:JSON.stringify(e,null,2)}),r.jsx("p",{className:"mb-4",children:"يرجى التأكد من أن حسابك يحتوي على اسم مستخدم وصورة ورصيد."}),r.jsx("button",{onClick:t,className:"px-6 py-2 bg-red-600 rounded-lg text-white font-bold",children:"تسجيل الخروج"})]}):i?r.jsx($p,{userData:e,onLogout:w,wsService:n}):r.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white relative overflow-hidden",children:[r.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[r.jsx("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"}),r.jsx("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"}),r.jsx("div",{className:"absolute top-40 left-40 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"})]}),r.jsx("header",{className:"relative z-10 bg-black/30 backdrop-blur-xl border-b border-white/10 shadow-2xl",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsxs("div",{className:"flex justify-between items-center h-20",children:[r.jsx("div",{className:"flex items-center space-x-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg",children:r.jsx(Tr,{className:"w-7 h-7 text-white"})}),r.jsxs("div",{children:[r.jsxs("h1",{className:"text-2xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:[e.username," - Infinity Box"]}),r.jsx("p",{className:"text-xs text-gray-400",children:"عالم الألعاب المثير"})]})]})}),r.jsx("div",{className:"flex items-center space-x-6",children:r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-yellow-500/30 to-orange-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-yellow-500/50 shadow-lg hover:from-yellow-500/40 hover:to-orange-500/40 transition-all duration-300",children:[r.jsx(Tl,{className:"w-6 h-6 text-yellow-300"}),r.jsx("span",{className:"text-yellow-300 font-bold text-lg",children:((b=e.goldCoins)==null?void 0:b.toLocaleString())||0})]}),r.jsxs("div",{className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500/30 to-purple-500/30 backdrop-blur-sm rounded-lg px-4 py-2 border border-blue-500/50 shadow-lg hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-300",children:[r.jsx(u0,{className:"w-6 h-6 text-blue-300"}),r.jsx("span",{className:"text-blue-300 font-bold text-lg",children:e.pearls||0})]})]})}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("button",{onClick:()=>d(!c),className:"relative p-2 bg-white/10 backdrop-blur-sm rounded-lg hover:bg-white/20 transition-all duration-300 border border-white/20",children:[r.jsx(Rh,{className:"w-5 h-5 text-white"}),r.jsx("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),r.jsxs("div",{className:"flex items-center space-x-3 bg-gradient-to-r from-purple-500/20 to-blue-500/20 backdrop-blur-sm rounded-xl px-4 py-2 border border-purple-500/30 hover:from-purple-500/30 hover:to-blue-500/30 transition-all duration-300 shadow-lg",children:[r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:e.profileImage||"/images/default-avatar.png",alt:e.username,className:"w-12 h-12 rounded-full border-2 border-white/30 shadow-lg object-cover",onError:N=>{N.currentTarget.src="/images/default-avatar.png"}}),r.jsx("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"})]}),r.jsxs("div",{className:"text-right",children:[r.jsx("p",{className:"font-bold text-white text-lg",children:e.username}),r.jsxs("p",{className:"text-xs text-yellow-300",children:["مستوى ",e.level||1," - ",e.experience||0," XP"]})]})]}),r.jsxs("button",{onClick:w,className:"flex items-center space-x-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 backdrop-blur-sm rounded-lg transition-all duration-300 border border-red-500/30 text-red-400 hover:text-red-300",children:[r.jsx(h0,{className:"w-4 h-4"}),r.jsx("span",{className:"text-sm font-medium",children:"خروج"})]})]})]})})}),r.jsx("nav",{className:"relative z-10 bg-black/20 backdrop-blur-xl border-b border-white/10 shadow-lg",children:r.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:r.jsxs("div",{className:"flex space-x-8",children:[r.jsx("button",{onClick:()=>g("games"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="games"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎮 الألعاب"}),r.jsx("button",{onClick:()=>g("leaderboard"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="leaderboard"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🏆 المتصدرين"}),r.jsx("button",{onClick:()=>g("voice"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="voice"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"🎤 الغرفة الصوتية"}),r.jsx("button",{onClick:()=>g("profile"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="profile"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"👤 الملف الشخصي"}),e.isAdmin&&r.jsx("button",{onClick:()=>g("admin"),className:`py-4 px-6 border-b-2 font-medium text-sm transition-all duration-300 rounded-t-lg ${l==="admin"?"border-yellow-400 text-yellow-400 bg-yellow-400/10":"border-transparent text-gray-300 hover:text-white hover:bg-white/5"}`,children:"⚙️ الإدارة"})]})})}),r.jsxs("main",{className:"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[l==="games"&&r.jsx(Ws,{setActiveTab:g}),l==="leaderboard"&&r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(es,{className:"w-12 h-12 text-yellow-400 mr-4"}),r.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:"🏆 المتصدرين"})]}),r.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:r.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة قائمة المتصدرين..."})})]}),l==="voice"&&n&&r.jsx(qp,{user:e,wsService:n}),l==="profile"&&r.jsxs("div",{className:"text-center",children:[r.jsxs("div",{className:"flex items-center justify-center mb-8",children:[r.jsx(pn,{className:"w-12 h-12 text-blue-400 mr-4"}),r.jsx("h2",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"👤 الملف الشخصي"})]}),r.jsx("div",{className:"bg-white/10 backdrop-blur-xl rounded-2xl p-8 border border-white/20",children:r.jsx("p",{className:"text-gray-300 text-lg",children:"قريباً سيتم إضافة الملف الشخصي..."})})]}),l==="admin"&&e.isAdmin&&r.jsx(gi,{userData:e,onLogout:w})]}),c&&r.jsxs("div",{className:"fixed top-20 right-4 z-50 w-80 bg-black/90 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl",children:[r.jsx("div",{className:"p-4 border-b border-white/10",children:r.jsx("h3",{className:"text-lg font-semibold text-white",children:"الإشعارات"})}),r.jsx("div",{className:"p-4",children:r.jsx("p",{className:"text-gray-400 text-center",children:"لا توجد إشعارات جديدة"})})]})]})};function Wp(){const[e,t]=y.useState(!1),[n,s]=y.useState(null),[l,o]=y.useState(!0),[i]=y.useState(()=>new A0(`ws${window.location.protocol==="https:"?"s":""}://${window.location.host}/ws`));y.useEffect(()=>{localStorage.removeItem("activeTab");const d=localStorage.getItem("token");console.log("🔍 App: Checking token:",d?"Token exists":"No token found"),d?(console.log("🔄 App: Attempting to get current user..."),K.getCurrentUser().then(g=>{if(console.log("✅ App: User data received:",g),g&&typeof g=="object")s(g),localStorage.setItem("isAdmin",g.isAdmin?"true":"false");else{const b=localStorage.getItem("isAdmin")==="true";s({id:"",username:"",isAdmin:b})}console.log("🔓 App: Setting authenticated to true"),t(!0);const w=localStorage.getItem("token");w&&i.connect(w).then(()=>{console.log("✅ WebSocket connected on app load")}).catch(b=>{console.error("❌ Failed to connect to WebSocket on app load:",b)})}).catch(g=>{console.log("❌ App: Error getting user:",g),g.message.includes("MULTIPLE_LOGIN")&&alert("تم تسجيل الدخول من جهاز آخر. سيتم تسجيل خروجك من هذا الجهاز."),console.log("🔒 App: Setting authenticated to false"),t(!1),s(null),localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin")}).finally(()=>{console.log("⏹️ App: Loading finished"),o(!1)})):(console.log("🔒 App: No token found, setting authenticated to false"),o(!1))},[]);const a=async d=>{s(d),t(!0);try{const g=localStorage.getItem("token");g&&await i.connect(g)}catch(g){console.error("Failed to connect to WebSocket:",g)}},c=()=>{localStorage.removeItem("token"),localStorage.removeItem("username"),localStorage.removeItem("isAdmin"),i.disconnect(),t(!1),s(null)};return l?(console.log("⏳ App: Showing loading screen"),r.jsx("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-indigo-950 to-slate-800 flex items-center justify-center",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),r.jsx("p",{className:"text-white text-lg",children:"جاري التحميل..."})]})})):e?(console.log("🏠 App: Showing MainDashboard (authenticated)"),n?r.jsx(Hp,{user:n,onLogout:c,wsService:i}):null):(console.log("🔐 App: Showing AuthPage (not authenticated)"),r.jsx(np,{onAuthSuccess:a}))}l0(document.getElementById("root")).render(r.jsx(y.StrictMode,{children:r.jsx(Wp,{})}));
