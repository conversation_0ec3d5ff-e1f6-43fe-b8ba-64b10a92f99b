# أوامر بناء التطبيق
# ===================

## أوامر البناء الأساسية:

### 1. تطوير التطبيق (Development)
npm run dev
# يشغل التطبيق في وضع التطوير
# يستخدم NODE_ENV=development

### 2. بناء التطبيق للإنتاج (Production Build)
npm run build
# يبني التطبيق باستخدام Vite
# ينشئ ملفات مُحسّنة للإنتاج

### 3. تشغيل التطبيق في وضع الإنتاج
npm run start
# يشغل التطبيق في وضع الإنتاج
# يستخدم NODE_ENV=production

### 4. فحص TypeScript
npm run check
# يتحقق من أخطاء TypeScript في الكود

### 5. إنشاء حساب مدير
npm run create-admin
# ينشئ حساب مدير جديد

## أوامر إضافية مفيدة:

### تثبيت التبعيات:
npm install

### تشغيل التطبيق مباشرة:
npm run dev

### بناء ثم تشغيل:
npm run build && npm run start

## معلومات تقنية:
- التطبيق يستخدم Vite كأداة بناء
- React مع TypeScript للواجهة الأمامية
- Express.js للخادم الخلفي
- Tailwind CSS للتصميم

## تسلسل العمل الموصى به:
1. npm install (لتثبيت التبعيات)
2. npm run dev (للتطوير)
3. npm run build (للإنتاج)
4. npm run start (لتشغيل الإنتاج)

## ملاحظات:
- تأكد من وجود Node.js مثبت على النظام
- تأكد من وجود جميع ملفات التكوين المطلوبة
- في حالة وجود أخطاء، استخدم npm run check لفحص TypeScript 