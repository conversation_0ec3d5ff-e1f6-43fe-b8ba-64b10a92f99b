<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار المحادثة الصوتية - INFINITYBOX25</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #ffd700;
        }
        
        .button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        .button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(46, 204, 113, 0.3);
            border: 1px solid #2ecc71;
            color: #2ecc71;
        }
        
        .status.error {
            background: rgba(231, 76, 60, 0.3);
            border: 1px solid #e74c3c;
            color: #e74c3c;
        }
        
        .status.info {
            background: rgba(52, 152, 219, 0.3);
            border: 1px solid #3498db;
            color: #3498db;
        }
        
        .log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .audio-visualizer {
            width: 100%;
            height: 60px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
        }
        
        .audio-bar {
            position: absolute;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to top, #ff6b6b, #ffd700);
            border-radius: 2px;
            transition: height 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 اختبار المحادثة الصوتية - INFINITYBOX25</h1>
        
        <div class="test-section">
            <h3>🔧 اختبار الوصول للمايك</h3>
            <button class="button" onclick="testMicrophone()">اختبار المايك</button>
            <button class="button" onclick="stopMicrophone()">إيقاف المايك</button>
            <div id="micStatus" class="status info">جاهز لاختبار المايك</div>
            <div class="audio-visualizer" id="audioVisualizer">
                <div class="audio-bar" id="audioBar"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🌐 اختبار الاتصال بالخادم</h3>
            <button class="button" onclick="testWebSocket()">اختبار WebSocket</button>
            <button class="button" onclick="testVoiceRoom()">اختبار الغرفة الصوتية</button>
            <div id="wsStatus" class="status info">جاهز لاختبار الاتصال</div>
        </div>
        
        <div class="test-section">
            <h3>🎯 اختبار WebRTC</h3>
            <button class="button" onclick="testWebRTC()">اختبار WebRTC</button>
            <button class="button" onclick="testVoiceActivity()">اختبار كشف الصوت</button>
            <div id="webrtcStatus" class="status info">جاهز لاختبار WebRTC</div>
        </div>
        
        <div class="test-section">
            <h3>📊 سجل الاختبارات</h3>
            <div id="testLog" class="log"></div>
        </div>
    </div>

    <script>
        let localStream = null;
        let audioContext = null;
        let analyser = null;
        let wsConnection = null;
        let isSpeaking = false;
        let voiceActivityThreshold = 20;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#e74c3c' : type === 'success' ? '#2ecc71' : '#3498db';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testMicrophone() {
            try {
                log('🎤 بدء اختبار المايك...', 'info');
                
                localStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 48000,
                        channelCount: 1
                    }
                });

                document.getElementById('micStatus').className = 'status success';
                document.getElementById('micStatus').textContent = '✅ تم الوصول للمايك بنجاح';
                
                log('✅ تم الوصول للمايك بنجاح', 'success');
                
                // إعداد كشف النشاط الصوتي
                setupVoiceActivityDetection();
                
            } catch (error) {
                document.getElementById('micStatus').className = 'status error';
                document.getElementById('micStatus').textContent = '❌ فشل في الوصول للمايك: ' + error.message;
                log('❌ فشل في الوصول للمايك: ' + error.message, 'error');
            }
        }

        function stopMicrophone() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
                
                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                    analyser = null;
                }
                
                document.getElementById('micStatus').className = 'status info';
                document.getElementById('micStatus').textContent = '🔇 تم إيقاف المايك';
                log('🔇 تم إيقاف المايك', 'info');
            }
        }

        function setupVoiceActivityDetection() {
            if (!localStream) return;

            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                analyser = audioContext.createAnalyser();
                const microphone = audioContext.createMediaStreamSource(localStream);

                analyser.fftSize = 512;
                analyser.smoothingTimeConstant = 0.8;
                microphone.connect(analyser);

                monitorVoiceActivity();
                log('🎵 تم إعداد كشف النشاط الصوتي', 'success');
            } catch (error) {
                log('⚠️ فشل في إعداد كشف النشاط الصوتي: ' + error.message, 'error');
            }
        }

        function monitorVoiceActivity() {
            if (!analyser) return;

            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            const audioBar = document.getElementById('audioBar');

            const checkActivity = () => {
                if (!analyser) return;

                analyser.getByteFrequencyData(dataArray);
                const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
                const level = Math.min(100, (average / 128) * 100);

                // تحديث شريط الصوت
                audioBar.style.height = level + '%';

                const currentlySpeaking = level > voiceActivityThreshold;

                if (currentlySpeaking !== isSpeaking) {
                    isSpeaking = currentlySpeaking;
                    log(`🎤 حالة الصوت: ${isSpeaking ? 'يتحدث' : 'صامت'} (المستوى: ${level.toFixed(1)})`, isSpeaking ? 'success' : 'info');
                }

                requestAnimationFrame(checkActivity);
            };

            checkActivity();
        }

        async function testWebSocket() {
            try {
                log('🌐 بدء اختبار WebSocket...', 'info');
                
                const wsUrl = 'wss://infinitybox25.onrender.com';
                wsConnection = new WebSocket(wsUrl);

                wsConnection.onopen = () => {
                    document.getElementById('wsStatus').className = 'status success';
                    document.getElementById('wsStatus').textContent = '✅ تم الاتصال بالخادم بنجاح';
                    log('✅ تم الاتصال بالخادم بنجاح', 'success');
                };

                wsConnection.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 رسالة من الخادم: ${data.type || 'unknown'}`, 'info');
                    } catch (error) {
                        log(`📨 رسالة نصية: ${event.data}`, 'info');
                    }
                };

                wsConnection.onerror = (error) => {
                    document.getElementById('wsStatus').className = 'status error';
                    document.getElementById('wsStatus').textContent = '❌ خطأ في الاتصال بالخادم';
                    log('❌ خطأ في الاتصال بالخادم: ' + error, 'error');
                };

                wsConnection.onclose = () => {
                    document.getElementById('wsStatus').className = 'status info';
                    document.getElementById('wsStatus').textContent = '🔌 تم إغلاق الاتصال';
                    log('🔌 تم إغلاق الاتصال', 'info');
                };

            } catch (error) {
                document.getElementById('wsStatus').className = 'status error';
                document.getElementById('wsStatus').textContent = '❌ فشل في الاتصال: ' + error.message;
                log('❌ فشل في الاتصال: ' + error.message, 'error');
            }
        }

        async function testVoiceRoom() {
            try {
                log('🎤 بدء اختبار الغرفة الصوتية...', 'info');
                
                const response = await fetch('https://infinitybox25.onrender.com/api/voice-room', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const roomData = await response.json();
                    log('✅ تم الوصول للغرفة الصوتية بنجاح', 'success');
                    log(`📊 معلومات الغرفة: ${roomData.seats?.length || 0} مقاعد، ${roomData.maxUsers || 0} مستخدمين كحد أقصى`, 'info');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

            } catch (error) {
                log('❌ فشل في الوصول للغرفة الصوتية: ' + error.message, 'error');
            }
        }

        async function testWebRTC() {
            try {
                log('🎯 بدء اختبار WebRTC...', 'info');
                
                // اختبار إنشاء RTCPeerConnection
                const config = {
                    iceServers: [
                        { urls: 'stun:stun.l.google.com:19302' },
                        { urls: 'stun:stun1.l.google.com:19302' }
                    ]
                };

                const peerConnection = new RTCPeerConnection(config);
                
                // إضافة الصوت المحلي إذا كان متاحاً
                if (localStream) {
                    localStream.getTracks().forEach(track => {
                        peerConnection.addTrack(track, localStream);
                    });
                    log('✅ تم إضافة الصوت المحلي لـ WebRTC', 'success');
                }

                // اختبار إنشاء offer
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                log('✅ تم إنشاء WebRTC offer بنجاح', 'success');
                log('🎯 اختبار WebRTC مكتمل', 'success');

                // تنظيف
                peerConnection.close();

            } catch (error) {
                log('❌ فشل في اختبار WebRTC: ' + error.message, 'error');
            }
        }

        function testVoiceActivity() {
            if (!localStream) {
                log('❌ يجب تشغيل المايك أولاً', 'error');
                return;
            }

            log('🎵 بدء اختبار كشف النشاط الصوتي...', 'info');
            log('🎤 تحدث الآن لاختبار كشف النشاط الصوتي', 'info');
            
            // إعادة إعداد كشف النشاط الصوتي
            setupVoiceActivityDetection();
        }

        // تنظيف عند إغلاق الصفحة
        window.addEventListener('beforeunload', () => {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
            }
            if (wsConnection) {
                wsConnection.close();
            }
        });

        log('🚀 تم تحميل صفحة اختبار المحادثة الصوتية', 'success');
    </script>
</body>
</html> 